let e,t,n=!1,l=!1;const s="undefined"!=typeof window?window:{},o=s.document||{head:{}},i={t:0,l:"",jmp:e=>e(),raf:e=>requestAnimationFrame(e),ael:(e,t,n,l)=>e.addEventListener(t,n,l),rel:(e,t,n,l)=>e.removeEventListener(t,n,l),ce:(e,t)=>new CustomEvent(e,t)},c=e=>Promise.resolve(e),r=(()=>{try{return new CSSStyleSheet,"function"==typeof(new CSSStyleSheet).replaceSync}catch(e){}return!1})(),u=(e,t,n)=>{n&&n.map((([n,l,s])=>{const o=a(e,n),c=f(t,s),r=h(n);i.ael(o,l,c,r),(t.o=t.o||[]).push((()=>i.rel(o,l,c,r)))}))},f=(e,t)=>n=>{try{256&e.t?e.i[t](n):(e.u=e.u||[]).push([t,n])}catch(e){ee(e)}},a=(e,t)=>4&t?o:e,h=e=>0!=(2&e),d="http://www.w3.org/1999/xlink",p=new WeakMap,y=e=>"sc-"+e.h,m={},$=e=>"object"==(e=typeof e)||"function"===e,w=(e,t,...n)=>{let l=null,s=null,o=!1,i=!1;const c=[],r=t=>{for(let n=0;n<t.length;n++)l=t[n],Array.isArray(l)?r(l):null!=l&&"boolean"!=typeof l&&((o="function"!=typeof e&&!$(l))&&(l+=""),o&&i?c[c.length-1].p+=l:c.push(o?b(null,l):l),i=o)};if(r(n),t){t.key&&(s=t.key);{const e=t.className||t.class;e&&(t.class="object"!=typeof e?e:Object.keys(e).filter((t=>e[t])).join(" "))}}if("function"==typeof e)return e(null===t?{}:t,c,v);const u=b(e,null);return u.m=t,c.length>0&&(u.$=c),u.g=s,u},b=(e,t)=>({t:0,v:e,p:t,j:null,$:null,m:null,g:null}),g={},v={forEach:(e,t)=>e.map(j).forEach(t),map:(e,t)=>e.map(j).map(t).map(k)},j=e=>({vattrs:e.m,vchildren:e.$,vkey:e.g,vname:e.k,vtag:e.v,vtext:e.p}),k=e=>{if("function"==typeof e.vtag){const t=Object.assign({},e.vattrs);return e.vkey&&(t.key=e.vkey),e.vname&&(t.name=e.vname),w(e.vtag,t,...e.vchildren||[])}const t=b(e.vtag,e.vtext);return t.m=e.vattrs,t.$=e.vchildren,t.g=e.vkey,t.k=e.vname,t},S=(e,t,n,l,o,c)=>{if(n!==l){let r=Z(e,t),u=t.toLowerCase();if("class"===t){const t=e.classList,s=M(n),o=M(l);t.remove(...s.filter((e=>e&&!o.includes(e)))),t.add(...o.filter((e=>e&&!s.includes(e))))}else if("style"===t){for(const t in n)l&&null!=l[t]||(t.includes("-")?e.style.removeProperty(t):e.style[t]="");for(const t in l)n&&l[t]===n[t]||(t.includes("-")?e.style.setProperty(t,l[t]):e.style[t]=l[t])}else if("key"===t);else if("ref"===t)l&&l(e);else if(r||"o"!==t[0]||"n"!==t[1]){const s=$(l);if((r||s&&null!==l)&&!o)try{if(e.tagName.includes("-"))e[t]=l;else{const s=null==l?"":l;"list"===t?r=!1:null!=n&&e[t]==s||(e[t]=s)}}catch(e){}let i=!1;u!==(u=u.replace(/^xlink\:?/,""))&&(t=u,i=!0),null==l||!1===l?!1===l&&""!==e.getAttribute(t)||(i?e.removeAttributeNS(d,t):e.removeAttribute(t)):(!r||4&c||o)&&!s&&(l=!0===l?"":l,i?e.setAttributeNS(d,t,l):e.setAttribute(t,l))}else t="-"===t[2]?t.slice(3):Z(s,u)?u.slice(2):u[2]+t.slice(3),n&&i.rel(e,t,n,!1),l&&i.ael(e,t,l,!1)}},O=/\s/,M=e=>e?e.split(O):[],x=(e,t,n,l)=>{const s=11===t.j.nodeType&&t.j.host?t.j.host:t.j,o=e&&e.m||m,i=t.m||m;for(l in o)l in i||S(s,l,o[l],void 0,n,t.t);for(l in i)S(s,l,o[l],i[l],n,t.t)},C=(t,l,s)=>{const i=l.$[s];let c,r,u=0;if(null!==i.p)c=i.j=o.createTextNode(i.p);else{if(n||(n="svg"===i.v),c=i.j=o.createElementNS(n?"http://www.w3.org/2000/svg":"http://www.w3.org/1999/xhtml",i.v),n&&"foreignObject"===i.v&&(n=!1),x(null,i,n),null!=e&&c["s-si"]!==e&&c.classList.add(c["s-si"]=e),i.$)for(u=0;u<i.$.length;++u)r=C(t,i,u),r&&c.appendChild(r);"svg"===i.v?n=!1:"foreignObject"===c.tagName&&(n=!0)}return c},P=(e,n,l,s,o,i)=>{let c,r=e;for(r.shadowRoot&&r.tagName===t&&(r=r.shadowRoot);o<=i;++o)s[o]&&(c=C(null,l,o),c&&(s[o].j=c,r.insertBefore(c,n)))},E=(e,t,n,l,s)=>{for(;t<=n;++t)(l=e[t])&&(s=l.j,T(l),s.remove())},L=(e,t)=>e.v===t.v&&e.g===t.g,N=(e,t)=>{const l=t.j=e.j,s=e.$,o=t.$,i=t.v,c=t.p;null===c?(n="svg"===i||"foreignObject"!==i&&n,"slot"===i||x(e,t,n),null!==s&&null!==o?((e,t,n,l)=>{let s,o,i=0,c=0,r=0,u=0,f=t.length-1,a=t[0],h=t[f],d=l.length-1,p=l[0],y=l[d];for(;i<=f&&c<=d;)if(null==a)a=t[++i];else if(null==h)h=t[--f];else if(null==p)p=l[++c];else if(null==y)y=l[--d];else if(L(a,p))N(a,p),a=t[++i],p=l[++c];else if(L(h,y))N(h,y),h=t[--f],y=l[--d];else if(L(a,y))N(a,y),e.insertBefore(a.j,h.j.nextSibling),a=t[++i],y=l[--d];else if(L(h,p))N(h,p),e.insertBefore(h.j,a.j),h=t[--f],p=l[++c];else{for(r=-1,u=i;u<=f;++u)if(t[u]&&null!==t[u].g&&t[u].g===p.g){r=u;break}r>=0?(o=t[r],o.v!==p.v?s=C(t&&t[c],n,r):(N(o,p),t[r]=void 0,s=o.j),p=l[++c]):(s=C(t&&t[c],n,c),p=l[++c]),s&&a.j.parentNode.insertBefore(s,a.j)}i>f?P(e,null==l[d+1]?null:l[d+1].j,n,l,c,d):c>d&&E(t,i,f)})(l,s,t,o):null!==o?(null!==e.p&&(l.textContent=""),P(l,null,t,o,0,o.length-1)):null!==s&&E(s,0,s.length-1),n&&"svg"===i&&(n=!1)):e.p!==c&&(l.data=c)},T=e=>{e.m&&e.m.ref&&e.m.ref(null),e.$&&e.$.map(T)},U=e=>Q(e).S,W=(e,t,n)=>{const l=U(e);return{emit:e=>A(l,t,{bubbles:!!(4&n),composed:!!(2&n),cancelable:!!(1&n),detail:e})}},A=(e,t,n)=>{const l=i.ce(t,n);return e.dispatchEvent(l),l},D=(e,t)=>{t&&!e.O&&t["s-p"]&&t["s-p"].push(new Promise((t=>e.O=t)))},H=(e,t)=>{if(e.t|=16,!(4&e.t))return D(e,e.M),fe((()=>R(e,t)));e.t|=512},R=(e,t)=>{const n=e.i;let l;return t&&(e.t|=256,e.u&&(e.u.map((([e,t])=>z(n,e,t))),e.u=null),l=z(n,"componentWillLoad")),B(l,(()=>q(e,n,t)))},q=async(e,t,n)=>{const l=e.S,s=l["s-rc"];n&&(e=>{const t=e.C,n=e.S,l=t.t,s=((e,t)=>{let n=y(t);const l=le.get(n);if(e=11===e.nodeType?e:o,l)if("string"==typeof l){let t,s=p.get(e=e.head||e);s||p.set(e,s=new Set),s.has(n)||(t=o.createElement("style"),t.innerHTML=l,e.insertBefore(t,e.querySelector("link")),s&&s.add(n))}else e.adoptedStyleSheets.includes(l)||(e.adoptedStyleSheets=[...e.adoptedStyleSheets,l]);return n})(n.shadowRoot?n.shadowRoot:n.getRootNode(),t);10&l&&(n["s-sc"]=s,n.classList.add(s+"-h"))})(e);F(e,t),s&&(s.map((e=>e())),l["s-rc"]=void 0);{const t=l["s-p"],n=()=>V(e);0===t.length?n():(Promise.all(t).then(n),e.t|=4,t.length=0)}},F=(n,l)=>{try{l=l.render(),n.t&=-17,n.t|=2,((n,l)=>{const s=n.S,o=n.C,i=n.P||b(null,null),c=(e=>e&&e.v===g)(l)?l:w(null,null,l);t=s.tagName,o.L&&(c.m=c.m||{},o.L.map((([e,t])=>c.m[t]=s[e]))),c.v=null,c.t|=4,n.P=c,c.j=i.j=s.shadowRoot||s,e=s["s-sc"],N(i,c)})(n,l)}catch(e){ee(e,n.S)}return null},V=e=>{const t=e.S,n=e.i,l=e.M;64&e.t?z(n,"componentDidUpdate"):(e.t|=64,G(t),z(n,"componentDidLoad"),e.N(t),l||_()),e.T(t),e.O&&(e.O(),e.O=void 0),512&e.t&&ue((()=>H(e,!1))),e.t&=-517},_=()=>{G(o.documentElement),ue((()=>A(s,"appload",{detail:{namespace:"deckdeckgo-highlight-code"}})))},z=(e,t,n)=>{if(e&&e[t])try{return e[t](n)}catch(e){ee(e)}},B=(e,t)=>e&&e.then?e.then(t):t(),G=e=>e.classList.add("hydrated"),I=(e,t,n)=>{if(t.U){e.watchers&&(t.W=e.watchers);const l=Object.entries(t.U),s=e.prototype;if(l.map((([e,[l]])=>{31&l||2&n&&32&l?Object.defineProperty(s,e,{get(){return((e,t)=>Q(this).A.get(t))(0,e)},set(n){((e,t,n,l)=>{const s=Q(e),o=s.S,i=s.A.get(t),c=s.t,r=s.i;if(n=((e,t)=>null==e||$(e)?e:4&t?"false"!==e&&(""===e||!!e):1&t?e+"":e)(n,l.U[t][0]),(!(8&c)||void 0===i)&&n!==i&&(!Number.isNaN(i)||!Number.isNaN(n))&&(s.A.set(t,n),r)){if(l.W&&128&c){const e=l.W[t];e&&e.map((e=>{try{r[e](n,i,t)}catch(e){ee(e,o)}}))}2==(18&c)&&H(s,!1)}})(this,e,n,t)},configurable:!0,enumerable:!0}):1&n&&64&l&&Object.defineProperty(s,e,{value(...t){const n=Q(this);return n.D.then((()=>n.i[e](...t)))}})})),1&n){const n=new Map;s.attributeChangedCallback=function(e,t,l){i.jmp((()=>{const t=n.get(e);if(this.hasOwnProperty(t))l=this[t],delete this[t];else if(s.hasOwnProperty(t)&&"number"==typeof this[t]&&this[t]==l)return;this[t]=(null!==l||"boolean"!=typeof this[t])&&l}))},e.observedAttributes=l.filter((([e,t])=>15&t[0])).map((([e,l])=>{const s=l[1]||e;return n.set(s,e),512&l[0]&&t.L.push([e,s]),s}))}}return e},J=(e,t={})=>{const n=[],l=t.exclude||[],c=s.customElements,f=o.head,a=f.querySelector("meta[charset]"),h=o.createElement("style"),d=[];let p,m=!0;Object.assign(i,t),i.l=new URL(t.resourcesUrl||"./",o.baseURI).href,e.map((e=>{e[1].map((t=>{const s={t:t[0],h:t[1],U:t[2],H:t[3]};s.U=t[2],s.H=t[3],s.L=[],s.W={};const o=s.h,f=class extends HTMLElement{constructor(e){super(e),Y(e=this,s),1&s.t&&e.attachShadow({mode:"open"})}connectedCallback(){p&&(clearTimeout(p),p=null),m?d.push(this):i.jmp((()=>(e=>{if(0==(1&i.t)){const t=Q(e),n=t.C,l=()=>{};if(1&t.t)u(e,t,n.H);else{t.t|=1;{let n=e;for(;n=n.parentNode||n.host;)if(n["s-p"]){D(t,t.M=n);break}}n.U&&Object.entries(n.U).map((([t,[n]])=>{if(31&n&&e.hasOwnProperty(t)){const n=e[t];delete e[t],e[t]=n}})),(async(e,t,n,l,s)=>{if(0==(32&t.t)){{if(t.t|=32,(s=ne(n)).then){const e=()=>{};s=await s,e()}s.isProxied||(n.W=s.watchers,I(s,n,2),s.isProxied=!0);const e=()=>{};t.t|=8;try{new s(t)}catch(e){ee(e)}t.t&=-9,t.t|=128,e()}if(s.style){let e=s.style;const t=y(n);if(!le.has(t)){const l=()=>{};((e,t,n)=>{let l=le.get(e);r&&n?(l=l||new CSSStyleSheet,"string"==typeof l?l=t:l.replaceSync(t)):l=t,le.set(e,l)})(t,e,!!(1&n.t)),l()}}}const o=t.M,i=()=>H(t,!0);o&&o["s-rc"]?o["s-rc"].push(i):i()})(0,t,n)}l()}})(this)))}disconnectedCallback(){i.jmp((()=>(()=>{if(0==(1&i.t)){const e=Q(this);e.o&&(e.o.map((e=>e())),e.o=void 0)}})()))}componentOnReady(){return Q(this).R}};s.q=e[0],l.includes(o)||c.get(o)||(n.push(o),c.define(o,I(f,s,1)))}))})),h.innerHTML=n+"{visibility:hidden}.hydrated{visibility:inherit}",h.setAttribute("data-styles",""),f.insertBefore(h,a?a.nextSibling:f.firstChild),m=!1,d.length?d.map((e=>e.connectedCallback())):i.jmp((()=>p=setTimeout(_,30)))},K=new WeakMap,Q=e=>K.get(e),X=(e,t)=>K.set(t.i=e,t),Y=(e,t)=>{const n={t:0,S:e,C:t,A:new Map};return n.D=new Promise((e=>n.T=e)),n.R=new Promise((e=>n.N=e)),e["s-p"]=[],e["s-rc"]=[],u(e,n,t.H),K.set(e,n)},Z=(e,t)=>t in e,ee=(e,t)=>(0,console.error)(e,t),te=new Map,ne=e=>{const t=e.h.replace(/-/g,"_"),n=e.q,l=te.get(n);return l?l[t]:import(`./${n}.entry.js`).then((e=>(te.set(n,e),e[t])),ee)
/*!__STENCIL_STATIC_IMPORT_SWITCH__*/},le=new Map,se=[],oe=[],ie=(e,t)=>n=>{e.push(n),l||(l=!0,t&&4&i.t?ue(re):i.raf(re))},ce=e=>{for(let t=0;t<e.length;t++)try{e[t](performance.now())}catch(e){ee(e)}e.length=0},re=()=>{ce(se),ce(oe),(l=se.length>0)&&i.raf(re)},ue=e=>c().then(e),fe=ie(oe,!0);export{g as H,J as b,W as c,U as g,w as h,c as p,X as r}