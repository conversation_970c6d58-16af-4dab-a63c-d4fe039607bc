:host {
  display: block;
  position: absolute;
  inset: auto 0 0 auto;
}

button {
  width: 1.6rem;
  height: 1.6rem;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 16px;
  border-radius: 50%;
  background: #f4f5f8;
  color: #000000;
  border: 1px solid transparent;
  outline: none;
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.12);
  isolation: isolate;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.15s ease-out;
}
button:active {
  box-shadow: none;
  transform: translateX(1px) translateY(1px);
}