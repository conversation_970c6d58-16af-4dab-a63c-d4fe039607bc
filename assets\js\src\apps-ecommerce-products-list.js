import{c as g,i as m}from"../../admin.bundle-DI9_jvUJ.js";/* empty css                               *//* empty css                           */import"../../main-Cyta4iCA.js";VirtualSelect.init({ele:"#sampleSelect",options:[{label:"All",value:"All"},{label:"Watch",value:"Watch"},{label:"Footwear",value:"Footwear"},{label:"Fashion",value:"Fashion"},{label:"Bags",value:"Bags"},{label:"Accessories",value:"Accessories"}]});var f=document.getElementById("slider");noUiSlider.create(f,{start:[20,80],connect:!0,range:{min:0,max:100}});const b=[{id:"PEP-19115",name:"Blouse Ruffle Tube top",category:"Fashion",inStock:!0,price:"$14.99",quantity:154,revenue:"$15,236",status:"Published",image:"assets/images/products/img-01.png"},{id:"PEP-19116",name:"Cotton T-shirt",category:"Apparel",inStock:!0,price:"$24.99",quantity:89,revenue:"$12,350",status:"Published",image:"assets/images/products/img-02.png"},{id:"PEP-19117",name:"Wireless Headphones",category:"Electronics",inStock:!0,price:"$99.99",quantity:45,revenue:"$8,750",status:"Published",image:"assets/images/products/img-03.png"},{id:"PEP-19118",name:"Leather Wallet",category:"Accessories",inStock:!1,price:"$35.50",quantity:0,revenue:"$0",status:"Inactive",image:"assets/images/products/img-04.png"},{id:"PEP-19119",name:"Smart Watch",category:"Electronics",inStock:!0,price:"$199.99",quantity:32,revenue:"$17,890",status:"Published",image:"assets/images/products/img-05.png"},{id:"PEP-19120",name:"Running Shoes",category:"Footwear",inStock:!0,price:"$79.99",quantity:67,revenue:"$9,875",status:"Published",image:"assets/images/products/img-06.png"},{id:"PEP-19121",name:"Denim Jeans",category:"Fashion",inStock:!0,price:"$45.99",quantity:120,revenue:"$13,450",status:"Published",image:"assets/images/products/img-07.png"},{id:"PEP-19122",name:"Sunglasses",category:"Accessories",inStock:!1,price:"$29.99",quantity:0,revenue:"$0",status:"Inactive",image:"assets/images/products/img-08.png"},{id:"PEP-19123",name:"Backpack",category:"Bags",inStock:!0,price:"$59.99",quantity:43,revenue:"$7,890",status:"Published",image:"assets/images/products/img-09.png"},{id:"PEP-19124",name:"Water Bottle",category:"Accessories",inStock:!0,price:"$19.99",quantity:78,revenue:"$4,320",status:"Published",image:"assets/images/products/img-10.png"},{id:"PEP-19125",name:"Gaming Mouse",category:"Electronics",inStock:!0,price:"$49.99",quantity:55,revenue:"$6,500",status:"Published",image:"assets/images/products/img-11.png"},{id:"PEP-19126",name:"Yoga Mat",category:"Fitness",inStock:!0,price:"$29.99",quantity:40,revenue:"$3,200",status:"Published",image:"assets/images/products/img-12.png"},{id:"PEP-19127",name:"Bluetooth Speaker",category:"Electronics",inStock:!0,price:"$89.99",quantity:33,revenue:"$4,200",status:"Published",image:"assets/images/products/img-13.png"},{id:"PEP-19128",name:"Leather Belt",category:"Accessories",inStock:!1,price:"$24.99",quantity:0,revenue:"$0",status:"Inactive",image:"assets/images/products/img-14.png"},{id:"PEP-19129",name:"Formal Shirt",category:"Fashion",inStock:!0,price:"$39.99",quantity:90,revenue:"$7,199",status:"Published",image:"assets/images/products/img-15.png"},{id:"PEP-19130",name:"Travel Backpack",category:"Bags",inStock:!0,price:"$69.99",quantity:29,revenue:"$2,900",status:"Published",image:"assets/images/products/img-16.png"},{id:"PEP-19131",name:"Winter Jacket",category:"Fashion",inStock:!0,price:"$119.99",quantity:18,revenue:"$2,100",status:"Published",image:"assets/images/products/img-17.png"}];class k{constructor(e){this.data=e||[],this.tableBody=document.querySelector("tbody"),this.tableHeaders=document.querySelectorAll("th[data-sort]"),this.checkAll=document.getElementById("checkboxDataAll"),this.trashButton=document.querySelector(".trash-button"),this.deleteModal=new window.bootstrap.Modal(document.getElementById("deleteModal")),this.deleteConfirmBtn=document.querySelector("#deleteModal .delete-btn"),this.searchInput=document.getElementById("searchProductInput"),this.paginationContainer=document.querySelector(".products-pagination"),this.filterForm=document.querySelector("#filterForm"),this.currentPage=1,this.itemsPerPage=10,this.filteredData=[...this.data],this.selectedRows=[],this.searchTerm="",this.filters={published:!1,inactive:!1},this.sortConfig={key:null,direction:"asc"},this.renderTable(),this.init()}init(){this.initCheckAll(),this.initDeleteFunctionality(),this.initStatusToggle(),this.initSearch(),this.initPagination(),this.initFilters(),this.initSorting()}initSorting(){this.tableHeaders.forEach(e=>{e.addEventListener("click",()=>{const t=e.getAttribute("data-sort");this.sortData(t)})})}sortData(e){this.sortConfig.key===e?this.sortConfig.direction=this.sortConfig.direction==="asc"?"desc":"asc":(this.sortConfig.key=e,this.sortConfig.direction="asc"),this.updateSortIndicators(),this.filteredData.sort((t,s)=>{let i=t[e],a=s[e];return e==="price"||e==="revenue"?(i=parseFloat(i.replace(/[^0-9.-]+/g,"")),a=parseFloat(a.replace(/[^0-9.-]+/g,""))):typeof i=="string"&&(i=i.toLowerCase(),a=a.toLowerCase()),i<a?this.sortConfig.direction==="asc"?-1:1:i>a?this.sortConfig.direction==="asc"?1:-1:0}),this.currentPage=1,this.renderTable()}updateSortIndicators(){if(this.tableHeaders.forEach(e=>{e.classList.remove("sorted-asc","sorted-desc");const t=e.querySelector(".sort-icon");t&&t.remove()}),this.sortConfig.key){const e=document.querySelector(`th[data-sort="${this.sortConfig.key}"]`);if(e){e.classList.add(this.sortConfig.direction==="asc"?"sorted-asc":"sorted-desc");const t=document.createElement("span");t.className="sort-icon ms-1",t.innerHTML=this.sortConfig.direction==="asc"?'<i class="ri-arrow-up-line ms-1"></i>':'<i class="ri-arrow-down-line ms-1"></i>',e.appendChild(t)}}}renderTable(){this.tableBody.innerHTML="";const e=(this.currentPage-1)*this.itemsPerPage,t=e+this.itemsPerPage,s=this.filteredData.slice(e,t);s.length===0?this.renderEmptyState():s.forEach((i,a)=>{const r=e+a;this.tableBody.appendChild(this.createTableRow(i,r))}),this.renderPagination(),this.updatePageInfo(),this.updateSortIndicators(),this.checkboxes=document.querySelectorAll('.form-check-input[id^="checkboxData"]'),this.statusSwitches=document.querySelectorAll('.form-switch input[type="checkbox"]'),this.tableRows=document.querySelectorAll("tbody tr")}createTableRow(e,t){const s=document.createElement("tr"),i=document.createElement("td");i.innerHTML=`
            <div class="form-check check-primary">
                <input class="form-check-input" title="checkbox" type="checkbox" id="checkboxData${t+1}">
                <label class="form-check-label d-none" for="checkboxData${t+1}">
                    Data ${t+1}
                </label>
            </div>
        `,s.appendChild(i);const a=document.createElement("td");a.innerHTML=`<a href="#!" class="link link-custom-primary">${e.id}</a>`,s.appendChild(a);const r=document.createElement("td");r.innerHTML=`
            <div class="d-flex align-items-center gap-2">
                <div class="avatar size-9 border rounded-1 p-1">
                    <img src="${e.image}" loading="lazy" alt="" class="img-fluid">
                </div>
                <a href="apps-ecommerce-product-overview.html" class="text-reset fw-semibold">${e.name}</a>
            </div>
        `,s.appendChild(r);const n=document.createElement("td");n.textContent=e.category,s.appendChild(n);const c=document.createElement("td");c.innerHTML=`
            <div class="form-switch switch-light-secondary">
                <input type="checkbox" id="switchProduct${t+1}" ${e.inStock?"checked":""} />
                <label class="label" for="switchProduct${t+1}"></label>
            </div>
        `,s.appendChild(c);const o=document.createElement("td");o.textContent=e.price,s.appendChild(o);const l=document.createElement("td");l.textContent=e.quantity,s.appendChild(l);const d=document.createElement("td");d.textContent=e.revenue,s.appendChild(d);const h=document.createElement("td"),p=e.status==="Published"?"badge bg-success-subtle border border-success-subtle text-success":"badge bg-light-subtle border border-dark-subtle text-dark";h.innerHTML=`<span class="${p}">${e.status}</span>`,s.appendChild(h);const u=document.createElement("td");return u.innerHTML=`
            <div class="dropdown">
                <a href="#!" class="link link-custom-primary" type="button" data-bs-toggle="dropdown" aria-expanded="false" title="dropdown-button">
                    <i class="ri-more-2-fill"></i>
                </a>
                <ul class="dropdown-menu">
                    <li>
                        <a href="apps-ecommerce-product-overview.html" class="dropdown-item d-flex gap-3 align-items-center">
                            <i class="ri-eye-line"></i>
                            <span>Overview</span>
                        </a>
                    </li>
                    <li>
                        <a href="apps-ecommerce-create-products.html" class="dropdown-item d-flex gap-3 align-items-center">
                            <i class="ri-pencil-line"></i>
                            Edit
                        </a>
                    </li>
                    <li>
                        <a href="#!" class="dropdown-item d-flex gap-3 align-items-center" data-bs-toggle="modal" data-bs-target="#deleteModal">
                            <i class="ri-delete-bin-line"></i>
                            <span>Delete</span>
                        </a>
                    </li>
                </ul>
            </div>
        `,s.appendChild(u),s.dataset.id=e.id,s}renderEmptyState(){const t=document.createElement("tr");t.innerHTML=`
            <td colspan="10" class="text-center py-4">
                <div class="d-flex flex-column align-items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" class="mx-auto size-12" viewBox="0 0 48 48">
                        <linearGradient id="SVGID_1__h35ynqzIJzH4_gr1" x1="34.598" x2="15.982" y1="15.982" y2="34.598" gradientUnits="userSpaceOnUse">
                            <stop offset="0" stop-color="#60e8fe"></stop>
                            <stop offset=".033" stop-color="#6ae9fe"></stop>
                            <stop offset=".197" stop-color="#97f0fe"></stop>
                            <stop offset=".362" stop-color="#bdf5ff"></stop>
                            <stop offset=".525" stop-color="#dafaff"></stop>
                            <stop offset=".687" stop-color="#eefdff"></stop>
                            <stop offset=".846" stop-color="#fbfeff"></stop>
                            <stop offset="1" stop-color="#fff"></stop>
                        </linearGradient>
                        <path fill="url(#SVGID_1__h35ynqzIJzH4_gr1)" d="M40.036,33.826L31.68,25.6c0.847-1.739,1.335-3.684,1.335-5.748c0-7.27-5.894-13.164-13.164-13.164 S6.688,12.582,6.688,19.852c0,7.27,5.894,13.164,13.164,13.164c2.056,0,3.995-0.485,5.728-1.326l3.914,4.015l4.331,4.331    c1.715,1.715,4.496,1.715,6.211,0C41.751,38.321,41.751,35.541,40.036,33.826z"></path>
                        <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M31.95,25.739l8.086,8.086c1.715,1.715,1.715,4.496,0,6.211l0,0c-1.715,1.715-4.496,1.715-6.211,0 l-4.331-4.331"></path>
                        <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M7.525,24.511c-1.771-4.694-0.767-10.196,3.011-13.975c3.847-3.847,9.48-4.817,14.228-2.912"></path>
                        <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M30.856,12.603c3.376,5.114,2.814,12.063-1.688,16.565c-4.858,4.858-12.565,5.129-17.741,0.814"></path>
                    </svg>
                    <p class="mt-2 text-center text-gray-500 dark:text-dark-500">No matching records found</p>
                    <p class="text-muted mb-0">We couldn't find any products matching your search.</p>
                </div>
            </td>
        `,this.tableBody.appendChild(t)}initCheckAll(){this.checkAll.addEventListener("change",()=>{this.checkboxes&&(this.checkboxes.forEach(e=>{e.checked=this.checkAll.checked}),this.updateSelectedRows(),this.updateTrashButtonVisibility())}),this.tableBody.addEventListener("change",e=>{e.target&&e.target.type==="checkbox"&&e.target.id.startsWith("checkboxData")&&(this.updateCheckAllState(),this.updateSelectedRows(),this.updateTrashButtonVisibility())})}updateCheckAllState(){if(this.checkboxes&&this.checkboxes.length>0){const e=Array.from(this.checkboxes).every(t=>t.checked);Array.from(this.checkboxes).some(t=>t.checked),this.checkAll.checked=e}else this.checkAll.checked=!1}updateSelectedRows(){this.selectedRows=Array.from(this.checkboxes||[]).filter(e=>e.checked).map(e=>e.closest("tr"))}updateTrashButtonVisibility(){this.selectedRows.length>0?this.trashButton.classList.remove("d-none"):this.trashButton.classList.add("d-none")}initDeleteFunctionality(){this.trashButton.addEventListener("click",()=>{this.deleteModal.show()}),this.deleteConfirmBtn.addEventListener("click",()=>{this.deleteSelectedRows(),this.deleteModal.hide()}),document.addEventListener("click",e=>{e.target&&e.target.closest(".remove-item-btn")&&(this.selectedRows=[e.target.closest("tr")])})}deleteSelectedRows(){const e=this.selectedRows.map(t=>t.dataset.id);this.data=this.data.filter(t=>!e.includes(t.id)),this.applyFiltersAndSearch(),this.selectedRows=[],this.checkAll.checked=!1,this.updateTrashButtonVisibility(),this.renderTable()}initStatusToggle(){this.tableBody.addEventListener("change",e=>{if(e.target&&e.target.type==="checkbox"&&e.target.id.startsWith("switchProduct")){const t=e.target.closest("tr"),s=t.dataset.id,i=e.target.checked,a=this.data.find(c=>c.id===s);a&&(a.inStock=i,a.status=i?"Published":"Inactive");const n=t.querySelector("td:nth-child(9)").querySelector(".badge");t.querySelector("td:nth-child(8)"),t.querySelector("td:nth-child(7)"),i?(n.className="badge bg-success-subtle border border-success-subtle text-success",n.textContent="Published"):(n.className="badge bg-light-subtle border border-dark-subtle text-dark",n.textContent="Inactive")}})}generateRandomRevenue(){return"$"+(Math.floor(Math.random()*2e4)+1e3).toLocaleString()}initSearch(){this.searchInput.addEventListener("input",e=>{this.searchTerm=e.target.value.toLowerCase(),this.currentPage=1,this.applyFiltersAndSearch(),this.renderTable()})}applyFiltersAndSearch(){this.filteredData=this.data.filter(e=>{const t=this.searchTerm===""||e.name.toLowerCase().includes(this.searchTerm)||e.id.toLowerCase().includes(this.searchTerm)||e.category.toLowerCase().includes(this.searchTerm);let s=!0;return(this.filters.published||this.filters.inactive)&&(s=this.filters.published&&e.status==="Published"||this.filters.inactive&&e.status==="Inactive"),t&&s})}initPagination(){this.paginationContainer.addEventListener("click",e=>{if(e.preventDefault(),e.target&&e.target.classList.contains("page-link")){const t=e.target;if(t.textContent.includes("Previous"))this.currentPage>1&&this.currentPage--;else if(t.textContent.includes("Next")){const s=Math.ceil(this.filteredData.length/this.itemsPerPage);this.currentPage<s&&this.currentPage++}else this.currentPage=parseInt(t.textContent);this.renderTable()}})}renderPagination(){const e=Math.ceil(this.filteredData.length/this.itemsPerPage);this.paginationContainer.innerHTML="";const t=document.createElement("li");t.className=`page-item ${this.currentPage===1?"disabled":""}`,t.innerHTML='<a class="page-link" href="#"><i data-lucide="chevron-left" class="size-4"></i>Previous</a>',this.paginationContainer.appendChild(t);for(let i=1;i<=e;i++){const a=document.createElement("li");a.className=`page-item ${i===this.currentPage?"active":""}`,a.innerHTML=`<a class="page-link" href="#">${i}</a>`,this.paginationContainer.appendChild(a)}if(e===0){const i=document.createElement("li");i.className="page-item active",i.innerHTML='<a class="page-link" href="#">1</a>',this.paginationContainer.appendChild(i)}const s=document.createElement("li");s.className=`page-item ${this.currentPage>=e?"disabled":""}`,s.innerHTML='<a class="page-link" href="#">Next<i data-lucide="chevron-right" class="size-4"></i></a>',this.paginationContainer.appendChild(s),g({icons:m})}updatePageInfo(){const e=document.querySelector(".col-md-6 p.text-muted");if(!e)return;const t=this.filteredData.length,s=t===0?0:Math.min((this.currentPage-1)*this.itemsPerPage+1,t),i=Math.min(s+this.itemsPerPage-1,t);e.innerHTML=`Showing <b class="me-1">${s}-${i}</b> of <b class="ms-1">${t}</b> Results`}initFilters(){if(!this.filterForm)return;const e=document.getElementById("publishedStatus"),t=document.getElementById("inactiveStatus");if(!e||!t)return;const s=this.filterForm.querySelector('button[type="reset"]');this.filterForm.addEventListener("submit",i=>{i.preventDefault(),this.filters.published=e.checked,this.filters.inactive=t.checked,this.currentPage=1,this.applyFiltersAndSearch(),this.renderTable()}),s&&s.addEventListener("click",()=>{e.checked=!1,t.checked=!1,this.filters.published=!1,this.filters.inactive=!1,this.applyFiltersAndSearch(),this.renderTable()})}}document.addEventListener("DOMContentLoaded",()=>{new k(b)});
