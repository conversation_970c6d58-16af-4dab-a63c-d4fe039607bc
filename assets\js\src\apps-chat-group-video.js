import"../../admin.bundle-DI9_jvUJ.js";import"../../main-Cyta4iCA.js";let i=0;function c(e){const n=Math.floor(e/3600),o=Math.floor(e%3600/60),t=e%60;return[n.toString().padStart(2,"0"),o.toString().padStart(2,"0"),t.toString().padStart(2,"0")].join(":")}function a(){i++,document.getElementById("videoCallTime").textContent=c(i)}setInterval(a,1e3);document.getElementById("addPin").addEventListener("click",function(){const e=document.getElementById("newPinText").value.trim(),n=c(i);if(e){const o=document.getElementById("keyMoments"),t=document.createElement("a");t.href="#!",t.className="d-flex gap-3 align-items-center text-muted";const m=document.createElement("p");m.className="w-28",m.textContent=n;const d=document.createElement("p");d.textContent=e,t.appendChild(m),t.appendChild(d),o.appendChild(t),document.getElementById("newPinText").value="",t.scrollIntoView({behavior:"smooth"})}});document.getElementById("newPinText").addEventListener("keypress",function(e){e.key==="Enter"&&document.getElementById("addPin").click()});
