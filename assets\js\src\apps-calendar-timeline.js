import"../../admin.bundle-DI9_jvUJ.js";import"../../main-Cyta4iCA.js";var ct,E,Hn,Pn,Se,oe,_r,Bn,Ln,$e={},Un=[],Os=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;function te(t,e){for(var r in e)t[r]=e[r];return t}function Wn(t){var e=t.parentNode;e&&e.removeChild(t)}function h(t,e,r){var n,i,s,l={};for(s in e)s=="key"?n=e[s]:s=="ref"?i=e[s]:l[s]=e[s];if(arguments.length>2&&(l.children=arguments.length>3?ct.call(arguments,2):r),typeof t=="function"&&t.defaultProps!=null)for(s in t.defaultProps)l[s]===void 0&&(l[s]=t.defaultProps[s]);return Ge(t,l,n,i,null)}function Ge(t,e,r,n,i){var s={type:t,props:e,key:r,ref:n,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:i??++Hn};return i==null&&E.vnode!=null&&E.vnode(s),s}function N(){return{current:null}}function I(t){return t.children}function Ns(t,e,r,n,i){var s;for(s in r)s==="children"||s==="key"||s in e||Xe(t,s,null,r[s],n);for(s in e)i&&typeof e[s]!="function"||s==="children"||s==="key"||s==="value"||s==="checked"||r[s]===e[s]||Xe(t,s,e[s],r[s],n)}function Dr(t,e,r){e[0]==="-"?t.setProperty(e,r??""):t[e]=r==null?"":typeof r!="number"||Os.test(e)?r:r+"px"}function Xe(t,e,r,n,i){var s;e:if(e==="style")if(typeof r=="string")t.style.cssText=r;else{if(typeof n=="string"&&(t.style.cssText=n=""),n)for(e in n)r&&e in r||Dr(t.style,e,"");if(r)for(e in r)n&&r[e]===n[e]||Dr(t.style,e,r[e])}else if(e[0]==="o"&&e[1]==="n")s=e!==(e=e.replace(/Capture$/,"")),e=e.toLowerCase()in t?e.toLowerCase().slice(2):e.slice(2),t.l||(t.l={}),t.l[e+s]=r,r?n||t.addEventListener(e,s?Tr:Ir,s):t.removeEventListener(e,s?Tr:Ir,s);else if(e!=="dangerouslySetInnerHTML"){if(i)e=e.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if(e!=="width"&&e!=="height"&&e!=="href"&&e!=="list"&&e!=="form"&&e!=="tabIndex"&&e!=="download"&&e in t)try{t[e]=r??"";break e}catch{}typeof r=="function"||(r==null||r===!1&&e.indexOf("-")==-1?t.removeAttribute(e):t.setAttribute(e,r))}}function Ir(t){Se=!0;try{return this.l[t.type+!1](E.event?E.event(t):t)}finally{Se=!1}}function Tr(t){Se=!0;try{return this.l[t.type+!0](E.event?E.event(t):t)}finally{Se=!1}}function W(t,e){this.props=t,this.context=e}function De(t,e){if(e==null)return t.__?De(t.__,t.__.__k.indexOf(t)+1):null;for(var r;e<t.__k.length;e++)if((r=t.__k[e])!=null&&r.__e!=null)return r.__e;return typeof t.type=="function"?De(t):null}function zn(t){var e,r;if((t=t.__)!=null&&t.__c!=null){for(t.__e=t.__c.base=null,e=0;e<t.__k.length;e++)if((r=t.__k[e])!=null&&r.__e!=null){t.__e=t.__c.base=r.__e;break}return zn(t)}}function Hs(t){Se?setTimeout(t):Bn(t)}function Mt(t){(!t.__d&&(t.__d=!0)&&oe.push(t)&&!Je.__r++||_r!==E.debounceRendering)&&((_r=E.debounceRendering)||Hs)(Je)}function Je(){var t,e,r,n,i,s,l,o;for(oe.sort(function(a,u){return a.__v.__b-u.__v.__b});t=oe.shift();)t.__d&&(e=oe.length,n=void 0,i=void 0,l=(s=(r=t).__v).__e,(o=r.__P)&&(n=[],(i=te({},s)).__v=s.__v+1,Gt(o,s,i,r.__n,o.ownerSVGElement!==void 0,s.__h!=null?[l]:null,n,l??De(s),s.__h),qn(n,s),s.__e!=l&&zn(s)),oe.length>e&&oe.sort(function(a,u){return a.__v.__b-u.__v.__b}));Je.__r=0}function Fn(t,e,r,n,i,s,l,o,a,u){var c,d,f,p,m,v,b,y=n&&n.__k||Un,S=y.length;for(r.__k=[],c=0;c<e.length;c++)if((p=r.__k[c]=(p=e[c])==null||typeof p=="boolean"?null:typeof p=="string"||typeof p=="number"||typeof p=="bigint"?Ge(null,p,null,null,p):Array.isArray(p)?Ge(I,{children:p},null,null,null):p.__b>0?Ge(p.type,p.props,p.key,p.ref?p.ref:null,p.__v):p)!=null){if(p.__=r,p.__b=r.__b+1,(f=y[c])===null||f&&p.key==f.key&&p.type===f.type)y[c]=void 0;else for(d=0;d<S;d++){if((f=y[d])&&p.key==f.key&&p.type===f.type){y[d]=void 0;break}f=null}Gt(t,p,f=f||$e,i,s,l,o,a,u),m=p.__e,(d=p.ref)&&f.ref!=d&&(b||(b=[]),f.ref&&b.push(f.ref,null,p),b.push(d,p.__c||m,p)),m!=null?(v==null&&(v=m),typeof p.type=="function"&&p.__k===f.__k?p.__d=a=jn(p,a,t):a=Vn(t,p,f,y,m,a),typeof r.type=="function"&&(r.__d=a)):a&&f.__e==a&&a.parentNode!=t&&(a=De(f))}for(r.__e=v,c=S;c--;)y[c]!=null&&(typeof r.type=="function"&&y[c].__e!=null&&y[c].__e==r.__d&&(r.__d=Gn(n).nextSibling),Yn(y[c],y[c]));if(b)for(c=0;c<b.length;c++)Qn(b[c],b[++c],b[++c])}function jn(t,e,r){for(var n,i=t.__k,s=0;i&&s<i.length;s++)(n=i[s])&&(n.__=t,e=typeof n.type=="function"?jn(n,e,r):Vn(r,n,n,i,n.__e,e));return e}function Ke(t,e){return e=e||[],t==null||typeof t=="boolean"||(Array.isArray(t)?t.some(function(r){Ke(r,e)}):e.push(t)),e}function Vn(t,e,r,n,i,s){var l,o,a;if(e.__d!==void 0)l=e.__d,e.__d=void 0;else if(r==null||i!=s||i.parentNode==null)e:if(s==null||s.parentNode!==t)t.appendChild(i),l=null;else{for(o=s,a=0;(o=o.nextSibling)&&a<n.length;a+=1)if(o==i)break e;t.insertBefore(i,s),l=s}return l!==void 0?l:i.nextSibling}function Gn(t){var e,r,n;if(t.type==null||typeof t.type=="string")return t.__e;if(t.__k){for(e=t.__k.length-1;e>=0;e--)if((r=t.__k[e])&&(n=Gn(r)))return n}return null}function Gt(t,e,r,n,i,s,l,o,a){var u,c,d,f,p,m,v,b,y,S,C,w,x,k,M,_=e.type;if(e.constructor!==void 0)return null;r.__h!=null&&(a=r.__h,o=e.__e=r.__e,e.__h=null,s=[o]),(u=E.__b)&&u(e);try{e:if(typeof _=="function"){if(b=e.props,y=(u=_.contextType)&&n[u.__c],S=u?y?y.props.value:u.__:n,r.__c?v=(c=e.__c=r.__c).__=c.__E:("prototype"in _&&_.prototype.render?e.__c=c=new _(b,S):(e.__c=c=new W(b,S),c.constructor=_,c.render=Bs),y&&y.sub(c),c.props=b,c.state||(c.state={}),c.context=S,c.__n=n,d=c.__d=!0,c.__h=[],c._sb=[]),c.__s==null&&(c.__s=c.state),_.getDerivedStateFromProps!=null&&(c.__s==c.state&&(c.__s=te({},c.__s)),te(c.__s,_.getDerivedStateFromProps(b,c.__s))),f=c.props,p=c.state,c.__v=e,d)_.getDerivedStateFromProps==null&&c.componentWillMount!=null&&c.componentWillMount(),c.componentDidMount!=null&&c.__h.push(c.componentDidMount);else{if(_.getDerivedStateFromProps==null&&b!==f&&c.componentWillReceiveProps!=null&&c.componentWillReceiveProps(b,S),!c.__e&&c.shouldComponentUpdate!=null&&c.shouldComponentUpdate(b,c.__s,S)===!1||e.__v===r.__v){for(e.__v!==r.__v&&(c.props=b,c.state=c.__s,c.__d=!1),e.__e=r.__e,e.__k=r.__k,e.__k.forEach(function(G){G&&(G.__=e)}),C=0;C<c._sb.length;C++)c.__h.push(c._sb[C]);c._sb=[],c.__h.length&&l.push(c);break e}c.componentWillUpdate!=null&&c.componentWillUpdate(b,c.__s,S),c.componentDidUpdate!=null&&c.__h.push(function(){c.componentDidUpdate(f,p,m)})}if(c.context=S,c.props=b,c.__P=t,w=E.__r,x=0,"prototype"in _&&_.prototype.render){for(c.state=c.__s,c.__d=!1,w&&w(e),u=c.render(c.props,c.state,c.context),k=0;k<c._sb.length;k++)c.__h.push(c._sb[k]);c._sb=[]}else do c.__d=!1,w&&w(e),u=c.render(c.props,c.state,c.context),c.state=c.__s;while(c.__d&&++x<25);c.state=c.__s,c.getChildContext!=null&&(n=te(te({},n),c.getChildContext())),d||c.getSnapshotBeforeUpdate==null||(m=c.getSnapshotBeforeUpdate(f,p)),M=u!=null&&u.type===I&&u.key==null?u.props.children:u,Fn(t,Array.isArray(M)?M:[M],e,r,n,i,s,l,o,a),c.base=e.__e,e.__h=null,c.__h.length&&l.push(c),v&&(c.__E=c.__=null),c.__e=!1}else s==null&&e.__v===r.__v?(e.__k=r.__k,e.__e=r.__e):e.__e=Ps(r.__e,e,r,n,i,s,l,a);(u=E.diffed)&&u(e)}catch(G){e.__v=null,(a||s!=null)&&(e.__e=o,e.__h=!!a,s[s.indexOf(o)]=null),E.__e(G,e,r)}}function qn(t,e){E.__c&&E.__c(e,t),t.some(function(r){try{t=r.__h,r.__h=[],t.some(function(n){n.call(r)})}catch(n){E.__e(n,r.__v)}})}function Ps(t,e,r,n,i,s,l,o){var a,u,c,d=r.props,f=e.props,p=e.type,m=0;if(p==="svg"&&(i=!0),s!=null){for(;m<s.length;m++)if((a=s[m])&&"setAttribute"in a==!!p&&(p?a.localName===p:a.nodeType===3)){t=a,s[m]=null;break}}if(t==null){if(p===null)return document.createTextNode(f);t=i?document.createElementNS("http://www.w3.org/2000/svg",p):document.createElement(p,f.is&&f),s=null,o=!1}if(p===null)d===f||o&&t.data===f||(t.data=f);else{if(s=s&&ct.call(t.childNodes),u=(d=r.props||$e).dangerouslySetInnerHTML,c=f.dangerouslySetInnerHTML,!o){if(s!=null)for(d={},m=0;m<t.attributes.length;m++)d[t.attributes[m].name]=t.attributes[m].value;(c||u)&&(c&&(u&&c.__html==u.__html||c.__html===t.innerHTML)||(t.innerHTML=c&&c.__html||""))}if(Ns(t,f,d,i,o),c)e.__k=[];else if(m=e.props.children,Fn(t,Array.isArray(m)?m:[m],e,r,n,i&&p!=="foreignObject",s,l,s?s[0]:r.__k&&De(r,0),o),s!=null)for(m=s.length;m--;)s[m]!=null&&Wn(s[m]);o||("value"in f&&(m=f.value)!==void 0&&(m!==t.value||p==="progress"&&!m||p==="option"&&m!==d.value)&&Xe(t,"value",m,d.value,!1),"checked"in f&&(m=f.checked)!==void 0&&m!==t.checked&&Xe(t,"checked",m,d.checked,!1))}return t}function Qn(t,e,r){try{typeof t=="function"?t(e):t.current=e}catch(n){E.__e(n,r)}}function Yn(t,e,r){var n,i;if(E.unmount&&E.unmount(t),(n=t.ref)&&(n.current&&n.current!==t.__e||Qn(n,null,e)),(n=t.__c)!=null){if(n.componentWillUnmount)try{n.componentWillUnmount()}catch(s){E.__e(s,e)}n.base=n.__P=null,t.__c=void 0}if(n=t.__k)for(i=0;i<n.length;i++)n[i]&&Yn(n[i],e,r||typeof t.type!="function");r||t.__e==null||Wn(t.__e),t.__=t.__e=t.__d=void 0}function Bs(t,e,r){return this.constructor(t,r)}function Ie(t,e,r){var n,i,s;E.__&&E.__(t,e),i=(n=!1)?null:e.__k,s=[],Gt(e,t=e.__k=h(I,null,[t]),i||$e,$e,e.ownerSVGElement!==void 0,i?null:e.firstChild?ct.call(e.childNodes):null,s,i?i.__e:e.firstChild,n),qn(s,t)}function Ls(t,e){var r={__c:e="__cC"+Ln++,__:t,Consumer:function(n,i){return n.children(i)},Provider:function(n){var i,s;return this.getChildContext||(i=[],(s={})[e]=this,this.getChildContext=function(){return s},this.shouldComponentUpdate=function(l){this.props.value!==l.value&&i.some(function(o){o.__e=!0,Mt(o)})},this.sub=function(l){i.push(l);var o=l.componentWillUnmount;l.componentWillUnmount=function(){i.splice(i.indexOf(l),1),o&&o.call(l)}}),n.children}};return r.Provider.__=r.Consumer.contextType=r}ct=Un.slice,E={__e:function(t,e,r,n){for(var i,s,l;e=e.__;)if((i=e.__c)&&!i.__)try{if((s=i.constructor)&&s.getDerivedStateFromError!=null&&(i.setState(s.getDerivedStateFromError(t)),l=i.__d),i.componentDidCatch!=null&&(i.componentDidCatch(t,n||{}),l=i.__d),l)return i.__E=i}catch(o){t=o}throw t}},Hn=0,Pn=function(t){return t!=null&&t.constructor===void 0},Se=!1,W.prototype.setState=function(t,e){var r;r=this.__s!=null&&this.__s!==this.state?this.__s:this.__s=te({},this.state),typeof t=="function"&&(t=t(te({},r),this.props)),t&&te(r,t),t!=null&&this.__v&&(e&&this._sb.push(e),Mt(this))},W.prototype.forceUpdate=function(t){this.__v&&(this.__e=!0,t&&this.__h.push(t),Mt(this))},W.prototype.render=I,oe=[],Bn=typeof Promise=="function"?Promise.prototype.then.bind(Promise.resolve()):setTimeout,Je.__r=0,Ln=0;var q,gt,kr,Zn=[],mt=[],Mr=E.__b,xr=E.__r,Or=E.diffed,Nr=E.__c,Hr=E.unmount;function Us(){for(var t;t=Zn.shift();)if(t.__P&&t.__H)try{t.__H.__h.forEach(qe),t.__H.__h.forEach(xt),t.__H.__h=[]}catch(e){t.__H.__h=[],E.__e(e,t.__v)}}E.__b=function(t){q=null,Mr&&Mr(t)},E.__r=function(t){xr&&xr(t);var e=(q=t.__c).__H;e&&(gt===q?(e.__h=[],q.__h=[],e.__.forEach(function(r){r.__N&&(r.__=r.__N),r.__V=mt,r.__N=r.i=void 0})):(e.__h.forEach(qe),e.__h.forEach(xt),e.__h=[])),gt=q},E.diffed=function(t){Or&&Or(t);var e=t.__c;e&&e.__H&&(e.__H.__h.length&&(Zn.push(e)!==1&&kr===E.requestAnimationFrame||((kr=E.requestAnimationFrame)||Ws)(Us)),e.__H.__.forEach(function(r){r.i&&(r.__H=r.i),r.__V!==mt&&(r.__=r.__V),r.i=void 0,r.__V=mt})),gt=q=null},E.__c=function(t,e){e.some(function(r){try{r.__h.forEach(qe),r.__h=r.__h.filter(function(n){return!n.__||xt(n)})}catch(n){e.some(function(i){i.__h&&(i.__h=[])}),e=[],E.__e(n,r.__v)}}),Nr&&Nr(t,e)},E.unmount=function(t){Hr&&Hr(t);var e,r=t.__c;r&&r.__H&&(r.__H.__.forEach(function(n){try{qe(n)}catch(i){e=i}}),r.__H=void 0,e&&E.__e(e,r.__v))};var Pr=typeof requestAnimationFrame=="function";function Ws(t){var e,r=function(){clearTimeout(n),Pr&&cancelAnimationFrame(e),setTimeout(t)},n=setTimeout(r,100);Pr&&(e=requestAnimationFrame(r))}function qe(t){var e=q,r=t.__c;typeof r=="function"&&(t.__c=void 0,r()),q=e}function xt(t){var e=q;t.__c=t.__(),q=e}function zs(t,e){for(var r in e)t[r]=e[r];return t}function Br(t,e){for(var r in t)if(r!=="__source"&&!(r in e))return!0;for(var n in e)if(n!=="__source"&&t[n]!==e[n])return!0;return!1}function Lr(t){this.props=t}(Lr.prototype=new W).isPureReactComponent=!0,Lr.prototype.shouldComponentUpdate=function(t,e){return Br(this.props,t)||Br(this.state,e)};var Ur=E.__b;E.__b=function(t){t.type&&t.type.__f&&t.ref&&(t.props.ref=t.ref,t.ref=null),Ur&&Ur(t)};var Fs=E.__e;E.__e=function(t,e,r,n){if(t.then){for(var i,s=e;s=s.__;)if((i=s.__c)&&i.__c)return e.__e==null&&(e.__e=r.__e,e.__k=r.__k),i.__c(t,e)}Fs(t,e,r,n)};var Wr=E.unmount;function $n(t,e,r){return t&&(t.__c&&t.__c.__H&&(t.__c.__H.__.forEach(function(n){typeof n.__c=="function"&&n.__c()}),t.__c.__H=null),(t=zs({},t)).__c!=null&&(t.__c.__P===r&&(t.__c.__P=e),t.__c=null),t.__k=t.__k&&t.__k.map(function(n){return $n(n,e,r)})),t}function Xn(t,e,r){return t&&(t.__v=null,t.__k=t.__k&&t.__k.map(function(n){return Xn(n,e,r)}),t.__c&&t.__c.__P===e&&(t.__e&&r.insertBefore(t.__e,t.__d),t.__c.__e=!0,t.__c.__P=r)),t}function vt(){this.__u=0,this.t=null,this.__b=null}function Jn(t){var e=t.__.__c;return e&&e.__a&&e.__a(t)}function Be(){this.u=null,this.o=null}E.unmount=function(t){var e=t.__c;e&&e.__R&&e.__R(),e&&t.__h===!0&&(t.type=null),Wr&&Wr(t)},(vt.prototype=new W).__c=function(t,e){var r=e.__c,n=this;n.t==null&&(n.t=[]),n.t.push(r);var i=Jn(n.__v),s=!1,l=function(){s||(s=!0,r.__R=null,i?i(o):o())};r.__R=l;var o=function(){if(!--n.__u){if(n.state.__a){var u=n.state.__a;n.__v.__k[0]=Xn(u,u.__c.__P,u.__c.__O)}var c;for(n.setState({__a:n.__b=null});c=n.t.pop();)c.forceUpdate()}},a=e.__h===!0;n.__u++||a||n.setState({__a:n.__b=n.__v.__k[0]}),t.then(l,l)},vt.prototype.componentWillUnmount=function(){this.t=[]},vt.prototype.render=function(t,e){if(this.__b){if(this.__v.__k){var r=document.createElement("div"),n=this.__v.__k[0].__c;this.__v.__k[0]=$n(this.__b,r,n.__O=n.__P)}this.__b=null}var i=e.__a&&h(I,null,t.fallback);return i&&(i.__h=null),[h(I,null,e.__a?null:t.children),i]};var zr=function(t,e,r){if(++r[1]===r[0]&&t.o.delete(e),t.props.revealOrder&&(t.props.revealOrder[0]!=="t"||!t.o.size))for(r=t.u;r;){for(;r.length>3;)r.pop()();if(r[1]<r[0])break;t.u=r=r[2]}};function js(t){return this.getChildContext=function(){return t.context},t.children}function Vs(t){var e=this,r=t.i;e.componentWillUnmount=function(){Ie(null,e.l),e.l=null,e.i=null},e.i&&e.i!==r&&e.componentWillUnmount(),t.__v?(e.l||(e.i=r,e.l={nodeType:1,parentNode:r,childNodes:[],appendChild:function(n){this.childNodes.push(n),e.i.appendChild(n)},insertBefore:function(n,i){this.childNodes.push(n),e.i.appendChild(n)},removeChild:function(n){this.childNodes.splice(this.childNodes.indexOf(n)>>>1,1),e.i.removeChild(n)}}),Ie(h(js,{context:e.context},t.__v),e.l)):e.l&&e.componentWillUnmount()}function Gs(t,e){var r=h(Vs,{__v:t,i:e});return r.containerInfo=e,r}(Be.prototype=new W).__a=function(t){var e=this,r=Jn(e.__v),n=e.o.get(t);return n[0]++,function(i){var s=function(){e.props.revealOrder?(n.push(i),zr(e,t,n)):i()};r?r(s):s()}},Be.prototype.render=function(t){this.u=null,this.o=new Map;var e=Ke(t.children);t.revealOrder&&t.revealOrder[0]==="b"&&e.reverse();for(var r=e.length;r--;)this.o.set(e[r],this.u=[1,0,this.u]);return t.children},Be.prototype.componentDidUpdate=Be.prototype.componentDidMount=function(){var t=this;this.o.forEach(function(e,r){zr(t,r,e)})};var qs=typeof Symbol<"u"&&Symbol.for&&Symbol.for("react.element")||60103,Qs=/^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|image|letter|lighting|marker(?!H|W|U)|overline|paint|pointer|shape|stop|strikethrough|stroke|text(?!L)|transform|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/,Ys=typeof document<"u",Zs=function(t){return(typeof Symbol<"u"&&typeof Symbol()=="symbol"?/fil|che|rad/i:/fil|che|ra/i).test(t)};W.prototype.isReactComponent={},["componentWillMount","componentWillReceiveProps","componentWillUpdate"].forEach(function(t){Object.defineProperty(W.prototype,t,{configurable:!0,get:function(){return this["UNSAFE_"+t]},set:function(e){Object.defineProperty(this,t,{configurable:!0,writable:!0,value:e})}})});var Fr=E.event;function $s(){}function Xs(){return this.cancelBubble}function Js(){return this.defaultPrevented}E.event=function(t){return Fr&&(t=Fr(t)),t.persist=$s,t.isPropagationStopped=Xs,t.isDefaultPrevented=Js,t.nativeEvent=t};var jr={configurable:!0,get:function(){return this.class}},Vr=E.vnode;E.vnode=function(t){var e=t.type,r=t.props,n=r;if(typeof e=="string"){var i=e.indexOf("-")===-1;for(var s in n={},r){var l=r[s];Ys&&s==="children"&&e==="noscript"||s==="value"&&"defaultValue"in r&&l==null||(s==="defaultValue"&&"value"in r&&r.value==null?s="value":s==="download"&&l===!0?l="":/ondoubleclick/i.test(s)?s="ondblclick":/^onchange(textarea|input)/i.test(s+e)&&!Zs(r.type)?s="oninput":/^onfocus$/i.test(s)?s="onfocusin":/^onblur$/i.test(s)?s="onfocusout":/^on(Ani|Tra|Tou|BeforeInp|Compo)/.test(s)?s=s.toLowerCase():i&&Qs.test(s)?s=s.replace(/[A-Z0-9]/g,"-$&").toLowerCase():l===null&&(l=void 0),/^oninput$/i.test(s)&&(s=s.toLowerCase(),n[s]&&(s="oninputCapture")),n[s]=l)}e=="select"&&n.multiple&&Array.isArray(n.value)&&(n.value=Ke(r.children).forEach(function(o){o.props.selected=n.value.indexOf(o.props.value)!=-1})),e=="select"&&n.defaultValue!=null&&(n.value=Ke(r.children).forEach(function(o){o.props.selected=n.multiple?n.defaultValue.indexOf(o.props.value)!=-1:n.defaultValue==o.props.value})),t.props=n,r.class!=r.className&&(jr.enumerable="className"in r,r.className!=null&&(n.class=r.className),Object.defineProperty(n,"className",jr))}t.$$typeof=qs,Vr&&Vr(t)};var Gr=E.__r;E.__r=function(t){Gr&&Gr(t),t.__c};const Kn=[],Ot=new Map;function qt(t){Kn.push(t),Ot.forEach(e=>{ti(e,t)})}function Ks(t){t.isConnected&&t.getRootNode&&ei(t.getRootNode())}function ei(t){let e=Ot.get(t);if(!e||!e.isConnected){if(e=t.querySelector("style[data-fullcalendar]"),!e){e=document.createElement("style"),e.setAttribute("data-fullcalendar","");const r=tl();r&&(e.nonce=r);const n=t===document?document.head:t,i=t===document?n.querySelector("script,link[rel=stylesheet],link[as=style],style"):n.firstChild;n.insertBefore(e,i)}Ot.set(t,e),el(e)}}function el(t){for(const e of Kn)ti(t,e)}function ti(t,e){const{sheet:r}=t,n=r.cssRules.length;e.split("}").forEach((i,s)=>{i=i.trim(),i&&r.insertRule(i+"}",n+s)})}let bt;function tl(){return bt===void 0&&(bt=rl()),bt}function rl(){const t=document.querySelector('meta[name="csp-nonce"]');if(t&&t.hasAttribute("content"))return t.getAttribute("content");const e=document.querySelector("script[nonce]");return e&&e.nonce||""}typeof document<"u"&&ei(document);var nl=':root{--fc-small-font-size:.85em;--fc-page-bg-color:#fff;--fc-neutral-bg-color:hsla(0,0%,82%,.3);--fc-neutral-text-color:grey;--fc-border-color:#ddd;--fc-button-text-color:#fff;--fc-button-bg-color:#2c3e50;--fc-button-border-color:#2c3e50;--fc-button-hover-bg-color:#1e2b37;--fc-button-hover-border-color:#1a252f;--fc-button-active-bg-color:#1a252f;--fc-button-active-border-color:#151e27;--fc-event-bg-color:#3788d8;--fc-event-border-color:#3788d8;--fc-event-text-color:#fff;--fc-event-selected-overlay-color:rgba(0,0,0,.25);--fc-more-link-bg-color:#d0d0d0;--fc-more-link-text-color:inherit;--fc-event-resizer-thickness:8px;--fc-event-resizer-dot-total-width:8px;--fc-event-resizer-dot-border-width:1px;--fc-non-business-color:hsla(0,0%,84%,.3);--fc-bg-event-color:#8fdf82;--fc-bg-event-opacity:0.3;--fc-highlight-color:rgba(188,232,241,.3);--fc-today-bg-color:rgba(255,220,40,.15);--fc-now-indicator-color:red}.fc-not-allowed,.fc-not-allowed .fc-event{cursor:not-allowed}.fc{display:flex;flex-direction:column;font-size:1em}.fc,.fc *,.fc :after,.fc :before{box-sizing:border-box}.fc table{border-collapse:collapse;border-spacing:0;font-size:1em}.fc th{text-align:center}.fc td,.fc th{padding:0;vertical-align:top}.fc a[data-navlink]{cursor:pointer}.fc a[data-navlink]:hover{text-decoration:underline}.fc-direction-ltr{direction:ltr;text-align:left}.fc-direction-rtl{direction:rtl;text-align:right}.fc-theme-standard td,.fc-theme-standard th{border:1px solid var(--fc-border-color)}.fc-liquid-hack td,.fc-liquid-hack th{position:relative}@font-face{font-family:fcicons;font-style:normal;font-weight:400;src:url("data:application/x-font-ttf;charset=utf-8;base64,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") format("truetype")}.fc-icon{speak:none;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;display:inline-block;font-family:fcicons!important;font-style:normal;font-variant:normal;font-weight:400;height:1em;line-height:1;text-align:center;text-transform:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;width:1em}.fc-icon-chevron-left:before{content:"\\e900"}.fc-icon-chevron-right:before{content:"\\e901"}.fc-icon-chevrons-left:before{content:"\\e902"}.fc-icon-chevrons-right:before{content:"\\e903"}.fc-icon-minus-square:before{content:"\\e904"}.fc-icon-plus-square:before{content:"\\e905"}.fc-icon-x:before{content:"\\e906"}.fc .fc-button{border-radius:0;font-family:inherit;font-size:inherit;line-height:inherit;margin:0;overflow:visible;text-transform:none}.fc .fc-button:focus{outline:1px dotted;outline:5px auto -webkit-focus-ring-color}.fc .fc-button{-webkit-appearance:button}.fc .fc-button:not(:disabled){cursor:pointer}.fc .fc-button{background-color:transparent;border:1px solid transparent;border-radius:.25em;display:inline-block;font-size:1em;font-weight:400;line-height:1.5;padding:.4em .65em;text-align:center;-webkit-user-select:none;-moz-user-select:none;user-select:none;vertical-align:middle}.fc .fc-button:hover{text-decoration:none}.fc .fc-button:focus{box-shadow:0 0 0 .2rem rgba(44,62,80,.25);outline:0}.fc .fc-button:disabled{opacity:.65}.fc .fc-button-primary{background-color:var(--fc-button-bg-color);border-color:var(--fc-button-border-color);color:var(--fc-button-text-color)}.fc .fc-button-primary:hover{background-color:var(--fc-button-hover-bg-color);border-color:var(--fc-button-hover-border-color);color:var(--fc-button-text-color)}.fc .fc-button-primary:disabled{background-color:var(--fc-button-bg-color);border-color:var(--fc-button-border-color);color:var(--fc-button-text-color)}.fc .fc-button-primary:focus{box-shadow:0 0 0 .2rem rgba(76,91,106,.5)}.fc .fc-button-primary:not(:disabled).fc-button-active,.fc .fc-button-primary:not(:disabled):active{background-color:var(--fc-button-active-bg-color);border-color:var(--fc-button-active-border-color);color:var(--fc-button-text-color)}.fc .fc-button-primary:not(:disabled).fc-button-active:focus,.fc .fc-button-primary:not(:disabled):active:focus{box-shadow:0 0 0 .2rem rgba(76,91,106,.5)}.fc .fc-button .fc-icon{font-size:1.5em;vertical-align:middle}.fc .fc-button-group{display:inline-flex;position:relative;vertical-align:middle}.fc .fc-button-group>.fc-button{flex:1 1 auto;position:relative}.fc .fc-button-group>.fc-button.fc-button-active,.fc .fc-button-group>.fc-button:active,.fc .fc-button-group>.fc-button:focus,.fc .fc-button-group>.fc-button:hover{z-index:1}.fc-direction-ltr .fc-button-group>.fc-button:not(:first-child){border-bottom-left-radius:0;border-top-left-radius:0;margin-left:-1px}.fc-direction-ltr .fc-button-group>.fc-button:not(:last-child){border-bottom-right-radius:0;border-top-right-radius:0}.fc-direction-rtl .fc-button-group>.fc-button:not(:first-child){border-bottom-right-radius:0;border-top-right-radius:0;margin-right:-1px}.fc-direction-rtl .fc-button-group>.fc-button:not(:last-child){border-bottom-left-radius:0;border-top-left-radius:0}.fc .fc-toolbar{align-items:center;display:flex;justify-content:space-between}.fc .fc-toolbar.fc-header-toolbar{margin-bottom:1.5em}.fc .fc-toolbar.fc-footer-toolbar{margin-top:1.5em}.fc .fc-toolbar-title{font-size:1.75em;margin:0}.fc-direction-ltr .fc-toolbar>*>:not(:first-child){margin-left:.75em}.fc-direction-rtl .fc-toolbar>*>:not(:first-child){margin-right:.75em}.fc-direction-rtl .fc-toolbar-ltr{flex-direction:row-reverse}.fc .fc-scroller{-webkit-overflow-scrolling:touch;position:relative}.fc .fc-scroller-liquid{height:100%}.fc .fc-scroller-liquid-absolute{bottom:0;left:0;position:absolute;right:0;top:0}.fc .fc-scroller-harness{direction:ltr;overflow:hidden;position:relative}.fc .fc-scroller-harness-liquid{height:100%}.fc-direction-rtl .fc-scroller-harness>.fc-scroller{direction:rtl}.fc-theme-standard .fc-scrollgrid{border:1px solid var(--fc-border-color)}.fc .fc-scrollgrid,.fc .fc-scrollgrid table{table-layout:fixed;width:100%}.fc .fc-scrollgrid table{border-left-style:hidden;border-right-style:hidden;border-top-style:hidden}.fc .fc-scrollgrid{border-bottom-width:0;border-collapse:separate;border-right-width:0}.fc .fc-scrollgrid-liquid{height:100%}.fc .fc-scrollgrid-section,.fc .fc-scrollgrid-section table,.fc .fc-scrollgrid-section>td{height:1px}.fc .fc-scrollgrid-section-liquid>td{height:100%}.fc .fc-scrollgrid-section>*{border-left-width:0;border-top-width:0}.fc .fc-scrollgrid-section-footer>*,.fc .fc-scrollgrid-section-header>*{border-bottom-width:0}.fc .fc-scrollgrid-section-body table,.fc .fc-scrollgrid-section-footer table{border-bottom-style:hidden}.fc .fc-scrollgrid-section-sticky>*{background:var(--fc-page-bg-color);position:sticky;z-index:3}.fc .fc-scrollgrid-section-header.fc-scrollgrid-section-sticky>*{top:0}.fc .fc-scrollgrid-section-footer.fc-scrollgrid-section-sticky>*{bottom:0}.fc .fc-scrollgrid-sticky-shim{height:1px;margin-bottom:-1px}.fc-sticky{position:sticky}.fc .fc-view-harness{flex-grow:1;position:relative}.fc .fc-view-harness-active>.fc-view{bottom:0;left:0;position:absolute;right:0;top:0}.fc .fc-col-header-cell-cushion{display:inline-block;padding:2px 4px}.fc .fc-bg-event,.fc .fc-highlight,.fc .fc-non-business{bottom:0;left:0;position:absolute;right:0;top:0}.fc .fc-non-business{background:var(--fc-non-business-color)}.fc .fc-bg-event{background:var(--fc-bg-event-color);opacity:var(--fc-bg-event-opacity)}.fc .fc-bg-event .fc-event-title{font-size:var(--fc-small-font-size);font-style:italic;margin:.5em}.fc .fc-highlight{background:var(--fc-highlight-color)}.fc .fc-cell-shaded,.fc .fc-day-disabled{background:var(--fc-neutral-bg-color)}a.fc-event,a.fc-event:hover{text-decoration:none}.fc-event.fc-event-draggable,.fc-event[href]{cursor:pointer}.fc-event .fc-event-main{position:relative;z-index:2}.fc-event-dragging:not(.fc-event-selected){opacity:.75}.fc-event-dragging.fc-event-selected{box-shadow:0 2px 7px rgba(0,0,0,.3)}.fc-event .fc-event-resizer{display:none;position:absolute;z-index:4}.fc-event-selected .fc-event-resizer,.fc-event:hover .fc-event-resizer{display:block}.fc-event-selected .fc-event-resizer{background:var(--fc-page-bg-color);border-color:inherit;border-radius:calc(var(--fc-event-resizer-dot-total-width)/2);border-style:solid;border-width:var(--fc-event-resizer-dot-border-width);height:var(--fc-event-resizer-dot-total-width);width:var(--fc-event-resizer-dot-total-width)}.fc-event-selected .fc-event-resizer:before{bottom:-20px;content:"";left:-20px;position:absolute;right:-20px;top:-20px}.fc-event-selected,.fc-event:focus{box-shadow:0 2px 5px rgba(0,0,0,.2)}.fc-event-selected:before,.fc-event:focus:before{bottom:0;content:"";left:0;position:absolute;right:0;top:0;z-index:3}.fc-event-selected:after,.fc-event:focus:after{background:var(--fc-event-selected-overlay-color);bottom:-1px;content:"";left:-1px;position:absolute;right:-1px;top:-1px;z-index:1}.fc-h-event{background-color:var(--fc-event-bg-color);border:1px solid var(--fc-event-border-color);display:block}.fc-h-event .fc-event-main{color:var(--fc-event-text-color)}.fc-h-event .fc-event-main-frame{display:flex}.fc-h-event .fc-event-time{max-width:100%;overflow:hidden}.fc-h-event .fc-event-title-container{flex-grow:1;flex-shrink:1;min-width:0}.fc-h-event .fc-event-title{display:inline-block;left:0;max-width:100%;overflow:hidden;right:0;vertical-align:top}.fc-h-event.fc-event-selected:before{bottom:-10px;top:-10px}.fc-direction-ltr .fc-daygrid-block-event:not(.fc-event-start),.fc-direction-rtl .fc-daygrid-block-event:not(.fc-event-end){border-bottom-left-radius:0;border-left-width:0;border-top-left-radius:0}.fc-direction-ltr .fc-daygrid-block-event:not(.fc-event-end),.fc-direction-rtl .fc-daygrid-block-event:not(.fc-event-start){border-bottom-right-radius:0;border-right-width:0;border-top-right-radius:0}.fc-h-event:not(.fc-event-selected) .fc-event-resizer{bottom:0;top:0;width:var(--fc-event-resizer-thickness)}.fc-direction-ltr .fc-h-event:not(.fc-event-selected) .fc-event-resizer-start,.fc-direction-rtl .fc-h-event:not(.fc-event-selected) .fc-event-resizer-end{cursor:w-resize;left:calc(var(--fc-event-resizer-thickness)*-.5)}.fc-direction-ltr .fc-h-event:not(.fc-event-selected) .fc-event-resizer-end,.fc-direction-rtl .fc-h-event:not(.fc-event-selected) .fc-event-resizer-start{cursor:e-resize;right:calc(var(--fc-event-resizer-thickness)*-.5)}.fc-h-event.fc-event-selected .fc-event-resizer{margin-top:calc(var(--fc-event-resizer-dot-total-width)*-.5);top:50%}.fc-direction-ltr .fc-h-event.fc-event-selected .fc-event-resizer-start,.fc-direction-rtl .fc-h-event.fc-event-selected .fc-event-resizer-end{left:calc(var(--fc-event-resizer-dot-total-width)*-.5)}.fc-direction-ltr .fc-h-event.fc-event-selected .fc-event-resizer-end,.fc-direction-rtl .fc-h-event.fc-event-selected .fc-event-resizer-start{right:calc(var(--fc-event-resizer-dot-total-width)*-.5)}.fc .fc-popover{box-shadow:0 2px 6px rgba(0,0,0,.15);position:absolute;z-index:9999}.fc .fc-popover-header{align-items:center;display:flex;flex-direction:row;justify-content:space-between;padding:3px 4px}.fc .fc-popover-title{margin:0 2px}.fc .fc-popover-close{cursor:pointer;font-size:1.1em;opacity:.65}.fc-theme-standard .fc-popover{background:var(--fc-page-bg-color);border:1px solid var(--fc-border-color)}.fc-theme-standard .fc-popover-header{background:var(--fc-neutral-bg-color)}';qt(nl);class Te{constructor(e){this.drainedOption=e,this.isRunning=!1,this.isDirty=!1,this.pauseDepths={},this.timeoutId=0}request(e){this.isDirty=!0,this.isPaused()||(this.clearTimeout(),e==null?this.tryDrain():this.timeoutId=setTimeout(this.tryDrain.bind(this),e))}pause(e=""){let{pauseDepths:r}=this;r[e]=(r[e]||0)+1,this.clearTimeout()}resume(e="",r){let{pauseDepths:n}=this;e in n&&(r?delete n[e]:(n[e]-=1,n[e]<=0&&delete n[e]),this.tryDrain())}isPaused(){return Object.keys(this.pauseDepths).length}tryDrain(){if(!this.isRunning&&!this.isPaused()){for(this.isRunning=!0;this.isDirty;)this.isDirty=!1,this.drained();this.isRunning=!1}}clear(){this.clearTimeout(),this.isDirty=!1,this.pauseDepths={}}clearTimeout(){this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=0)}drained(){this.drainedOption&&this.drainedOption()}}function Qt(t){t.parentNode&&t.parentNode.removeChild(t)}function V(t,e){if(t.closest)return t.closest(e);if(!document.documentElement.contains(t))return null;do{if(ri(t,e))return t;t=t.parentElement||t.parentNode}while(t!==null&&t.nodeType===1);return null}function ri(t,e){return(t.matches||t.matchesSelector||t.msMatchesSelector).call(t,e)}function Ce(t,e){let r=t instanceof HTMLElement?[t]:t,n=[];for(let i=0;i<r.length;i+=1){let s=r[i].querySelectorAll(e);for(let l=0;l<s.length;l+=1)n.push(s[l])}return n}function il(t,e){let r=t instanceof HTMLElement?[t]:t,n=[];for(let i=0;i<r.length;i+=1){let s=r[i].children;for(let l=0;l<s.length;l+=1){let o=s[l];ri(o,e)&&n.push(o)}}return n}const sl=/(top|left|right|bottom|width|height)$/i;function Yt(t,e){for(let r in e)ni(t,r,e[r])}function ni(t,e,r){r==null?t.style[e]="":typeof r=="number"&&sl.test(e)?t.style[e]=`${r}px`:t.style[e]=r}function ll(t){var e,r;return(r=(e=t.composedPath)===null||e===void 0?void 0:e.call(t)[0])!==null&&r!==void 0?r:t.target}let qr=0;function Zt(){return qr+=1,"fc-dom-"+qr}function ol(t,e){return r=>{let n=V(r.target,t);n&&e.call(n,r,n)}}function ii(t,e,r,n){let i=ol(r,n);return t.addEventListener(e,i),()=>{t.removeEventListener(e,i)}}function al(t,e,r,n){let i;return ii(t,"mouseover",e,(s,l)=>{if(l!==i){i=l,r(s,l);let o=a=>{i=null,n(a,l),l.removeEventListener("mouseleave",o)};l.addEventListener("mouseleave",o)}})}function si(t){return Object.assign({onClick:t},li(t))}function li(t){return{tabIndex:0,onKeyDown(e){(e.key==="Enter"||e.key===" ")&&(t(e),e.preventDefault())}}}let Qr=0;function $(){return Qr+=1,String(Qr)}function $t(t){let e=[],r=[],n,i;for(typeof t=="string"?r=t.split(/\s*,\s*/):typeof t=="function"?r=[t]:Array.isArray(t)&&(r=t),n=0;n<r.length;n+=1)i=r[n],typeof i=="string"?e.push(i.charAt(0)==="-"?{field:i.substring(1),order:-1}:{field:i,order:1}):typeof i=="function"&&e.push({func:i});return e}function oi(t,e,r){let n,i;for(n=0;n<r.length;n+=1)if(i=cl(t,e,r[n]),i)return i;return 0}function cl(t,e,r){return r.func?r.func(t,e):ai(t[r.field],e[r.field])*(r.order||1)}function ai(t,e){return!t&&!e?0:e==null?-1:t==null?1:typeof t=="string"||typeof e=="string"?String(t).localeCompare(String(e)):t-e}function be(t,e){let r=String(t);return"000".substr(0,e-r.length)+r}function we(t,e,r){return typeof t=="function"?t(...e):typeof t=="string"?e.reduce((n,i,s)=>n.replace("$"+s,i||""),t):r}function ce(t){return t%1===0}function ul(t){let e=t.querySelector(".fc-scrollgrid-shrink-frame"),r=t.querySelector(".fc-scrollgrid-shrink-cushion");if(!e)throw new Error("needs fc-scrollgrid-shrink-frame className");if(!r)throw new Error("needs fc-scrollgrid-shrink-cushion className");return t.getBoundingClientRect().width-e.getBoundingClientRect().width+r.getBoundingClientRect().width}const Yr=["years","months","days","milliseconds"],dl=/^(-?)(?:(\d+)\.)?(\d+):(\d\d)(?::(\d\d)(?:\.(\d\d\d))?)?/;function D(t,e){return typeof t=="string"?fl(t):typeof t=="object"&&t?Zr(t):typeof t=="number"?Zr({[e||"milliseconds"]:t}):null}function fl(t){let e=dl.exec(t);if(e){let r=e[1]?-1:1;return{years:0,months:0,days:r*(e[2]?parseInt(e[2],10):0),milliseconds:r*((e[3]?parseInt(e[3],10):0)*60*60*1e3+(e[4]?parseInt(e[4],10):0)*60*1e3+(e[5]?parseInt(e[5],10):0)*1e3+(e[6]?parseInt(e[6],10):0))}}return null}function Zr(t){let e={years:t.years||t.year||0,months:t.months||t.month||0,days:t.days||t.day||0,milliseconds:(t.hours||t.hour||0)*60*60*1e3+(t.minutes||t.minute||0)*60*1e3+(t.seconds||t.second||0)*1e3+(t.milliseconds||t.millisecond||t.ms||0)},r=t.weeks||t.week;return r&&(e.days+=r*7,e.specifiedWeeks=!0),e}function hl(t,e){return t.years===e.years&&t.months===e.months&&t.days===e.days&&t.milliseconds===e.milliseconds}function ci(t){return!t.years&&!t.months&&!t.milliseconds?t.days:0}function pl(t,e){return{years:t.years-e.years,months:t.months-e.months,days:t.days-e.days,milliseconds:t.milliseconds-e.milliseconds}}function gl(t,e){return{years:t.years*e,months:t.months*e,days:t.days*e,milliseconds:t.milliseconds*e}}function ml(t){return ye(t)/365}function vl(t){return ye(t)/30}function ye(t){return Y(t)/864e5}function bl(t){return Y(t)/(1e3*60)}function yl(t){return Y(t)/1e3}function Y(t){return t.years*(365*864e5)+t.months*(30*864e5)+t.days*864e5+t.milliseconds}function ke(t,e){let r=null;for(let n=0;n<Yr.length;n+=1){let i=Yr[n];if(e[i]){let s=t[i]/e[i];if(!ce(s)||r!==null&&r!==s)return null;r=s}else if(t[i])return null}return r}function ne(t){let e=t.milliseconds;if(e){if(e%1e3!==0)return{unit:"millisecond",value:e};if(e%(1e3*60)!==0)return{unit:"second",value:e/1e3};if(e%(1e3*60*60)!==0)return{unit:"minute",value:e/(1e3*60)};if(e)return{unit:"hour",value:e/(1e3*60*60)}}return t.days?t.specifiedWeeks&&t.days%7===0?{unit:"week",value:t.days/7}:{unit:"day",value:t.days}:t.months?{unit:"month",value:t.months}:t.years?{unit:"year",value:t.years}:{unit:"millisecond",value:0}}function F(t,e,r){if(t===e)return!0;let n=t.length,i;if(n!==e.length)return!1;for(i=0;i<n;i+=1)if(!(r?r(t[i],e[i]):t[i]===e[i]))return!1;return!0}const ui=["sun","mon","tue","wed","thu","fri","sat"];function j(t,e){let r=ae(t);return r[2]+=e,U(r)}function de(t,e){let r=ae(t);return r[6]+=e,U(r)}function ut(t,e){return(e.valueOf()-t.valueOf())/(1e3*60*60*24)}function Sl(t,e){return(e.valueOf()-t.valueOf())/(1e3*60*60)}function El(t,e){return(e.valueOf()-t.valueOf())/(1e3*60)}function Cl(t,e){return(e.valueOf()-t.valueOf())/1e3}function Al(t,e){let r=T(t),n=T(e);return{years:0,months:0,days:Math.round(ut(r,n)),milliseconds:e.valueOf()-n.valueOf()-(t.valueOf()-r.valueOf())}}function Rl(t,e){let r=Me(t,e);return r!==null&&r%7===0?r/7:null}function Me(t,e){return re(t)===re(e)?Math.round(ut(t,e)):null}function T(t){return U([t.getUTCFullYear(),t.getUTCMonth(),t.getUTCDate()])}function wl(t){return U([t.getUTCFullYear(),t.getUTCMonth(),t.getUTCDate(),t.getUTCHours()])}function _l(t){return U([t.getUTCFullYear(),t.getUTCMonth(),t.getUTCDate(),t.getUTCHours(),t.getUTCMinutes()])}function Dl(t){return U([t.getUTCFullYear(),t.getUTCMonth(),t.getUTCDate(),t.getUTCHours(),t.getUTCMinutes(),t.getUTCSeconds()])}function Il(t,e,r){let n=t.getUTCFullYear(),i=yt(t,n,e,r);if(i<1)return yt(t,n-1,e,r);let s=yt(t,n+1,e,r);return s>=1?Math.min(i,s):i}function yt(t,e,r,n){let i=U([e,0,1+Tl(e,r,n)]),s=T(t),l=Math.round(ut(i,s));return Math.floor(l/7)+1}function Tl(t,e,r){let n=7+e-r;return-((7+U([t,0,n]).getUTCDay()-e)%7)+n-1}function $r(t){return[t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()]}function Xr(t){return new Date(t[0],t[1]||0,t[2]==null?1:t[2],t[3]||0,t[4]||0,t[5]||0)}function ae(t){return[t.getUTCFullYear(),t.getUTCMonth(),t.getUTCDate(),t.getUTCHours(),t.getUTCMinutes(),t.getUTCSeconds(),t.getUTCMilliseconds()]}function U(t){return t.length===1&&(t=t.concat([0])),new Date(Date.UTC(...t))}function Xt(t){return!isNaN(t.valueOf())}function re(t){return t.getUTCHours()*1e3*60*60+t.getUTCMinutes()*1e3*60+t.getUTCSeconds()*1e3+t.getUTCMilliseconds()}function Jt(t,e,r=!1){let n=t.toISOString();return n=n.replace(".000",""),r&&(n=n.replace("T00:00:00Z","")),n.length>10&&(e==null?n=n.replace("Z",""):e!==0&&(n=n.replace("Z",Kt(e,!0)))),n}function kl(t){return t.toISOString().replace(/T.*$/,"")}function Kt(t,e=!1){let r=t<0?"-":"+",n=Math.abs(t),i=Math.floor(n/60),s=Math.round(n%60);return e?`${r+be(i,2)}:${be(s,2)}`:`GMT${r}${i}${s?`:${be(s,2)}`:""}`}function A(t,e,r){let n,i;return function(...s){if(!n)i=t.apply(this,s);else if(!F(n,s)){let l=t.apply(this,s);(!e||!e(l,i))&&(i=l)}return n=s,i}}function ue(t,e,r){let n,i;return s=>(n?L(n,s)||(i=t.call(this,s)):i=t.call(this,s),n=s,i)}function St(t,e,r){let n=[],i=[];return s=>{let l=n.length,o=s.length,a=0;for(;a<l;a+=1)if(s[a]){if(!F(n[a],s[a])){let u=t.apply(this,s[a]);(!e||!e(u,i[a]))&&(i[a]=u)}}for(;a<o;a+=1)i[a]=t.apply(this,s[a]);return n=s,i.splice(o),i}}function Jr(t,e,r){let n={},i={};return s=>{let l={};for(let o in s)if(!i[o])l[o]=t.apply(this,s[o]);else if(F(n[o],s[o]))l[o]=i[o];else{r&&r(i[o]);let a=t.apply(this,s[o]);l[o]=a}return n=s,i=l,l}}const Et={week:3,separator:9,omitZeroMinute:9,meridiem:9,omitCommas:9},et={timeZoneName:7,era:6,year:5,month:4,day:2,weekday:2,hour:1,minute:1,second:1},Le=/\s*([ap])\.?m\.?/i,Ml=/,/g,xl=/\s+/g,Ol=/\u200e/g,Nl=/UTC|GMT/;class Hl{constructor(e){let r={},n={},i=9;for(let s in e)s in Et?(n[s]=e[s],Et[s]<9&&(i=Math.min(Et[s],i))):(r[s]=e[s],s in et&&(i=Math.min(et[s],i)));this.standardDateProps=r,this.extendedSettings=n,this.smallestUnitNum=i,this.buildFormattingFunc=A(Kr)}format(e,r){return this.buildFormattingFunc(this.standardDateProps,this.extendedSettings,r)(e)}formatRange(e,r,n,i){let{standardDateProps:s,extendedSettings:l}=this,o=zl(e.marker,r.marker,n.calendarSystem);if(!o)return this.format(e,n);let a=o;a>1&&(s.year==="numeric"||s.year==="2-digit")&&(s.month==="numeric"||s.month==="2-digit")&&(s.day==="numeric"||s.day==="2-digit")&&(a=1);let u=this.format(e,n),c=this.format(r,n);if(u===c)return u;let d=Fl(s,a),f=Kr(d,l,n),p=f(e),m=f(r),v=jl(u,p,c,m),b=l.separator||i||n.defaultSeparator||"";return v?v.before+p+b+m+v.after:u+b+c}getSmallestUnit(){switch(this.smallestUnitNum){case 7:case 6:case 5:return"year";case 4:return"month";case 3:return"week";case 2:return"day";default:return"time"}}}function Kr(t,e,r){let n=Object.keys(t).length;return n===1&&t.timeZoneName==="short"?i=>Kt(i.timeZoneOffset):n===0&&e.week?i=>Wl(r.computeWeekNumber(i.marker),r.weekText,r.weekTextLong,r.locale,e.week):Pl(t,e,r)}function Pl(t,e,r){t=Object.assign({},t),e=Object.assign({},e),Bl(t,e),t.timeZone="UTC";let n=new Intl.DateTimeFormat(r.locale.codes,t),i;if(e.omitZeroMinute){let s=Object.assign({},t);delete s.minute,i=new Intl.DateTimeFormat(r.locale.codes,s)}return s=>{let{marker:l}=s,o;i&&!l.getUTCMinutes()?o=i:o=n;let a=o.format(l);return Ll(a,s,t,e,r)}}function Bl(t,e){t.timeZoneName&&(t.hour||(t.hour="2-digit"),t.minute||(t.minute="2-digit")),t.timeZoneName==="long"&&(t.timeZoneName="short"),e.omitZeroMinute&&(t.second||t.millisecond)&&delete e.omitZeroMinute}function Ll(t,e,r,n,i){return t=t.replace(Ol,""),r.timeZoneName==="short"&&(t=Ul(t,i.timeZone==="UTC"||e.timeZoneOffset==null?"UTC":Kt(e.timeZoneOffset))),n.omitCommas&&(t=t.replace(Ml,"").trim()),n.omitZeroMinute&&(t=t.replace(":00","")),n.meridiem===!1?t=t.replace(Le,"").trim():n.meridiem==="narrow"?t=t.replace(Le,(s,l)=>l.toLocaleLowerCase()):n.meridiem==="short"?t=t.replace(Le,(s,l)=>`${l.toLocaleLowerCase()}m`):n.meridiem==="lowercase"&&(t=t.replace(Le,s=>s.toLocaleLowerCase())),t=t.replace(xl," "),t=t.trim(),t}function Ul(t,e){let r=!1;return t=t.replace(Nl,()=>(r=!0,e)),r||(t+=` ${e}`),t}function Wl(t,e,r,n,i){let s=[];return i==="long"?s.push(r):(i==="short"||i==="narrow")&&s.push(e),(i==="long"||i==="short")&&s.push(" "),s.push(n.simpleNumberFormat.format(t)),n.options.direction==="rtl"&&s.reverse(),s.join("")}function zl(t,e,r){return r.getMarkerYear(t)!==r.getMarkerYear(e)?5:r.getMarkerMonth(t)!==r.getMarkerMonth(e)?4:r.getMarkerDay(t)!==r.getMarkerDay(e)?2:re(t)!==re(e)?1:0}function Fl(t,e){let r={};for(let n in t)(!(n in et)||et[n]<=e)&&(r[n]=t[n]);return r}function jl(t,e,r,n){let i=0;for(;i<t.length;){let s=t.indexOf(e,i);if(s===-1)break;let l=t.substr(0,s);i=s+e.length;let o=t.substr(i),a=0;for(;a<r.length;){let u=r.indexOf(n,a);if(u===-1)break;let c=r.substr(0,u);a=u+n.length;let d=r.substr(a);if(l===c&&o===d)return{before:l,after:o}}}return null}function en(t,e){let r=e.markerToArray(t.marker);return{marker:t.marker,timeZoneOffset:t.timeZoneOffset,array:r,year:r[0],month:r[1],day:r[2],hour:r[3],minute:r[4],second:r[5],millisecond:r[6]}}function tt(t,e,r,n){let i=en(t,r.calendarSystem),s=e?en(e,r.calendarSystem):null;return{date:i,start:i,end:s,timeZone:r.timeZone,localeCodes:r.locale.codes,defaultSeparator:n||r.defaultSeparator}}class Vl{constructor(e){this.cmdStr=e}format(e,r,n){return r.cmdFormatter(this.cmdStr,tt(e,null,r,n))}formatRange(e,r,n,i){return n.cmdFormatter(this.cmdStr,tt(e,r,n,i))}}class Gl{constructor(e){this.func=e}format(e,r,n){return this.func(tt(e,null,r,n))}formatRange(e,r,n,i){return this.func(tt(e,r,n,i))}}function P(t){return typeof t=="object"&&t?new Hl(t):typeof t=="string"?new Vl(t):typeof t=="function"?new Gl(t):null}const tn={navLinkDayClick:g,navLinkWeekClick:g,duration:D,bootstrapFontAwesome:g,buttonIcons:g,customButtons:g,defaultAllDayEventDuration:D,defaultTimedEventDuration:D,nextDayThreshold:D,scrollTime:D,scrollTimeReset:Boolean,slotMinTime:D,slotMaxTime:D,dayPopoverFormat:P,slotDuration:D,snapDuration:D,headerToolbar:g,footerToolbar:g,defaultRangeSeparator:String,titleRangeSeparator:String,forceEventDuration:Boolean,dayHeaders:Boolean,dayHeaderFormat:P,dayHeaderClassNames:g,dayHeaderContent:g,dayHeaderDidMount:g,dayHeaderWillUnmount:g,dayCellClassNames:g,dayCellContent:g,dayCellDidMount:g,dayCellWillUnmount:g,initialView:String,aspectRatio:Number,weekends:Boolean,weekNumberCalculation:g,weekNumbers:Boolean,weekNumberClassNames:g,weekNumberContent:g,weekNumberDidMount:g,weekNumberWillUnmount:g,editable:Boolean,viewClassNames:g,viewDidMount:g,viewWillUnmount:g,nowIndicator:Boolean,nowIndicatorClassNames:g,nowIndicatorContent:g,nowIndicatorDidMount:g,nowIndicatorWillUnmount:g,showNonCurrentDates:Boolean,lazyFetching:Boolean,startParam:String,endParam:String,timeZoneParam:String,timeZone:String,locales:g,locale:g,themeSystem:String,dragRevertDuration:Number,dragScroll:Boolean,allDayMaintainDuration:Boolean,unselectAuto:Boolean,dropAccept:g,eventOrder:$t,eventOrderStrict:Boolean,handleWindowResize:Boolean,windowResizeDelay:Number,longPressDelay:Number,eventDragMinDistance:Number,expandRows:Boolean,height:g,contentHeight:g,direction:String,weekNumberFormat:P,eventResizableFromStart:Boolean,displayEventTime:Boolean,displayEventEnd:Boolean,weekText:String,weekTextLong:String,progressiveEventRendering:Boolean,businessHours:g,initialDate:g,now:g,eventDataTransform:g,stickyHeaderDates:g,stickyFooterScrollbar:g,viewHeight:g,defaultAllDay:Boolean,eventSourceFailure:g,eventSourceSuccess:g,eventDisplay:String,eventStartEditable:Boolean,eventDurationEditable:Boolean,eventOverlap:g,eventConstraint:g,eventAllow:g,eventBackgroundColor:String,eventBorderColor:String,eventTextColor:String,eventColor:String,eventClassNames:g,eventContent:g,eventDidMount:g,eventWillUnmount:g,selectConstraint:g,selectOverlap:g,selectAllow:g,droppable:Boolean,unselectCancel:String,slotLabelFormat:g,slotLaneClassNames:g,slotLaneContent:g,slotLaneDidMount:g,slotLaneWillUnmount:g,slotLabelClassNames:g,slotLabelContent:g,slotLabelDidMount:g,slotLabelWillUnmount:g,dayMaxEvents:g,dayMaxEventRows:g,dayMinWidth:Number,slotLabelInterval:D,allDayText:String,allDayClassNames:g,allDayContent:g,allDayDidMount:g,allDayWillUnmount:g,slotMinWidth:Number,navLinks:Boolean,eventTimeFormat:P,rerenderDelay:Number,moreLinkText:g,moreLinkHint:g,selectMinDistance:Number,selectable:Boolean,selectLongPressDelay:Number,eventLongPressDelay:Number,selectMirror:Boolean,eventMaxStack:Number,eventMinHeight:Number,eventMinWidth:Number,eventShortHeight:Number,slotEventOverlap:Boolean,plugins:g,firstDay:Number,dayCount:Number,dateAlignment:String,dateIncrement:D,hiddenDays:g,fixedWeekCount:Boolean,validRange:g,visibleRange:g,titleFormat:g,eventInteractive:Boolean,noEventsText:String,viewHint:g,navLinkHint:g,closeHint:String,timeHint:String,eventHint:String,moreLinkClick:g,moreLinkClassNames:g,moreLinkContent:g,moreLinkDidMount:g,moreLinkWillUnmount:g,monthStartFormat:P,handleCustomRendering:g,customRenderingMetaMap:g,customRenderingReplaces:Boolean},_e={eventDisplay:"auto",defaultRangeSeparator:" - ",titleRangeSeparator:" – ",defaultTimedEventDuration:"01:00:00",defaultAllDayEventDuration:{day:1},forceEventDuration:!1,nextDayThreshold:"00:00:00",dayHeaders:!0,initialView:"",aspectRatio:1.35,headerToolbar:{start:"title",center:"",end:"today prev,next"},weekends:!0,weekNumbers:!1,weekNumberCalculation:"local",editable:!1,nowIndicator:!1,scrollTime:"06:00:00",scrollTimeReset:!0,slotMinTime:"00:00:00",slotMaxTime:"24:00:00",showNonCurrentDates:!0,lazyFetching:!0,startParam:"start",endParam:"end",timeZoneParam:"timeZone",timeZone:"local",locales:[],locale:"",themeSystem:"standard",dragRevertDuration:500,dragScroll:!0,allDayMaintainDuration:!1,unselectAuto:!0,dropAccept:"*",eventOrder:"start,-duration,allDay,title",dayPopoverFormat:{month:"long",day:"numeric",year:"numeric"},handleWindowResize:!0,windowResizeDelay:100,longPressDelay:1e3,eventDragMinDistance:5,expandRows:!1,navLinks:!1,selectable:!1,eventMinHeight:15,eventMinWidth:30,eventShortHeight:30,monthStartFormat:{month:"long",day:"numeric"}},rn={datesSet:g,eventsSet:g,eventAdd:g,eventChange:g,eventRemove:g,windowResize:g,eventClick:g,eventMouseEnter:g,eventMouseLeave:g,select:g,unselect:g,loading:g,_unmount:g,_beforeprint:g,_afterprint:g,_noEventDrop:g,_noEventResize:g,_resize:g,_scrollRequest:g},nn={buttonText:g,buttonHints:g,views:g,plugins:g,initialEvents:g,events:g,eventSources:g},se={headerToolbar:le,footerToolbar:le,buttonText:le,buttonHints:le,buttonIcons:le,dateIncrement:le,plugins:Ue,events:Ue,eventSources:Ue,resources:Ue};function le(t,e){return typeof t=="object"&&typeof e=="object"&&t&&e?L(t,e):t===e}function Ue(t,e){return Array.isArray(t)&&Array.isArray(e)?F(t,e):t===e}const ql={type:String,component:g,buttonText:String,buttonTextKey:String,dateProfileGeneratorClass:g,usesMinMaxTime:Boolean,classNames:g,content:g,didMount:g,willUnmount:g};function Ct(t){return er(t,se)}function Ne(t,e){let r={},n={};for(let i in e)i in t&&(r[i]=e[i](t[i]));for(let i in t)i in e||(n[i]=t[i]);return{refined:r,extra:n}}function g(t){return t}const{hasOwnProperty:rt}=Object.prototype;function er(t,e){let r={};if(e){for(let n in e)if(e[n]===le){let i=[];for(let s=t.length-1;s>=0;s-=1){let l=t[s][n];if(typeof l=="object"&&l)i.unshift(l);else if(l!==void 0){r[n]=l;break}}i.length&&(r[n]=er(i))}}for(let n=t.length-1;n>=0;n-=1){let i=t[n];for(let s in i)s in r||(r[s]=i[s])}return r}function K(t,e){let r={};for(let n in t)e(t[n],n)&&(r[n]=t[n]);return r}function B(t,e){let r={};for(let n in t)r[n]=e(t[n],n);return r}function di(t){let e={};for(let r of t)e[r]=!0;return e}function tr(t){let e=[];for(let r in t)e.push(t[r]);return e}function L(t,e){if(t===e)return!0;for(let r in t)if(rt.call(t,r)&&!(r in e))return!1;for(let r in e)if(rt.call(e,r)&&t[r]!==e[r])return!1;return!0}const Ql=/^on[A-Z]/;function Yl(t,e){const r=Nt(t,e);for(let n of r)if(!Ql.test(n))return!1;return!0}function Nt(t,e){let r=[];for(let n in t)rt.call(t,n)&&(n in e||r.push(n));for(let n in e)rt.call(e,n)&&t[n]!==e[n]&&r.push(n);return r}function Qe(t,e,r={}){if(t===e)return!0;for(let n in e)if(!(n in t&&Zl(t[n],e[n],r[n])))return!1;for(let n in t)if(!(n in e))return!1;return!0}function Zl(t,e,r){return t===e||r===!0?!0:r?r(t,e):!1}function fi(t,e=0,r,n=1){let i=[];r==null&&(r=Object.keys(t).length);for(let s=e;s<r;s+=n){let l=t[s];l!==void 0&&i.push(l)}return i}let hi={};function $l(t,e){hi[t]=e}function Xl(t){return new hi[t]}class Jl{getMarkerYear(e){return e.getUTCFullYear()}getMarkerMonth(e){return e.getUTCMonth()}getMarkerDay(e){return e.getUTCDate()}arrayToMarker(e){return U(e)}markerToArray(e){return ae(e)}}$l("gregory",Jl);const Kl=/^\s*(\d{4})(-?(\d{2})(-?(\d{2})([T ](\d{2}):?(\d{2})(:?(\d{2})(\.(\d+))?)?(Z|(([-+])(\d{2})(:?(\d{2}))?))?)?)?)?$/;function eo(t){let e=Kl.exec(t);if(e){let r=new Date(Date.UTC(Number(e[1]),e[3]?Number(e[3])-1:0,Number(e[5]||1),Number(e[7]||0),Number(e[8]||0),Number(e[10]||0),e[12]?+`0.${e[12]}`*1e3:0));if(Xt(r)){let n=null;return e[13]&&(n=(e[15]==="-"?-1:1)*(Number(e[16]||0)*60+Number(e[18]||0))),{marker:r,isTimeUnspecified:!e[6],timeZoneOffset:n}}}return null}class to{constructor(e){let r=this.timeZone=e.timeZone,n=r!=="local"&&r!=="UTC";e.namedTimeZoneImpl&&n&&(this.namedTimeZoneImpl=new e.namedTimeZoneImpl(r)),this.canComputeOffset=!!(!n||this.namedTimeZoneImpl),this.calendarSystem=Xl(e.calendarSystem),this.locale=e.locale,this.weekDow=e.locale.week.dow,this.weekDoy=e.locale.week.doy,e.weekNumberCalculation==="ISO"&&(this.weekDow=1,this.weekDoy=4),typeof e.firstDay=="number"&&(this.weekDow=e.firstDay),typeof e.weekNumberCalculation=="function"&&(this.weekNumberFunc=e.weekNumberCalculation),this.weekText=e.weekText!=null?e.weekText:e.locale.options.weekText,this.weekTextLong=(e.weekTextLong!=null?e.weekTextLong:e.locale.options.weekTextLong)||this.weekText,this.cmdFormatter=e.cmdFormatter,this.defaultSeparator=e.defaultSeparator}createMarker(e){let r=this.createMarkerMeta(e);return r===null?null:r.marker}createNowMarker(){return this.canComputeOffset?this.timestampToMarker(new Date().valueOf()):U($r(new Date))}createMarkerMeta(e){if(typeof e=="string")return this.parse(e);let r=null;return typeof e=="number"?r=this.timestampToMarker(e):e instanceof Date?(e=e.valueOf(),isNaN(e)||(r=this.timestampToMarker(e))):Array.isArray(e)&&(r=U(e)),r===null||!Xt(r)?null:{marker:r,isTimeUnspecified:!1,forcedTzo:null}}parse(e){let r=eo(e);if(r===null)return null;let{marker:n}=r,i=null;return r.timeZoneOffset!==null&&(this.canComputeOffset?n=this.timestampToMarker(n.valueOf()-r.timeZoneOffset*60*1e3):i=r.timeZoneOffset),{marker:n,isTimeUnspecified:r.isTimeUnspecified,forcedTzo:i}}getYear(e){return this.calendarSystem.getMarkerYear(e)}getMonth(e){return this.calendarSystem.getMarkerMonth(e)}getDay(e){return this.calendarSystem.getMarkerDay(e)}add(e,r){let n=this.calendarSystem.markerToArray(e);return n[0]+=r.years,n[1]+=r.months,n[2]+=r.days,n[6]+=r.milliseconds,this.calendarSystem.arrayToMarker(n)}subtract(e,r){let n=this.calendarSystem.markerToArray(e);return n[0]-=r.years,n[1]-=r.months,n[2]-=r.days,n[6]-=r.milliseconds,this.calendarSystem.arrayToMarker(n)}addYears(e,r){let n=this.calendarSystem.markerToArray(e);return n[0]+=r,this.calendarSystem.arrayToMarker(n)}addMonths(e,r){let n=this.calendarSystem.markerToArray(e);return n[1]+=r,this.calendarSystem.arrayToMarker(n)}diffWholeYears(e,r){let{calendarSystem:n}=this;return re(e)===re(r)&&n.getMarkerDay(e)===n.getMarkerDay(r)&&n.getMarkerMonth(e)===n.getMarkerMonth(r)?n.getMarkerYear(r)-n.getMarkerYear(e):null}diffWholeMonths(e,r){let{calendarSystem:n}=this;return re(e)===re(r)&&n.getMarkerDay(e)===n.getMarkerDay(r)?n.getMarkerMonth(r)-n.getMarkerMonth(e)+(n.getMarkerYear(r)-n.getMarkerYear(e))*12:null}greatestWholeUnit(e,r){let n=this.diffWholeYears(e,r);return n!==null?{unit:"year",value:n}:(n=this.diffWholeMonths(e,r),n!==null?{unit:"month",value:n}:(n=Rl(e,r),n!==null?{unit:"week",value:n}:(n=Me(e,r),n!==null?{unit:"day",value:n}:(n=Sl(e,r),ce(n)?{unit:"hour",value:n}:(n=El(e,r),ce(n)?{unit:"minute",value:n}:(n=Cl(e,r),ce(n)?{unit:"second",value:n}:{unit:"millisecond",value:r.valueOf()-e.valueOf()}))))))}countDurationsBetween(e,r,n){let i;return n.years&&(i=this.diffWholeYears(e,r),i!==null)?i/ml(n):n.months&&(i=this.diffWholeMonths(e,r),i!==null)?i/vl(n):n.days&&(i=Me(e,r),i!==null)?i/ye(n):(r.valueOf()-e.valueOf())/Y(n)}startOf(e,r){return r==="year"?this.startOfYear(e):r==="month"?this.startOfMonth(e):r==="week"?this.startOfWeek(e):r==="day"?T(e):r==="hour"?wl(e):r==="minute"?_l(e):r==="second"?Dl(e):null}startOfYear(e){return this.calendarSystem.arrayToMarker([this.calendarSystem.getMarkerYear(e)])}startOfMonth(e){return this.calendarSystem.arrayToMarker([this.calendarSystem.getMarkerYear(e),this.calendarSystem.getMarkerMonth(e)])}startOfWeek(e){return this.calendarSystem.arrayToMarker([this.calendarSystem.getMarkerYear(e),this.calendarSystem.getMarkerMonth(e),e.getUTCDate()-(e.getUTCDay()-this.weekDow+7)%7])}computeWeekNumber(e){return this.weekNumberFunc?this.weekNumberFunc(this.toDate(e)):Il(e,this.weekDow,this.weekDoy)}format(e,r,n={}){return r.format({marker:e,timeZoneOffset:n.forcedTzo!=null?n.forcedTzo:this.offsetForMarker(e)},this)}formatRange(e,r,n,i={}){return i.isEndExclusive&&(r=de(r,-1)),n.formatRange({marker:e,timeZoneOffset:i.forcedStartTzo!=null?i.forcedStartTzo:this.offsetForMarker(e)},{marker:r,timeZoneOffset:i.forcedEndTzo!=null?i.forcedEndTzo:this.offsetForMarker(r)},this,i.defaultSeparator)}formatIso(e,r={}){let n=null;return r.omitTimeZoneOffset||(r.forcedTzo!=null?n=r.forcedTzo:n=this.offsetForMarker(e)),Jt(e,n,r.omitTime)}timestampToMarker(e){return this.timeZone==="local"?U($r(new Date(e))):this.timeZone==="UTC"||!this.namedTimeZoneImpl?new Date(e):U(this.namedTimeZoneImpl.timestampToArray(e))}offsetForMarker(e){return this.timeZone==="local"?-Xr(ae(e)).getTimezoneOffset():this.timeZone==="UTC"?0:this.namedTimeZoneImpl?this.namedTimeZoneImpl.offsetForArray(ae(e)):null}toDate(e,r){return this.timeZone==="local"?Xr(ae(e)):this.timeZone==="UTC"?new Date(e.valueOf()):this.namedTimeZoneImpl?new Date(e.valueOf()-this.namedTimeZoneImpl.offsetForArray(ae(e))*1e3*60):new Date(e.valueOf()-(r||0))}}class He{constructor(e){this.iconOverrideOption&&this.setIconOverride(e[this.iconOverrideOption])}setIconOverride(e){let r,n;if(typeof e=="object"&&e){r=Object.assign({},this.iconClasses);for(n in e)r[n]=this.applyIconOverridePrefix(e[n]);this.iconClasses=r}else e===!1&&(this.iconClasses={})}applyIconOverridePrefix(e){let r=this.iconOverridePrefix;return r&&e.indexOf(r)!==0&&(e=r+e),e}getClass(e){return this.classes[e]||""}getIconClass(e,r){let n;return r&&this.rtlIconClasses?n=this.rtlIconClasses[e]||this.iconClasses[e]:n=this.iconClasses[e],n?`${this.baseIconClass} ${n}`:""}getCustomButtonIconClass(e){let r;return this.iconOverrideCustomButtonOption&&(r=e[this.iconOverrideCustomButtonOption],r)?`${this.baseIconClass} ${this.applyIconOverridePrefix(r)}`:""}}He.prototype.classes={};He.prototype.iconClasses={};He.prototype.baseIconClass="";He.prototype.iconOverridePrefix="";function nt(t){t();let e=E.debounceRendering,r=[];function n(i){r.push(i)}for(E.debounceRendering=n,Ie(h(ro,{}),document.createElement("div"));r.length;)r.shift()();E.debounceRendering=e}class ro extends W{render(){return h("div",{})}componentDidMount(){this.setState({})}}function pi(t){let e=Ls(t),r=e.Provider;return e.Provider=function(){let n=!this.getChildContext,i=r.apply(this,arguments);if(n){let s=[];this.shouldComponentUpdate=l=>{this.props.value!==l.value&&s.forEach(o=>{o.context=l.value,o.forceUpdate()})},this.sub=l=>{s.push(l);let o=l.componentWillUnmount;l.componentWillUnmount=()=>{s.splice(s.indexOf(l),1),o&&o.call(l)}}}return i},e}class no{constructor(e,r,n,i){this.execFunc=e,this.emitter=r,this.scrollTime=n,this.scrollTimeReset=i,this.handleScrollRequest=s=>{this.queuedRequest=Object.assign({},this.queuedRequest||{},s),this.drain()},r.on("_scrollRequest",this.handleScrollRequest),this.fireInitialScroll()}detach(){this.emitter.off("_scrollRequest",this.handleScrollRequest)}update(e){e&&this.scrollTimeReset?this.fireInitialScroll():this.drain()}fireInitialScroll(){this.handleScrollRequest({time:this.scrollTime})}drain(){this.queuedRequest&&this.execFunc(this.queuedRequest)&&(this.queuedRequest=null)}}const pe=pi({});function io(t,e,r,n,i,s,l,o,a,u,c,d,f,p){return{dateEnv:i,nowManager:s,options:r,pluginHooks:o,emitter:c,dispatch:a,getCurrentData:u,calendarApi:d,viewSpec:t,viewApi:e,dateProfileGenerator:n,theme:l,isRtl:r.direction==="rtl",addResizeHandler(m){c.on("_resize",m)},removeResizeHandler(m){c.off("_resize",m)},createScrollResponder(m){return new no(m,c,D(r.scrollTime),r.scrollTimeReset)},registerInteractiveComponent:f,unregisterInteractiveComponent:p}}class ge extends W{shouldComponentUpdate(e,r){return this.debug&&console.log(Nt(e,this.props),Nt(r,this.state)),!Qe(this.props,e,this.propEquality)||!Qe(this.state,r,this.stateEquality)}safeSetState(e){Qe(this.state,Object.assign(Object.assign({},this.state),e),this.stateEquality)||this.setState(e)}}ge.addPropsEquality=so;ge.addStateEquality=lo;ge.contextType=pe;ge.prototype.propEquality={};ge.prototype.stateEquality={};class R extends ge{}R.contextType=pe;function so(t){let e=Object.create(this.prototype.propEquality);Object.assign(e,t),this.prototype.propEquality=e}function lo(t){let e=Object.create(this.prototype.stateEquality);Object.assign(e,t),this.prototype.stateEquality=e}function Z(t,e){typeof t=="function"?t(e):t&&(t.current=e)}class rr extends R{constructor(){super(...arguments),this.id=$(),this.queuedDomNodes=[],this.currentDomNodes=[],this.handleEl=e=>{const{options:r}=this.context,{generatorName:n}=this.props;(!r.customRenderingReplaces||!Ht(n,r))&&this.updateElRef(e)},this.updateElRef=e=>{this.props.elRef&&Z(this.props.elRef,e)}}render(){const{props:e,context:r}=this,{options:n}=r,{customGenerator:i,defaultGenerator:s,renderProps:l}=e,o=gi(e,[],this.handleEl);let a=!1,u,c=[],d;if(i!=null){const f=typeof i=="function"?i(l,h):i;if(f===!0)a=!0;else{const p=f&&typeof f=="object";p&&"html"in f?o.dangerouslySetInnerHTML={__html:f.html}:p&&"domNodes"in f?c=Array.prototype.slice.call(f.domNodes):(p?Pn(f):typeof f!="function")?u=f:d=f}}else a=!Ht(e.generatorName,n);return a&&s&&(u=s(l)),this.queuedDomNodes=c,this.currentGeneratorMeta=d,h(e.elTag,o,u)}componentDidMount(){this.applyQueueudDomNodes(),this.triggerCustomRendering(!0)}componentDidUpdate(){this.applyQueueudDomNodes(),this.triggerCustomRendering(!0)}componentWillUnmount(){this.triggerCustomRendering(!1)}triggerCustomRendering(e){var r;const{props:n,context:i}=this,{handleCustomRendering:s,customRenderingMetaMap:l}=i.options;if(s){const o=(r=this.currentGeneratorMeta)!==null&&r!==void 0?r:l==null?void 0:l[n.generatorName];o&&s(Object.assign(Object.assign({id:this.id,isActive:e,containerEl:this.base,reportNewContainerEl:this.updateElRef,generatorMeta:o},n),{elClasses:(n.elClasses||[]).filter(oo)}))}}applyQueueudDomNodes(){const{queuedDomNodes:e,currentDomNodes:r}=this,n=this.base;if(!F(e,r)){r.forEach(Qt);for(let i of e)n.appendChild(i);this.currentDomNodes=e}}}rr.addPropsEquality({elClasses:F,elStyle:L,elAttrs:Yl,renderProps:L});function Ht(t,e){var r;return!!(e.handleCustomRendering&&t&&(!((r=e.customRenderingMetaMap)===null||r===void 0)&&r[t]))}function gi(t,e,r){const n=Object.assign(Object.assign({},t.elAttrs),{ref:r});return(t.elClasses||e)&&(n.className=(t.elClasses||[]).concat(e||[]).concat(n.className||[]).filter(Boolean).join(" ")),t.elStyle&&(n.style=t.elStyle),n}function oo(t){return!!t}const mi=pi(0);class H extends W{constructor(){super(...arguments),this.InnerContent=ao.bind(void 0,this),this.handleEl=e=>{this.el=e,this.props.elRef&&(Z(this.props.elRef,e),e&&this.didMountMisfire&&this.componentDidMount())}}render(){const{props:e}=this,r=co(e.classNameGenerator,e.renderProps);if(e.children){const n=gi(e,r,this.handleEl),i=e.children(this.InnerContent,e.renderProps,n);return e.elTag?h(e.elTag,n,i):i}else return h(rr,Object.assign(Object.assign({},e),{elRef:this.handleEl,elTag:e.elTag||"div",elClasses:(e.elClasses||[]).concat(r),renderId:this.context}))}componentDidMount(){var e,r;this.el?(r=(e=this.props).didMount)===null||r===void 0||r.call(e,Object.assign(Object.assign({},this.props.renderProps),{el:this.el})):this.didMountMisfire=!0}componentWillUnmount(){var e,r;(r=(e=this.props).willUnmount)===null||r===void 0||r.call(e,Object.assign(Object.assign({},this.props.renderProps),{el:this.el}))}}H.contextType=mi;function ao(t,e){const r=t.props;return h(rr,Object.assign({renderProps:r.renderProps,generatorName:r.generatorName,customGenerator:r.customGenerator,defaultGenerator:r.defaultGenerator,renderId:t.context},e))}function co(t,e){const r=typeof t=="function"?t(e):t||[];return typeof r=="string"?[r]:r}class vi extends R{render(){let{props:e,context:r}=this,{options:n}=r,i={view:r.viewApi};return h(H,Object.assign({},e,{elTag:e.elTag||"div",elClasses:[...bi(e.viewSpec),...e.elClasses||[]],renderProps:i,classNameGenerator:n.viewClassNames,generatorName:void 0,didMount:n.viewDidMount,willUnmount:n.viewWillUnmount}),()=>e.children)}}function bi(t){return[`fc-${t.type}-view`,"fc-view"]}function uo(t,e){let r=null,n=null;return t.start&&(r=e.createMarker(t.start)),t.end&&(n=e.createMarker(t.end)),!r&&!n||r&&n&&n<r?null:{start:r,end:n}}function sn(t,e){let r=[],{start:n}=e,i,s;for(t.sort(fo),i=0;i<t.length;i+=1)s=t[i],s.start>n&&r.push({start:n,end:s.start}),s.end>n&&(n=s.end);return n<e.end&&r.push({start:n,end:e.end}),r}function fo(t,e){return t.start.valueOf()-e.start.valueOf()}function Ee(t,e){let{start:r,end:n}=t,i=null;return e.start!==null&&(r===null?r=e.start:r=new Date(Math.max(r.valueOf(),e.start.valueOf()))),e.end!=null&&(n===null?n=e.end:n=new Date(Math.min(n.valueOf(),e.end.valueOf()))),(r===null||n===null||r<n)&&(i={start:r,end:n}),i}function ho(t,e){return(t.start===null?null:t.start.valueOf())===(e.start===null?null:e.start.valueOf())&&(t.end===null?null:t.end.valueOf())===(e.end===null?null:e.end.valueOf())}function dt(t,e){return(t.end===null||e.start===null||t.end>e.start)&&(t.start===null||e.end===null||t.start<e.end)}function po(t,e){return(t.start===null||e.start!==null&&e.start>=t.start)&&(t.end===null||e.end!==null&&e.end<=t.end)}function J(t,e){return(t.start===null||e>=t.start)&&(t.end===null||e<t.end)}function go(t,e){return e.start!=null&&t<e.start?e.start:e.end!=null&&t>=e.end?new Date(e.end.valueOf()-1):t}function yi(t){let e=Math.floor(ut(t.start,t.end))||1,r=T(t.start),n=j(r,e);return{start:r,end:n}}function nr(t,e=D(0)){let r=null,n=null;if(t.end){n=T(t.end);let i=t.end.valueOf()-n.valueOf();i&&i>=Y(e)&&(n=j(n,1))}return t.start&&(r=T(t.start),n&&n<=r&&(n=j(r,1))),{start:r,end:n}}function We(t,e,r,n){return n==="year"?D(r.diffWholeYears(t,e),"year"):n==="month"?D(r.diffWholeMonths(t,e),"month"):Al(t,e)}class mo{constructor(e){this.props=e,this.initHiddenDays()}buildPrev(e,r,n){let{dateEnv:i}=this.props,s=i.subtract(i.startOf(r,e.currentRangeUnit),e.dateIncrement);return this.build(s,-1,n)}buildNext(e,r,n){let{dateEnv:i}=this.props,s=i.add(i.startOf(r,e.currentRangeUnit),e.dateIncrement);return this.build(s,1,n)}build(e,r,n=!0){let{props:i}=this,s,l,o,a,u,c;return s=this.buildValidRange(),s=this.trimHiddenDays(s),n&&(e=go(e,s)),l=this.buildCurrentRangeInfo(e,r),o=/^(year|month|week|day)$/.test(l.unit),a=this.buildRenderRange(this.trimHiddenDays(l.range),l.unit,o),a=this.trimHiddenDays(a),u=a,i.showNonCurrentDates||(u=Ee(u,l.range)),u=this.adjustActiveRange(u),u=Ee(u,s),c=dt(l.range,s),J(a,e)||(e=a.start),{currentDate:e,validRange:s,currentRange:l.range,currentRangeUnit:l.unit,isRangeAllDay:o,activeRange:u,renderRange:a,slotMinTime:i.slotMinTime,slotMaxTime:i.slotMaxTime,isValid:c,dateIncrement:this.buildDateIncrement(l.duration)}}buildValidRange(){let e=this.props.validRangeInput,r=typeof e=="function"?e.call(this.props.calendarApi,this.props.dateEnv.toDate(this.props.nowManager.getDateMarker())):e;return this.refineRange(r)||{start:null,end:null}}buildCurrentRangeInfo(e,r){let{props:n}=this,i=null,s=null,l=null,o;return n.duration?(i=n.duration,s=n.durationUnit,l=this.buildRangeFromDuration(e,r,i,s)):(o=this.props.dayCount)?(s="day",l=this.buildRangeFromDayCount(e,r,o)):(l=this.buildCustomVisibleRange(e))?s=n.dateEnv.greatestWholeUnit(l.start,l.end).unit:(i=this.getFallbackDuration(),s=ne(i).unit,l=this.buildRangeFromDuration(e,r,i,s)),{duration:i,unit:s,range:l}}getFallbackDuration(){return D({day:1})}adjustActiveRange(e){let{dateEnv:r,usesMinMaxTime:n,slotMinTime:i,slotMaxTime:s}=this.props,{start:l,end:o}=e;return n&&(ye(i)<0&&(l=T(l),l=r.add(l,i)),ye(s)>1&&(o=T(o),o=j(o,-1),o=r.add(o,s))),{start:l,end:o}}buildRangeFromDuration(e,r,n,i){let{dateEnv:s,dateAlignment:l}=this.props,o,a,u;if(!l){let{dateIncrement:d}=this.props;d&&Y(d)<Y(n)?l=ne(d).unit:l=i}ye(n)<=1&&this.isHiddenDay(o)&&(o=this.skipHiddenDays(o,r),o=T(o));function c(){o=s.startOf(e,l),a=s.add(o,n),u={start:o,end:a}}return c(),this.trimHiddenDays(u)||(e=this.skipHiddenDays(e,r),c()),u}buildRangeFromDayCount(e,r,n){let{dateEnv:i,dateAlignment:s}=this.props,l=0,o=e,a;s&&(o=i.startOf(o,s)),o=T(o),o=this.skipHiddenDays(o,r),a=o;do a=j(a,1),this.isHiddenDay(a)||(l+=1);while(l<n);return{start:o,end:a}}buildCustomVisibleRange(e){let{props:r}=this,n=r.visibleRangeInput,i=typeof n=="function"?n.call(r.calendarApi,r.dateEnv.toDate(e)):n,s=this.refineRange(i);return s&&(s.start==null||s.end==null)?null:s}buildRenderRange(e,r,n){return e}buildDateIncrement(e){let{dateIncrement:r}=this.props,n;return r||((n=this.props.dateAlignment)?D(1,n):e||D({days:1}))}refineRange(e){if(e){let r=uo(e,this.props.dateEnv);return r&&(r=nr(r)),r}return null}initHiddenDays(){let e=this.props.hiddenDays||[],r=[],n=0,i;for(this.props.weekends===!1&&e.push(0,6),i=0;i<7;i+=1)(r[i]=e.indexOf(i)!==-1)||(n+=1);if(!n)throw new Error("invalid hiddenDays");this.isHiddenDayHash=r}trimHiddenDays(e){let{start:r,end:n}=e;return r&&(r=this.skipHiddenDays(r)),n&&(n=this.skipHiddenDays(n,-1,!0)),r==null||n==null||r<n?{start:r,end:n}:null}isHiddenDay(e){return e instanceof Date&&(e=e.getUTCDay()),this.isHiddenDayHash[e]}skipHiddenDays(e,r=1,n=!1){for(;this.isHiddenDayHash[(e.getUTCDay()+(n?r:0)+7)%7];)e=j(e,r);return e}}function ir(t,e,r,n){return{instanceId:$(),defId:t,range:e,forcedStartTzo:r??null,forcedEndTzo:n??null}}function vo(t,e,r,n){for(let i=0;i<n.length;i+=1){let s=n[i].parse(t,r);if(s){let{allDay:l}=t;return l==null&&(l=e,l==null&&(l=s.allDayGuess,l==null&&(l=!1))),{allDay:l,duration:s.duration,typeData:s.typeData,typeId:i}}}return null}function fe(t,e,r){let{dateEnv:n,pluginHooks:i,options:s}=r,{defs:l,instances:o}=t;o=K(o,a=>!l[a.defId].recurringDef);for(let a in l){let u=l[a];if(u.recurringDef){let{duration:c}=u.recurringDef;c||(c=u.allDay?s.defaultAllDayEventDuration:s.defaultTimedEventDuration);let d=bo(u,c,e,n,i.recurringTypes);for(let f of d){let p=ir(a,{start:f,end:n.add(f,c)});o[p.instanceId]=p}}}return{defs:l,instances:o}}function bo(t,e,r,n,i){let l=i[t.recurringDef.typeId].expand(t.recurringDef.typeData,{start:n.subtract(r.start,e),end:r.end},n);return t.allDay&&(l=l.map(T)),l}const Ye={id:String,groupId:String,title:String,url:String,interactive:Boolean},Si={start:g,end:g,date:g,allDay:Boolean},yo=Object.assign(Object.assign(Object.assign({},Ye),Si),{extendedProps:g});function Ei(t,e,r,n,i=sr(r),s,l){let{refined:o,extra:a}=Ci(t,r,i),u=Eo(e,r),c=vo(o,u,r.dateEnv,r.pluginHooks.recurringTypes);if(c){let f=Pt(o,a,e?e.sourceId:"",c.allDay,!!c.duration,r,s);return f.recurringDef={typeId:c.typeId,typeData:c.typeData,duration:c.duration},{def:f,instance:null}}let d=So(o,u,r,n);if(d){let f=Pt(o,a,e?e.sourceId:"",d.allDay,d.hasEnd,r,s),p=ir(f.defId,d.range,d.forcedStartTzo,d.forcedEndTzo);return l&&f.publicId&&l[f.publicId]&&(p.instanceId=l[f.publicId]),{def:f,instance:p}}return null}function Ci(t,e,r=sr(e)){return Ne(t,r)}function sr(t){return Object.assign(Object.assign(Object.assign({},it),yo),t.pluginHooks.eventRefiners)}function Pt(t,e,r,n,i,s,l){let o={title:t.title||"",groupId:t.groupId||"",publicId:t.id||"",url:t.url||"",recurringDef:null,defId:(l&&t.id?l[t.id]:"")||$(),sourceId:r,allDay:n,hasEnd:i,interactive:t.interactive,ui:Oe(t,s),extendedProps:Object.assign(Object.assign({},t.extendedProps||{}),e)};for(let a of s.pluginHooks.eventDefMemberAdders)Object.assign(o,a(t));return Object.freeze(o.ui.classNames),Object.freeze(o.extendedProps),o}function So(t,e,r,n){let{allDay:i}=t,s,l=null,o=!1,a,u=null,c=t.start!=null?t.start:t.date;if(s=r.dateEnv.createMarkerMeta(c),s)l=s.marker;else if(!n)return null;return t.end!=null&&(a=r.dateEnv.createMarkerMeta(t.end)),i==null&&(e!=null?i=e:i=(!s||s.isTimeUnspecified)&&(!a||a.isTimeUnspecified)),i&&l&&(l=T(l)),a&&(u=a.marker,i&&(u=T(u)),l&&u<=l&&(u=null)),u?o=!0:n||(o=r.options.forceEventDuration||!1,u=r.dateEnv.add(l,i?r.options.defaultAllDayEventDuration:r.options.defaultTimedEventDuration)),{allDay:i,hasEnd:o,range:{start:l,end:u},forcedStartTzo:s?s.forcedTzo:null,forcedEndTzo:a?a.forcedTzo:null}}function Eo(t,e){let r=null;return t&&(r=t.defaultAllDay),r==null&&(r=e.options.defaultAllDay),r}function xe(t,e,r,n,i,s){let l=ee(),o=sr(r);for(let a of t){let u=Ei(a,e,r,n,o,i,s);u&&Bt(u,l)}return l}function Bt(t,e=ee()){return e.defs[t.def.defId]=t.def,t.instance&&(e.instances[t.instance.instanceId]=t.instance),e}function Co(t,e){let r=t.instances[e];if(r){let n=t.defs[r.defId],i=ht(t,s=>Ao(n,s));return i.defs[n.defId]=n,i.instances[r.instanceId]=r,i}return ee()}function Ao(t,e){return!!(t.groupId&&t.groupId===e.groupId)}function ee(){return{defs:{},instances:{}}}function ft(t,e){return{defs:Object.assign(Object.assign({},t.defs),e.defs),instances:Object.assign(Object.assign({},t.instances),e.instances)}}function ht(t,e){let r=K(t.defs,e),n=K(t.instances,i=>r[i.defId]);return{defs:r,instances:n}}function Ro(t,e){let{defs:r,instances:n}=t,i={},s={};for(let l in r)e.defs[l]||(i[l]=r[l]);for(let l in n)!e.instances[l]&&i[n[l].defId]&&(s[l]=n[l]);return{defs:i,instances:s}}function wo(t,e){return Array.isArray(t)?xe(t,null,e,!0):typeof t=="object"&&t?xe([t],null,e,!0):t!=null?String(t):null}function Lt(t){return Array.isArray(t)?t:typeof t=="string"?t.split(/\s+/):[]}const it={display:String,editable:Boolean,startEditable:Boolean,durationEditable:Boolean,constraint:g,overlap:g,allow:g,className:Lt,classNames:Lt,color:String,backgroundColor:String,borderColor:String,textColor:String},_o={display:null,startEditable:null,durationEditable:null,constraints:[],overlap:null,allows:[],backgroundColor:"",borderColor:"",textColor:"",classNames:[]};function Oe(t,e){let r=wo(t.constraint,e);return{display:t.display||null,startEditable:t.startEditable!=null?t.startEditable:t.editable,durationEditable:t.durationEditable!=null?t.durationEditable:t.editable,constraints:r!=null?[r]:[],overlap:t.overlap!=null?t.overlap:null,allows:t.allow!=null?[t.allow]:[],backgroundColor:t.backgroundColor||t.color||"",borderColor:t.borderColor||t.color||"",textColor:t.textColor||"",classNames:(t.className||[]).concat(t.classNames||[])}}function lr(t){return t.reduce(Do,_o)}function Do(t,e){return{display:e.display!=null?e.display:t.display,startEditable:e.startEditable!=null?e.startEditable:t.startEditable,durationEditable:e.durationEditable!=null?e.durationEditable:t.durationEditable,constraints:t.constraints.concat(e.constraints),overlap:typeof e.overlap=="boolean"?e.overlap:t.overlap,allows:t.allows.concat(e.allows),backgroundColor:e.backgroundColor||t.backgroundColor,borderColor:e.borderColor||t.borderColor,textColor:e.textColor||t.textColor,classNames:t.classNames.concat(e.classNames)}}const Io={id:String,defaultAllDay:Boolean,url:String,format:String,events:g,eventDataTransform:g,success:g,failure:g};function Ai(t,e,r=Ri(e)){let n;if(typeof t=="string"?n={url:t}:typeof t=="function"||Array.isArray(t)?n={events:t}:typeof t=="object"&&t&&(n=t),n){let{refined:i,extra:s}=Ne(n,r),l=To(i,e);if(l)return{_raw:t,isFetching:!1,latestFetchId:"",fetchRange:null,defaultAllDay:i.defaultAllDay,eventDataTransform:i.eventDataTransform,success:i.success,failure:i.failure,publicId:i.id||"",sourceId:$(),sourceDefId:l.sourceDefId,meta:l.meta,ui:Oe(i,e),extendedProps:s}}return null}function Ri(t){return Object.assign(Object.assign(Object.assign({},it),Io),t.pluginHooks.eventSourceRefiners)}function To(t,e){let r=e.pluginHooks.eventSourceDefs;for(let n=r.length-1;n>=0;n-=1){let s=r[n].parseMeta(t);if(s)return{sourceDefId:n,meta:s}}return null}function ko(t,e,r,n,i){switch(e.type){case"RECEIVE_EVENTS":return Mo(t,r[e.sourceId],e.fetchId,e.fetchRange,e.rawEvents,i);case"RESET_RAW_EVENTS":return xo(t,r[e.sourceId],e.rawEvents,n.activeRange,i);case"ADD_EVENTS":return Oo(t,e.eventStore,n?n.activeRange:null,i);case"RESET_EVENTS":return e.eventStore;case"MERGE_EVENTS":return ft(t,e.eventStore);case"PREV":case"NEXT":case"CHANGE_DATE":case"CHANGE_VIEW_TYPE":return n?fe(t,n.activeRange,i):t;case"REMOVE_EVENTS":return Ro(t,e.eventStore);case"REMOVE_EVENT_SOURCE":return _i(t,e.sourceId);case"REMOVE_ALL_EVENT_SOURCES":return ht(t,s=>!s.sourceId);case"REMOVE_ALL_EVENTS":return ee();default:return t}}function Mo(t,e,r,n,i,s){if(e&&r===e.latestFetchId){let l=xe(wi(i,e,s),e,s);return n&&(l=fe(l,n,s)),ft(_i(t,e.sourceId),l)}return t}function xo(t,e,r,n,i){const{defIdMap:s,instanceIdMap:l}=Ho(t);let o=xe(wi(r,e,i),e,i,!1,s,l);return fe(o,n,i)}function wi(t,e,r){let n=r.options.eventDataTransform,i=e?e.eventDataTransform:null;return i&&(t=ln(t,i)),n&&(t=ln(t,n)),t}function ln(t,e){let r;if(!e)r=t;else{r=[];for(let n of t){let i=e(n);i?r.push(i):i==null&&r.push(n)}}return r}function Oo(t,e,r,n){return r&&(e=fe(e,r,n)),ft(t,e)}function on(t,e,r){let{defs:n}=t,i=B(t.instances,s=>n[s.defId].allDay?s:Object.assign(Object.assign({},s),{range:{start:r.createMarker(e.toDate(s.range.start,s.forcedStartTzo)),end:r.createMarker(e.toDate(s.range.end,s.forcedEndTzo))},forcedStartTzo:r.canComputeOffset?null:s.forcedStartTzo,forcedEndTzo:r.canComputeOffset?null:s.forcedEndTzo}));return{defs:n,instances:i}}function _i(t,e){return ht(t,r=>r.sourceId!==e)}function No(t,e){return{defs:t.defs,instances:K(t.instances,r=>!e[r.instanceId])}}function Ho(t){const{defs:e,instances:r}=t,n={},i={};for(let s in e){const l=e[s],{publicId:o}=l;o&&(n[o]=s)}for(let s in r){const l=r[s],o=e[l.defId],{publicId:a}=o;a&&(i[a]=s)}return{defIdMap:n,instanceIdMap:i}}class Di{constructor(){this.handlers={},this.thisContext=null}setThisContext(e){this.thisContext=e}setOptions(e){this.options=e}on(e,r){Po(this.handlers,e,r)}off(e,r){Bo(this.handlers,e,r)}trigger(e,...r){let n=this.handlers[e]||[],i=this.options&&this.options[e],s=[].concat(i||[],n);for(let l of s)l.apply(this.thisContext,r)}hasHandlers(e){return!!(this.handlers[e]&&this.handlers[e].length||this.options&&this.options[e])}}function Po(t,e,r){(t[e]||(t[e]=[])).push(r)}function Bo(t,e,r){r?t[e]&&(t[e]=t[e].filter(n=>n!==r)):delete t[e]}const Lo={startTime:"09:00",endTime:"17:00",daysOfWeek:[1,2,3,4,5],display:"inverse-background",classNames:"fc-non-business",groupId:"_businessHours"};function Ii(t,e){return xe(Uo(t),null,e)}function Uo(t){let e;return t===!0?e=[{}]:Array.isArray(t)?e=t.filter(r=>r.daysOfWeek):typeof t=="object"&&t?e=[t]:e=[],e=e.map(r=>Object.assign(Object.assign({},Lo),r)),e}function Wo(t,e,r){r.emitter.trigger("select",Object.assign(Object.assign({},or(t,r)),{jsEvent:null,view:r.viewApi||r.calendarApi.view}))}function zo(t,e){e.emitter.trigger("unselect",{jsEvent:t?t.origEvent:null,view:e.viewApi||e.calendarApi.view})}function or(t,e){let r={};for(let n of e.pluginHooks.dateSpanTransforms)Object.assign(r,n(t,e));return Object.assign(r,na(t,e.dateEnv)),r}function an(t,e,r){let{dateEnv:n,options:i}=r,s=e;return t?(s=T(s),s=n.add(s,i.defaultAllDayEventDuration)):s=n.add(s,i.defaultTimedEventDuration),s}function Fo(t,e,r,n){let i=st(t.defs,e),s=ee();for(let l in t.defs){let o=t.defs[l];s.defs[l]=jo(o,i[l],r,n)}for(let l in t.instances){let o=t.instances[l],a=s.defs[o.defId];s.instances[l]=Vo(o,a,i[o.defId],r,n)}return s}function jo(t,e,r,n){let i=r.standardProps||{};i.hasEnd==null&&e.durationEditable&&(r.startDelta||r.endDelta)&&(i.hasEnd=!0);let s=Object.assign(Object.assign(Object.assign({},t),i),{ui:Object.assign(Object.assign({},t.ui),i.ui)});r.extendedProps&&(s.extendedProps=Object.assign(Object.assign({},s.extendedProps),r.extendedProps));for(let l of n.pluginHooks.eventDefMutationAppliers)l(s,r,n);return!s.hasEnd&&n.options.forceEventDuration&&(s.hasEnd=!0),s}function Vo(t,e,r,n,i){let{dateEnv:s}=i,l=n.standardProps&&n.standardProps.allDay===!0,o=n.standardProps&&n.standardProps.hasEnd===!1,a=Object.assign({},t);return l&&(a.range=yi(a.range)),n.datesDelta&&r.startEditable&&(a.range={start:s.add(a.range.start,n.datesDelta),end:s.add(a.range.end,n.datesDelta)}),n.startDelta&&r.durationEditable&&(a.range={start:s.add(a.range.start,n.startDelta),end:a.range.end}),n.endDelta&&r.durationEditable&&(a.range={start:a.range.start,end:s.add(a.range.end,n.endDelta)}),o&&(a.range={start:a.range.start,end:an(e.allDay,a.range.start,i)}),e.allDay&&(a.range={start:T(a.range.start),end:T(a.range.end)}),a.range.end<a.range.start&&(a.range.end=an(e.allDay,a.range.start,i)),a}class me{constructor(e,r){this.context=e,this.internalEventSource=r}remove(){this.context.dispatch({type:"REMOVE_EVENT_SOURCE",sourceId:this.internalEventSource.sourceId})}refetch(){this.context.dispatch({type:"FETCH_EVENT_SOURCES",sourceIds:[this.internalEventSource.sourceId],isRefetch:!0})}get id(){return this.internalEventSource.publicId}get url(){return this.internalEventSource.meta.url}get format(){return this.internalEventSource.meta.format}}class O{constructor(e,r,n){this._context=e,this._def=r,this._instance=n||null}setProp(e,r){if(e in Si)console.warn("Could not set date-related prop 'name'. Use one of the date-related methods instead.");else if(e==="id")r=Ye[e](r),this.mutate({standardProps:{publicId:r}});else if(e in Ye)r=Ye[e](r),this.mutate({standardProps:{[e]:r}});else if(e in it){let n=it[e](r);e==="color"?n={backgroundColor:r,borderColor:r}:e==="editable"?n={startEditable:r,durationEditable:r}:n={[e]:r},this.mutate({standardProps:{ui:n}})}else console.warn(`Could not set prop '${e}'. Use setExtendedProp instead.`)}setExtendedProp(e,r){this.mutate({extendedProps:{[e]:r}})}setStart(e,r={}){let{dateEnv:n}=this._context,i=n.createMarker(e);if(i&&this._instance){let s=this._instance.range,l=We(s.start,i,n,r.granularity);r.maintainDuration?this.mutate({datesDelta:l}):this.mutate({startDelta:l})}}setEnd(e,r={}){let{dateEnv:n}=this._context,i;if(!(e!=null&&(i=n.createMarker(e),!i))&&this._instance)if(i){let s=We(this._instance.range.end,i,n,r.granularity);this.mutate({endDelta:s})}else this.mutate({standardProps:{hasEnd:!1}})}setDates(e,r,n={}){let{dateEnv:i}=this._context,s={allDay:n.allDay},l=i.createMarker(e),o;if(l&&!(r!=null&&(o=i.createMarker(r),!o))&&this._instance){let a=this._instance.range;n.allDay===!0&&(a=yi(a));let u=We(a.start,l,i,n.granularity);if(o){let c=We(a.end,o,i,n.granularity);hl(u,c)?this.mutate({datesDelta:u,standardProps:s}):this.mutate({startDelta:u,endDelta:c,standardProps:s})}else s.hasEnd=!1,this.mutate({datesDelta:u,standardProps:s})}}moveStart(e){let r=D(e);r&&this.mutate({startDelta:r})}moveEnd(e){let r=D(e);r&&this.mutate({endDelta:r})}moveDates(e){let r=D(e);r&&this.mutate({datesDelta:r})}setAllDay(e,r={}){let n={allDay:e},{maintainDuration:i}=r;i==null&&(i=this._context.options.allDayMaintainDuration),this._def.allDay!==e&&(n.hasEnd=i),this.mutate({standardProps:n})}formatRange(e){let{dateEnv:r}=this._context,n=this._instance,i=P(e);return this._def.hasEnd?r.formatRange(n.range.start,n.range.end,i,{forcedStartTzo:n.forcedStartTzo,forcedEndTzo:n.forcedEndTzo}):r.format(n.range.start,i,{forcedTzo:n.forcedStartTzo})}mutate(e){let r=this._instance;if(r){let n=this._def,i=this._context,{eventStore:s}=i.getCurrentData(),l=Co(s,r.instanceId);l=Fo(l,{"":{display:"",startEditable:!0,durationEditable:!0,constraints:[],overlap:null,allows:[],backgroundColor:"",borderColor:"",textColor:"",classNames:[]}},e,i);let a=new O(i,n,r);this._def=l.defs[n.defId],this._instance=l.instances[r.instanceId],i.dispatch({type:"MERGE_EVENTS",eventStore:l}),i.emitter.trigger("eventChange",{oldEvent:a,event:this,relatedEvents:ar(l,i,r),revert(){i.dispatch({type:"RESET_EVENTS",eventStore:s})}})}}remove(){let e=this._context,r=Ti(this);e.dispatch({type:"REMOVE_EVENTS",eventStore:r}),e.emitter.trigger("eventRemove",{event:this,relatedEvents:[],revert(){e.dispatch({type:"MERGE_EVENTS",eventStore:r})}})}get source(){let{sourceId:e}=this._def;return e?new me(this._context,this._context.getCurrentData().eventSources[e]):null}get start(){return this._instance?this._context.dateEnv.toDate(this._instance.range.start):null}get end(){return this._instance&&this._def.hasEnd?this._context.dateEnv.toDate(this._instance.range.end):null}get startStr(){let e=this._instance;return e?this._context.dateEnv.formatIso(e.range.start,{omitTime:this._def.allDay,forcedTzo:e.forcedStartTzo}):""}get endStr(){let e=this._instance;return e&&this._def.hasEnd?this._context.dateEnv.formatIso(e.range.end,{omitTime:this._def.allDay,forcedTzo:e.forcedEndTzo}):""}get id(){return this._def.publicId}get groupId(){return this._def.groupId}get allDay(){return this._def.allDay}get title(){return this._def.title}get url(){return this._def.url}get display(){return this._def.ui.display||"auto"}get startEditable(){return this._def.ui.startEditable}get durationEditable(){return this._def.ui.durationEditable}get constraint(){return this._def.ui.constraints[0]||null}get overlap(){return this._def.ui.overlap}get allow(){return this._def.ui.allows[0]||null}get backgroundColor(){return this._def.ui.backgroundColor}get borderColor(){return this._def.ui.borderColor}get textColor(){return this._def.ui.textColor}get classNames(){return this._def.ui.classNames}get extendedProps(){return this._def.extendedProps}toPlainObject(e={}){let r=this._def,{ui:n}=r,{startStr:i,endStr:s}=this,l={allDay:r.allDay};return r.title&&(l.title=r.title),i&&(l.start=i),s&&(l.end=s),r.publicId&&(l.id=r.publicId),r.groupId&&(l.groupId=r.groupId),r.url&&(l.url=r.url),n.display&&n.display!=="auto"&&(l.display=n.display),e.collapseColor&&n.backgroundColor&&n.backgroundColor===n.borderColor?l.color=n.backgroundColor:(n.backgroundColor&&(l.backgroundColor=n.backgroundColor),n.borderColor&&(l.borderColor=n.borderColor)),n.textColor&&(l.textColor=n.textColor),n.classNames.length&&(l.classNames=n.classNames),Object.keys(r.extendedProps).length&&(e.collapseExtendedProps?Object.assign(l,r.extendedProps):l.extendedProps=r.extendedProps),l}toJSON(){return this.toPlainObject()}}function Ti(t){let e=t._def,r=t._instance;return{defs:{[e.defId]:e},instances:r?{[r.instanceId]:r}:{}}}function ar(t,e,r){let{defs:n,instances:i}=t,s=[],l=r?r.instanceId:"";for(let o in i){let a=i[o],u=n[a.defId];a.instanceId!==l&&s.push(new O(e,u,a))}return s}function cn(t,e,r,n){let i={},s={},l={},o=[],a=[],u=st(t.defs,e);for(let c in t.defs){let d=t.defs[c];u[d.defId].display==="inverse-background"&&(d.groupId?(i[d.groupId]=[],l[d.groupId]||(l[d.groupId]=d)):s[c]=[])}for(let c in t.instances){let d=t.instances[c],f=t.defs[d.defId],p=u[f.defId],m=d.range,v=!f.allDay&&n?nr(m,n):m,b=Ee(v,r);b&&(p.display==="inverse-background"?f.groupId?i[f.groupId].push(b):s[d.defId].push(b):p.display!=="none"&&(p.display==="background"?o:a).push({def:f,ui:p,instance:d,range:b,isStart:v.start&&v.start.valueOf()===b.start.valueOf(),isEnd:v.end&&v.end.valueOf()===b.end.valueOf()}))}for(let c in i){let d=i[c],f=sn(d,r);for(let p of f){let m=l[c],v=u[m.defId];o.push({def:m,ui:v,instance:null,range:p,isStart:!1,isEnd:!1})}}for(let c in s){let d=s[c],f=sn(d,r);for(let p of f)o.push({def:t.defs[c],ui:u[c],instance:null,range:p,isStart:!1,isEnd:!1})}return{bg:o,fg:a}}function un(t,e){t.fcSeg=e}function Ut(t){return t.fcSeg||t.parentNode.fcSeg||null}function st(t,e){return B(t,r=>ki(r,e))}function ki(t,e){let r=[];return e[""]&&r.push(e[""]),e[t.defId]&&r.push(e[t.defId]),r.push(t.ui),lr(r)}function Go(t,e){let r=t.map(qo);return r.sort((n,i)=>oi(n,i,e)),r.map(n=>n._seg)}function qo(t){let{eventRange:e}=t,r=e.def,n=e.instance?e.instance.range:e.range,i=n.start?n.start.valueOf():0,s=n.end?n.end.valueOf():0;return Object.assign(Object.assign(Object.assign({},r.extendedProps),r),{id:r.publicId,start:i,end:s,duration:s-i,allDay:Number(r.allDay),_seg:t})}function Qo(t,e){let{pluginHooks:r}=e,n=r.isDraggableTransformers,{def:i,ui:s}=t.eventRange,l=s.startEditable;for(let o of n)l=o(l,i,s,e);return l}function Yo(t,e){return t.isStart&&t.eventRange.ui.durationEditable&&e.options.eventResizableFromStart}function Zo(t,e){return t.isEnd&&t.eventRange.ui.durationEditable}function $o(t,e,r,n,i,s,l){let{dateEnv:o,options:a}=r,{displayEventTime:u,displayEventEnd:c}=a,d=t.eventRange.def,f=t.eventRange.instance;u==null&&(u=n!==!1),c==null&&(c=i!==!1);let p=f.range.start,m=f.range.end,v=t.start||t.eventRange.range.start,b=t.end||t.eventRange.range.end,y=T(p).valueOf()===T(v).valueOf(),S=T(de(m,-1)).valueOf()===T(de(b,-1)).valueOf();return u&&!d.allDay&&(y||S)?(v=y?p:v,b=S?m:b,c&&d.hasEnd?o.formatRange(v,b,e,{forcedStartTzo:f.forcedStartTzo,forcedEndTzo:f.forcedEndTzo}):o.format(v,e,{forcedTzo:f.forcedStartTzo})):""}function cr(t,e,r){let n=t.eventRange.range;return{isPast:n.end<=(r||e.start),isFuture:n.start>=(r||e.end),isToday:e&&J(e,n.start)}}function Xo(t){let e=["fc-event"];return t.isMirror&&e.push("fc-event-mirror"),t.isDraggable&&e.push("fc-event-draggable"),(t.isStartResizable||t.isEndResizable)&&e.push("fc-event-resizable"),t.isDragging&&e.push("fc-event-dragging"),t.isResizing&&e.push("fc-event-resizing"),t.isSelected&&e.push("fc-event-selected"),t.isStart&&e.push("fc-event-start"),t.isEnd&&e.push("fc-event-end"),t.isPast&&e.push("fc-event-past"),t.isToday&&e.push("fc-event-today"),t.isFuture&&e.push("fc-event-future"),e}function Jo(t){return t.instance?t.instance.instanceId:`${t.def.defId}:${t.range.start.toISOString()}`}function Ko(t,e){let{def:r,instance:n}=t.eventRange,{url:i}=r;if(i)return{href:i};let{emitter:s,options:l}=e,{eventInteractive:o}=l;return o==null&&(o=r.interactive,o==null&&(o=!!s.hasHandlers("eventClick"))),o?li(a=>{s.trigger("eventClick",{el:a.target,event:new O(e,r,n),jsEvent:a,view:e.viewApi})}):{}}const ea={start:g,end:g,allDay:Boolean};function ta(t,e,r){let n=ra(t,e),{range:i}=n;if(!i.start)return null;if(!i.end){if(r==null)return null;i.end=e.add(i.start,r)}return n}function ra(t,e){let{refined:r,extra:n}=Ne(t,ea),i=r.start?e.createMarkerMeta(r.start):null,s=r.end?e.createMarkerMeta(r.end):null,{allDay:l}=r;return l==null&&(l=i&&i.isTimeUnspecified&&(!s||s.isTimeUnspecified)),Object.assign({range:{start:i?i.marker:null,end:s?s.marker:null},allDay:l},n)}function na(t,e){return Object.assign(Object.assign({},xi(t.range,e,t.allDay)),{allDay:t.allDay})}function Mi(t,e,r){return Object.assign(Object.assign({},xi(t,e,r)),{timeZone:e.timeZone})}function xi(t,e,r){return{start:e.toDate(t.start),end:e.toDate(t.end),startStr:e.formatIso(t.start,{omitTime:r}),endStr:e.formatIso(t.end,{omitTime:r})}}function ia(t,e,r){let n=Ci({editable:!1},r),i=Pt(n.refined,n.extra,"",t.allDay,!0,r);return{def:i,ui:ki(i,e),instance:ir(i.defId,t.range),range:t.range,isStart:!0,isEnd:!0}}function Oi(t,e,r){let n=!1,i=function(o){n||(n=!0,e(o))},s=function(o){n||(n=!0,r(o))},l=t(i,s);l&&typeof l.then=="function"&&l.then(i,s)}class dn extends Error{constructor(e,r){super(e),this.response=r}}function Ni(t,e,r){t=t.toUpperCase();const n={method:t};return t==="GET"?e+=(e.indexOf("?")===-1?"?":"&")+new URLSearchParams(r):(n.body=new URLSearchParams(r),n.headers={"Content-Type":"application/x-www-form-urlencoded"}),fetch(e,n).then(i=>{if(i.ok)return i.json().then(s=>[s,i],()=>{throw new dn("Failure parsing JSON",i)});throw new dn("Request failed",i)})}let At;function ur(){return At==null&&(At=sa()),At}function sa(){if(typeof document>"u")return!0;let t=document.createElement("div");t.style.position="absolute",t.style.top="0px",t.style.left="0px",t.innerHTML="<table><tr><td><div></div></td></tr></table>",t.querySelector("table").style.height="100px",t.querySelector("div").style.height="100%",document.body.appendChild(t);let r=t.querySelector("div").offsetHeight>0;return document.body.removeChild(t),r}class la extends R{constructor(){super(...arguments),this.state={forPrint:!1},this.handleBeforePrint=()=>{nt(()=>{this.setState({forPrint:!0})})},this.handleAfterPrint=()=>{nt(()=>{this.setState({forPrint:!1})})}}render(){let{props:e}=this,{options:r}=e,{forPrint:n}=this.state,i=n||r.height==="auto"||r.contentHeight==="auto",s=!i&&r.height!=null?r.height:"",l=["fc",n?"fc-media-print":"fc-media-screen",`fc-direction-${r.direction}`,e.theme.getClass("root")];return ur()||l.push("fc-liquid-hack"),e.children(l,s,i,n)}componentDidMount(){let{emitter:e}=this.props;e.on("_beforeprint",this.handleBeforePrint),e.on("_afterprint",this.handleAfterPrint)}componentWillUnmount(){let{emitter:e}=this.props;e.off("_beforeprint",this.handleBeforePrint),e.off("_afterprint",this.handleAfterPrint)}}class Hi{constructor(e){this.component=e.component,this.isHitComboAllowed=e.isHitComboAllowed||null}destroy(){}}function oa(t,e){return{component:t,el:e.el,useEventCenter:e.useEventCenter!=null?e.useEventCenter:!0,isHitComboAllowed:e.isHitComboAllowed||null}}const fn={};class Pe extends W{constructor(e,r){super(e,r),this.handleRefresh=()=>{let n=this.computeTiming();n.state.nowDate.valueOf()!==this.state.nowDate.valueOf()&&this.setState(n.state),this.clearTimeout(),this.setTimeout(n.waitMs)},this.handleVisibilityChange=()=>{document.hidden||this.handleRefresh()},this.state=this.computeTiming().state}render(){let{props:e,state:r}=this;return e.children(r.nowDate,r.todayRange)}componentDidMount(){this.setTimeout(),this.context.nowManager.addResetListener(this.handleRefresh),document.addEventListener("visibilitychange",this.handleVisibilityChange)}componentDidUpdate(e){e.unit!==this.props.unit&&(this.clearTimeout(),this.setTimeout())}componentWillUnmount(){this.clearTimeout(),this.context.nowManager.removeResetListener(this.handleRefresh),document.removeEventListener("visibilitychange",this.handleVisibilityChange)}computeTiming(){let{props:e,context:r}=this,n=r.nowManager.getDateMarker(),i=r.dateEnv.startOf(n,e.unit),l=r.dateEnv.add(i,D(1,e.unit)).valueOf()-n.valueOf();return l=Math.min(1e3*60*60*24,l),{state:{nowDate:i,todayRange:aa(i)},waitMs:l}}setTimeout(e=this.computeTiming().waitMs){this.timeoutId=setTimeout(()=>{const r=this.computeTiming();this.setState(r.state,()=>{this.setTimeout(r.waitMs)})},e)}clearTimeout(){this.timeoutId&&clearTimeout(this.timeoutId)}}Pe.contextType=pe;function aa(t){let e=T(t),r=j(e,1);return{start:e,end:r}}class Ae{getCurrentData(){return this.currentDataManager.getCurrentData()}dispatch(e){this.currentDataManager.dispatch(e)}get view(){return this.getCurrentData().viewApi}batchRendering(e){e()}updateSize(){this.trigger("_resize",!0)}setOption(e,r){this.dispatch({type:"SET_OPTION",optionName:e,rawOptionValue:r})}getOption(e){return this.currentDataManager.currentCalendarOptionsInput[e]}getAvailableLocaleCodes(){return Object.keys(this.getCurrentData().availableRawLocales)}on(e,r){let{currentDataManager:n}=this;n.currentCalendarOptionsRefiners[e]?n.emitter.on(e,r):console.warn(`Unknown listener name '${e}'`)}off(e,r){this.currentDataManager.emitter.off(e,r)}trigger(e,...r){this.currentDataManager.emitter.trigger(e,...r)}changeView(e,r){this.batchRendering(()=>{if(this.unselect(),r)if(r.start&&r.end)this.dispatch({type:"CHANGE_VIEW_TYPE",viewType:e}),this.dispatch({type:"SET_OPTION",optionName:"visibleRange",rawOptionValue:r});else{let{dateEnv:n}=this.getCurrentData();this.dispatch({type:"CHANGE_VIEW_TYPE",viewType:e,dateMarker:n.createMarker(r)})}else this.dispatch({type:"CHANGE_VIEW_TYPE",viewType:e})})}zoomTo(e,r){let n=this.getCurrentData(),i;r=r||"day",i=n.viewSpecs[r]||this.getUnitViewSpec(r),this.unselect(),i?this.dispatch({type:"CHANGE_VIEW_TYPE",viewType:i.type,dateMarker:e}):this.dispatch({type:"CHANGE_DATE",dateMarker:e})}getUnitViewSpec(e){let{viewSpecs:r,toolbarConfig:n}=this.getCurrentData(),i=[].concat(n.header?n.header.viewsWithButtons:[],n.footer?n.footer.viewsWithButtons:[]),s,l;for(let o in r)i.push(o);for(s=0;s<i.length;s+=1)if(l=r[i[s]],l&&l.singleUnit===e)return l;return null}prev(){this.unselect(),this.dispatch({type:"PREV"})}next(){this.unselect(),this.dispatch({type:"NEXT"})}prevYear(){let e=this.getCurrentData();this.unselect(),this.dispatch({type:"CHANGE_DATE",dateMarker:e.dateEnv.addYears(e.currentDate,-1)})}nextYear(){let e=this.getCurrentData();this.unselect(),this.dispatch({type:"CHANGE_DATE",dateMarker:e.dateEnv.addYears(e.currentDate,1)})}today(){let e=this.getCurrentData();this.unselect(),this.dispatch({type:"CHANGE_DATE",dateMarker:e.nowManager.getDateMarker()})}gotoDate(e){let r=this.getCurrentData();this.unselect(),this.dispatch({type:"CHANGE_DATE",dateMarker:r.dateEnv.createMarker(e)})}incrementDate(e){let r=this.getCurrentData(),n=D(e);n&&(this.unselect(),this.dispatch({type:"CHANGE_DATE",dateMarker:r.dateEnv.add(r.currentDate,n)}))}getDate(){let e=this.getCurrentData();return e.dateEnv.toDate(e.currentDate)}formatDate(e,r){let{dateEnv:n}=this.getCurrentData();return n.format(n.createMarker(e),P(r))}formatRange(e,r,n){let{dateEnv:i}=this.getCurrentData();return i.formatRange(i.createMarker(e),i.createMarker(r),P(n),n)}formatIso(e,r){let{dateEnv:n}=this.getCurrentData();return n.formatIso(n.createMarker(e),{omitTime:r})}select(e,r){let n;r==null?e.start!=null?n=e:n={start:e,end:null}:n={start:e,end:r};let i=this.getCurrentData(),s=ta(n,i.dateEnv,D({days:1}));s&&(this.dispatch({type:"SELECT_DATES",selection:s}),Wo(s,null,i))}unselect(e){let r=this.getCurrentData();r.dateSelection&&(this.dispatch({type:"UNSELECT_DATES"}),zo(e,r))}addEvent(e,r){if(e instanceof O){let l=e._def,o=e._instance;return this.getCurrentData().eventStore.defs[l.defId]||(this.dispatch({type:"ADD_EVENTS",eventStore:Bt({def:l,instance:o})}),this.triggerEventAdd(e)),e}let n=this.getCurrentData(),i;if(r instanceof me)i=r.internalEventSource;else if(typeof r=="boolean")r&&([i]=tr(n.eventSources));else if(r!=null){let l=this.getEventSourceById(r);if(!l)return console.warn(`Could not find an event source with ID "${r}"`),null;i=l.internalEventSource}let s=Ei(e,i,n,!1);if(s){let l=new O(n,s.def,s.def.recurringDef?null:s.instance);return this.dispatch({type:"ADD_EVENTS",eventStore:Bt(s)}),this.triggerEventAdd(l),l}return null}triggerEventAdd(e){let{emitter:r}=this.getCurrentData();r.trigger("eventAdd",{event:e,relatedEvents:[],revert:()=>{this.dispatch({type:"REMOVE_EVENTS",eventStore:Ti(e)})}})}getEventById(e){let r=this.getCurrentData(),{defs:n,instances:i}=r.eventStore;e=String(e);for(let s in n){let l=n[s];if(l.publicId===e){if(l.recurringDef)return new O(r,l,null);for(let o in i){let a=i[o];if(a.defId===l.defId)return new O(r,l,a)}}}return null}getEvents(){let e=this.getCurrentData();return ar(e.eventStore,e)}removeAllEvents(){this.dispatch({type:"REMOVE_ALL_EVENTS"})}getEventSources(){let e=this.getCurrentData(),r=e.eventSources,n=[];for(let i in r)n.push(new me(e,r[i]));return n}getEventSourceById(e){let r=this.getCurrentData(),n=r.eventSources;e=String(e);for(let i in n)if(n[i].publicId===e)return new me(r,n[i]);return null}addEventSource(e){let r=this.getCurrentData();if(e instanceof me)return r.eventSources[e.internalEventSource.sourceId]||this.dispatch({type:"ADD_EVENT_SOURCES",sources:[e.internalEventSource]}),e;let n=Ai(e,r);return n?(this.dispatch({type:"ADD_EVENT_SOURCES",sources:[n]}),new me(r,n)):null}removeAllEventSources(){this.dispatch({type:"REMOVE_ALL_EVENT_SOURCES"})}refetchEvents(){this.dispatch({type:"FETCH_EVENT_SOURCES",isRefetch:!0})}scrollToTime(e){let r=D(e);r&&this.trigger("_scrollRequest",{time:r})}}function ca(t,e){let r={left:Math.max(t.left,e.left),right:Math.min(t.right,e.right),top:Math.max(t.top,e.top),bottom:Math.min(t.bottom,e.bottom)};return r.left<r.right&&r.top<r.bottom?r:!1}function hn(t,e,r){return{left:t.left+e,right:t.right+e,top:t.top+r,bottom:t.bottom+r}}const Rt=ee();class ua{constructor(){this.getKeysForEventDefs=A(this._getKeysForEventDefs),this.splitDateSelection=A(this._splitDateSpan),this.splitEventStore=A(this._splitEventStore),this.splitIndividualUi=A(this._splitIndividualUi),this.splitEventDrag=A(this._splitInteraction),this.splitEventResize=A(this._splitInteraction),this.eventUiBuilders={}}splitProps(e){let r=this.getKeyInfo(e),n=this.getKeysForEventDefs(e.eventStore),i=this.splitDateSelection(e.dateSelection),s=this.splitIndividualUi(e.eventUiBases,n),l=this.splitEventStore(e.eventStore,n),o=this.splitEventDrag(e.eventDrag),a=this.splitEventResize(e.eventResize),u={};this.eventUiBuilders=B(r,(c,d)=>this.eventUiBuilders[d]||A(da));for(let c in r){let d=r[c],f=l[c]||Rt,p=this.eventUiBuilders[c];u[c]={businessHours:d.businessHours||e.businessHours,dateSelection:i[c]||null,eventStore:f,eventUiBases:p(e.eventUiBases[""],d.ui,s[c]),eventSelection:f.instances[e.eventSelection]?e.eventSelection:"",eventDrag:o[c]||null,eventResize:a[c]||null}}return u}_splitDateSpan(e){let r={};if(e){let n=this.getKeysForDateSpan(e);for(let i of n)r[i]=e}return r}_getKeysForEventDefs(e){return B(e.defs,r=>this.getKeysForEventDef(r))}_splitEventStore(e,r){let{defs:n,instances:i}=e,s={};for(let l in n)for(let o of r[l])s[o]||(s[o]=ee()),s[o].defs[l]=n[l];for(let l in i){let o=i[l];for(let a of r[o.defId])s[a]&&(s[a].instances[l]=o)}return s}_splitIndividualUi(e,r){let n={};for(let i in e)if(i)for(let s of r[i])n[s]||(n[s]={}),n[s][i]=e[i];return n}_splitInteraction(e){let r={};if(e){let n=this._splitEventStore(e.affectedEvents,this._getKeysForEventDefs(e.affectedEvents)),i=this._getKeysForEventDefs(e.mutatedEvents),s=this._splitEventStore(e.mutatedEvents,i),l=o=>{r[o]||(r[o]={affectedEvents:n[o]||Rt,mutatedEvents:s[o]||Rt,isEvent:e.isEvent})};for(let o in n)l(o);for(let o in s)l(o)}return r}}function da(t,e,r){let n=[];t&&n.push(t),e&&n.push(e);let i={"":lr(n)};return r&&Object.assign(i,r),i}function dr(t,e,r,n){return{dow:t.getUTCDay(),isDisabled:!!(n&&(!n.activeRange||!J(n.activeRange,t))),isOther:!!(n&&!J(n.currentRange,t)),isToday:!!(e&&J(e,t)),isPast:!!(r?t<r:e&&t<e.start),isFuture:!!(r?t>r:e&&t>=e.end)}}function fr(t,e){let r=["fc-day",`fc-day-${ui[t.dow]}`];return t.isDisabled?r.push("fc-day-disabled"):(t.isToday&&(r.push("fc-day-today"),r.push(e.getClass("today"))),t.isPast&&r.push("fc-day-past"),t.isFuture&&r.push("fc-day-future"),t.isOther&&r.push("fc-day-other")),r}function Pi(t,e){let r=["fc-slot",`fc-slot-${ui[t.dow]}`];return t.isDisabled?r.push("fc-slot-disabled"):(t.isToday&&(r.push("fc-slot-today"),r.push(e.getClass("today"))),t.isPast&&r.push("fc-slot-past"),t.isFuture&&r.push("fc-slot-future")),r}const fa=P({year:"numeric",month:"long",day:"numeric"}),ha=P({week:"long"});function pa(t,e,r="day",n=!0){const{dateEnv:i,options:s,calendarApi:l}=t;let o=i.format(e,r==="week"?ha:fa);if(s.navLinks){let a=i.toDate(e);const u=c=>{let d=r==="day"?s.navLinkDayClick:r==="week"?s.navLinkWeekClick:null;typeof d=="function"?d.call(l,i.toDate(e),c):(typeof d=="string"&&(r=d),l.zoomTo(e,r))};return Object.assign({title:we(s.navLinkHint,[o,a],o),"data-navlink":""},n?si(u):{onClick:u})}return{"aria-label":o}}let wt=null;function lt(){return wt===null&&(wt=ga()),wt}function ga(){let t=document.createElement("div");Yt(t,{position:"absolute",top:-1e3,left:0,border:0,padding:0,overflow:"scroll",direction:"rtl"}),t.innerHTML="<div></div>",document.body.appendChild(t);let r=t.firstChild.getBoundingClientRect().left>t.getBoundingClientRect().left;return Qt(t),r}let _t;function Bi(){return _t||(_t=ma()),_t}function ma(){let t=document.createElement("div");t.style.overflow="scroll",t.style.position="absolute",t.style.top="-9999px",t.style.left="-9999px",document.body.appendChild(t);let e=Li(t);return document.body.removeChild(t),e}function Li(t){return{x:t.offsetHeight-t.clientHeight,y:t.offsetWidth-t.clientWidth}}function Ui(t,e=!1){let r=window.getComputedStyle(t),n=parseInt(r.borderLeftWidth,10)||0,i=parseInt(r.borderRightWidth,10)||0,s=parseInt(r.borderTopWidth,10)||0,l=parseInt(r.borderBottomWidth,10)||0,o=Li(t),a=o.y-n-i,u=o.x-s-l,c={borderLeft:n,borderRight:i,borderTop:s,borderBottom:l,scrollbarBottom:u,scrollbarLeft:0,scrollbarRight:0};return lt()&&r.direction==="rtl"?c.scrollbarLeft=a:c.scrollbarRight=a,e&&(c.paddingLeft=parseInt(r.paddingLeft,10)||0,c.paddingRight=parseInt(r.paddingRight,10)||0,c.paddingTop=parseInt(r.paddingTop,10)||0,c.paddingBottom=parseInt(r.paddingBottom,10)||0),c}function va(t,e=!1,r){let n=t.getBoundingClientRect(),i=Ui(t,e),s={left:n.left+i.borderLeft+i.scrollbarLeft,right:n.right-i.borderRight-i.scrollbarRight,top:n.top+i.borderTop,bottom:n.bottom-i.borderBottom-i.scrollbarBottom};return e&&(s.left+=i.paddingLeft,s.right-=i.paddingRight,s.top+=i.paddingTop,s.bottom-=i.paddingBottom),s}function ba(t){let e=ya(t),r=t.getBoundingClientRect();for(let n of e){let i=ca(r,n.getBoundingClientRect());if(i)r=i;else return null}return r}function ya(t){let e=[];for(;t instanceof HTMLElement;){let r=window.getComputedStyle(t);if(r.position==="fixed")break;/(auto|scroll)/.test(r.overflow+r.overflowY+r.overflowX)&&e.push(t),t=t.parentNode}return e}class Wt{constructor(e,r,n,i){this.els=r;let s=this.originClientRect=e.getBoundingClientRect();n&&this.buildElHorizontals(s.left),i&&this.buildElVerticals(s.top)}buildElHorizontals(e){let r=[],n=[];for(let i of this.els){let s=i.getBoundingClientRect();r.push(s.left-e),n.push(s.right-e)}this.lefts=r,this.rights=n}buildElVerticals(e){let r=[],n=[];for(let i of this.els){let s=i.getBoundingClientRect();r.push(s.top-e),n.push(s.bottom-e)}this.tops=r,this.bottoms=n}leftToIndex(e){let{lefts:r,rights:n}=this,i=r.length,s;for(s=0;s<i;s+=1)if(e>=r[s]&&e<n[s])return s}topToIndex(e){let{tops:r,bottoms:n}=this,i=r.length,s;for(s=0;s<i;s+=1)if(e>=r[s]&&e<n[s])return s}getWidth(e){return this.rights[e]-this.lefts[e]}getHeight(e){return this.bottoms[e]-this.tops[e]}similarTo(e){return ze(this.tops||[],e.tops||[])&&ze(this.bottoms||[],e.bottoms||[])&&ze(this.lefts||[],e.lefts||[])&&ze(this.rights||[],e.rights||[])}}function ze(t,e){const r=t.length;if(r!==e.length)return!1;for(let n=0;n<r;n++)if(Math.round(t[n])!==Math.round(e[n]))return!1;return!0}class pt extends R{constructor(){super(...arguments),this.uid=$()}prepareHits(){}queryHit(e,r,n,i){return null}isValidSegDownEl(e){return!this.props.eventDrag&&!this.props.eventResize&&!V(e,".fc-event-mirror")}isValidDateDownEl(e){return!V(e,".fc-event:not(.fc-bg-event)")&&!V(e,".fc-more-link")&&!V(e,"a[data-navlink]")&&!V(e,".fc-popover")}}class Sa{constructor(e=r=>r.thickness||1){this.getEntryThickness=e,this.strictOrder=!1,this.allowReslicing=!1,this.maxCoord=-1,this.maxStackCnt=-1,this.levelCoords=[],this.entriesByLevel=[],this.stackCnts={}}addSegs(e){let r=[];for(let n of e)this.insertEntry(n,r);return r}insertEntry(e,r){let n=this.findInsertion(e);this.isInsertionValid(n,e)?this.insertEntryAt(e,n):this.handleInvalidInsertion(n,e,r)}isInsertionValid(e,r){return(this.maxCoord===-1||e.levelCoord+this.getEntryThickness(r)<=this.maxCoord)&&(this.maxStackCnt===-1||e.stackCnt<this.maxStackCnt)}handleInvalidInsertion(e,r,n){if(this.allowReslicing&&e.touchingEntry){const i=Object.assign(Object.assign({},r),{span:Wi(r.span,e.touchingEntry.span)});n.push(i),this.splitEntry(r,e.touchingEntry,n)}else n.push(r)}splitEntry(e,r,n){let i=e.span,s=r.span;i.start<s.start&&this.insertEntry({index:e.index,thickness:e.thickness,span:{start:i.start,end:s.start}},n),i.end>s.end&&this.insertEntry({index:e.index,thickness:e.thickness,span:{start:s.end,end:i.end}},n)}insertEntryAt(e,r){let{entriesByLevel:n,levelCoords:i}=this;r.lateral===-1?(Dt(i,r.level,r.levelCoord),Dt(n,r.level,[e])):Dt(n[r.level],r.lateral,e),this.stackCnts[gn(e)]=r.stackCnt}findInsertion(e){let{levelCoords:r,entriesByLevel:n,strictOrder:i,stackCnts:s}=this,l=r.length,o=0,a=-1,u=-1,c=null,d=0;for(let m=0;m<l;m+=1){const v=r[m];if(!i&&v>=o+this.getEntryThickness(e))break;let b=n[m],y,S=mn(b,e.span.start,pn),C=S[0]+S[1];for(;(y=b[C])&&y.span.start<e.span.end;){let w=v+this.getEntryThickness(y);w>o&&(o=w,c=y,a=m,u=C),w===o&&(d=Math.max(d,s[gn(y)]+1)),C+=1}}let f=0;if(c)for(f=a+1;f<l&&r[f]<o;)f+=1;let p=-1;return f<l&&r[f]===o&&(p=mn(n[f],e.span.end,pn)[0]),{touchingLevel:a,touchingLateral:u,touchingEntry:c,stackCnt:d,levelCoord:o,level:f,lateral:p}}toRects(){let{entriesByLevel:e,levelCoords:r}=this,n=e.length,i=[];for(let s=0;s<n;s+=1){let l=e[s],o=r[s];for(let a of l)i.push(Object.assign(Object.assign({},a),{thickness:this.getEntryThickness(a),levelCoord:o}))}return i}}function pn(t){return t.span.end}function gn(t){return t.index+":"+t.span.start}function Ea(t){let e=[];for(let r of t){let n=[],i={span:r.span,entries:[r]};for(let s of e)Wi(s.span,i.span)?i={entries:s.entries.concat(i.entries),span:Ca(s.span,i.span)}:n.push(s);n.push(i),e=n}return e}function Ca(t,e){return{start:Math.min(t.start,e.start),end:Math.max(t.end,e.end)}}function Wi(t,e){let r=Math.max(t.start,e.start),n=Math.min(t.end,e.end);return r<n?{start:r,end:n}:null}function Dt(t,e,r){t.splice(e,0,r)}function mn(t,e,r){let n=0,i=t.length;if(!i||e<r(t[n]))return[0,0];if(e>r(t[i-1]))return[i,0];for(;n<i;){let s=Math.floor(n+(i-n)/2),l=r(t[s]);if(e<l)i=s;else if(e>l)n=s+1;else return[s,1]}return[n,0]}const he={};P({weekday:"long"});class Aa{constructor(){this.sliceBusinessHours=A(this._sliceBusinessHours),this.sliceDateSelection=A(this._sliceDateSpan),this.sliceEventStore=A(this._sliceEventStore),this.sliceEventDrag=A(this._sliceInteraction),this.sliceEventResize=A(this._sliceInteraction),this.forceDayIfListItem=!1}sliceProps(e,r,n,i,...s){let{eventUiBases:l}=e,o=this.sliceEventStore(e.eventStore,l,r,n,...s);return{dateSelectionSegs:this.sliceDateSelection(e.dateSelection,r,n,l,i,...s),businessHourSegs:this.sliceBusinessHours(e.businessHours,r,n,i,...s),fgEventSegs:o.fg,bgEventSegs:o.bg,eventDrag:this.sliceEventDrag(e.eventDrag,l,r,n,...s),eventResize:this.sliceEventResize(e.eventResize,l,r,n,...s),eventSelection:e.eventSelection}}sliceNowDate(e,r,n,i,...s){return this._sliceDateSpan({range:{start:e,end:de(e,1)},allDay:!1},r,n,{},i,...s)}_sliceBusinessHours(e,r,n,i,...s){return e?this._sliceEventStore(fe(e,Fe(r,!!n),i),{},r,n,...s).bg:[]}_sliceEventStore(e,r,n,i,...s){if(e){let l=cn(e,r,Fe(n,!!i),i);return{bg:this.sliceEventRanges(l.bg,s),fg:this.sliceEventRanges(l.fg,s)}}return{bg:[],fg:[]}}_sliceInteraction(e,r,n,i,...s){if(!e)return null;let l=cn(e.mutatedEvents,r,Fe(n,!!i),i);return{segs:this.sliceEventRanges(l.fg,s),affectedInstances:e.affectedEvents.instances,isEvent:e.isEvent}}_sliceDateSpan(e,r,n,i,s,...l){if(!e)return[];let o=Fe(r,!!n),a=Ee(e.range,o);if(a){e=Object.assign(Object.assign({},e),{range:a});let u=ia(e,i,s),c=this.sliceRange(e.range,...l);for(let d of c)d.eventRange=u;return c}return[]}sliceEventRanges(e,r){let n=[];for(let i of e)n.push(...this.sliceEventRange(i,r));return n}sliceEventRange(e,r){let n=e.range;this.forceDayIfListItem&&e.ui.display==="list-item"&&(n={start:n.start,end:j(n.start,1)});let i=this.sliceRange(n,...r);for(let s of i)s.eventRange=e,s.isStart=e.isStart&&s.isStart,s.isEnd=e.isEnd&&s.isEnd;return i}}function Fe(t,e){let r=t.activeRange;return e?r:{start:de(r.start,t.slotMinTime.milliseconds),end:de(r.end,t.slotMaxTime.milliseconds-864e5)}}function Ra(t,e,r={},n){return!(t.eventDrag&&!wa(t,e,r,n)||t.dateSelection&&!_a(t,e,r,n))}function wa(t,e,r,n){let i=e.getCurrentData(),s=t.eventDrag,l=s.mutatedEvents,o=l.defs,a=l.instances,u=st(o,s.isEvent?t.eventUiBases:{"":i.selectionConfig});n&&(u=B(u,n));let c=No(t.eventStore,s.affectedEvents.instances),d=c.defs,f=c.instances,p=st(d,t.eventUiBases);for(let m in a){let v=a[m],b=v.range,y=u[v.defId],S=o[v.defId];if(!zi(y.constraints,b,c,t.businessHours,e))return!1;let{eventOverlap:C}=e.options,w=typeof C=="function"?C:null;for(let k in f){let M=f[k];if(dt(b,M.range)&&(p[M.defId].overlap===!1&&s.isEvent||y.overlap===!1||w&&!w(new O(e,d[M.defId],M),new O(e,S,v))))return!1}let x=i.eventStore;for(let k of y.allows){let M=Object.assign(Object.assign({},r),{range:v.range,allDay:S.allDay}),_=x.defs[S.defId],G=x.instances[m],Re;if(_?Re=new O(e,_,G):Re=new O(e,S),!k(or(M,e),Re))return!1}}return!0}function _a(t,e,r,n){let i=t.eventStore,s=i.defs,l=i.instances,o=t.dateSelection,a=o.range,{selectionConfig:u}=e.getCurrentData();if(n&&(u=n(u)),!zi(u.constraints,a,i,t.businessHours,e))return!1;let{selectOverlap:c}=e.options,d=typeof c=="function"?c:null;for(let f in l){let p=l[f];if(dt(a,p.range)&&(u.overlap===!1||d&&!d(new O(e,s[p.defId],p),null)))return!1}for(let f of u.allows){let p=Object.assign(Object.assign({},r),o);if(!f(or(p,e),null))return!1}return!0}function zi(t,e,r,n,i){for(let s of t)if(!Ia(Da(s,e,r,n,i),e))return!1;return!0}function Da(t,e,r,n,i){return t==="businessHours"?It(fe(n,e,i)):typeof t=="string"?It(ht(r,s=>s.groupId===t)):typeof t=="object"&&t?It(fe(t,e,i)):[]}function It(t){let{instances:e}=t,r=[];for(let n in e)r.push(e[n].range);return r}function Ia(t,e){for(let r of t)if(po(r,e))return!0;return!1}const je=/^(visible|hidden)$/;class Fi extends R{constructor(){super(...arguments),this.handleEl=e=>{this.el=e,Z(this.props.elRef,e)}}render(){let{props:e}=this,{liquid:r,liquidIsAbsolute:n}=e,i=r&&n,s=["fc-scroller"];return r&&(n?s.push("fc-scroller-liquid-absolute"):s.push("fc-scroller-liquid")),h("div",{ref:this.handleEl,className:s.join(" "),style:{overflowX:e.overflowX,overflowY:e.overflowY,left:i&&-(e.overcomeLeft||0)||"",right:i&&-(e.overcomeRight||0)||"",bottom:i&&-(e.overcomeBottom||0)||"",marginLeft:!i&&-(e.overcomeLeft||0)||"",marginRight:!i&&-(e.overcomeRight||0)||"",marginBottom:!i&&-(e.overcomeBottom||0)||"",maxHeight:e.maxHeight||""}},e.children)}needsXScrolling(){if(je.test(this.props.overflowX))return!1;let{el:e}=this,r=this.el.getBoundingClientRect().width-this.getYScrollbarWidth(),{children:n}=e;for(let i=0;i<n.length;i+=1)if(n[i].getBoundingClientRect().width>r)return!0;return!1}needsYScrolling(){if(je.test(this.props.overflowY))return!1;let{el:e}=this,r=this.el.getBoundingClientRect().height-this.getXScrollbarWidth(),{children:n}=e;for(let i=0;i<n.length;i+=1)if(n[i].getBoundingClientRect().height>r)return!0;return!1}getXScrollbarWidth(){return je.test(this.props.overflowX)?0:this.el.offsetHeight-this.el.clientHeight}getYScrollbarWidth(){return je.test(this.props.overflowY)?0:this.el.offsetWidth-this.el.clientWidth}}class Q{constructor(e){this.masterCallback=e,this.currentMap={},this.depths={},this.callbackMap={},this.handleValue=(r,n)=>{let{depths:i,currentMap:s}=this,l=!1,o=!1;r!==null?(l=n in s,s[n]=r,i[n]=(i[n]||0)+1,o=!0):(i[n]-=1,i[n]||(delete s[n],delete this.callbackMap[n],l=!0)),this.masterCallback&&(l&&this.masterCallback(null,String(n)),o&&this.masterCallback(r,String(n)))}}createRef(e){let r=this.callbackMap[e];return r||(r=this.callbackMap[e]=n=>{this.handleValue(n,String(e))}),r}collect(e,r,n){return fi(this.currentMap,e,r,n)}getAll(){return tr(this.currentMap)}}function ji(t){let e=Ce(t,".fc-scrollgrid-shrink"),r=0;for(let n of e)r=Math.max(r,ul(n));return Math.ceil(r)}function hr(t,e){return t.liquid&&e.liquid}function Vi(t,e){return e.maxHeight!=null||hr(t,e)}function Gi(t,e,r,n){let{expandRows:i}=r;return typeof e.content=="function"?e.content(r):h("table",{role:"presentation",className:[e.tableClassName,t.syncRowHeights?"fc-scrollgrid-sync-table":""].join(" "),style:{minWidth:r.tableMinWidth,width:r.clientWidth,height:i?r.clientHeight:""}},r.tableColGroupNode,h(n?"thead":"tbody",{role:"presentation"},typeof e.rowContent=="function"?e.rowContent(r):e.rowContent))}function qi(t,e){return F(t,e,L)}function Qi(t,e){let r=[];for(let n of t){let i=n.span||1;for(let s=0;s<i;s+=1)r.push(h("col",{style:{width:n.width==="shrink"?Yi(e):n.width||"",minWidth:n.minWidth||""}}))}return h("colgroup",{},...r)}function Yi(t){return t??4}function Zi(t){for(let e of t)if(e.width==="shrink")return!0;return!1}function $i(t,e){let r=["fc-scrollgrid",e.theme.getClass("table")];return t&&r.push("fc-scrollgrid-liquid"),r}function Xi(t,e){let r=["fc-scrollgrid-section",`fc-scrollgrid-section-${t.type}`,t.className];return e&&t.liquid&&t.maxHeight==null&&r.push("fc-scrollgrid-section-liquid"),t.isSticky&&r.push("fc-scrollgrid-section-sticky"),r}function zt(t){return h("div",{className:"fc-scrollgrid-sticky-shim",style:{width:t.clientWidth,minWidth:t.tableMinWidth}})}function Ji(t){let{stickyHeaderDates:e}=t;return(e==null||e==="auto")&&(e=t.height==="auto"||t.viewHeight==="auto"),e}function Ki(t){let{stickyFooterScrollbar:e}=t;return(e==null||e==="auto")&&(e=t.height==="auto"||t.viewHeight==="auto"),e}class Ta extends R{constructor(){super(...arguments),this.processCols=A(e=>e,qi),this.renderMicroColGroup=A(Qi),this.scrollerRefs=new Q,this.scrollerElRefs=new Q(this._handleScrollerEl.bind(this)),this.state={shrinkWidth:null,forceYScrollbars:!1,scrollerClientWidths:{},scrollerClientHeights:{}},this.handleSizing=()=>{this.safeSetState(Object.assign({shrinkWidth:this.computeShrinkWidth()},this.computeScrollerDims()))}}render(){let{props:e,state:r,context:n}=this,i=e.sections||[],s=this.processCols(e.cols),l=this.renderMicroColGroup(s,r.shrinkWidth),o=$i(e.liquid,n);e.collapsibleWidth&&o.push("fc-scrollgrid-collapsible");let a=i.length,u=0,c,d=[],f=[],p=[];for(;u<a&&(c=i[u]).type==="header";)d.push(this.renderSection(c,l,!0)),u+=1;for(;u<a&&(c=i[u]).type==="body";)f.push(this.renderSection(c,l,!1)),u+=1;for(;u<a&&(c=i[u]).type==="footer";)p.push(this.renderSection(c,l,!0)),u+=1;let m=!ur();const v={role:"rowgroup"};return h("table",{role:"grid",className:o.join(" "),style:{height:e.height}},!!(!m&&d.length)&&h("thead",v,...d),!!(!m&&f.length)&&h("tbody",v,...f),!!(!m&&p.length)&&h("tfoot",v,...p),m&&h("tbody",v,...d,...f,...p))}renderSection(e,r,n){return"outerContent"in e?h(I,{key:e.key},e.outerContent):h("tr",{key:e.key,role:"presentation",className:Xi(e,this.props.liquid).join(" ")},this.renderChunkTd(e,r,e.chunk,n))}renderChunkTd(e,r,n,i){if("outerContent"in n)return n.outerContent;let{props:s}=this,{forceYScrollbars:l,scrollerClientWidths:o,scrollerClientHeights:a}=this.state,u=Vi(s,e),c=hr(s,e),d=s.liquid?l?"scroll":u?"auto":"hidden":"visible",f=e.key,p=Gi(e,n,{tableColGroupNode:r,tableMinWidth:"",clientWidth:!s.collapsibleWidth&&o[f]!==void 0?o[f]:null,clientHeight:a[f]!==void 0?a[f]:null,expandRows:e.expandRows,syncRowHeights:!1,rowSyncHeights:[],reportRowHeightChange:()=>{}},i);return h(i?"th":"td",{ref:n.elRef,role:"presentation"},h("div",{className:`fc-scroller-harness${c?" fc-scroller-harness-liquid":""}`},h(Fi,{ref:this.scrollerRefs.createRef(f),elRef:this.scrollerElRefs.createRef(f),overflowY:d,overflowX:s.liquid?"hidden":"visible",maxHeight:e.maxHeight,liquid:c,liquidIsAbsolute:!0},p)))}_handleScrollerEl(e,r){let n=ka(this.props.sections,r);n&&Z(n.chunk.scrollerElRef,e)}componentDidMount(){this.handleSizing(),this.context.addResizeHandler(this.handleSizing)}componentDidUpdate(){this.handleSizing()}componentWillUnmount(){this.context.removeResizeHandler(this.handleSizing)}computeShrinkWidth(){return Zi(this.props.cols)?ji(this.scrollerElRefs.getAll()):0}computeScrollerDims(){let e=Bi(),{scrollerRefs:r,scrollerElRefs:n}=this,i=!1,s={},l={};for(let o in r.currentMap){let a=r.currentMap[o];if(a&&a.needsYScrolling()){i=!0;break}}for(let o of this.props.sections){let a=o.key,u=n.currentMap[a];if(u){let c=u.parentNode;s[a]=Math.floor(c.getBoundingClientRect().width-(i?e.y:0)),l[a]=Math.floor(c.getBoundingClientRect().height)}}return{forceYScrollbars:i,scrollerClientWidths:s,scrollerClientHeights:l}}}Ta.addStateEquality({scrollerClientWidths:L,scrollerClientHeights:L});function ka(t,e){for(let r of t)if(r.key===e)return r;return null}class es extends R{constructor(){super(...arguments),this.handleEl=e=>{this.el=e,e&&un(e,this.props.seg)}}render(){const{props:e,context:r}=this,{options:n}=r,{seg:i}=e,{eventRange:s}=i,{ui:l}=s,o={event:new O(r,s.def,s.instance),view:r.viewApi,timeText:e.timeText,textColor:l.textColor,backgroundColor:l.backgroundColor,borderColor:l.borderColor,isDraggable:!e.disableDragging&&Qo(i,r),isStartResizable:!e.disableResizing&&Yo(i,r),isEndResizable:!e.disableResizing&&Zo(i),isMirror:!!(e.isDragging||e.isResizing||e.isDateSelecting),isStart:!!i.isStart,isEnd:!!i.isEnd,isPast:!!e.isPast,isFuture:!!e.isFuture,isToday:!!e.isToday,isSelected:!!e.isSelected,isDragging:!!e.isDragging,isResizing:!!e.isResizing};return h(H,Object.assign({},e,{elRef:this.handleEl,elClasses:[...Xo(o),...i.eventRange.ui.classNames,...e.elClasses||[]],renderProps:o,generatorName:"eventContent",customGenerator:n.eventContent,defaultGenerator:e.defaultGenerator,classNameGenerator:n.eventClassNames,didMount:n.eventDidMount,willUnmount:n.eventWillUnmount}))}componentDidUpdate(e){this.el&&this.props.seg!==e.seg&&un(this.el,this.props.seg)}}class Ma extends R{render(){let{props:e,context:r}=this,{options:n}=r,{seg:i}=e,{ui:s}=i.eventRange,l=n.eventTimeFormat||e.defaultTimeFormat,o=$o(i,l,r,e.defaultDisplayEventTime,e.defaultDisplayEventEnd);return h(es,Object.assign({},e,{elTag:"a",elStyle:{borderColor:s.borderColor,backgroundColor:s.backgroundColor},elAttrs:Ko(i,r),defaultGenerator:xa,timeText:o}),(a,u)=>h(I,null,h(a,{elTag:"div",elClasses:["fc-event-main"],elStyle:{color:u.textColor}}),!!u.isStartResizable&&h("div",{className:"fc-event-resizer fc-event-resizer-start"}),!!u.isEndResizable&&h("div",{className:"fc-event-resizer fc-event-resizer-end"})))}}function xa(t){return h("div",{className:"fc-event-main-frame"},t.timeText&&h("div",{className:"fc-event-time"},t.timeText),h("div",{className:"fc-event-title-container"},h("div",{className:"fc-event-title fc-sticky"},t.event.title||h(I,null," "))))}const pr=t=>h(pe.Consumer,null,e=>{let{options:r}=e,n={isAxis:t.isAxis,date:e.dateEnv.toDate(t.date),view:e.viewApi};return h(H,Object.assign({},t,{elTag:t.elTag||"div",renderProps:n,generatorName:"nowIndicatorContent",customGenerator:r.nowIndicatorContent,classNameGenerator:r.nowIndicatorClassNames,didMount:r.nowIndicatorDidMount,willUnmount:r.nowIndicatorWillUnmount}))}),Oa=P({day:"numeric"});class Na extends R{constructor(){super(...arguments),this.refineRenderProps=ue(Pa)}render(){let{props:e,context:r}=this,{options:n}=r,i=this.refineRenderProps({date:e.date,dateProfile:e.dateProfile,todayRange:e.todayRange,isMonthStart:e.isMonthStart||!1,showDayNumber:e.showDayNumber,extraRenderProps:e.extraRenderProps,viewApi:r.viewApi,dateEnv:r.dateEnv,monthStartFormat:n.monthStartFormat});return h(H,Object.assign({},e,{elClasses:[...fr(i,r.theme),...e.elClasses||[]],elAttrs:Object.assign(Object.assign({},e.elAttrs),i.isDisabled?{}:{"data-date":kl(e.date)}),renderProps:i,generatorName:"dayCellContent",customGenerator:n.dayCellContent,defaultGenerator:e.defaultGenerator,classNameGenerator:i.isDisabled?void 0:n.dayCellClassNames,didMount:n.dayCellDidMount,willUnmount:n.dayCellWillUnmount}))}}function Ha(t){return!!(t.dayCellContent||Ht("dayCellContent",t))}function Pa(t){let{date:e,dateEnv:r,dateProfile:n,isMonthStart:i}=t,s=dr(e,t.todayRange,null,n),l=t.showDayNumber?r.format(e,i?t.monthStartFormat:Oa):"";return Object.assign(Object.assign(Object.assign({date:r.toDate(e),view:t.viewApi},s),{isMonthStart:i,dayNumberText:l}),t.extraRenderProps)}class Ba extends R{render(){let{props:e}=this,{seg:r}=e;return h(es,{elTag:"div",elClasses:["fc-bg-event"],elStyle:{backgroundColor:r.eventRange.ui.backgroundColor},defaultGenerator:La,seg:r,timeText:"",isDragging:!1,isResizing:!1,isDateSelecting:!1,isSelected:!1,isPast:e.isPast,isFuture:e.isFuture,isToday:e.isToday,disableDragging:!0,disableResizing:!0})}}function La(t){let{title:e}=t.event;return e&&h("div",{className:"fc-event-title"},t.event.title)}function Ua(t){return h("div",{className:`fc-${t}`})}const Tt=10;class Wa extends R{constructor(){super(...arguments),this.state={titleId:Zt()},this.handleRootEl=e=>{this.rootEl=e,this.props.elRef&&Z(this.props.elRef,e)},this.handleDocumentMouseDown=e=>{const r=ll(e);this.rootEl.contains(r)||this.handleCloseClick()},this.handleDocumentKeyDown=e=>{e.key==="Escape"&&this.handleCloseClick()},this.handleCloseClick=()=>{let{onClose:e}=this.props;e&&e()}}render(){let{theme:e,options:r}=this.context,{props:n,state:i}=this,s=["fc-popover",e.getClass("popover")].concat(n.extraClassNames||[]);return Gs(h("div",Object.assign({},n.extraAttrs,{id:n.id,className:s.join(" "),"aria-labelledby":i.titleId,ref:this.handleRootEl}),h("div",{className:"fc-popover-header "+e.getClass("popoverHeader")},h("span",{className:"fc-popover-title",id:i.titleId},n.title),h("span",{className:"fc-popover-close "+e.getIconClass("close"),title:r.closeHint,onClick:this.handleCloseClick})),h("div",{className:"fc-popover-body "+e.getClass("popoverContent")},n.children)),n.parentEl)}componentDidMount(){document.addEventListener("mousedown",this.handleDocumentMouseDown),document.addEventListener("keydown",this.handleDocumentKeyDown),this.updateSize()}componentWillUnmount(){document.removeEventListener("mousedown",this.handleDocumentMouseDown),document.removeEventListener("keydown",this.handleDocumentKeyDown)}updateSize(){let{isRtl:e}=this.context,{alignmentEl:r,alignGridTop:n}=this.props,{rootEl:i}=this,s=ba(r);if(s){let l=i.getBoundingClientRect(),o=n?V(r,".fc-scrollgrid").getBoundingClientRect().top:s.top,a=e?s.right-l.width:s.left;o=Math.max(o,Tt),a=Math.min(a,document.documentElement.clientWidth-Tt-l.width),a=Math.max(a,Tt);let u=i.offsetParent.getBoundingClientRect();Yt(i,{top:o-u.top,left:a-u.left})}}}class za extends pt{constructor(){super(...arguments),this.handleRootEl=e=>{this.rootEl=e,e?this.context.registerInteractiveComponent(this,{el:e,useEventCenter:!1}):this.context.unregisterInteractiveComponent(this)}}render(){let{options:e,dateEnv:r}=this.context,{props:n}=this,{startDate:i,todayRange:s,dateProfile:l}=n,o=r.format(i,e.dayPopoverFormat);return h(Na,{elRef:this.handleRootEl,date:i,dateProfile:l,todayRange:s},(a,u,c)=>h(Wa,{elRef:c.ref,id:n.id,title:o,extraClassNames:["fc-more-popover"].concat(c.className||[]),extraAttrs:c,parentEl:n.parentEl,alignmentEl:n.alignmentEl,alignGridTop:n.alignGridTop,onClose:n.onClose},Ha(e)&&h(a,{elTag:"div",elClasses:["fc-more-popover-misc"]}),n.children))}queryHit(e,r,n,i){let{rootEl:s,props:l}=this;return e>=0&&e<n&&r>=0&&r<i?{dateProfile:l.dateProfile,dateSpan:Object.assign({allDay:!l.forceTimed,range:{start:l.startDate,end:l.endDate}},l.extraDateSpan),dayEl:s,rect:{left:0,top:0,right:n,bottom:i},layer:1}:null}}class Fa extends R{constructor(){super(...arguments),this.state={isPopoverOpen:!1,popoverId:Zt()},this.handleLinkEl=e=>{this.linkEl=e,this.props.elRef&&Z(this.props.elRef,e)},this.handleClick=e=>{let{props:r,context:n}=this,{moreLinkClick:i}=n.options,s=vn(r).start;function l(o){let{def:a,instance:u,range:c}=o.eventRange;return{event:new O(n,a,u),start:n.dateEnv.toDate(c.start),end:n.dateEnv.toDate(c.end),isStart:o.isStart,isEnd:o.isEnd}}typeof i=="function"&&(i=i({date:s,allDay:!!r.allDayDate,allSegs:r.allSegs.map(l),hiddenSegs:r.hiddenSegs.map(l),jsEvent:e,view:n.viewApi})),!i||i==="popover"?this.setState({isPopoverOpen:!0}):typeof i=="string"&&n.calendarApi.zoomTo(s,i)},this.handlePopoverClose=()=>{this.setState({isPopoverOpen:!1})}}render(){let{props:e,state:r}=this;return h(pe.Consumer,null,n=>{let{viewApi:i,options:s,calendarApi:l}=n,{moreLinkText:o}=s,{moreCnt:a}=e,u=vn(e),c=typeof o=="function"?o.call(l,a):`+${a} ${o}`,d=we(s.moreLinkHint,[a],c),f={num:a,shortText:`+${a}`,text:c,view:i};return h(I,null,!!e.moreCnt&&h(H,{elTag:e.elTag||"a",elRef:this.handleLinkEl,elClasses:[...e.elClasses||[],"fc-more-link"],elStyle:e.elStyle,elAttrs:Object.assign(Object.assign(Object.assign({},e.elAttrs),si(this.handleClick)),{title:d,"aria-expanded":r.isPopoverOpen,"aria-controls":r.isPopoverOpen?r.popoverId:""}),renderProps:f,generatorName:"moreLinkContent",customGenerator:s.moreLinkContent,defaultGenerator:e.defaultGenerator||ja,classNameGenerator:s.moreLinkClassNames,didMount:s.moreLinkDidMount,willUnmount:s.moreLinkWillUnmount},e.children),r.isPopoverOpen&&h(za,{id:r.popoverId,startDate:u.start,endDate:u.end,dateProfile:e.dateProfile,todayRange:e.todayRange,extraDateSpan:e.extraDateSpan,parentEl:this.parentEl,alignmentEl:e.alignmentElRef?e.alignmentElRef.current:this.linkEl,alignGridTop:e.alignGridTop,forceTimed:e.forceTimed,onClose:this.handlePopoverClose},e.popoverContent()))})}componentDidMount(){this.updateParentEl()}componentDidUpdate(){this.updateParentEl()}updateParentEl(){this.linkEl&&(this.parentEl=V(this.linkEl,".fc-view-harness"))}}function ja(t){return t.text}function vn(t){if(t.allDayDate)return{start:t.allDayDate,end:j(t.allDayDate,1)};let{hiddenSegs:e}=t;return{start:gr(e),end:Ga(e)}}function gr(t){return t.reduce(Va).eventRange.range.start}function Va(t,e){return t.eventRange.range.start<e.eventRange.range.start?t:e}function Ga(t){return t.reduce(qa).eventRange.range.end}function qa(t,e){return t.eventRange.range.end>e.eventRange.range.end?t:e}const Qa=[],ts={code:"en",week:{dow:0,doy:4},direction:"ltr",buttonText:{prev:"prev",next:"next",prevYear:"prev year",nextYear:"next year",year:"year",today:"today",month:"month",week:"week",day:"day",list:"list"},weekText:"W",weekTextLong:"Week",closeHint:"Close",timeHint:"Time",eventHint:"Event",allDayText:"all-day",moreLinkText:"more",noEventsText:"No events to display"},rs=Object.assign(Object.assign({},ts),{buttonHints:{prev:"Previous $0",next:"Next $0",today(t,e){return e==="day"?"Today":`This ${t}`}},viewHint:"$0 view",navLinkHint:"Go to $0",moreLinkHint(t){return`Show ${t} more event${t===1?"":"s"}`}});function Ya(t){let e=t.length>0?t[0].code:"en",r=Qa.concat(t),n={en:rs};for(let i of r)n[i.code]=i;return{map:n,defaultCode:e}}function ns(t,e){return typeof t=="object"&&!Array.isArray(t)?is(t.code,[t.code],t):Za(t,e)}function Za(t,e){let r=[].concat(t||[]),n=$a(r,e)||rs;return is(t,r,n)}function $a(t,e){for(let r=0;r<t.length;r+=1){let n=t[r].toLocaleLowerCase().split("-");for(let i=n.length;i>0;i-=1){let s=n.slice(0,i).join("-");if(e[s])return e[s]}}return null}function is(t,e,r){let n=er([ts,r],["buttonText"]);delete n.code;let{week:i}=n;return delete n.week,{codeArg:t,codes:e,week:i,simpleNumberFormat:new Intl.NumberFormat(t),options:n}}function X(t){return{id:$(),name:t.name,premiumReleaseDate:t.premiumReleaseDate?new Date(t.premiumReleaseDate):void 0,deps:t.deps||[],reducers:t.reducers||[],isLoadingFuncs:t.isLoadingFuncs||[],contextInit:[].concat(t.contextInit||[]),eventRefiners:t.eventRefiners||{},eventDefMemberAdders:t.eventDefMemberAdders||[],eventSourceRefiners:t.eventSourceRefiners||{},isDraggableTransformers:t.isDraggableTransformers||[],eventDragMutationMassagers:t.eventDragMutationMassagers||[],eventDefMutationAppliers:t.eventDefMutationAppliers||[],dateSelectionTransformers:t.dateSelectionTransformers||[],datePointTransforms:t.datePointTransforms||[],dateSpanTransforms:t.dateSpanTransforms||[],views:t.views||{},viewPropsTransformers:t.viewPropsTransformers||[],isPropsValid:t.isPropsValid||null,externalDefTransforms:t.externalDefTransforms||[],viewContainerAppends:t.viewContainerAppends||[],eventDropTransformers:t.eventDropTransformers||[],componentInteractions:t.componentInteractions||[],calendarInteractions:t.calendarInteractions||[],themeClasses:t.themeClasses||{},eventSourceDefs:t.eventSourceDefs||[],cmdFormatter:t.cmdFormatter,recurringTypes:t.recurringTypes||[],namedTimeZonedImpl:t.namedTimeZonedImpl,initialView:t.initialView||"",elementDraggingImpl:t.elementDraggingImpl,optionChangeHandlers:t.optionChangeHandlers||{},scrollGridImpl:t.scrollGridImpl||null,listenerRefiners:t.listenerRefiners||{},optionRefiners:t.optionRefiners||{},propSetHandlers:t.propSetHandlers||{}}}function Xa(t,e){let r={},n={premiumReleaseDate:void 0,reducers:[],isLoadingFuncs:[],contextInit:[],eventRefiners:{},eventDefMemberAdders:[],eventSourceRefiners:{},isDraggableTransformers:[],eventDragMutationMassagers:[],eventDefMutationAppliers:[],dateSelectionTransformers:[],datePointTransforms:[],dateSpanTransforms:[],views:{},viewPropsTransformers:[],isPropsValid:null,externalDefTransforms:[],viewContainerAppends:[],eventDropTransformers:[],componentInteractions:[],calendarInteractions:[],themeClasses:{},eventSourceDefs:[],cmdFormatter:null,recurringTypes:[],namedTimeZonedImpl:null,initialView:"",elementDraggingImpl:null,optionChangeHandlers:{},scrollGridImpl:null,listenerRefiners:{},optionRefiners:{},propSetHandlers:{}};function i(s){for(let l of s){const o=l.name,a=r[o];a===void 0?(r[o]=l.id,i(l.deps),n=Ka(n,l)):a!==l.id&&console.warn(`Duplicate plugin '${o}'`)}}return t&&i(t),i(e),n}function Ja(){let t=[],e=[],r;return(n,i)=>((!r||!F(n,t)||!F(i,e))&&(r=Xa(n,i)),t=n,e=i,r)}function Ka(t,e){return{premiumReleaseDate:ec(t.premiumReleaseDate,e.premiumReleaseDate),reducers:t.reducers.concat(e.reducers),isLoadingFuncs:t.isLoadingFuncs.concat(e.isLoadingFuncs),contextInit:t.contextInit.concat(e.contextInit),eventRefiners:Object.assign(Object.assign({},t.eventRefiners),e.eventRefiners),eventDefMemberAdders:t.eventDefMemberAdders.concat(e.eventDefMemberAdders),eventSourceRefiners:Object.assign(Object.assign({},t.eventSourceRefiners),e.eventSourceRefiners),isDraggableTransformers:t.isDraggableTransformers.concat(e.isDraggableTransformers),eventDragMutationMassagers:t.eventDragMutationMassagers.concat(e.eventDragMutationMassagers),eventDefMutationAppliers:t.eventDefMutationAppliers.concat(e.eventDefMutationAppliers),dateSelectionTransformers:t.dateSelectionTransformers.concat(e.dateSelectionTransformers),datePointTransforms:t.datePointTransforms.concat(e.datePointTransforms),dateSpanTransforms:t.dateSpanTransforms.concat(e.dateSpanTransforms),views:Object.assign(Object.assign({},t.views),e.views),viewPropsTransformers:t.viewPropsTransformers.concat(e.viewPropsTransformers),isPropsValid:e.isPropsValid||t.isPropsValid,externalDefTransforms:t.externalDefTransforms.concat(e.externalDefTransforms),viewContainerAppends:t.viewContainerAppends.concat(e.viewContainerAppends),eventDropTransformers:t.eventDropTransformers.concat(e.eventDropTransformers),calendarInteractions:t.calendarInteractions.concat(e.calendarInteractions),componentInteractions:t.componentInteractions.concat(e.componentInteractions),themeClasses:Object.assign(Object.assign({},t.themeClasses),e.themeClasses),eventSourceDefs:t.eventSourceDefs.concat(e.eventSourceDefs),cmdFormatter:e.cmdFormatter||t.cmdFormatter,recurringTypes:t.recurringTypes.concat(e.recurringTypes),namedTimeZonedImpl:e.namedTimeZonedImpl||t.namedTimeZonedImpl,initialView:t.initialView||e.initialView,elementDraggingImpl:t.elementDraggingImpl||e.elementDraggingImpl,optionChangeHandlers:Object.assign(Object.assign({},t.optionChangeHandlers),e.optionChangeHandlers),scrollGridImpl:e.scrollGridImpl||t.scrollGridImpl,listenerRefiners:Object.assign(Object.assign({},t.listenerRefiners),e.listenerRefiners),optionRefiners:Object.assign(Object.assign({},t.optionRefiners),e.optionRefiners),propSetHandlers:Object.assign(Object.assign({},t.propSetHandlers),e.propSetHandlers)}}function ec(t,e){return t===void 0?e:e===void 0?t:new Date(Math.max(t.valueOf(),e.valueOf()))}class ie extends He{}ie.prototype.classes={root:"fc-theme-standard",tableCellShaded:"fc-cell-shaded",buttonGroup:"fc-button-group",button:"fc-button fc-button-primary",buttonActive:"fc-button-active"};ie.prototype.baseIconClass="fc-icon";ie.prototype.iconClasses={close:"fc-icon-x",prev:"fc-icon-chevron-left",next:"fc-icon-chevron-right",prevYear:"fc-icon-chevrons-left",nextYear:"fc-icon-chevrons-right"};ie.prototype.rtlIconClasses={prev:"fc-icon-chevron-right",next:"fc-icon-chevron-left",prevYear:"fc-icon-chevrons-right",nextYear:"fc-icon-chevrons-left"};ie.prototype.iconOverrideOption="buttonIcons";ie.prototype.iconOverrideCustomButtonOption="icon";ie.prototype.iconOverridePrefix="fc-icon-";function tc(t,e){let r={},n;for(n in t)Ft(n,r,t,e);for(n in e)Ft(n,r,t,e);return r}function Ft(t,e,r,n){if(e[t])return e[t];let i=rc(t,e,r,n);return i&&(e[t]=i),i}function rc(t,e,r,n){let i=r[t],s=n[t],l=c=>i&&i[c]!==null?i[c]:s&&s[c]!==null?s[c]:null,o=l("component"),a=l("superType"),u=null;if(a){if(a===t)throw new Error("Can't have a custom view type that references itself");u=Ft(a,e,r,n)}return!o&&u&&(o=u.component),o?{type:t,component:o,defaults:Object.assign(Object.assign({},u?u.defaults:{}),i?i.rawOptions:{}),overrides:Object.assign(Object.assign({},u?u.overrides:{}),s?s.rawOptions:{})}:null}function bn(t){return B(t,nc)}function nc(t){let e=typeof t=="function"?{component:t}:t,{component:r}=e;return e.content?r=yn(e):r&&!(r.prototype instanceof R)&&(r=yn(Object.assign(Object.assign({},e),{content:r}))),{superType:e.type,component:r,rawOptions:e}}function yn(t){return e=>h(pe.Consumer,null,r=>h(H,{elTag:"div",elClasses:bi(r.viewSpec),renderProps:Object.assign(Object.assign({},e),{nextDayThreshold:r.options.nextDayThreshold}),generatorName:void 0,customGenerator:t.content,classNameGenerator:t.classNames,didMount:t.didMount,willUnmount:t.willUnmount}))}function ic(t,e,r,n){let i=bn(t),s=bn(e.views),l=tc(i,s);return B(l,o=>sc(o,s,e,r,n))}function sc(t,e,r,n,i){let s=t.overrides.duration||t.defaults.duration||n.duration||r.duration,l=null,o="",a="",u={};if(s&&(l=lc(s),l)){let f=ne(l);o=f.unit,f.value===1&&(a=o,u=e[o]?e[o].rawOptions:{})}let c=f=>{let p=f.buttonText||{},m=t.defaults.buttonTextKey;return m!=null&&p[m]!=null?p[m]:p[t.type]!=null?p[t.type]:p[a]!=null?p[a]:null},d=f=>{let p=f.buttonHints||{},m=t.defaults.buttonTextKey;return m!=null&&p[m]!=null?p[m]:p[t.type]!=null?p[t.type]:p[a]!=null?p[a]:null};return{type:t.type,component:t.component,duration:l,durationUnit:o,singleUnit:a,optionDefaults:t.defaults,optionOverrides:Object.assign(Object.assign({},u),t.overrides),buttonTextOverride:c(n)||c(r)||t.overrides.buttonText,buttonTextDefault:c(i)||t.defaults.buttonText||c(_e)||t.type,buttonTitleOverride:d(n)||d(r)||t.overrides.buttonHint,buttonTitleDefault:d(i)||t.defaults.buttonHint||d(_e)}}let Sn={};function lc(t){let e=JSON.stringify(t),r=Sn[e];return r===void 0&&(r=D(t),Sn[e]=r),r}function oc(t,e){switch(e.type){case"CHANGE_VIEW_TYPE":t=e.viewType}return t}function ac(t,e){switch(e.type){case"CHANGE_DATE":return e.dateMarker;default:return t}}function cc(t,e,r){let n=t.initialDate;return n!=null?e.createMarker(n):r.getDateMarker()}function uc(t,e){switch(e.type){case"SET_OPTION":return Object.assign(Object.assign({},t),{[e.optionName]:e.rawOptionValue});default:return t}}function dc(t,e,r,n){let i;switch(e.type){case"CHANGE_VIEW_TYPE":return n.build(e.dateMarker||r);case"CHANGE_DATE":return n.build(e.dateMarker);case"PREV":if(i=n.buildPrev(t,r),i.isValid)return i;break;case"NEXT":if(i=n.buildNext(t,r),i.isValid)return i;break}return t}function fc(t,e,r){let n=e?e.activeRange:null;return ls({},yc(t,r),n,r)}function hc(t,e,r,n){let i=r?r.activeRange:null;switch(e.type){case"ADD_EVENT_SOURCES":return ls(t,e.sources,i,n);case"REMOVE_EVENT_SOURCE":return gc(t,e.sourceId);case"PREV":case"NEXT":case"CHANGE_DATE":case"CHANGE_VIEW_TYPE":return r?os(t,i,n):t;case"FETCH_EVENT_SOURCES":return mr(t,e.sourceIds?di(e.sourceIds):as(t,n),i,e.isRefetch||!1,n);case"RECEIVE_EVENTS":case"RECEIVE_EVENT_ERROR":return bc(t,e.sourceId,e.fetchId,e.fetchRange);case"REMOVE_ALL_EVENT_SOURCES":return{};default:return t}}function pc(t,e,r){let n=e?e.activeRange:null;return mr(t,as(t,r),n,!0,r)}function ss(t){for(let e in t)if(t[e].isFetching)return!0;return!1}function ls(t,e,r,n){let i={};for(let s of e)i[s.sourceId]=s;return r&&(i=os(i,r,n)),Object.assign(Object.assign({},t),i)}function gc(t,e){return K(t,r=>r.sourceId!==e)}function os(t,e,r){return mr(t,K(t,n=>mc(n,e,r)),e,!1,r)}function mc(t,e,r){return cs(t,r)?!r.options.lazyFetching||!t.fetchRange||t.isFetching||e.start<t.fetchRange.start||e.end>t.fetchRange.end:!t.latestFetchId}function mr(t,e,r,n,i){let s={};for(let l in t){let o=t[l];e[l]?s[l]=vc(o,r,n,i):s[l]=o}return s}function vc(t,e,r,n){let{options:i,calendarApi:s}=n,l=n.pluginHooks.eventSourceDefs[t.sourceDefId],o=$();return l.fetch({eventSource:t,range:e,isRefetch:r,context:n},a=>{let{rawEvents:u}=a;i.eventSourceSuccess&&(u=i.eventSourceSuccess.call(s,u,a.response)||u),t.success&&(u=t.success.call(s,u,a.response)||u),n.dispatch({type:"RECEIVE_EVENTS",sourceId:t.sourceId,fetchId:o,fetchRange:e,rawEvents:u})},a=>{let u=!1;i.eventSourceFailure&&(i.eventSourceFailure.call(s,a),u=!0),t.failure&&(t.failure(a),u=!0),u||console.warn(a.message,a),n.dispatch({type:"RECEIVE_EVENT_ERROR",sourceId:t.sourceId,fetchId:o,fetchRange:e,error:a})}),Object.assign(Object.assign({},t),{isFetching:!0,latestFetchId:o})}function bc(t,e,r,n){let i=t[e];return i&&r===i.latestFetchId?Object.assign(Object.assign({},t),{[e]:Object.assign(Object.assign({},i),{isFetching:!1,fetchRange:n})}):t}function as(t,e){return K(t,r=>cs(r,e))}function yc(t,e){let r=Ri(e),n=[].concat(t.eventSources||[]),i=[];t.initialEvents&&n.unshift(t.initialEvents),t.events&&n.unshift(t.events);for(let s of n){let l=Ai(s,e,r);l&&i.push(l)}return i}function cs(t,e){return!e.pluginHooks.eventSourceDefs[t.sourceDefId].ignoreRange}function Sc(t,e){switch(e.type){case"UNSELECT_DATES":return null;case"SELECT_DATES":return e.selection;default:return t}}function Ec(t,e){switch(e.type){case"UNSELECT_EVENT":return"";case"SELECT_EVENT":return e.eventInstanceId;default:return t}}function Cc(t,e){let r;switch(e.type){case"UNSET_EVENT_DRAG":return null;case"SET_EVENT_DRAG":return r=e.state,{affectedEvents:r.affectedEvents,mutatedEvents:r.mutatedEvents,isEvent:r.isEvent};default:return t}}function Ac(t,e){let r;switch(e.type){case"UNSET_EVENT_RESIZE":return null;case"SET_EVENT_RESIZE":return r=e.state,{affectedEvents:r.affectedEvents,mutatedEvents:r.mutatedEvents,isEvent:r.isEvent};default:return t}}function Rc(t,e,r,n,i){let s=t.headerToolbar?En(t.headerToolbar,t,e,r,n,i):null,l=t.footerToolbar?En(t.footerToolbar,t,e,r,n,i):null;return{header:s,footer:l}}function En(t,e,r,n,i,s){let l={},o=[],a=!1;for(let u in t){let c=t[u],d=wc(c,e,r,n,i,s);l[u]=d.widgets,o.push(...d.viewsWithButtons),a=a||d.hasTitle}return{sectionWidgets:l,viewsWithButtons:o,hasTitle:a}}function wc(t,e,r,n,i,s){let l=e.direction==="rtl",o=e.customButtons||{},a=r.buttonText||{},u=e.buttonText||{},c=r.buttonHints||{},d=e.buttonHints||{},f=t?t.split(" "):[],p=[],m=!1;return{widgets:f.map(b=>b.split(",").map(y=>{if(y==="title")return m=!0,{buttonName:y};let S,C,w,x,k,M;if(S=o[y])w=_=>{S.click&&S.click.call(_.target,_,_.target)},(x=n.getCustomButtonIconClass(S))||(x=n.getIconClass(y,l))||(k=S.text),M=S.hint||S.text;else if(C=i[y]){p.push(y),w=()=>{s.changeView(y)},(k=C.buttonTextOverride)||(x=n.getIconClass(y,l))||(k=C.buttonTextDefault);let _=C.buttonTextOverride||C.buttonTextDefault;M=we(C.buttonTitleOverride||C.buttonTitleDefault||e.viewHint,[_,y],_)}else if(s[y])if(w=()=>{s[y]()},(k=a[y])||(x=n.getIconClass(y,l))||(k=u[y]),y==="prevYear"||y==="nextYear"){let _=y==="prevYear"?"prev":"next";M=we(c[_]||d[_],[u.year||"year","year"],u[y])}else M=_=>we(c[y]||d[y],[u[_]||_,_],u[y]);return{buttonName:y,buttonClick:w,buttonIcon:x,buttonText:k,buttonHint:M}})),viewsWithButtons:p,hasTitle:m}}class _c{constructor(e,r,n){this.type=e,this.getCurrentData=r,this.dateEnv=n}get calendar(){return this.getCurrentData().calendarApi}get title(){return this.getCurrentData().viewTitle}get activeStart(){return this.dateEnv.toDate(this.getCurrentData().dateProfile.activeRange.start)}get activeEnd(){return this.dateEnv.toDate(this.getCurrentData().dateProfile.activeRange.end)}get currentStart(){return this.dateEnv.toDate(this.getCurrentData().dateProfile.currentRange.start)}get currentEnd(){return this.dateEnv.toDate(this.getCurrentData().dateProfile.currentRange.end)}getOption(e){return this.getCurrentData().options[e]}}let Dc={ignoreRange:!0,parseMeta(t){return Array.isArray(t.events)?t.events:null},fetch(t,e){e({rawEvents:t.eventSource.meta})}};const Ic=X({name:"array-event-source",eventSourceDefs:[Dc]});let Tc={parseMeta(t){return typeof t.events=="function"?t.events:null},fetch(t,e,r){const{dateEnv:n}=t.context,i=t.eventSource.meta;Oi(i.bind(null,Mi(t.range,n)),s=>e({rawEvents:s}),r)}};const kc=X({name:"func-event-source",eventSourceDefs:[Tc]}),Mc={method:String,extraParams:g,startParam:String,endParam:String,timeZoneParam:String};let xc={parseMeta(t){return t.url&&(t.format==="json"||!t.format)?{url:t.url,format:"json",method:(t.method||"GET").toUpperCase(),extraParams:t.extraParams,startParam:t.startParam,endParam:t.endParam,timeZoneParam:t.timeZoneParam}:null},fetch(t,e,r){const{meta:n}=t.eventSource,i=Nc(n,t.range,t.context);Ni(n.method,n.url,i).then(([s,l])=>{e({rawEvents:s,response:l})},r)}};const Oc=X({name:"json-event-source",eventSourceRefiners:Mc,eventSourceDefs:[xc]});function Nc(t,e,r){let{dateEnv:n,options:i}=r,s,l,o,a,u={};return s=t.startParam,s==null&&(s=i.startParam),l=t.endParam,l==null&&(l=i.endParam),o=t.timeZoneParam,o==null&&(o=i.timeZoneParam),typeof t.extraParams=="function"?a=t.extraParams():a=t.extraParams||{},Object.assign(u,a),u[s]=n.formatIso(e.start),u[l]=n.formatIso(e.end),n.timeZone!=="local"&&(u[o]=n.timeZone),u}const Hc={daysOfWeek:g,startTime:D,endTime:D,duration:D,startRecur:g,endRecur:g};let Pc={parse(t,e){if(t.daysOfWeek||t.startTime||t.endTime||t.startRecur||t.endRecur){let r={daysOfWeek:t.daysOfWeek||null,startTime:t.startTime||null,endTime:t.endTime||null,startRecur:t.startRecur?e.createMarker(t.startRecur):null,endRecur:t.endRecur?e.createMarker(t.endRecur):null,dateEnv:e},n;return t.duration&&(n=t.duration),!n&&t.startTime&&t.endTime&&(n=pl(t.endTime,t.startTime)),{allDayGuess:!t.startTime&&!t.endTime,duration:n,typeData:r}}return null},expand(t,e,r){let n=Ee(e,{start:t.startRecur,end:t.endRecur});return n?Lc(t.daysOfWeek,t.startTime,t.dateEnv,r,n):[]}};const Bc=X({name:"simple-recurring-event",recurringTypes:[Pc],eventRefiners:Hc});function Lc(t,e,r,n,i){let s=t?di(t):null,l=T(i.start),o=i.end,a=[];for(;l<o;){let u;(!s||s[l.getUTCDay()])&&(e?u=n.add(l,e):u=l,a.push(n.createMarker(r.toDate(u)))),l=j(l,1)}return a}const Uc=X({name:"change-handler",optionChangeHandlers:{events(t,e){Cn([t],e)},eventSources:Cn}});function Cn(t,e){let r=tr(e.getCurrentData().eventSources);if(r.length===1&&t.length===1&&Array.isArray(r[0]._raw)&&Array.isArray(t[0])){e.dispatch({type:"RESET_RAW_EVENTS",sourceId:r[0].sourceId,rawEvents:t[0]});return}let n=[];for(let i of t){let s=!1;for(let l=0;l<r.length;l+=1)if(r[l]._raw===i){r.splice(l,1),s=!0;break}s||n.push(i)}for(let i of r)e.dispatch({type:"REMOVE_EVENT_SOURCE",sourceId:i.sourceId});for(let i of n)e.calendarApi.addEventSource(i)}function Wc(t,e){e.emitter.trigger("datesSet",Object.assign(Object.assign({},Mi(t.activeRange,e.dateEnv)),{view:e.viewApi}))}function zc(t,e){let{emitter:r}=e;r.hasHandlers("eventsSet")&&r.trigger("eventsSet",ar(t,e))}const Fc=[Ic,kc,Oc,Bc,Uc,X({name:"misc",isLoadingFuncs:[t=>ss(t.eventSources)],propSetHandlers:{dateProfile:Wc,eventStore:zc}})];class jc{constructor(e,r){this.runTaskOption=e,this.drainedOption=r,this.queue=[],this.delayedRunner=new Te(this.drain.bind(this))}request(e,r){this.queue.push(e),this.delayedRunner.request(r)}pause(e){this.delayedRunner.pause(e)}resume(e,r){this.delayedRunner.resume(e,r)}drain(){let{queue:e}=this;for(;e.length;){let r=[],n;for(;n=e.shift();)this.runTask(n),r.push(n);this.drained(r)}}runTask(e){this.runTaskOption&&this.runTaskOption(e)}drained(e){this.drainedOption&&this.drainedOption(e)}}function Vc(t,e,r){let n;return/^(year|month)$/.test(t.currentRangeUnit)?n=t.currentRange:n=t.activeRange,r.formatRange(n.start,n.end,P(e.titleFormat||Gc(t)),{isEndExclusive:t.isRangeAllDay,defaultSeparator:e.titleRangeSeparator})}function Gc(t){let{currentRangeUnit:e}=t;if(e==="year")return{year:"numeric"};if(e==="month")return{year:"numeric",month:"long"};let r=Me(t.currentRange.start,t.currentRange.end);return r!==null&&r>1?{year:"numeric",month:"short",day:"numeric"}:{year:"numeric",month:"long",day:"numeric"}}class An{constructor(){this.resetListeners=new Set}handleInput(e,r){const n=this.dateEnv;if(e!==n&&(typeof r=="function"?this.nowFn=r:n||(this.nowAnchorDate=e.toDate(r?e.createMarker(r):e.createNowMarker()),this.nowAnchorQueried=Date.now()),this.dateEnv=e,n))for(const i of this.resetListeners.values())i()}getDateMarker(){return this.nowAnchorDate?this.dateEnv.timestampToMarker(this.nowAnchorDate.valueOf()+(Date.now()-this.nowAnchorQueried)):this.dateEnv.createMarker(this.nowFn())}addResetListener(e){this.resetListeners.add(e)}removeResetListener(e){this.resetListeners.delete(e)}}class qc{constructor(e){this.computeCurrentViewData=A(this._computeCurrentViewData),this.organizeRawLocales=A(Ya),this.buildLocale=A(ns),this.buildPluginHooks=Ja(),this.buildDateEnv=A(Qc),this.buildTheme=A(Yc),this.parseToolbars=A(Rc),this.buildViewSpecs=A(ic),this.buildDateProfileGenerator=ue(Zc),this.buildViewApi=A($c),this.buildViewUiProps=ue(Kc),this.buildEventUiBySource=A(Xc,L),this.buildEventUiBases=A(Jc),this.parseContextBusinessHours=ue(eu),this.buildTitle=A(Vc),this.nowManager=new An,this.emitter=new Di,this.actionRunner=new jc(this._handleAction.bind(this),this.updateData.bind(this)),this.currentCalendarOptionsInput={},this.currentCalendarOptionsRefined={},this.currentViewOptionsInput={},this.currentViewOptionsRefined={},this.currentCalendarOptionsRefiners={},this.optionsForRefining=[],this.optionsForHandling=[],this.getCurrentData=()=>this.data,this.dispatch=f=>{this.actionRunner.request(f)},this.props=e,this.actionRunner.pause(),this.nowManager=new An;let r={},n=this.computeOptionsData(e.optionOverrides,r,e.calendarApi),i=n.calendarOptions.initialView||n.pluginHooks.initialView,s=this.computeCurrentViewData(i,n,e.optionOverrides,r);e.calendarApi.currentDataManager=this,this.emitter.setThisContext(e.calendarApi),this.emitter.setOptions(s.options);let l={nowManager:this.nowManager,dateEnv:n.dateEnv,options:n.calendarOptions,pluginHooks:n.pluginHooks,calendarApi:e.calendarApi,dispatch:this.dispatch,emitter:this.emitter,getCurrentData:this.getCurrentData},o=cc(n.calendarOptions,n.dateEnv,this.nowManager),a=s.dateProfileGenerator.build(o);J(a.activeRange,o)||(o=a.currentRange.start);for(let f of n.pluginHooks.contextInit)f(l);let u=fc(n.calendarOptions,a,l),c={dynamicOptionOverrides:r,currentViewType:i,currentDate:o,dateProfile:a,businessHours:this.parseContextBusinessHours(l),eventSources:u,eventUiBases:{},eventStore:ee(),renderableEventStore:ee(),dateSelection:null,eventSelection:"",eventDrag:null,eventResize:null,selectionConfig:this.buildViewUiProps(l).selectionConfig},d=Object.assign(Object.assign({},l),c);for(let f of n.pluginHooks.reducers)Object.assign(c,f(null,null,d));kt(c,l)&&this.emitter.trigger("loading",!0),this.state=c,this.updateData(),this.actionRunner.resume()}resetOptions(e,r){let{props:n}=this;r===void 0?n.optionOverrides=e:(n.optionOverrides=Object.assign(Object.assign({},n.optionOverrides||{}),e),this.optionsForRefining.push(...r)),(r===void 0||r.length)&&this.actionRunner.request({type:"NOTHING"})}_handleAction(e){let{props:r,state:n,emitter:i}=this,s=uc(n.dynamicOptionOverrides,e),l=this.computeOptionsData(r.optionOverrides,s,r.calendarApi),o=oc(n.currentViewType,e),a=this.computeCurrentViewData(o,l,r.optionOverrides,s);r.calendarApi.currentDataManager=this,i.setThisContext(r.calendarApi),i.setOptions(a.options);let u={nowManager:this.nowManager,dateEnv:l.dateEnv,options:l.calendarOptions,pluginHooks:l.pluginHooks,calendarApi:r.calendarApi,dispatch:this.dispatch,emitter:i,getCurrentData:this.getCurrentData},{currentDate:c,dateProfile:d}=n;this.data&&this.data.dateProfileGenerator!==a.dateProfileGenerator&&(d=a.dateProfileGenerator.build(c)),c=ac(c,e),d=dc(d,e,c,a.dateProfileGenerator),(e.type==="PREV"||e.type==="NEXT"||!J(d.currentRange,c))&&(c=d.currentRange.start);let f=hc(n.eventSources,e,d,u),p=ko(n.eventStore,e,f,d,u),v=ss(f)&&!a.options.progressiveEventRendering&&n.renderableEventStore||p,{eventUiSingleBase:b,selectionConfig:y}=this.buildViewUiProps(u),S=this.buildEventUiBySource(f),C=this.buildEventUiBases(v.defs,b,S),w={dynamicOptionOverrides:s,currentViewType:o,currentDate:c,dateProfile:d,eventSources:f,eventStore:p,renderableEventStore:v,selectionConfig:y,eventUiBases:C,businessHours:this.parseContextBusinessHours(u),dateSelection:Sc(n.dateSelection,e),eventSelection:Ec(n.eventSelection,e),eventDrag:Cc(n.eventDrag,e),eventResize:Ac(n.eventResize,e)},x=Object.assign(Object.assign({},u),w);for(let _ of l.pluginHooks.reducers)Object.assign(w,_(n,e,x));let k=kt(n,u),M=kt(w,u);!k&&M?i.trigger("loading",!0):k&&!M&&i.trigger("loading",!1),this.state=w,r.onAction&&r.onAction(e)}updateData(){let{props:e,state:r}=this,n=this.data,i=this.computeOptionsData(e.optionOverrides,r.dynamicOptionOverrides,e.calendarApi),s=this.computeCurrentViewData(r.currentViewType,i,e.optionOverrides,r.dynamicOptionOverrides),l=this.data=Object.assign(Object.assign(Object.assign({nowManager:this.nowManager,viewTitle:this.buildTitle(r.dateProfile,s.options,i.dateEnv),calendarApi:e.calendarApi,dispatch:this.dispatch,emitter:this.emitter,getCurrentData:this.getCurrentData},i),s),r),o=i.pluginHooks.optionChangeHandlers,a=n&&n.calendarOptions,u=i.calendarOptions;if(a&&a!==u){a.timeZone!==u.timeZone&&(r.eventSources=l.eventSources=pc(l.eventSources,r.dateProfile,l),r.eventStore=l.eventStore=on(l.eventStore,n.dateEnv,l.dateEnv),r.renderableEventStore=l.renderableEventStore=on(l.renderableEventStore,n.dateEnv,l.dateEnv));for(let c in o)(this.optionsForHandling.indexOf(c)!==-1||a[c]!==u[c])&&o[c](u[c],l)}this.optionsForHandling=[],e.onData&&e.onData(l)}computeOptionsData(e,r,n){if(!this.optionsForRefining.length&&e===this.stableOptionOverrides&&r===this.stableDynamicOptionOverrides)return this.stableCalendarOptionsData;let{refinedOptions:i,pluginHooks:s,localeDefaults:l,availableLocaleData:o,extra:a}=this.processRawCalendarOptions(e,r);Rn(a);let u=this.buildDateEnv(i.timeZone,i.locale,i.weekNumberCalculation,i.firstDay,i.weekText,s,o,i.defaultRangeSeparator),c=this.buildViewSpecs(s.views,this.stableOptionOverrides,this.stableDynamicOptionOverrides,l),d=this.buildTheme(i,s),f=this.parseToolbars(i,this.stableOptionOverrides,d,c,n);return this.stableCalendarOptionsData={calendarOptions:i,pluginHooks:s,dateEnv:u,viewSpecs:c,theme:d,toolbarConfig:f,localeDefaults:l,availableRawLocales:o.map}}processRawCalendarOptions(e,r){let{locales:n,locale:i}=Ct([_e,e,r]),s=this.organizeRawLocales(n),l=s.map,o=this.buildLocale(i||s.defaultCode,l).options,a=this.buildPluginHooks(e.plugins||[],Fc),u=this.currentCalendarOptionsRefiners=Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},tn),rn),nn),a.listenerRefiners),a.optionRefiners),c={},d=Ct([_e,o,e,r]),f={},p=this.currentCalendarOptionsInput,m=this.currentCalendarOptionsRefined,v=!1;for(let b in d)this.optionsForRefining.indexOf(b)===-1&&(d[b]===p[b]||se[b]&&b in p&&se[b](p[b],d[b]))?f[b]=m[b]:u[b]?(f[b]=u[b](d[b]),v=!0):c[b]=p[b];return v&&(this.currentCalendarOptionsInput=d,this.currentCalendarOptionsRefined=f,this.stableOptionOverrides=e,this.stableDynamicOptionOverrides=r),this.optionsForHandling.push(...this.optionsForRefining),this.optionsForRefining=[],{rawOptions:this.currentCalendarOptionsInput,refinedOptions:this.currentCalendarOptionsRefined,pluginHooks:a,availableLocaleData:s,localeDefaults:o,extra:c}}_computeCurrentViewData(e,r,n,i){let s=r.viewSpecs[e];if(!s)throw new Error(`viewType "${e}" is not available. Please make sure you've loaded all neccessary plugins`);let{refinedOptions:l,extra:o}=this.processRawViewOptions(s,r.pluginHooks,r.localeDefaults,n,i);Rn(o),this.nowManager.handleInput(r.dateEnv,l.now);let a=this.buildDateProfileGenerator({dateProfileGeneratorClass:s.optionDefaults.dateProfileGeneratorClass,nowManager:this.nowManager,duration:s.duration,durationUnit:s.durationUnit,usesMinMaxTime:s.optionDefaults.usesMinMaxTime,dateEnv:r.dateEnv,calendarApi:this.props.calendarApi,slotMinTime:l.slotMinTime,slotMaxTime:l.slotMaxTime,showNonCurrentDates:l.showNonCurrentDates,dayCount:l.dayCount,dateAlignment:l.dateAlignment,dateIncrement:l.dateIncrement,hiddenDays:l.hiddenDays,weekends:l.weekends,validRangeInput:l.validRange,visibleRangeInput:l.visibleRange,fixedWeekCount:l.fixedWeekCount}),u=this.buildViewApi(e,this.getCurrentData,r.dateEnv);return{viewSpec:s,options:l,dateProfileGenerator:a,viewApi:u}}processRawViewOptions(e,r,n,i,s){let l=Ct([_e,e.optionDefaults,n,i,e.optionOverrides,s]),o=Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},tn),rn),nn),ql),r.listenerRefiners),r.optionRefiners),a={},u=this.currentViewOptionsInput,c=this.currentViewOptionsRefined,d=!1,f={};for(let p in l)l[p]===u[p]||se[p]&&se[p](l[p],u[p])?a[p]=c[p]:(l[p]===this.currentCalendarOptionsInput[p]||se[p]&&se[p](l[p],this.currentCalendarOptionsInput[p])?p in this.currentCalendarOptionsRefined&&(a[p]=this.currentCalendarOptionsRefined[p]):o[p]?a[p]=o[p](l[p]):f[p]=l[p],d=!0);return d&&(this.currentViewOptionsInput=l,this.currentViewOptionsRefined=a),{rawOptions:this.currentViewOptionsInput,refinedOptions:this.currentViewOptionsRefined,extra:f}}}function Qc(t,e,r,n,i,s,l,o){let a=ns(e||l.defaultCode,l.map);return new to({calendarSystem:"gregory",timeZone:t,namedTimeZoneImpl:s.namedTimeZonedImpl,locale:a,weekNumberCalculation:r,firstDay:n,weekText:i,cmdFormatter:s.cmdFormatter,defaultSeparator:o})}function Yc(t,e){let r=e.themeClasses[t.themeSystem]||ie;return new r(t)}function Zc(t){let e=t.dateProfileGeneratorClass||mo;return new e(t)}function $c(t,e,r){return new _c(t,e,r)}function Xc(t){return B(t,e=>e.ui)}function Jc(t,e,r){let n={"":e};for(let i in t){let s=t[i];s.sourceId&&r[s.sourceId]&&(n[i]=r[s.sourceId])}return n}function Kc(t){let{options:e}=t;return{eventUiSingleBase:Oe({display:e.eventDisplay,editable:e.editable,startEditable:e.eventStartEditable,durationEditable:e.eventDurationEditable,constraint:e.eventConstraint,overlap:typeof e.eventOverlap=="boolean"?e.eventOverlap:void 0,allow:e.eventAllow,backgroundColor:e.eventBackgroundColor,borderColor:e.eventBorderColor,textColor:e.eventTextColor,color:e.eventColor},t),selectionConfig:Oe({constraint:e.selectConstraint,overlap:typeof e.selectOverlap=="boolean"?e.selectOverlap:void 0,allow:e.selectAllow},t)}}function kt(t,e){for(let r of e.pluginHooks.isLoadingFuncs)if(r(t))return!0;return!1}function eu(t){return Ii(t.options.businessHours,t)}function Rn(t,e){for(let r in t)console.warn(`Unknown option '${r}'`)}class tu extends R{render(){let e=this.props.widgetGroups.map(r=>this.renderWidgetGroup(r));return h("div",{className:"fc-toolbar-chunk"},...e)}renderWidgetGroup(e){let{props:r}=this,{theme:n}=this.context,i=[],s=!0;for(let l of e){let{buttonName:o,buttonClick:a,buttonText:u,buttonIcon:c,buttonHint:d}=l;if(o==="title")s=!1,i.push(h("h2",{className:"fc-toolbar-title",id:r.titleId},r.title));else{let f=o===r.activeButton,p=!r.isTodayEnabled&&o==="today"||!r.isPrevEnabled&&o==="prev"||!r.isNextEnabled&&o==="next",m=[`fc-${o}-button`,n.getClass("button")];f&&m.push(n.getClass("buttonActive")),i.push(h("button",{type:"button",title:typeof d=="function"?d(r.navUnit):d,disabled:p,"aria-pressed":f,className:m.join(" "),onClick:a},u||(c?h("span",{className:c,role:"img"}):"")))}}if(i.length>1){let l=s&&n.getClass("buttonGroup")||"";return h("div",{className:l},...i)}return i[0]}}class wn extends R{render(){let{model:e,extraClassName:r}=this.props,n=!1,i,s,l=e.sectionWidgets,o=l.center;return l.left?(n=!0,i=l.left):i=l.start,l.right?(n=!0,s=l.right):s=l.end,h("div",{className:[r||"","fc-toolbar",n?"fc-toolbar-ltr":""].join(" ")},this.renderSection("start",i||[]),this.renderSection("center",o||[]),this.renderSection("end",s||[]))}renderSection(e,r){let{props:n}=this;return h(tu,{key:e,widgetGroups:r,title:n.title,navUnit:n.navUnit,activeButton:n.activeButton,isTodayEnabled:n.isTodayEnabled,isPrevEnabled:n.isPrevEnabled,isNextEnabled:n.isNextEnabled,titleId:n.titleId})}}class ru extends R{constructor(){super(...arguments),this.state={availableWidth:null},this.handleEl=e=>{this.el=e,Z(this.props.elRef,e),this.updateAvailableWidth()},this.handleResize=()=>{this.updateAvailableWidth()}}render(){let{props:e,state:r}=this,{aspectRatio:n}=e,i=["fc-view-harness",n||e.liquid||e.height?"fc-view-harness-active":"fc-view-harness-passive"],s="",l="";return n?r.availableWidth!==null?s=r.availableWidth/n:l=`${1/n*100}%`:s=e.height||"",h("div",{"aria-labelledby":e.labeledById,ref:this.handleEl,className:i.join(" "),style:{height:s,paddingBottom:l}},e.children)}componentDidMount(){this.context.addResizeHandler(this.handleResize)}componentWillUnmount(){this.context.removeResizeHandler(this.handleResize)}updateAvailableWidth(){this.el&&this.props.aspectRatio&&this.setState({availableWidth:this.el.offsetWidth})}}class nu extends Hi{constructor(e){super(e),this.handleSegClick=(r,n)=>{let{component:i}=this,{context:s}=i,l=Ut(n);if(l&&i.isValidSegDownEl(r.target)){let o=V(r.target,".fc-event-forced-url"),a=o?o.querySelector("a[href]").href:"";s.emitter.trigger("eventClick",{el:n,event:new O(i.context,l.eventRange.def,l.eventRange.instance),jsEvent:r,view:s.viewApi}),a&&!r.defaultPrevented&&(window.location.href=a)}},this.destroy=ii(e.el,"click",".fc-event",this.handleSegClick)}}class iu extends Hi{constructor(e){super(e),this.handleEventElRemove=r=>{r===this.currentSegEl&&this.handleSegLeave(null,this.currentSegEl)},this.handleSegEnter=(r,n)=>{Ut(n)&&(this.currentSegEl=n,this.triggerEvent("eventMouseEnter",r,n))},this.handleSegLeave=(r,n)=>{this.currentSegEl&&(this.currentSegEl=null,this.triggerEvent("eventMouseLeave",r,n))},this.removeHoverListeners=al(e.el,".fc-event",this.handleSegEnter,this.handleSegLeave)}destroy(){this.removeHoverListeners()}triggerEvent(e,r,n){let{component:i}=this,{context:s}=i,l=Ut(n);(!r||i.isValidSegDownEl(r.target))&&s.emitter.trigger(e,{el:n,event:new O(s,l.eventRange.def,l.eventRange.instance),jsEvent:r,view:s.viewApi})}}class su extends ge{constructor(){super(...arguments),this.buildViewContext=A(io),this.buildViewPropTransformers=A(ou),this.buildToolbarProps=A(lu),this.headerRef=N(),this.footerRef=N(),this.interactionsStore={},this.state={viewLabelId:Zt()},this.registerInteractiveComponent=(e,r)=>{let n=oa(e,r),l=[nu,iu].concat(this.props.pluginHooks.componentInteractions).map(o=>new o(n));this.interactionsStore[e.uid]=l,fn[e.uid]=n},this.unregisterInteractiveComponent=e=>{let r=this.interactionsStore[e.uid];if(r){for(let n of r)n.destroy();delete this.interactionsStore[e.uid]}delete fn[e.uid]},this.resizeRunner=new Te(()=>{this.props.emitter.trigger("_resize",!0),this.props.emitter.trigger("windowResize",{view:this.props.viewApi})}),this.handleWindowResize=e=>{let{options:r}=this.props;r.handleWindowResize&&e.target===window&&this.resizeRunner.request(r.windowResizeDelay)}}render(){let{props:e}=this,{toolbarConfig:r,options:n}=e,i=!1,s="",l;e.isHeightAuto||e.forPrint?s="":n.height!=null?i=!0:n.contentHeight!=null?s=n.contentHeight:l=Math.max(n.aspectRatio,.5);let o=this.buildViewContext(e.viewSpec,e.viewApi,e.options,e.dateProfileGenerator,e.dateEnv,e.nowManager,e.theme,e.pluginHooks,e.dispatch,e.getCurrentData,e.emitter,e.calendarApi,this.registerInteractiveComponent,this.unregisterInteractiveComponent),a=r.header&&r.header.hasTitle?this.state.viewLabelId:void 0;return h(pe.Provider,{value:o},h(Pe,{unit:"day"},u=>{let c=this.buildToolbarProps(e.viewSpec,e.dateProfile,e.dateProfileGenerator,e.currentDate,u,e.viewTitle);return h(I,null,r.header&&h(wn,Object.assign({ref:this.headerRef,extraClassName:"fc-header-toolbar",model:r.header,titleId:a},c)),h(ru,{liquid:i,height:s,aspectRatio:l,labeledById:a},this.renderView(e),this.buildAppendContent()),r.footer&&h(wn,Object.assign({ref:this.footerRef,extraClassName:"fc-footer-toolbar",model:r.footer,titleId:""},c)))}))}componentDidMount(){let{props:e}=this;this.calendarInteractions=e.pluginHooks.calendarInteractions.map(n=>new n(e)),window.addEventListener("resize",this.handleWindowResize);let{propSetHandlers:r}=e.pluginHooks;for(let n in r)r[n](e[n],e)}componentDidUpdate(e){let{props:r}=this,{propSetHandlers:n}=r.pluginHooks;for(let i in n)r[i]!==e[i]&&n[i](r[i],r)}componentWillUnmount(){window.removeEventListener("resize",this.handleWindowResize),this.resizeRunner.clear();for(let e of this.calendarInteractions)e.destroy();this.props.emitter.trigger("_unmount")}buildAppendContent(){let{props:e}=this,r=e.pluginHooks.viewContainerAppends.map(n=>n(e));return h(I,{},...r)}renderView(e){let{pluginHooks:r}=e,{viewSpec:n}=e,i={dateProfile:e.dateProfile,businessHours:e.businessHours,eventStore:e.renderableEventStore,eventUiBases:e.eventUiBases,dateSelection:e.dateSelection,eventSelection:e.eventSelection,eventDrag:e.eventDrag,eventResize:e.eventResize,isHeightAuto:e.isHeightAuto,forPrint:e.forPrint},s=this.buildViewPropTransformers(r.viewPropsTransformers);for(let o of s)Object.assign(i,o.transform(i,e));let l=n.component;return h(l,Object.assign({},i))}}function lu(t,e,r,n,i,s){let l=r.build(i,void 0,!1),o=r.buildPrev(e,n,!1),a=r.buildNext(e,n,!1);return{title:s,activeButton:t.type,navUnit:t.singleUnit,isTodayEnabled:l.isValid&&!J(e.currentRange,i),isPrevEnabled:o.isValid,isNextEnabled:a.isValid}}function ou(t){return t.map(e=>new e)}class au extends Ae{constructor(e,r={}){super(),this.isRendering=!1,this.isRendered=!1,this.currentClassNames=[],this.customContentRenderId=0,this.handleAction=n=>{switch(n.type){case"SET_EVENT_DRAG":case"SET_EVENT_RESIZE":this.renderRunner.tryDrain()}},this.handleData=n=>{this.currentData=n,this.renderRunner.request(n.calendarOptions.rerenderDelay)},this.handleRenderRequest=()=>{if(this.isRendering){this.isRendered=!0;let{currentData:n}=this;nt(()=>{Ie(h(la,{options:n.calendarOptions,theme:n.theme,emitter:n.emitter},(i,s,l,o)=>(this.setClassNames(i),this.setHeight(s),h(mi.Provider,{value:this.customContentRenderId},h(su,Object.assign({isHeightAuto:l,forPrint:o},n))))),this.el)})}else this.isRendered&&(this.isRendered=!1,Ie(null,this.el),this.setClassNames([]),this.setHeight(""))},Ks(e),this.el=e,this.renderRunner=new Te(this.handleRenderRequest),new qc({optionOverrides:r,calendarApi:this,onAction:this.handleAction,onData:this.handleData})}render(){let e=this.isRendering;e?this.customContentRenderId+=1:this.isRendering=!0,this.renderRunner.request(),e&&this.updateSize()}destroy(){this.isRendering&&(this.isRendering=!1,this.renderRunner.request())}updateSize(){nt(()=>{super.updateSize()})}batchRendering(e){this.renderRunner.pause("batchRendering"),e(),this.renderRunner.resume("batchRendering")}pauseRendering(){this.renderRunner.pause("pauseRendering")}resumeRendering(){this.renderRunner.resume("pauseRendering",!0)}resetOptions(e,r){this.currentDataManager.resetOptions(e,r)}setClassNames(e){if(!F(e,this.currentClassNames)){let{classList:r}=this.el;for(let n of this.currentClassNames)r.remove(n);for(let n of e)r.add(n);this.currentClassNames=e}}setHeight(e){ni(this.el,"height",e)}}const cu="https://fullcalendar.io/docs/schedulerLicenseKey#invalid",uu="https://fullcalendar.io/docs/schedulerLicenseKey#outdated",du=["GPL-My-Project-Is-Open-Source","CC-Attribution-NonCommercial-NoDerivatives"],fu={position:"absolute",zIndex:99999,bottom:"1px",left:"1px",background:"#eee",borderColor:"#ddd",borderStyle:"solid",borderWidth:"1px 1px 0 0",padding:"2px 4px",fontSize:"12px",borderTopRightRadius:"3px"};function hu(t){let e=t.options.schedulerLicenseKey,r=typeof window<"u"?window.location.href:"";if(!gu(r)){let n=pu(e,t.pluginHooks.premiumReleaseDate);if(n!=="valid")return h("div",{className:"fc-license-message",style:fu},n==="outdated"?h(I,null,"Your license key is too old to work with this version. ",h("a",{href:uu},"More Info")):h(I,null,"Your license key is invalid. ",h("a",{href:cu},"More Info")))}return null}function pu(t,e){if(du.indexOf(t)!==-1)return"valid";const r=(t||"").match(/^(\d+)-fcs-(\d+)$/);if(r&&r[1].length===10){const n=new Date(parseInt(r[2],10)*1e3),i=he.mockSchedulerReleaseDate||e;if(Xt(i))return j(i,-372)<n?"valid":"outdated"}return"invalid"}function gu(t){return/\w+:\/\/fullcalendar\.io\/|\/examples\/[\w-]+\.html$/.test(t)}const mu={schedulerLicenseKey:String};var vr=X({name:"@fullcalendar/premium-common",premiumReleaseDate:"2025-04-02",optionRefiners:mu,viewContainerAppends:[hu]});function vu(t){let e=t.getBoundingClientRect(),r=Ui(t);return{left:e.left+r.borderLeft+r.scrollbarLeft-bu(t),top:e.top+r.borderTop-t.scrollTop}}function bu(t){let e=t.scrollLeft;if(window.getComputedStyle(t).direction==="rtl")switch(us()){case"negative":e*=-1;case"reverse":e=t.scrollWidth-e-t.clientWidth}return e}function yu(t,e){if(window.getComputedStyle(t).direction==="rtl")switch(us()){case"reverse":e=t.scrollWidth-e;break;case"negative":e=-(t.scrollWidth-e);break}t.scrollLeft=e}let _n;function us(){return _n||(_n=Su())}function Su(){let t=document.createElement("div");t.style.position="absolute",t.style.top="-1000px",t.style.width="100px",t.style.height="100px",t.style.overflow="scroll",t.style.direction="rtl";let e=document.createElement("div");e.style.width="200px",e.style.height="200px",t.appendChild(e),document.body.appendChild(t);let r;return t.scrollLeft>0?r="positive":(t.scrollLeft=1,t.scrollLeft>0?r="reverse":r="negative"),Qt(t),r}const Eu=".fc-sticky";class Cu{constructor(e,r){this.scrollEl=e,this.isRtl=r,this.updateSize=()=>{let{scrollEl:n}=this,i=Ce(n,Eu),s=this.queryElGeoms(i),l=n.clientWidth;Au(i,s,l)}}queryElGeoms(e){let{scrollEl:r,isRtl:n}=this,i=vu(r),s=[];for(let l of e){let o=hn(va(l.parentNode,!0),-i.left,-i.top),a=l.getBoundingClientRect(),u=window.getComputedStyle(l),c=window.getComputedStyle(l.parentNode).textAlign,d=null;c==="start"?c=n?"right":"left":c==="end"&&(c=n?"left":"right"),u.position!=="sticky"&&(d=hn(a,-i.left-(parseFloat(u.left)||0),-i.top-(parseFloat(u.top)||0))),s.push({parentBound:o,naturalBound:d,elWidth:a.width,elHeight:a.height,textAlign:c})}return s}}function Au(t,e,r){t.forEach((n,i)=>{let{textAlign:s,elWidth:l,parentBound:o}=e[i],a=o.right-o.left,u;s==="center"&&a>r?u=(r-l)/2:u="",Yt(n,{left:u,right:u,top:0})})}class Ru extends R{constructor(){super(...arguments),this.elRef=N(),this.state={xScrollbarWidth:0,yScrollbarWidth:0},this.handleScroller=e=>{this.scroller=e,Z(this.props.scrollerRef,e)},this.handleSizing=()=>{let{props:e}=this;e.overflowY==="scroll-hidden"&&this.setState({yScrollbarWidth:this.scroller.getYScrollbarWidth()}),e.overflowX==="scroll-hidden"&&this.setState({xScrollbarWidth:this.scroller.getXScrollbarWidth()})}}render(){let{props:e,state:r,context:n}=this,i=n.isRtl&&lt(),s=0,l=0,o=0,{overflowX:a,overflowY:u}=e;return e.forPrint&&(a="visible",u="visible"),a==="scroll-hidden"&&(o=r.xScrollbarWidth),u==="scroll-hidden"&&r.yScrollbarWidth!=null&&(i?s=r.yScrollbarWidth:l=r.yScrollbarWidth),h("div",{ref:this.elRef,className:"fc-scroller-harness"+(e.liquid?" fc-scroller-harness-liquid":"")},h(Fi,{ref:this.handleScroller,elRef:this.props.scrollerElRef,overflowX:a==="scroll-hidden"?"scroll":a,overflowY:u==="scroll-hidden"?"scroll":u,overcomeLeft:s,overcomeRight:l,overcomeBottom:o,maxHeight:typeof e.maxHeight=="number"?e.maxHeight+(a==="scroll-hidden"?r.xScrollbarWidth:0):"",liquid:e.liquid,liquidIsAbsolute:!0},e.children))}componentDidMount(){this.handleSizing(),this.context.addResizeHandler(this.handleSizing)}getSnapshotBeforeUpdate(e){return this.props.forPrint&&!e.forPrint?{simulateScrollLeft:this.scroller.el.scrollLeft}:{}}componentDidUpdate(e,r,n){const{props:i,scroller:{el:s}}=this;if(L(e,i)||this.handleSizing(),n.simulateScrollLeft!==void 0)s.style.left=-n.simulateScrollLeft+"px";else if(!i.forPrint&&e.forPrint){const l=-parseInt(s.style.left);s.style.left="",s.scrollLeft=l}}componentWillUnmount(){this.context.removeResizeHandler(this.handleSizing)}needsXScrolling(){return this.scroller.needsXScrolling()}needsYScrolling(){return this.scroller.needsYScrolling()}}const Dn="wheel mousewheel DomMouseScroll MozMousePixelScroll".split(" ");class wu{constructor(e){this.el=e,this.emitter=new Di,this.isScrolling=!1,this.isTouching=!1,this.isRecentlyWheeled=!1,this.isRecentlyScrolled=!1,this.wheelWaiter=new Te(this._handleWheelWaited.bind(this)),this.scrollWaiter=new Te(this._handleScrollWaited.bind(this)),this.handleScroll=()=>{this.startScroll(),this.emitter.trigger("scroll",this.isRecentlyWheeled,this.isTouching),this.isRecentlyScrolled=!0,this.scrollWaiter.request(500)},this.handleWheel=()=>{this.isRecentlyWheeled=!0,this.wheelWaiter.request(500)},this.handleTouchStart=()=>{this.isTouching=!0},this.handleTouchEnd=()=>{this.isTouching=!1,this.isRecentlyScrolled||this.endScroll()},e.addEventListener("scroll",this.handleScroll),e.addEventListener("touchstart",this.handleTouchStart,{passive:!0}),e.addEventListener("touchend",this.handleTouchEnd);for(let r of Dn)e.addEventListener(r,this.handleWheel)}destroy(){let{el:e}=this;e.removeEventListener("scroll",this.handleScroll),e.removeEventListener("touchstart",this.handleTouchStart,{passive:!0}),e.removeEventListener("touchend",this.handleTouchEnd);for(let r of Dn)e.removeEventListener(r,this.handleWheel)}startScroll(){this.isScrolling||(this.isScrolling=!0,this.emitter.trigger("scrollStart",this.isRecentlyWheeled,this.isTouching))}endScroll(){this.isScrolling&&(this.emitter.trigger("scrollEnd"),this.isScrolling=!1,this.isRecentlyScrolled=!0,this.isRecentlyWheeled=!1,this.scrollWaiter.clear(),this.wheelWaiter.clear())}_handleScrollWaited(){this.isRecentlyScrolled=!1,this.isTouching||this.endScroll()}_handleWheelWaited(){this.isRecentlyWheeled=!1}}class _u{constructor(e,r){this.isVertical=e,this.scrollEls=r,this.isPaused=!1,this.scrollListeners=r.map(n=>this.bindScroller(n))}destroy(){for(let e of this.scrollListeners)e.destroy()}bindScroller(e){let{scrollEls:r,isVertical:n}=this,i=new wu(e);const s=(o,a)=>{if(!this.isPaused&&((!this.masterEl||this.masterEl!==e&&(o||a))&&this.assignMaster(e),this.masterEl===e))for(let u of r)u!==e&&(n?u.scrollTop=e.scrollTop:u.scrollLeft=e.scrollLeft)},l=()=>{this.masterEl===e&&(this.masterEl=null)};return i.emitter.on("scroll",s),i.emitter.on("scrollEnd",l),i}assignMaster(e){this.masterEl=e;for(let r of this.scrollListeners)r.el!==e&&r.endScroll()}forceScrollLeft(e){this.isPaused=!0;for(let r of this.scrollListeners)yu(r.el,e);this.isPaused=!1}forceScrollTop(e){this.isPaused=!0;for(let r of this.scrollListeners)r.el.scrollTop=e;this.isPaused=!1}}he.SCROLLGRID_RESIZE_INTERVAL=500;class br extends R{constructor(){super(...arguments),this.compileColGroupStats=St(ku,xu),this.renderMicroColGroups=St(Qi),this.clippedScrollerRefs=new Q,this.scrollerElRefs=new Q(this._handleScrollerEl.bind(this)),this.chunkElRefs=new Q(this._handleChunkEl.bind(this)),this.scrollSyncersBySection={},this.scrollSyncersByColumn={},this.rowUnstableMap=new Map,this.rowInnerMaxHeightMap=new Map,this.anyRowHeightsChanged=!1,this.recentSizingCnt=0,this.state={shrinkWidths:[],forceYScrollbars:!1,forceXScrollbars:!1,scrollerClientWidths:{},scrollerClientHeights:{},sectionRowMaxHeights:[]},this.handleSizing=(e,r)=>{if(!this.allowSizing())return;r||(this.anyRowHeightsChanged=!0);let n={};(e||!r&&!this.rowUnstableMap.size)&&(n.sectionRowMaxHeights=this.computeSectionRowMaxHeights()),this.setState(Object.assign(Object.assign({shrinkWidths:this.computeShrinkWidths()},this.computeScrollerDims()),n),()=>{this.rowUnstableMap.size||this.updateStickyScrolling()})},this.handleRowHeightChange=(e,r)=>{let{rowUnstableMap:n,rowInnerMaxHeightMap:i}=this;if(!r)n.set(e,!0);else{n.delete(e);let s=In(e);(!i.has(e)||i.get(e)!==s)&&(i.set(e,s),this.anyRowHeightsChanged=!0),!n.size&&this.anyRowHeightsChanged&&(this.anyRowHeightsChanged=!1,this.setState({sectionRowMaxHeights:this.computeSectionRowMaxHeights()}))}}}render(){let{props:e,state:r,context:n}=this,{shrinkWidths:i}=r,s=this.compileColGroupStats(e.colGroups.map(y=>[y])),l=this.renderMicroColGroups(s.map((y,S)=>[y.cols,i[S]])),o=$i(e.liquid,n);this.getDims();let a=e.sections,u=a.length,c=0,d,f=[],p=[],m=[];for(;c<u&&(d=a[c]).type==="header";)f.push(this.renderSection(d,c,s,l,r.sectionRowMaxHeights,!0)),c+=1;for(;c<u&&(d=a[c]).type==="body";)p.push(this.renderSection(d,c,s,l,r.sectionRowMaxHeights,!1)),c+=1;for(;c<u&&(d=a[c]).type==="footer";)m.push(this.renderSection(d,c,s,l,r.sectionRowMaxHeights,!0)),c+=1;const v=!ur(),b={role:"rowgroup"};return h("table",{ref:e.elRef,role:"grid",className:o.join(" ")},Tu(s,i),!!(!v&&f.length)&&h("thead",b,...f),!!(!v&&p.length)&&h("tbody",b,...p),!!(!v&&m.length)&&h("tfoot",b,...m),v&&h("tbody",b,...f,...p,...m))}renderSection(e,r,n,i,s,l){return"outerContent"in e?h(I,{key:e.key},e.outerContent):h("tr",{key:e.key,role:"presentation",className:Xi(e,this.props.liquid).join(" ")},e.chunks.map((o,a)=>this.renderChunk(e,r,n[a],i[a],o,a,(s[r]||[])[a]||[],l)))}renderChunk(e,r,n,i,s,l,o,a){if("outerContent"in s)return h(I,{key:s.key},s.outerContent);let{state:u}=this,{scrollerClientWidths:c,scrollerClientHeights:d}=u,[f,p]=this.getDims(),m=r*p+l,v=!this.context.isRtl||lt()?p-1:0,b=l===v,y=r===f-1,S=y&&u.forceXScrollbars,C=b&&u.forceYScrollbars,w=n&&n.allowXScrolling,x=Vi(this.props,e),k=hr(this.props,e),M=e.expandRows&&k,_=n&&n.totalColMinWidth||"",G=Gi(e,s,{tableColGroupNode:i,tableMinWidth:_,clientWidth:c[m]!==void 0?c[m]:null,clientHeight:d[m]!==void 0?d[m]:null,expandRows:M,syncRowHeights:!!e.syncRowHeights,rowSyncHeights:o,reportRowHeightChange:this.handleRowHeightChange},a),Re=S?y?"scroll":"scroll-hidden":w?y?"auto":"scroll-hidden":"hidden",xs=C?b?"scroll":"scroll-hidden":x?b?"auto":"scroll-hidden":"hidden";return G=h(Ru,{ref:this.clippedScrollerRefs.createRef(m),scrollerElRef:this.scrollerElRefs.createRef(m),overflowX:Re,overflowY:xs,forPrint:this.props.forPrint,liquid:k,maxHeight:e.maxHeight},G),h(a?"th":"td",{key:s.key,ref:this.chunkElRefs.createRef(m),role:"presentation"},G)}componentDidMount(){this.getStickyScrolling=St(Ou),this.getScrollSyncersBySection=Jr(kn.bind(this,!0),null,Ve),this.getScrollSyncersByColumn=Jr(kn.bind(this,!1),null,Ve),this.updateScrollSyncers(),this.handleSizing(!1),this.context.addResizeHandler(this.handleSizing)}componentDidUpdate(e,r){this.updateScrollSyncers(),this.handleSizing(!1,r.sectionRowMaxHeights!==this.state.sectionRowMaxHeights)}componentWillUnmount(){this.context.removeResizeHandler(this.handleSizing),this.destroyScrollSyncers()}allowSizing(){let e=new Date;return!this.lastSizingDate||e.valueOf()>this.lastSizingDate.valueOf()+he.SCROLLGRID_RESIZE_INTERVAL?(this.lastSizingDate=e,this.recentSizingCnt=0,!0):(this.recentSizingCnt+=1)<=10}computeShrinkWidths(){let e=this.compileColGroupStats(this.props.colGroups.map(l=>[l])),[r,n]=this.getDims(),i=r*n,s=[];return e.forEach((l,o)=>{if(l.hasShrinkCol){let a=this.chunkElRefs.collect(o,i,n);s[o]=ji(a)}}),s}computeSectionRowMaxHeights(){let e=new Map,[r,n]=this.getDims(),i=[];for(let s=0;s<r;s+=1){let l=this.props.sections[s],o=[];if(l&&l.syncRowHeights){let a=[];for(let d=0;d<n;d+=1){let f=s*n+d,p=[],m=this.chunkElRefs.currentMap[f];m?p=Ce(m,".fc-scrollgrid-sync-table tr").map(v=>{let b=In(v);return e.set(v,b),b}):p=[],a.push(p)}let u=a[0].length,c=!0;for(let d=1;d<n;d+=1)if(!(l.chunks[d]&&l.chunks[d].outerContent!==void 0)&&a[d].length!==u){c=!1;break}if(c){for(let d=0;d<n;d+=1)o.push([]);for(let d=0;d<u;d+=1){let f=[];for(let m=0;m<n;m+=1){let v=a[m][d];v!=null&&f.push(v)}let p=Math.max(...f);for(let m=0;m<n;m+=1)o[m].push(p)}}else{let d=[];for(let p=0;p<n;p+=1)d.push(Du(a[p])+a[p].length);let f=Math.max(...d);for(let p=0;p<n;p+=1){let m=a[p].length,v=f-m,b=Math.floor(v/m),y=v-b*(m-1),S=[],C=0;for(C<m&&(S.push(y),C+=1);C<m;)S.push(b),C+=1;o.push(S)}}}i.push(o)}return this.rowInnerMaxHeightMap=e,i}computeScrollerDims(){let e=Bi(),[r,n]=this.getDims(),i=!this.context.isRtl||lt()?n-1:0,s=r-1,l=this.clippedScrollerRefs.currentMap,o=this.scrollerElRefs.currentMap,a=!1,u=!1,c={},d={};for(let f=0;f<r;f+=1){let p=f*n+i,m=l[p];if(m&&m.needsYScrolling()){a=!0;break}}for(let f=0;f<n;f+=1){let p=s*n+f,m=l[p];if(m&&m.needsXScrolling()){u=!0;break}}for(let f=0;f<r;f+=1)for(let p=0;p<n;p+=1){let m=f*n+p,v=o[m];if(v){let b=v.parentNode;c[m]=Math.floor(b.getBoundingClientRect().width-(p===i&&a?e.y:0)),d[m]=Math.floor(b.getBoundingClientRect().height-(f===s&&u?e.x:0))}}return{forceYScrollbars:a,forceXScrollbars:u,scrollerClientWidths:c,scrollerClientHeights:d}}updateStickyScrolling(){let{isRtl:e}=this.context,r=this.scrollerElRefs.getAll().map(n=>[n,e]);this.getStickyScrolling(r).forEach(n=>n.updateSize())}updateScrollSyncers(){let[e,r]=this.getDims(),n=e*r,i={},s={},l=this.scrollerElRefs.currentMap;for(let o=0;o<e;o+=1){let a=o*r,u=a+r;i[o]=fi(l,a,u,1)}for(let o=0;o<r;o+=1)s[o]=this.scrollerElRefs.collect(o,n,r);this.scrollSyncersBySection=this.getScrollSyncersBySection(i),this.scrollSyncersByColumn=this.getScrollSyncersByColumn(s)}destroyScrollSyncers(){B(this.scrollSyncersBySection,Ve),B(this.scrollSyncersByColumn,Ve)}getChunkConfigByIndex(e){let r=this.getDims()[1],n=Math.floor(e/r),i=e%r,s=this.props.sections[n];return s&&s.chunks[i]}forceScrollLeft(e,r){let n=this.scrollSyncersByColumn[e];n&&n.forceScrollLeft(r)}forceScrollTop(e,r){let n=this.scrollSyncersBySection[e];n&&n.forceScrollTop(r)}_handleChunkEl(e,r){let n=this.getChunkConfigByIndex(parseInt(r,10));n&&Z(n.elRef,e)}_handleScrollerEl(e,r){let n=this.getChunkConfigByIndex(parseInt(r,10));n&&Z(n.scrollerElRef,e)}getDims(){let e=this.props.sections.length,r=e?this.props.sections[0].chunks.length:0;return[e,r]}}br.addStateEquality({shrinkWidths:F,scrollerClientWidths:L,scrollerClientHeights:L});function Du(t){let e=0;for(let r of t)e+=r;return e}function In(t){let e=Ce(t,".fc-scrollgrid-sync-inner").map(Iu);return e.length?Math.max(...e):0}function Iu(t){return t.offsetHeight}function Tu(t,e){let r=t.map((n,i)=>{let s=n.width;return s==="shrink"&&(s=n.totalColWidth+Yi(e[i])+1),h("col",{style:{width:s}})});return h("colgroup",{},...r)}function ku(t){let e=Tn(t.cols,"width"),r=Tn(t.cols,"minWidth"),n=Zi(t.cols),i=t.width!=="shrink"&&!!(e||r||n);return{hasShrinkCol:n,totalColWidth:e,totalColMinWidth:r,allowXScrolling:i,cols:t.cols,width:t.width}}function Tn(t,e){let r=0;for(let n of t){let i=n[e];typeof i=="number"&&(r+=i*(n.span||1))}return r}const Mu={cols:qi};function xu(t,e){return Qe(t,e,Mu)}function kn(t,...e){return new _u(t,e)}function Ve(t){t.destroy()}function Ou(t,e){return new Cu(t,e)}var Nu='.fc .fc-timeline-body{min-height:100%;position:relative;z-index:1}.fc .fc-timeline-slots{bottom:0;position:absolute;top:0;z-index:1}.fc .fc-timeline-slots>table{height:100%}.fc .fc-timeline-slot-minor{border-style:dotted}.fc .fc-timeline-slot-frame{align-items:center;display:flex;justify-content:center}.fc .fc-timeline-header-row-chrono .fc-timeline-slot-frame{justify-content:flex-start}.fc .fc-timeline-header-row:last-child .fc-timeline-slot-frame{overflow:hidden}.fc .fc-timeline-slot-cushion{padding:4px 5px;white-space:nowrap}.fc-direction-ltr .fc-timeline-slot{border-right:0!important}.fc-direction-rtl .fc-timeline-slot{border-left:0!important}.fc .fc-timeline-now-indicator-container{bottom:0;left:0;position:absolute;right:0;top:0;width:0;z-index:4}.fc .fc-timeline-now-indicator-arrow,.fc .fc-timeline-now-indicator-line{border-color:var(--fc-now-indicator-color);border-style:solid;pointer-events:none;position:absolute;top:0}.fc .fc-timeline-now-indicator-arrow{border-left-color:transparent;border-right-color:transparent;border-width:6px 5px 0;margin:0 -6px}.fc .fc-timeline-now-indicator-line{border-width:0 0 0 1px;bottom:0;margin:0 -1px}.fc .fc-timeline-events{position:relative;width:0;z-index:3}.fc .fc-timeline-event-harness,.fc .fc-timeline-more-link{position:absolute;top:0}.fc-timeline-event{z-index:1}.fc-timeline-event.fc-event-mirror{z-index:2}.fc-timeline-event{align-items:center;border-radius:0;display:flex;font-size:var(--fc-small-font-size);margin-bottom:1px;padding:2px 1px;position:relative}.fc-timeline-event .fc-event-main{flex-grow:1;flex-shrink:1;min-width:0}.fc-timeline-event .fc-event-time{font-weight:700}.fc-timeline-event .fc-event-time,.fc-timeline-event .fc-event-title{padding:0 2px;white-space:nowrap}.fc-direction-ltr .fc-timeline-event.fc-event-end,.fc-direction-ltr .fc-timeline-more-link{margin-right:1px}.fc-direction-rtl .fc-timeline-event.fc-event-end,.fc-direction-rtl .fc-timeline-more-link{margin-left:1px}.fc-timeline-overlap-disabled .fc-timeline-event{margin-bottom:0;padding-bottom:5px;padding-top:5px}.fc-timeline-event:not(.fc-event-end):after,.fc-timeline-event:not(.fc-event-start):before{border-color:transparent #000;border-style:solid;border-width:5px;content:"";flex-grow:0;flex-shrink:0;height:0;margin:0 1px;opacity:.5;width:0}.fc-direction-ltr .fc-timeline-event:not(.fc-event-start):before,.fc-direction-rtl .fc-timeline-event:not(.fc-event-end):after{border-left:0}.fc-direction-ltr .fc-timeline-event:not(.fc-event-end):after,.fc-direction-rtl .fc-timeline-event:not(.fc-event-start):before{border-right:0}.fc-timeline-more-link{background:var(--fc-more-link-bg-color);color:var(--fc-more-link-text-color);cursor:pointer;font-size:var(--fc-small-font-size);padding:1px}.fc-timeline-more-link-inner{display:inline-block;left:0;padding:2px;right:0}.fc .fc-timeline-bg{bottom:0;left:0;position:absolute;right:0;top:0;width:0;z-index:2}.fc .fc-timeline-bg .fc-non-business{z-index:1}.fc .fc-timeline-bg .fc-bg-event{z-index:2}.fc .fc-timeline-bg .fc-highlight{z-index:3}.fc .fc-timeline-bg-harness{bottom:0;position:absolute;top:0}';qt(Nu);const Hu=18,ot=6,Pu=200;he.MAX_TIMELINE_SLOTS=1e3;const jt=[{years:1},{months:1},{days:1},{hours:1},{minutes:30},{minutes:15},{minutes:10},{minutes:5},{minutes:1},{seconds:30},{seconds:15},{seconds:10},{seconds:5},{seconds:1},{milliseconds:500},{milliseconds:100},{milliseconds:10},{milliseconds:1}];function ds(t,e,r,n){let i={labelInterval:r.slotLabelInterval,slotDuration:r.slotDuration};Lu(i,t,e),fs(i,t,e),Uu(i,t,e);let s=r.slotLabelFormat,l=Array.isArray(s)?s:s!=null?[s]:Wu(i,t,e,r);i.headerFormats=l.map(w=>P(w)),i.isTimeScale=!!i.slotDuration.milliseconds;let o=null;if(!i.isTimeScale){const w=ne(i.slotDuration).unit;/year|month|week/.test(w)&&(o=w)}i.largeUnit=o,i.emphasizeWeeks=ci(i.slotDuration)===1&&ve("weeks",t,e)>=2&&!r.businessHours;let a=r.snapDuration,u,c;a&&(u=D(a),c=ke(i.slotDuration,u)),c==null&&(u=i.slotDuration,c=1),i.snapDuration=u,i.snapsPerSlot=c;let d=Y(t.slotMaxTime)-Y(t.slotMinTime),f=Mn(t.renderRange.start,i,e),p=Mn(t.renderRange.end,i,e);i.isTimeScale&&(f=e.add(f,t.slotMinTime),p=e.add(j(p,-1),t.slotMaxTime)),i.timeWindowMs=d,i.normalizedRange={start:f,end:p};let m=[],v=f;for(;v<p;)at(v,i,t,n)&&m.push(v),v=e.add(v,i.slotDuration);i.slotDates=m;let b=-1,y=0;const S=[],C=[];for(v=f;v<p;)at(v,i,t,n)?(b+=1,S.push(b),C.push(y)):S.push(b+.5),v=e.add(v,i.snapDuration),y+=1;return i.snapDiffToIndex=S,i.snapIndexToDiff=C,i.snapCnt=b+1,i.slotCnt=i.snapCnt/i.snapsPerSlot,i.isWeekStarts=zu(i,e),i.cellRows=Fu(i,e),i.slotsPerLabel=ke(i.labelInterval,i.slotDuration),i}function Mn(t,e,r){let n=t;return e.isTimeScale||(n=T(n),e.largeUnit&&(n=r.startOf(n,e.largeUnit))),n}function Bu(t,e,r){if(!e.isTimeScale&&(t=nr(t),e.largeUnit)){let n=t;t={start:r.startOf(t.start,e.largeUnit),end:r.startOf(t.end,e.largeUnit)},(t.end.valueOf()!==n.end.valueOf()||t.end<=t.start)&&(t={start:t.start,end:r.add(t.end,e.slotDuration)})}return t}function at(t,e,r,n){if(n.isHiddenDay(t))return!1;if(e.isTimeScale){let i=T(t),l=t.valueOf()-i.valueOf()-Y(r.slotMinTime);return l=(l%864e5+864e5)%864e5,l<e.timeWindowMs}return!0}function Lu(t,e,r){const{currentRange:n}=e;if(t.labelInterval&&r.countDurationsBetween(n.start,n.end,t.labelInterval)>he.MAX_TIMELINE_SLOTS&&(console.warn("slotLabelInterval results in too many cells"),t.labelInterval=null),t.slotDuration&&r.countDurationsBetween(n.start,n.end,t.slotDuration)>he.MAX_TIMELINE_SLOTS&&(console.warn("slotDuration results in too many cells"),t.slotDuration=null),t.labelInterval&&t.slotDuration){const i=ke(t.labelInterval,t.slotDuration);(i===null||i<1)&&(console.warn("slotLabelInterval must be a multiple of slotDuration"),t.slotDuration=null)}}function fs(t,e,r){const{currentRange:n}=e;let{labelInterval:i}=t;if(!i){let s;if(t.slotDuration){for(s of jt){const l=D(s),o=ke(l,t.slotDuration);if(o!==null&&o<=ot){i=l;break}}i||(i=t.slotDuration)}else for(s of jt)if(i=D(s),r.countDurationsBetween(n.start,n.end,i)>=Hu)break;t.labelInterval=i}return i}function Uu(t,e,r){const{currentRange:n}=e;let{slotDuration:i}=t;if(!i){const s=fs(t,e,r);for(let l of jt){const o=D(l),a=ke(s,o);if(a!==null&&a>1&&a<=ot){i=o;break}}i&&r.countDurationsBetween(n.start,n.end,i)>Pu&&(i=null),i||(i=s),t.slotDuration=i}return i}function Wu(t,e,r,n){let i,s;const{labelInterval:l}=t;let o=ne(l).unit;const a=n.weekNumbers;let u=i=s=null;switch(o==="week"&&!a&&(o="day"),o){case"year":u={year:"numeric"};break;case"month":ve("years",e,r)>1&&(u={year:"numeric"}),i={month:"short"};break;case"week":ve("years",e,r)>1&&(u={year:"numeric"}),i={week:"narrow"};break;case"day":ve("years",e,r)>1?u={year:"numeric",month:"long"}:ve("months",e,r)>1&&(u={month:"long"}),a&&(i={week:"short"}),s={weekday:"narrow",day:"numeric"};break;case"hour":a&&(u={week:"short"}),ve("days",e,r)>1&&(i={weekday:"short",day:"numeric",month:"numeric",omitCommas:!0}),s={hour:"numeric",minute:"2-digit",omitZeroMinute:!0,meridiem:"short"};break;case"minute":bl(l)/60>=ot?(u={hour:"numeric",meridiem:"short"},i=c=>":"+be(c.date.minute,2)):u={hour:"numeric",minute:"numeric",meridiem:"short"};break;case"second":yl(l)/60>=ot?(u={hour:"numeric",minute:"2-digit",meridiem:"lowercase"},i=c=>":"+be(c.date.second,2)):u={hour:"numeric",minute:"2-digit",second:"2-digit",meridiem:"lowercase"};break;case"millisecond":u={hour:"numeric",minute:"2-digit",second:"2-digit",meridiem:"lowercase"},i=c=>"."+be(c.millisecond,3);break}return[].concat(u||[],i||[],s||[])}function ve(t,e,r){let n=e.currentRange,i=null;return t==="years"?i=r.diffWholeYears(n.start,n.end):t==="months"||t==="weeks"?i=r.diffWholeMonths(n.start,n.end):t==="days"&&(i=Me(n.start,n.end)),i||0}function zu(t,e){let{slotDates:r,emphasizeWeeks:n}=t,i=null,s=[];for(let l of r){let o=e.computeWeekNumber(l),a=n&&i!==null&&i!==o;i=o,s.push(a)}return s}function Fu(t,e){let r=t.slotDates,n=t.headerFormats,i=n.map(()=>[]),s=ci(t.slotDuration),l=s===7?"week":s===1?"day":null,o=n.map(a=>a.getSmallestUnit?a.getSmallestUnit():null);for(let a=0;a<r.length;a+=1){let u=r[a],c=t.isWeekStarts[a];for(let d=0;d<n.length;d+=1){let f=n[d],p=i[d],m=p[p.length-1],v=d===n.length-1,b=n.length>1&&!v,y=null,S=o[d]||(v?l:null);if(b){let C=e.format(u,f);!m||m.text!==C?y=xn(u,C,S):m.colspan+=1}else if(!m||ce(e.countDurationsBetween(t.normalizedRange.start,u,t.labelInterval))){let C=e.format(u,f);y=xn(u,C,S)}else m.colspan+=1;y&&(y.weekStart=c,p.push(y))}}return i}function xn(t,e,r){return{date:t,text:e,rowUnit:r,colspan:1,isWeekStart:!1}}class ju extends R{constructor(){super(...arguments),this.refineRenderProps=ue(qu),this.buildCellNavLinkAttrs=A(Vu)}render(){let{props:e,context:r}=this,{dateEnv:n,options:i}=r,{cell:s,dateProfile:l,tDateProfile:o}=e,a=dr(s.date,e.todayRange,e.nowDate,l),u=this.refineRenderProps({level:e.rowLevel,dateMarker:s.date,text:s.text,dateEnv:r.dateEnv,viewApi:r.viewApi});return h(H,{elTag:"th",elClasses:["fc-timeline-slot","fc-timeline-slot-label",s.isWeekStart&&"fc-timeline-slot-em",...s.rowUnit==="time"?Pi(a,r.theme):fr(a,r.theme)],elAttrs:{colSpan:s.colspan,"data-date":n.formatIso(s.date,{omitTime:!o.isTimeScale,omitTimeZoneOffset:!0})},renderProps:u,generatorName:"slotLabelContent",customGenerator:i.slotLabelContent,defaultGenerator:Gu,classNameGenerator:i.slotLabelClassNames,didMount:i.slotLabelDidMount,willUnmount:i.slotLabelWillUnmount},c=>h("div",{className:"fc-timeline-slot-frame",style:{height:e.rowInnerHeight}},h(c,{elTag:"a",elClasses:["fc-timeline-slot-cushion","fc-scrollgrid-sync-inner",e.isSticky&&"fc-sticky"],elAttrs:this.buildCellNavLinkAttrs(r,s.date,s.rowUnit)})))}}function Vu(t,e,r){return r&&r!=="time"?pa(t,e,r):{}}function Gu(t){return t.text}function qu(t){return{level:t.level,date:t.dateEnv.toDate(t.dateMarker),view:t.viewApi,text:t.text}}class Qu extends R{render(){let{dateProfile:e,tDateProfile:r,rowInnerHeights:n,todayRange:i,nowDate:s}=this.props,{cellRows:l}=r;return h(I,null,l.map((o,a)=>{let u=a===l.length-1,d=["fc-timeline-header-row",r.isTimeScale&&u?"fc-timeline-header-row-chrono":""];return h("tr",{key:a,className:d.join(" ")},o.map(f=>h(ju,{key:f.date.toISOString(),cell:f,rowLevel:a,dateProfile:e,tDateProfile:r,todayRange:i,nowDate:s,rowInnerHeight:n&&n[a],isSticky:!u})))}))}}class Yu{constructor(e,r,n,i,s,l){this.slatRootEl=e,this.dateProfile=n,this.tDateProfile=i,this.dateEnv=s,this.isRtl=l,this.outerCoordCache=new Wt(e,r,!0,!1),this.innerCoordCache=new Wt(e,il(r,"div"),!0,!1)}isDateInRange(e){return J(this.dateProfile.currentRange,e)}dateToCoord(e){let{tDateProfile:r}=this,i=this.computeDateSnapCoverage(e)/r.snapsPerSlot,s=Math.floor(i);s=Math.min(s,r.slotCnt-1);let l=i-s,{innerCoordCache:o,outerCoordCache:a}=this;return this.isRtl?a.originClientRect.width-(a.rights[s]-o.getWidth(s)*l):a.lefts[s]+o.getWidth(s)*l}rangeToCoords(e){return{start:this.dateToCoord(e.start),end:this.dateToCoord(e.end)}}durationToCoord(e){let{dateProfile:r,tDateProfile:n,dateEnv:i,isRtl:s}=this,l=0;if(r){let o=i.add(r.activeRange.start,e);n.isTimeScale||(o=T(o)),l=this.dateToCoord(o),!s&&l&&(l+=1)}return l}coordFromLeft(e){return this.isRtl?this.outerCoordCache.originClientRect.width-e:e}computeDateSnapCoverage(e){return Vt(e,this.tDateProfile,this.dateEnv)}}function Vt(t,e,r){let n=r.countDurationsBetween(e.normalizedRange.start,t,e.snapDuration);if(n<0)return 0;if(n>=e.snapDiffToIndex.length)return e.snapCnt;let i=Math.floor(n),s=e.snapDiffToIndex[i];return ce(s)?s+=n-i:s=Math.ceil(s),s}function yr(t,e){return t===null?{left:"",right:""}:e?{right:t,left:""}:{left:t,right:""}}function Sr(t,e){return t?e?{right:t.start,left:-t.end}:{left:t.start,right:-t.end}:{left:"",right:""}}class hs extends R{constructor(){super(...arguments),this.rootElRef=N()}render(){let{props:e,context:r}=this,n=ne(e.tDateProfile.slotDuration).unit,i=e.slatCoords&&e.slatCoords.dateProfile===e.dateProfile?e.slatCoords:null;return h(Pe,{unit:n},(s,l)=>h("div",{className:"fc-timeline-header",ref:this.rootElRef},h("table",{"aria-hidden":!0,className:"fc-scrollgrid-sync-table",style:{minWidth:e.tableMinWidth,width:e.clientWidth}},e.tableColGroupNode,h("tbody",null,h(Qu,{dateProfile:e.dateProfile,tDateProfile:e.tDateProfile,nowDate:s,todayRange:l,rowInnerHeights:e.rowInnerHeights}))),r.options.nowIndicator&&h("div",{className:"fc-timeline-now-indicator-container"},i&&i.isDateInRange(s)&&h(pr,{elClasses:["fc-timeline-now-indicator-arrow"],elStyle:yr(i.dateToCoord(s),r.isRtl),isAxis:!0,date:s}))))}componentDidMount(){this.updateSize()}componentDidUpdate(){this.updateSize()}updateSize(){this.props.onMaxCushionWidth&&this.props.onMaxCushionWidth(this.computeMaxCushionWidth())}computeMaxCushionWidth(){return Math.max(...Ce(this.rootElRef.current,".fc-timeline-header-row:last-child .fc-timeline-slot-cushion").map(e=>e.getBoundingClientRect().width))}}class Zu extends R{render(){let{props:e,context:r}=this,{dateEnv:n,options:i,theme:s}=r,{date:l,tDateProfile:o,isEm:a}=e,u=dr(e.date,e.todayRange,e.nowDate,e.dateProfile),c=Object.assign(Object.assign({date:n.toDate(e.date)},u),{view:r.viewApi});return h(H,{elTag:"td",elRef:e.elRef,elClasses:["fc-timeline-slot","fc-timeline-slot-lane",a&&"fc-timeline-slot-em",o.isTimeScale?ce(n.countDurationsBetween(o.normalizedRange.start,e.date,o.labelInterval))?"fc-timeline-slot-major":"fc-timeline-slot-minor":"",...e.isDay?fr(u,s):Pi(u,s)],elAttrs:{"data-date":n.formatIso(l,{omitTimeZoneOffset:!0,omitTime:!o.isTimeScale})},renderProps:c,generatorName:"slotLaneContent",customGenerator:i.slotLaneContent,classNameGenerator:i.slotLaneClassNames,didMount:i.slotLaneDidMount,willUnmount:i.slotLaneWillUnmount},d=>h(d,{elTag:"div"}))}}class $u extends R{render(){let{props:e}=this,{tDateProfile:r,cellElRefs:n}=e,{slotDates:i,isWeekStarts:s}=r,l=!r.isTimeScale&&!r.largeUnit;return h("tbody",null,h("tr",null,i.map((o,a)=>{let u=o.toISOString();return h(Zu,{key:u,elRef:n.createRef(u),date:o,dateProfile:e.dateProfile,tDateProfile:r,nowDate:e.nowDate,todayRange:e.todayRange,isEm:s[a],isDay:l})})))}}class ps extends R{constructor(){super(...arguments),this.rootElRef=N(),this.cellElRefs=new Q,this.handleScrollRequest=e=>{let{onScrollLeftRequest:r}=this.props,{coords:n}=this;if(r&&n){if(e.time){let i=n.coordFromLeft(n.durationToCoord(e.time));r(i)}return!0}return null}}render(){let{props:e,context:r}=this;return h("div",{className:"fc-timeline-slots",ref:this.rootElRef},h("table",{"aria-hidden":!0,className:r.theme.getClass("table"),style:{minWidth:e.tableMinWidth,width:e.clientWidth}},e.tableColGroupNode,h($u,{cellElRefs:this.cellElRefs,dateProfile:e.dateProfile,tDateProfile:e.tDateProfile,nowDate:e.nowDate,todayRange:e.todayRange})))}componentDidMount(){this.updateSizing(),this.scrollResponder=this.context.createScrollResponder(this.handleScrollRequest)}componentDidUpdate(e){this.updateSizing(),this.scrollResponder.update(e.dateProfile!==this.props.dateProfile)}componentWillUnmount(){this.scrollResponder.detach(),this.props.onCoords&&this.props.onCoords(null)}updateSizing(){let{props:e,context:r}=this;e.clientWidth!==null&&this.scrollResponder&&this.rootElRef.current.offsetWidth&&(this.coords=new Yu(this.rootElRef.current,Xu(this.cellElRefs.currentMap,e.tDateProfile.slotDates),e.dateProfile,e.tDateProfile,r.dateEnv,r.isRtl),e.onCoords&&e.onCoords(this.coords),this.scrollResponder.update(!1))}positionToHit(e){let{outerCoordCache:r}=this.coords,{dateEnv:n,isRtl:i}=this.context,{tDateProfile:s}=this.props,l=r.leftToIndex(e);if(l!=null){let o=r.getWidth(l),a=i?(r.rights[l]-e)/o:(e-r.lefts[l])/o,u=Math.floor(a*s.snapsPerSlot),c=n.add(s.slotDates[l],gl(s.snapDuration,u)),d=n.add(c,s.snapDuration);return{dateSpan:{range:{start:c,end:d},allDay:!this.props.tDateProfile.isTimeScale},dayEl:this.cellElRefs.currentMap[l],left:r.lefts[l],right:r.rights[l]}}return null}}function Xu(t,e){return e.map(r=>{let n=r.toISOString();return t[n]})}function gs(t,e,r){let n=[];if(r)for(let i of t){let s=r.rangeToCoords(i),l=Math.round(s.start),o=Math.round(s.end);o-l<e&&(o=l+e),n.push({start:l,end:o})}return n}function Ju(t,e,r,n,i,s){let l=[],o=[];for(let S=0;S<t.length;S+=1){let C=t[S],w=C.eventRange.instance.instanceId,x=r[w],k=e[S];x&&k?l.push({index:S,span:k,thickness:x}):o.push({seg:C,hcoords:k,top:null})}let a=new Sa;i!=null&&(a.strictOrder=i),s!=null&&(a.maxStackCnt=s);let u=a.addSegs(l),c=u.map(S=>({seg:t[S.index],hcoords:S.span,top:null})),d=Ea(u),f=[],p=[];const m=S=>t[S.index];for(let S=0;S<d.length;S+=1){let C=d[S],w=C.entries.map(m),x=n[Jt(gr(w))];x!=null?f.push({index:t.length+S,thickness:x,span:C.span}):p.push({seg:w,hcoords:C.span,top:null})}a.maxStackCnt=-1,a.addSegs(f);let v=a.toRects(),b=[],y=0;for(let S of v){let C=S.index;b.push({seg:C<t.length?t[C]:d[C-t.length].entries.map(m),hcoords:S.span,top:S.levelCoord}),y=Math.max(y,S.levelCoord+S.thickness)}return[b.concat(o,c,p),y]}class ms extends R{render(){let{props:e}=this,r=[].concat(e.eventResizeSegs,e.dateSelectionSegs);return e.timelineCoords&&h("div",{className:"fc-timeline-bg"},this.renderSegs(e.businessHourSegs||[],e.timelineCoords,"non-business"),this.renderSegs(e.bgEventSegs||[],e.timelineCoords,"bg-event"),this.renderSegs(r,e.timelineCoords,"highlight"))}renderSegs(e,r,n){let{todayRange:i,nowDate:s}=this.props,{isRtl:l}=this.context,o=gs(e,0,r),a=e.map((u,c)=>{let d=o[c],f=Sr(d,l);return h("div",{key:Jo(u.eventRange),className:"fc-timeline-bg-harness",style:f},n==="bg-event"?h(Ba,Object.assign({seg:u},cr(u,i,s))):Ua(n))});return h(I,null,a)}}class vs extends Aa{sliceRange(e,r,n,i,s){let l=Bu(e,i,s),o=[];if(Vt(l.start,i,s)<Vt(l.end,i,s)){let a=Ee(l,i.normalizedRange);a&&o.push({start:a.start,end:a.end,isStart:a.start.valueOf()===l.start.valueOf()&&at(a.start,i,r,n),isEnd:a.end.valueOf()===l.end.valueOf()&&at(de(a.end,-1),i,r,n)})}return o}}const Ku=P({hour:"numeric",minute:"2-digit",omitZeroMinute:!0,meridiem:"narrow"});class bs extends R{render(){let{props:e}=this;return h(Ma,Object.assign({},e,{elClasses:["fc-timeline-event","fc-h-event"],defaultTimeFormat:Ku,defaultDisplayEventTime:!e.isTimeScale}))}}class ed extends R{render(){let{props:e,context:r}=this,{hiddenSegs:n,placement:i,resourceId:s}=e,{top:l,hcoords:o}=i,a=o&&l!==null,u=Sr(o,r.isRtl),c=s?{resourceId:s}:{};return h(Fa,{elRef:e.elRef,elClasses:["fc-timeline-more-link"],elStyle:Object.assign({visibility:a?"":"hidden",top:l||0},u),allDayDate:null,moreCnt:n.length,allSegs:n,hiddenSegs:n,dateProfile:e.dateProfile,todayRange:e.todayRange,extraDateSpan:c,popoverContent:()=>h(I,null,n.map(d=>{let f=d.eventRange.instance.instanceId;return h("div",{key:f,style:{visibility:e.isForcedInvisible[f]?"hidden":""}},h(bs,Object.assign({isTimeScale:e.isTimeScale,seg:d,isDragging:!1,isResizing:!1,isDateSelecting:!1,isSelected:f===e.eventSelection},cr(d,e.todayRange,e.nowDate))))}))},d=>h(d,{elTag:"div",elClasses:["fc-timeline-more-link-inner","fc-sticky"]}))}}class Er extends R{constructor(){super(...arguments),this.slicer=new vs,this.sortEventSegs=A(Go),this.harnessElRefs=new Q,this.moreElRefs=new Q,this.innerElRef=N(),this.state={eventInstanceHeights:{},moreLinkHeights:{}},this.handleResize=e=>{e&&this.updateSize()}}render(){let{props:e,state:r,context:n}=this,{options:i}=n,{dateProfile:s,tDateProfile:l}=e,o=this.slicer.sliceProps(e,s,l.isTimeScale?null:e.nextDayThreshold,n,s,n.dateProfileGenerator,l,n.dateEnv),a=(o.eventDrag?o.eventDrag.segs:null)||(o.eventResize?o.eventResize.segs:null)||[],u=this.sortEventSegs(o.fgEventSegs,i.eventOrder),c=gs(u,i.eventMinWidth,e.timelineCoords),[d,f]=Ju(u,c,r.eventInstanceHeights,r.moreLinkHeights,i.eventOrderStrict,i.eventMaxStack),p=(o.eventDrag?o.eventDrag.affectedInstances:null)||(o.eventResize?o.eventResize.affectedInstances:null)||{};return h(I,null,h(ms,{businessHourSegs:o.businessHourSegs,bgEventSegs:o.bgEventSegs,timelineCoords:e.timelineCoords,eventResizeSegs:o.eventResize?o.eventResize.segs:[],dateSelectionSegs:o.dateSelectionSegs,nowDate:e.nowDate,todayRange:e.todayRange}),h("div",{className:"fc-timeline-events fc-scrollgrid-sync-inner",ref:this.innerElRef,style:{height:f}},this.renderFgSegs(d,p,!1,!1,!1),this.renderFgSegs(td(a,e.timelineCoords,d),{},!!o.eventDrag,!!o.eventResize,!1)))}componentDidMount(){this.updateSize(),this.context.addResizeHandler(this.handleResize)}componentDidUpdate(e,r){(e.eventStore!==this.props.eventStore||e.timelineCoords!==this.props.timelineCoords||r.moreLinkHeights!==this.state.moreLinkHeights)&&this.updateSize()}componentWillUnmount(){this.context.removeResizeHandler(this.handleResize)}updateSize(){let{props:e}=this,{timelineCoords:r}=e;const n=this.innerElRef.current;e.onHeightChange&&e.onHeightChange(n,!1),r&&this.setState({eventInstanceHeights:B(this.harnessElRefs.currentMap,i=>Math.round(i.getBoundingClientRect().height)),moreLinkHeights:B(this.moreElRefs.currentMap,i=>Math.round(i.getBoundingClientRect().height))},()=>{e.onHeightChange&&e.onHeightChange(n,!0)}),e.syncParentMinHeight&&(n.parentElement.style.minHeight=n.style.height)}renderFgSegs(e,r,n,i,s){let{harnessElRefs:l,moreElRefs:o,props:a,context:u}=this,c=n||i||s;return h(I,null,e.map(d=>{let{seg:f,hcoords:p,top:m}=d;if(Array.isArray(f)){let S=Jt(gr(f));return h(ed,{key:"m:"+S,elRef:o.createRef(S),hiddenSegs:f,placement:d,dateProfile:a.dateProfile,nowDate:a.nowDate,todayRange:a.todayRange,isTimeScale:a.tDateProfile.isTimeScale,eventSelection:a.eventSelection,resourceId:a.resourceId,isForcedInvisible:r})}let v=f.eventRange.instance.instanceId,b=c||!!(!r[v]&&p&&m!==null),y=Sr(p,u.isRtl);return h("div",{key:"e:"+v,ref:c?null:l.createRef(v),className:"fc-timeline-event-harness",style:Object.assign({visibility:b?"":"hidden",top:m||0},y)},h(bs,Object.assign({isTimeScale:a.tDateProfile.isTimeScale,seg:f,isDragging:n,isResizing:i,isDateSelecting:s,isSelected:v===a.eventSelection},cr(f,a.todayRange,a.nowDate))))}))}}Er.addStateEquality({eventInstanceHeights:L,moreLinkHeights:L});function td(t,e,r){if(!t.length||!e)return[];let n=rd(r);return t.map(i=>({seg:i,hcoords:e.rangeToCoords(i),top:n[i.eventRange.instance.instanceId]}))}function rd(t){let e={};for(let r of t){let{seg:n}=r;Array.isArray(n)||(e[n.eventRange.instance.instanceId]=r.top)}return e}class nd extends pt{constructor(){super(...arguments),this.slatsRef=N(),this.state={coords:null},this.handeEl=e=>{e?this.context.registerInteractiveComponent(this,{el:e}):this.context.unregisterInteractiveComponent(this)},this.handleCoords=e=>{this.setState({coords:e}),this.props.onSlatCoords&&this.props.onSlatCoords(e)}}render(){let{props:e,state:r,context:n}=this,{options:i}=n,{dateProfile:s,tDateProfile:l}=e,o=ne(l.slotDuration).unit;return h("div",{className:"fc-timeline-body",ref:this.handeEl,style:{minWidth:e.tableMinWidth,height:e.clientHeight,width:e.clientWidth}},h(Pe,{unit:o},(a,u)=>h(I,null,h(ps,{ref:this.slatsRef,dateProfile:s,tDateProfile:l,nowDate:a,todayRange:u,clientWidth:e.clientWidth,tableColGroupNode:e.tableColGroupNode,tableMinWidth:e.tableMinWidth,onCoords:this.handleCoords,onScrollLeftRequest:e.onScrollLeftRequest}),h(Er,{dateProfile:s,tDateProfile:e.tDateProfile,nowDate:a,todayRange:u,nextDayThreshold:i.nextDayThreshold,businessHours:e.businessHours,eventStore:e.eventStore,eventUiBases:e.eventUiBases,dateSelection:e.dateSelection,eventSelection:e.eventSelection,eventDrag:e.eventDrag,eventResize:e.eventResize,timelineCoords:r.coords,syncParentMinHeight:!0}),i.nowIndicator&&r.coords&&r.coords.isDateInRange(a)&&h("div",{className:"fc-timeline-now-indicator-container"},h(pr,{elClasses:["fc-timeline-now-indicator-line"],elStyle:yr(r.coords.dateToCoord(a),n.isRtl),isAxis:!1,date:a})))))}queryHit(e,r,n,i){let l=this.slatsRef.current.positionToHit(e);return l?{dateProfile:this.props.dateProfile,dateSpan:l.dateSpan,rect:{left:l.left,right:l.right,top:0,bottom:i},dayEl:l.dayEl,layer:0}:null}}class id extends pt{constructor(){super(...arguments),this.buildTimelineDateProfile=A(ds),this.scrollGridRef=N(),this.state={slatCoords:null,slotCushionMaxWidth:null},this.handleSlatCoords=e=>{this.setState({slatCoords:e})},this.handleScrollLeftRequest=e=>{this.scrollGridRef.current.forceScrollLeft(0,e)},this.handleMaxCushionWidth=e=>{this.setState({slotCushionMaxWidth:Math.ceil(e)})}}render(){let{props:e,state:r,context:n}=this,{options:i}=n,s=!e.forPrint&&Ji(i),l=!e.forPrint&&Ki(i),o=this.buildTimelineDateProfile(e.dateProfile,n.dateEnv,i,n.dateProfileGenerator),{slotMinWidth:a}=i,u=ys(o,a||this.computeFallbackSlotMinWidth(o)),c=[{type:"header",key:"header",isSticky:s,chunks:[{key:"timeline",content:d=>h(hs,{dateProfile:e.dateProfile,clientWidth:d.clientWidth,clientHeight:d.clientHeight,tableMinWidth:d.tableMinWidth,tableColGroupNode:d.tableColGroupNode,tDateProfile:o,slatCoords:r.slatCoords,onMaxCushionWidth:a?null:this.handleMaxCushionWidth})}]},{type:"body",key:"body",liquid:!0,chunks:[{key:"timeline",content:d=>h(nd,Object.assign({},e,{clientWidth:d.clientWidth,clientHeight:d.clientHeight,tableMinWidth:d.tableMinWidth,tableColGroupNode:d.tableColGroupNode,tDateProfile:o,onSlatCoords:this.handleSlatCoords,onScrollLeftRequest:this.handleScrollLeftRequest}))}]}];return l&&c.push({type:"footer",key:"footer",isSticky:!0,chunks:[{key:"timeline",content:zt}]}),h(vi,{elClasses:["fc-timeline",i.eventOverlap===!1?"fc-timeline-overlap-disabled":""],viewSpec:n.viewSpec},h(br,{ref:this.scrollGridRef,liquid:!e.isHeightAuto&&!e.forPrint,forPrint:e.forPrint,collapsibleWidth:!1,colGroups:[{cols:u}],sections:c}))}computeFallbackSlotMinWidth(e){return Math.max(30,(this.state.slotCushionMaxWidth||0)/e.slotsPerLabel)}}function ys(t,e){return[{span:t.slotCnt,minWidth:e||1}]}var sd=X({name:"@fullcalendar/timeline",premiumReleaseDate:"2025-04-02",deps:[vr],initialView:"timelineDay",views:{timeline:{component:id,usesMinMaxTime:!0,eventResizableFromStart:!0},timelineDay:{type:"timeline",duration:{days:1}},timelineWeek:{type:"timeline",duration:{weeks:1}},timelineMonth:{type:"timeline",duration:{months:1}},timelineYear:{type:"timeline",duration:{years:1}}}});const Ss="_fc:",ld={id:String,parentId:String,children:g,title:String,businessHours:g,extendedProps:g,eventEditable:Boolean,eventStartEditable:Boolean,eventDurationEditable:Boolean,eventConstraint:g,eventOverlap:Boolean,eventAllow:g,eventClassNames:Lt,eventBackgroundColor:String,eventBorderColor:String,eventTextColor:String,eventColor:String};function Cr(t,e="",r,n){let{refined:i,extra:s}=Ne(t,ld),l={id:i.id||Ss+$(),parentId:i.parentId||e,title:i.title||"",businessHours:i.businessHours?Ii(i.businessHours,n):null,ui:Oe({editable:i.eventEditable,startEditable:i.eventStartEditable,durationEditable:i.eventDurationEditable,constraint:i.eventConstraint,overlap:i.eventOverlap,allow:i.eventAllow,classNames:i.eventClassNames,backgroundColor:i.eventBackgroundColor,borderColor:i.eventBorderColor,textColor:i.eventTextColor,color:i.eventColor},n),extendedProps:Object.assign(Object.assign({},s),i.extendedProps)};if(Object.freeze(l.ui.classNames),Object.freeze(l.extendedProps),!r[l.id]){if(r[l.id]=l,i.children)for(let o of i.children)Cr(o,l.id,r,n)}return l}function Es(t){return t.indexOf(Ss)===0?"":t}class z{constructor(e,r){this._context=e,this._resource=r}setProp(e,r){let n=this._resource;this._context.dispatch({type:"SET_RESOURCE_PROP",resourceId:n.id,propName:e,propValue:r}),this.sync(n)}setExtendedProp(e,r){let n=this._resource;this._context.dispatch({type:"SET_RESOURCE_EXTENDED_PROP",resourceId:n.id,propName:e,propValue:r}),this.sync(n)}sync(e){let r=this._context,n=e.id;this._resource=r.getCurrentData().resourceStore[n],r.emitter.trigger("resourceChange",{oldResource:new z(r,e),resource:this,revert(){r.dispatch({type:"ADD_RESOURCE",resourceHash:{[n]:e}})}})}remove(){let e=this._context,r=this._resource,n=r.id;e.dispatch({type:"REMOVE_RESOURCE",resourceId:n}),e.emitter.trigger("resourceRemove",{resource:this,revert(){e.dispatch({type:"ADD_RESOURCE",resourceHash:{[n]:r}})}})}getParent(){let e=this._context,r=this._resource.parentId;return r?new z(e,e.getCurrentData().resourceStore[r]):null}getChildren(){let e=this._resource.id,r=this._context,{resourceStore:n}=r.getCurrentData(),i=[];for(let s in n)n[s].parentId===e&&i.push(new z(r,n[s]));return i}getEvents(){let e=this._resource.id,r=this._context,{defs:n,instances:i}=r.getCurrentData().eventStore,s=[];for(let l in i){let o=i[l],a=n[o.defId];a.resourceIds.indexOf(e)!==-1&&s.push(new O(r,a,o))}return s}get id(){return Es(this._resource.id)}get title(){return this._resource.title}get eventConstraint(){return this._resource.ui.constraints[0]||null}get eventOverlap(){return this._resource.ui.overlap}get eventAllow(){return this._resource.ui.allows[0]||null}get eventBackgroundColor(){return this._resource.ui.backgroundColor}get eventBorderColor(){return this._resource.ui.borderColor}get eventTextColor(){return this._resource.ui.textColor}get eventClassNames(){return this._resource.ui.classNames}get extendedProps(){return this._resource.extendedProps}toPlainObject(e={}){let r=this._resource,{ui:n}=r,i=this.id,s={};return i&&(s.id=i),r.title&&(s.title=r.title),e.collapseEventColor&&n.backgroundColor&&n.backgroundColor===n.borderColor?s.eventColor=n.backgroundColor:(n.backgroundColor&&(s.eventBackgroundColor=n.backgroundColor),n.borderColor&&(s.eventBorderColor=n.borderColor)),n.textColor&&(s.eventTextColor=n.textColor),n.classNames.length&&(s.eventClassNames=n.classNames),Object.keys(r.extendedProps).length&&(e.collapseExtendedProps?Object.assign(s,r.extendedProps):s.extendedProps=r.extendedProps),s}toJSON(){return this.toPlainObject()}}function od(t,e){let r=[];for(let n in t)r.push(new z(e,t[n]));return r}class Cs extends ua{getKeyInfo(e){return Object.assign({"":{}},e.resourceStore)}getKeysForDateSpan(e){return[e.resourceId||""]}getKeysForEventDef(e){let r=e.resourceIds;return r.length?r:[""]}}const ad=$t("id,title");function cd(t,e){let{emitter:r}=e;r.hasHandlers("resourcesSet")&&r.trigger("resourcesSet",od(t,e))}function ud(t){return{resource:new z(t.context,t.resource)}}function dd(t,e,r,n,i,s){let l=fd(t,n?-1:1,e,r),o=[];return Ze(l,o,n,[],0,i,s),o}function Ze(t,e,r,n,i,s,l){for(let o=0;o<t.length;o+=1){let a=t[o],u=a.group;if(u)if(r){let c=e.length,d=n.length;if(Ze(a.children,e,r,n.concat(0),i,s,l),c<e.length){let f=e[c],p=f.rowSpans=f.rowSpans.slice();p[d]=e.length-c}}else{let c=u.spec.field+":"+u.value,d=s[c]!=null?s[c]:l;e.push({id:c,group:u,isExpanded:d}),d&&Ze(a.children,e,r,n,i+1,s,l)}else if(a.resource){let c=a.resource.id,d=s[c]!=null?s[c]:l;e.push({id:c,rowSpans:n,depth:i,isExpanded:d,hasChildren:!!a.children.length,resource:a.resource,resourceFields:a.resourceFields}),d&&Ze(a.children,e,r,n,i+1,s,l)}}}function fd(t,e,r,n){let i=hd(t,n),s=[];for(let l in i){let o=i[l];o.resource.parentId||As(o,s,r,0,e,n)}return s}function hd(t,e){let r={};for(let n in t){let i=t[n];r[n]={resource:i,resourceFields:ws(i),children:[]}}for(let n in t){let i=t[n];if(i.parentId){let s=r[i.parentId];s&&Rs(r[n],s.children,e)}}return r}function As(t,e,r,n,i,s){if(r.length&&(i===-1||n<=i)){let l=pd(t,e,r[0]);As(t,l.children,r.slice(1),n+1,i,s)}else Rs(t,e,s)}function pd(t,e,r){let n=t.resourceFields[r.field],i,s;if(r.order)for(s=0;s<e.length;s+=1){let l=e[s];if(l.group){let o=ai(n,l.group.value)*r.order;if(o===0){i=l;break}else if(o<0)break}}else for(s=0;s<e.length;s+=1){let l=e[s];if(l.group&&n===l.group.value){i=l;break}}return i||(i={group:{value:n,spec:r},children:[]},e.splice(s,0,i)),i}function Rs(t,e,r){let n;for(n=0;n<e.length&&!(oi(e[n].resourceFields,t.resourceFields,r)>0);n+=1);e.splice(n,0,t)}function ws(t){let e=Object.assign(Object.assign(Object.assign({},t.extendedProps),t.ui),t);return delete e.ui,delete e.extendedProps,e}function gd(t,e){return t.spec===e.spec&&t.value===e.value}function md(t,e,r){let n=e.dateSpan.resourceId,i=r.dateSpan.resourceId;n&&i&&n!==i&&(t.resourceMutation={matchResourceId:n,setResourceId:i})}function vd(t,e,r){let n=e.resourceMutation;if(n&&_s(t,r)){let i=t.resourceIds.indexOf(n.matchResourceId);if(i!==-1){let s=t.resourceIds.slice();s.splice(i,1),s.indexOf(n.setResourceId)===-1&&s.push(n.setResourceId),t.resourceIds=s}}}function _s(t,e){let{resourceEditable:r}=t;if(r==null){let n=t.sourceId&&e.getCurrentData().eventSources[t.sourceId];n&&(r=n.extendedProps.resourceEditable),r==null&&(r=e.options.eventResourceEditable,r==null&&(r=e.options.editable))}return r}function bd(t,e){let{resourceMutation:r}=t;if(r){let{calendarApi:n}=e;return{oldResource:n.getResourceById(r.matchResourceId),newResource:n.getResourceById(r.setResourceId)}}return{oldResource:null,newResource:null}}class yd{constructor(){this.filterResources=A(Sd)}transform(e,r){return r.viewSpec.optionDefaults.needsResourceData?{resourceStore:this.filterResources(r.resourceStore,r.options.filterResourcesWithEvents,r.eventStore,r.dateProfile.activeRange),resourceEntityExpansions:r.resourceEntityExpansions}:null}}function Sd(t,e,r,n){if(e){let i=Ed(r.instances,n),s=Cd(i,r.defs);return Object.assign(s,Ad(s,t)),K(t,(l,o)=>s[o])}return t}function Ed(t,e){return K(t,r=>dt(r.range,e))}function Cd(t,e){let r={};for(let n in t){let i=t[n];for(let s of e[i.defId].resourceIds)r[s]=!0}return r}function Ad(t,e){let r={};for(let n in t){let i;for(;(i=e[n])&&(n=i.parentId,n);)r[n]=!0}return r}function Rd(t,e,r,n){if(!t){let i=n.getCurrentData();if(i.viewSpecs[i.currentViewType].optionDefaults.needsResourceData&&_s(e,n))return!0}return t}class wd{constructor(){this.buildResourceEventUis=A(_d,L),this.injectResourceEventUis=A(Dd)}transform(e,r){return r.viewSpec.optionDefaults.needsResourceData?null:{eventUiBases:this.injectResourceEventUis(e.eventUiBases,e.eventStore.defs,this.buildResourceEventUis(r.resourceStore))}}}function _d(t){return B(t,e=>e.ui)}function Dd(t,e,r){return B(t,(n,i)=>i?Id(n,e[i],r):n)}function Id(t,e,r){let n=[];for(let i of e.resourceIds)r[i]&&n.unshift(r[i]);return n.unshift(t),lr(n)}let Ar=[];function Rr(t){Ar.push(t)}function Ds(t){return Ar[t]}function Td(){return Ar}const kd={id:String,resources:g,url:String,method:String,startParam:String,endParam:String,timeZoneParam:String,extraParams:g};function Md(t){let e;if(typeof t=="string"?e={url:t}:typeof t=="function"||Array.isArray(t)?e={resources:t}:typeof t=="object"&&t&&(e=t),e){let{refined:r,extra:n}=Ne(e,kd);Od(n);let i=xd(r);if(i)return{_raw:t,sourceId:$(),sourceDefId:i.sourceDefId,meta:i.meta,publicId:r.id||"",isFetching:!1,latestFetchId:"",fetchRange:null}}return null}function xd(t){let e=Td();for(let r=e.length-1;r>=0;r-=1){let i=e[r].parseMeta(t);if(i)return{meta:i,sourceDefId:r}}return null}function Od(t){for(let e in t)console.warn(`Unknown resource prop '${e}'`)}function Nd(t,e,r){let{options:n,dateProfile:i}=r;if(!t||!e)return On(n.initialResources||n.resources,i.activeRange,n.refetchResourcesOnNavigate,r);switch(e.type){case"RESET_RESOURCE_SOURCE":return On(e.resourceSourceInput,i.activeRange,n.refetchResourcesOnNavigate,r);case"PREV":case"NEXT":case"CHANGE_DATE":case"CHANGE_VIEW_TYPE":return Hd(t,i.activeRange,n.refetchResourcesOnNavigate,r);case"RECEIVE_RESOURCES":case"RECEIVE_RESOURCE_ERROR":return Bd(t,e.fetchId,e.fetchRange);case"REFETCH_RESOURCES":return wr(t,i.activeRange,r);default:return t}}function On(t,e,r,n){if(t){let i=Md(t);return i=wr(i,r?e:null,n),i}return null}function Hd(t,e,r,n){return r&&!Pd(t)&&(!t.fetchRange||!ho(t.fetchRange,e))?wr(t,e,n):t}function Pd(t){return!!Ds(t.sourceDefId).ignoreRange}function wr(t,e,r){let n=Ds(t.sourceDefId),i=$();return n.fetch({resourceSource:t,range:e,context:r},s=>{r.dispatch({type:"RECEIVE_RESOURCES",fetchId:i,fetchRange:e,rawResources:s.rawResources})},s=>{r.dispatch({type:"RECEIVE_RESOURCE_ERROR",fetchId:i,fetchRange:e,error:s})}),Object.assign(Object.assign({},t),{isFetching:!0,latestFetchId:i})}function Bd(t,e,r){return e===t.latestFetchId?Object.assign(Object.assign({},t),{isFetching:!1,fetchRange:r}):t}function Ld(t,e,r,n){if(!t||!e)return{};switch(e.type){case"RECEIVE_RESOURCES":return Ud(t,e.rawResources,e.fetchId,r,n);case"ADD_RESOURCE":return Wd(t,e.resourceHash);case"REMOVE_RESOURCE":return zd(t,e.resourceId);case"SET_RESOURCE_PROP":return Fd(t,e.resourceId,e.propName,e.propValue);case"SET_RESOURCE_EXTENDED_PROP":return jd(t,e.resourceId,e.propName,e.propValue);default:return t}}function Ud(t,e,r,n,i){if(n.latestFetchId===r){let s={};for(let l of e)Cr(l,"",s,i);return s}return t}function Wd(t,e){return Object.assign(Object.assign({},t),e)}function zd(t,e){let r=Object.assign({},t);delete r[e];for(let n in r)r[n].parentId===e&&(r[n]=Object.assign(Object.assign({},r[n]),{parentId:""}));return r}function Fd(t,e,r,n){let i=t[e];return i?Object.assign(Object.assign({},t),{[e]:Object.assign(Object.assign({},i),{[r]:n})}):t}function jd(t,e,r,n){let i=t[e];return i?Object.assign(Object.assign({},t),{[e]:Object.assign(Object.assign({},i),{extendedProps:Object.assign(Object.assign({},i.extendedProps),{[r]:n})})}):t}function Vd(t,e){if(!t||!e)return{};switch(e.type){case"SET_RESOURCE_ENTITY_EXPANDED":return Object.assign(Object.assign({},t),{[e.id]:e.isExpanded});default:return t}}function Gd(t,e,r){let n=Nd(t&&t.resourceSource,e,r),i=Ld(t&&t.resourceStore,e,n,r),s=Vd(t&&t.resourceEntityExpansions,e);return{resourceSource:n,resourceStore:i,resourceEntityExpansions:s}}const qd={resourceId:String,resourceIds:g,resourceEditable:Boolean};function Qd(t){return{resourceIds:Yd(t.resourceIds).concat(t.resourceId?[t.resourceId]:[]),resourceEditable:t.resourceEditable}}function Yd(t){return(t||[]).map(e=>String(e))}function Zd(t,e){let r=t.dateSpan.resourceId,n=e.dateSpan.resourceId;return r&&n?{resourceId:r}:null}Ae.prototype.addResource=function(t,e=!0){let r=this.getCurrentData(),n,i;t instanceof z?(i=t._resource,n={[i.id]:i}):(n={},i=Cr(t,"",n,r)),this.dispatch({type:"ADD_RESOURCE",resourceHash:n}),e&&this.trigger("_scrollRequest",{resourceId:i.id});let s=new z(r,i);return r.emitter.trigger("resourceAdd",{resource:s,revert:()=>{this.dispatch({type:"REMOVE_RESOURCE",resourceId:i.id})}}),s};Ae.prototype.getResourceById=function(t){t=String(t);let e=this.getCurrentData();if(e.resourceStore){let r=e.resourceStore[t];if(r)return new z(e,r)}return null};Ae.prototype.getResources=function(){let t=this.getCurrentData(),{resourceStore:e}=t,r=[];if(e)for(let n in e)r.push(new z(t,e[n]));return r};Ae.prototype.getTopLevelResources=function(){let t=this.getCurrentData(),{resourceStore:e}=t,r=[];if(e)for(let n in e)e[n].parentId||r.push(new z(t,e[n]));return r};Ae.prototype.refetchResources=function(){this.dispatch({type:"REFETCH_RESOURCES"})};function $d(t,e){return t.resourceId?{resource:e.calendarApi.getResourceById(t.resourceId)}:{}}function Xd(t,e){return t.resourceId?{resource:e.calendarApi.getResourceById(t.resourceId)}:{}}function Jd(t,e){let n=new Cs().splitProps(Object.assign(Object.assign({},t),{resourceStore:e.getCurrentData().resourceStore}));for(let i in n){let s=n[i];if(i&&n[""]&&(s=Object.assign(Object.assign({},s),{eventStore:ft(n[""].eventStore,s.eventStore),eventUiBases:Object.assign(Object.assign({},n[""].eventUiBases),s.eventUiBases)})),!Ra(s,e,{resourceId:i},Kd.bind(null,i)))return!1}return!0}function Kd(t,e){return Object.assign(Object.assign({},e),{constraints:ef(t,e.constraints)})}function ef(t,e){return e.map(r=>{let n=r.defs;if(n)for(let i in n){let s=n[i].resourceIds;if(s.length&&s.indexOf(t)===-1)return!1}return r})}function tf(t){return t.resourceId?{resourceId:t.resourceId}:{}}const rf={resources:nf};function nf(t,e){e.getCurrentData().resourceSource._raw!==t&&e.dispatch({type:"RESET_RESOURCE_SOURCE",resourceSourceInput:t})}const sf={initialResources:g,resources:g,eventResourceEditable:Boolean,refetchResourcesOnNavigate:Boolean,resourceOrder:$t,filterResourcesWithEvents:Boolean,resourceGroupField:String,resourceAreaWidth:g,resourceAreaColumns:g,resourcesInitiallyExpanded:Boolean,datesAboveResources:Boolean,needsResourceData:Boolean,resourceAreaHeaderClassNames:g,resourceAreaHeaderContent:g,resourceAreaHeaderDidMount:g,resourceAreaHeaderWillUnmount:g,resourceGroupLabelClassNames:g,resourceGroupLabelContent:g,resourceGroupLabelDidMount:g,resourceGroupLabelWillUnmount:g,resourceLabelClassNames:g,resourceLabelContent:g,resourceLabelDidMount:g,resourceLabelWillUnmount:g,resourceLaneClassNames:g,resourceLaneContent:g,resourceLaneDidMount:g,resourceLaneWillUnmount:g,resourceGroupLaneClassNames:g,resourceGroupLaneContent:g,resourceGroupLaneDidMount:g,resourceGroupLaneWillUnmount:g},lf={resourcesSet:g,resourceAdd:g,resourceChange:g,resourceRemove:g};O.prototype.getResources=function(){let{calendarApi:t}=this._context;return this._def.resourceIds.map(e=>t.getResourceById(e))};O.prototype.setResources=function(t){let e=[];for(let r of t){let n=null;typeof r=="string"?n=r:typeof r=="number"?n=String(r):r instanceof z?n=r.id:console.warn("unknown resource type: "+r),n&&e.push(n)}this.mutate({standardProps:{resourceIds:e}})};Rr({ignoreRange:!0,parseMeta(t){return Array.isArray(t.resources)?t.resources:null},fetch(t,e){e({rawResources:t.resourceSource.meta})}});Rr({parseMeta(t){return typeof t.resources=="function"?t.resources:null},fetch(t,e,r){const n=t.context.dateEnv,i=t.resourceSource.meta,s=t.range?{start:n.toDate(t.range.start),end:n.toDate(t.range.end),startStr:n.formatIso(t.range.start),endStr:n.formatIso(t.range.end),timeZone:n.timeZone}:{};Oi(i.bind(null,s),l=>e({rawResources:l}),r)}});Rr({parseMeta(t){return t.url?{url:t.url,method:(t.method||"GET").toUpperCase(),extraParams:t.extraParams}:null},fetch(t,e,r){const n=t.resourceSource.meta,i=of(n,t.range,t.context);Ni(n.method,n.url,i).then(([s,l])=>{e({rawResources:s,response:l})},r)}});function of(t,e,r){let{dateEnv:n,options:i}=r,s,l,o,a,u={};return e&&(s=t.startParam,s==null&&(s=i.startParam),l=t.endParam,l==null&&(l=i.endParam),o=t.timeZoneParam,o==null&&(o=i.timeZoneParam),u[s]=n.formatIso(e.start),u[l]=n.formatIso(e.end),n.timeZone!=="local"&&(u[o]=n.timeZone)),typeof t.extraParams=="function"?a=t.extraParams():a=t.extraParams||{},Object.assign(u,a),u}var af=X({name:"@fullcalendar/resource",premiumReleaseDate:"2025-04-02",deps:[vr],reducers:[Gd],isLoadingFuncs:[t=>t.resourceSource&&t.resourceSource.isFetching],eventRefiners:qd,eventDefMemberAdders:[Qd],isDraggableTransformers:[Rd],eventDragMutationMassagers:[md],eventDefMutationAppliers:[vd],dateSelectionTransformers:[Zd],datePointTransforms:[$d],dateSpanTransforms:[Xd],viewPropsTransformers:[yd,wd],isPropsValid:Jd,externalDefTransforms:[tf],eventDropTransformers:[bd],optionChangeHandlers:rf,optionRefiners:sf,listenerRefiners:lf,propSetHandlers:{resourceStore:cd}});function Is({depth:t,hasChildren:e,isExpanded:r,onExpanderClick:n}){let i=[];for(let l=0;l<t;l+=1)i.push(h("span",{className:"fc-icon"}));let s=["fc-icon"];return e&&(r?s.push("fc-icon-minus-square"):s.push("fc-icon-plus-square")),i.push(h("span",{className:"fc-datagrid-expander"+(e?"":" fc-datagrid-expander-placeholder"),onClick:n},h("span",{className:s.join(" ")}))),h(I,{},...i)}class cf extends R{constructor(){super(...arguments),this.refineRenderProps=ue(df),this.onExpanderClick=e=>{let{props:r}=this;r.hasChildren&&this.context.dispatch({type:"SET_RESOURCE_ENTITY_EXPANDED",id:r.resource.id,isExpanded:!r.isExpanded})}}render(){let{props:e,context:r}=this,{colSpec:n}=e,i=this.refineRenderProps({resource:e.resource,fieldValue:e.fieldValue,context:r});return h(H,{elTag:"td",elClasses:["fc-datagrid-cell","fc-resource"],elAttrs:{role:"gridcell","data-resource-id":e.resource.id},renderProps:i,generatorName:n.isMain?"resourceLabelContent":void 0,customGenerator:n.cellContent,defaultGenerator:uf,classNameGenerator:n.cellClassNames,didMount:n.cellDidMount,willUnmount:n.cellWillUnmount},s=>h("div",{className:"fc-datagrid-cell-frame",style:{height:e.innerHeight}},h("div",{className:"fc-datagrid-cell-cushion fc-scrollgrid-sync-inner"},n.isMain&&h(Is,{depth:e.depth,hasChildren:e.hasChildren,isExpanded:e.isExpanded,onExpanderClick:this.onExpanderClick}),h(s,{elTag:"span",elClasses:["fc-datagrid-cell-main"]}))))}}function uf(t){return t.fieldValue||h(I,null," ")}function df(t){return{resource:new z(t.context,t.resource),fieldValue:t.fieldValue,view:t.context.viewApi}}class ff extends R{render(){let{props:e,context:r}=this,{colSpec:n}=e,i={groupValue:e.fieldValue,view:r.viewApi};return h(H,{elTag:"td",elClasses:["fc-datagrid-cell","fc-resource-group"],elAttrs:{role:"gridcell",rowSpan:e.rowSpan},renderProps:i,generatorName:"resourceGroupLabelContent",customGenerator:n.cellContent,defaultGenerator:hf,classNameGenerator:n.cellClassNames,didMount:n.cellDidMount,willUnmount:n.cellWillUnmount},s=>h("div",{className:"fc-datagrid-cell-frame fc-datagrid-cell-frame-liquid"},h(s,{elTag:"div",elClasses:["fc-datagrid-cell-cushion","fc-sticky"]})))}}function hf(t){return t.groupValue||h(I,null," ")}class Ts extends R{render(){let{props:e}=this,{resource:r,rowSpans:n,depth:i}=e,s=ws(r);return h("tr",{role:"row"},e.colSpecs.map((l,o)=>{let a=n[o];if(a===0)return null;a==null&&(a=1);let u=l.field?s[l.field]:r.title||Es(r.id);return a>1?h(ff,{key:o,colSpec:l,fieldValue:u,rowSpan:a}):h(cf,{key:o,colSpec:l,resource:r,fieldValue:u,depth:i,hasChildren:e.hasChildren,isExpanded:e.isExpanded,innerHeight:e.innerHeight})}))}}Ts.addPropsEquality({rowSpans:F});class ks extends R{constructor(){super(...arguments),this.innerInnerRef=N(),this.onExpanderClick=()=>{let{props:e}=this;this.context.dispatch({type:"SET_RESOURCE_ENTITY_EXPANDED",id:e.id,isExpanded:!e.isExpanded})}}render(){let{props:e,context:r}=this,n={groupValue:e.group.value,view:r.viewApi},i=e.group.spec;return h("tr",{role:"row"},h(H,{elTag:"th",elClasses:["fc-datagrid-cell","fc-resource-group",r.theme.getClass("tableCellShaded")],elAttrs:{role:"columnheader",scope:"colgroup",colSpan:e.spreadsheetColCnt},renderProps:n,generatorName:"resourceGroupLabelContent",customGenerator:i.labelContent,defaultGenerator:pf,classNameGenerator:i.labelClassNames,didMount:i.labelDidMount,willUnmount:i.labelWillUnmount},s=>h("div",{className:"fc-datagrid-cell-frame",style:{height:e.innerHeight}},h("div",{className:"fc-datagrid-cell-cushion fc-scrollgrid-sync-inner",ref:this.innerInnerRef},h(Is,{depth:0,hasChildren:!0,isExpanded:e.isExpanded,onExpanderClick:this.onExpanderClick}),h(s,{elTag:"span",elClasses:["fc-datagrid-cell-main"]})))))}}ks.addPropsEquality({group:gd});function pf(t){return t.groupValue||h(I,null," ")}const gf=20;class mf extends R{constructor(){super(...arguments),this.resizerElRefs=new Q(this._handleColResizerEl.bind(this)),this.colDraggings={}}render(){let{colSpecs:e,superHeaderRendering:r,rowInnerHeights:n}=this.props,i={view:this.context.viewApi},s=[];if(n=n.slice(),r){let o=n.shift();s.push(h("tr",{key:"row-super",role:"row"},h(H,{elTag:"th",elClasses:["fc-datagrid-cell","fc-datagrid-cell-super"],elAttrs:{role:"columnheader",scope:"colgroup",colSpan:e.length},renderProps:i,generatorName:"resourceAreaHeaderContent",customGenerator:r.headerContent,defaultGenerator:r.headerDefault,classNameGenerator:r.headerClassNames,didMount:r.headerDidMount,willUnmount:r.headerWillUnmount},a=>h("div",{className:"fc-datagrid-cell-frame",style:{height:o}},h(a,{elTag:"div",elClasses:["fc-datagrid-cell-cushion","fc-scrollgrid-sync-inner"]})))))}let l=n.shift();return s.push(h("tr",{key:"row",role:"row"},e.map((o,a)=>{let u=a===e.length-1;return h(H,{key:a,elTag:"th",elClasses:["fc-datagrid-cell"],elAttrs:{role:"columnheader"},renderProps:i,generatorName:"resourceAreaHeaderContent",customGenerator:o.headerContent,defaultGenerator:o.headerDefault,classNameGenerator:o.headerClassNames,didMount:o.headerDidMount,willUnmount:o.headerWillUnmount},c=>h("div",{className:"fc-datagrid-cell-frame",style:{height:l}},h("div",{className:"fc-datagrid-cell-cushion fc-scrollgrid-sync-inner"},o.isMain&&h("span",{className:"fc-datagrid-expander fc-datagrid-expander-placeholder"},h("span",{className:"fc-icon"})),h(c,{elTag:"span",elClasses:["fc-datagrid-cell-main"]})),!u&&h("div",{className:"fc-datagrid-cell-resizer",ref:this.resizerElRefs.createRef(a)})))}))),h(I,null,s)}_handleColResizerEl(e,r){let{colDraggings:n}=this;if(e){let i=this.initColResizing(e,parseInt(r,10));i&&(n[r]=i)}else{let i=n[r];i&&(i.destroy(),delete n[r])}}initColResizing(e,r){let{pluginHooks:n,isRtl:i}=this.context,{onColWidthChange:s}=this.props,l=n.elementDraggingImpl;if(l){let o=new l(e),a,u;return o.emitter.on("dragstart",()=>{u=Ce(V(e,"tr"),"th").map(d=>d.getBoundingClientRect().width),a=u[r]}),o.emitter.on("dragmove",c=>{u[r]=Math.max(a+c.deltaX*(i?-1:1),gf),s&&s(u.slice())}),o.setAutoScrollEnabled(!1),o}return null}}class vf extends R{constructor(){super(...arguments),this.refineRenderProps=ue(ud),this.handleHeightChange=(e,r)=>{this.props.onHeightChange&&this.props.onHeightChange(V(e,"tr"),r)}}render(){let{props:e,context:r}=this,{options:n}=r,i=this.refineRenderProps({resource:e.resource,context:r});return h("tr",{ref:e.elRef},h(H,{elTag:"td",elClasses:["fc-timeline-lane","fc-resource"],elAttrs:{"data-resource-id":e.resource.id},renderProps:i,generatorName:"resourceLaneContent",customGenerator:n.resourceLaneContent,classNameGenerator:n.resourceLaneClassNames,didMount:n.resourceLaneDidMount,willUnmount:n.resourceLaneWillUnmount},s=>h("div",{className:"fc-timeline-lane-frame",style:{height:e.innerHeight}},h(s,{elTag:"div",elClasses:["fc-timeline-lane-misc"]}),h(Er,{dateProfile:e.dateProfile,tDateProfile:e.tDateProfile,nowDate:e.nowDate,todayRange:e.todayRange,nextDayThreshold:e.nextDayThreshold,businessHours:e.businessHours,eventStore:e.eventStore,eventUiBases:e.eventUiBases,dateSelection:e.dateSelection,eventSelection:e.eventSelection,eventDrag:e.eventDrag,eventResize:e.eventResize,timelineCoords:e.timelineCoords,onHeightChange:this.handleHeightChange,resourceId:e.resource.id}))))}}class bf extends R{render(){let{props:e,context:r}=this,{renderHooks:n}=e,i={groupValue:e.groupValue,view:r.viewApi};return h("tr",{ref:e.elRef},h(H,{elTag:"td",elRef:e.elRef,elClasses:["fc-timeline-lane","fc-resource-group",r.theme.getClass("tableCellShaded")],renderProps:i,generatorName:"resourceGroupLaneContent",customGenerator:n.laneContent,classNameGenerator:n.laneClassNames,didMount:n.laneDidMount,willUnmount:n.laneWillUnmount},s=>h(s,{elTag:"div",elStyle:{height:e.innerHeight}})))}}class yf extends R{render(){let{props:e,context:r}=this,{rowElRefs:n,innerHeights:i}=e;return h("tbody",null,e.rowNodes.map((s,l)=>{if(s.group)return h(bf,{key:s.id,elRef:n.createRef(s.id),groupValue:s.group.value,renderHooks:s.group.spec,innerHeight:i[l]||""});if(s.resource){let o=s.resource;return h(vf,Object.assign({key:s.id,elRef:n.createRef(s.id)},e.splitProps[o.id],{resource:o,dateProfile:e.dateProfile,tDateProfile:e.tDateProfile,nowDate:e.nowDate,todayRange:e.todayRange,nextDayThreshold:r.options.nextDayThreshold,businessHours:o.businessHours||e.fallbackBusinessHours,innerHeight:i[l]||"",timelineCoords:e.slatCoords,onHeightChange:e.onRowHeightChange}))}return null}))}}class Sf extends R{constructor(){super(...arguments),this.rootElRef=N(),this.rowElRefs=new Q}render(){let{props:e,context:r}=this;return h("table",{ref:this.rootElRef,"aria-hidden":!0,className:"fc-scrollgrid-sync-table "+r.theme.getClass("table"),style:{minWidth:e.tableMinWidth,width:e.clientWidth,height:e.minHeight}},h(yf,{rowElRefs:this.rowElRefs,rowNodes:e.rowNodes,dateProfile:e.dateProfile,tDateProfile:e.tDateProfile,nowDate:e.nowDate,todayRange:e.todayRange,splitProps:e.splitProps,fallbackBusinessHours:e.fallbackBusinessHours,slatCoords:e.slatCoords,innerHeights:e.innerHeights,onRowHeightChange:e.onRowHeightChange}))}componentDidMount(){this.updateCoords()}componentDidUpdate(){this.updateCoords()}componentWillUnmount(){this.props.onRowCoords&&this.props.onRowCoords(null)}updateCoords(){let{props:e}=this;e.onRowCoords&&e.clientWidth!==null&&this.props.onRowCoords(new Wt(this.rootElRef.current,Ef(this.rowElRefs.currentMap,e.rowNodes),!1,!0))}}function Ef(t,e){return e.map(r=>t[r.id])}class Cf extends pt{constructor(){super(...arguments),this.computeHasResourceBusinessHours=A(Af),this.resourceSplitter=new Cs,this.bgSlicer=new vs,this.slatsRef=N(),this.state={slatCoords:null},this.handleEl=e=>{e?this.context.registerInteractiveComponent(this,{el:e}):this.context.unregisterInteractiveComponent(this)},this.handleSlatCoords=e=>{this.setState({slatCoords:e}),this.props.onSlatCoords&&this.props.onSlatCoords(e)},this.handleRowCoords=e=>{this.rowCoords=e,this.props.onRowCoords&&this.props.onRowCoords(e)}}render(){let{props:e,state:r,context:n}=this,{dateProfile:i,tDateProfile:s}=e,l=ne(s.slotDuration).unit,o=this.computeHasResourceBusinessHours(e.rowNodes),a=this.resourceSplitter.splitProps(e),u=a[""],c=this.bgSlicer.sliceProps(u,i,s.isTimeScale?null:e.nextDayThreshold,n,i,n.dateProfileGenerator,s,n.dateEnv),d=r.slatCoords&&r.slatCoords.dateProfile===e.dateProfile?r.slatCoords:null;return h("div",{ref:this.handleEl,className:["fc-timeline-body",e.expandRows?"fc-timeline-body-expandrows":""].join(" "),style:{minWidth:e.tableMinWidth}},h(Pe,{unit:l},(f,p)=>h(I,null,h(ps,{ref:this.slatsRef,dateProfile:i,tDateProfile:s,nowDate:f,todayRange:p,clientWidth:e.clientWidth,tableColGroupNode:e.tableColGroupNode,tableMinWidth:e.tableMinWidth,onCoords:this.handleSlatCoords,onScrollLeftRequest:e.onScrollLeftRequest}),h(ms,{businessHourSegs:o?null:c.businessHourSegs,bgEventSegs:c.bgEventSegs,timelineCoords:d,eventResizeSegs:c.eventResize?c.eventResize.segs:[],dateSelectionSegs:c.dateSelectionSegs,nowDate:f,todayRange:p}),h(Sf,{rowNodes:e.rowNodes,dateProfile:i,tDateProfile:e.tDateProfile,nowDate:f,todayRange:p,splitProps:a,fallbackBusinessHours:o?e.businessHours:null,clientWidth:e.clientWidth,minHeight:e.expandRows?e.clientHeight:"",tableMinWidth:e.tableMinWidth,innerHeights:e.rowInnerHeights,slatCoords:d,onRowCoords:this.handleRowCoords,onRowHeightChange:e.onRowHeightChange}),n.options.nowIndicator&&d&&d.isDateInRange(f)&&h("div",{className:"fc-timeline-now-indicator-container"},h(pr,{elClasses:["fc-timeline-now-indicator-line"],elStyle:yr(d.dateToCoord(f),n.isRtl),isAxis:!1,date:f})))))}queryHit(e,r){let n=this.rowCoords,i=n.topToIndex(r);if(i!=null){let s=this.props.rowNodes[i].resource;if(s){let l=this.slatsRef.current.positionToHit(e);if(l)return{dateProfile:this.props.dateProfile,dateSpan:{range:l.dateSpan.range,allDay:l.dateSpan.allDay,resourceId:s.id},rect:{left:l.left,right:l.right,top:n.tops[i],bottom:n.bottoms[i]},dayEl:l.dayEl,layer:0}}}return null}}function Af(t){for(let e of t){let r=e.resource;if(r&&r.businessHours)return!0}return!1}const Nn=30;class Rf extends R{constructor(){super(...arguments),this.scrollGridRef=N(),this.timeBodyScrollerElRef=N(),this.spreadsheetHeaderChunkElRef=N(),this.rootElRef=N(),this.ensureScrollGridResizeId=0,this.state={resourceAreaWidthOverride:null},this.ensureScrollGridResize=()=>{this.ensureScrollGridResizeId&&clearTimeout(this.ensureScrollGridResizeId),this.ensureScrollGridResizeId=setTimeout(()=>{this.scrollGridRef.current.handleSizing(!1)},he.SCROLLGRID_RESIZE_INTERVAL+1)}}render(){let{props:e,state:r,context:n}=this,{options:i}=n,s=!e.forPrint&&Ji(i),l=!e.forPrint&&Ki(i),o=[{type:"header",key:"header",syncRowHeights:!0,isSticky:s,chunks:[{key:"datagrid",elRef:this.spreadsheetHeaderChunkElRef,tableClassName:"fc-datagrid-header",rowContent:e.spreadsheetHeaderRows},{key:"divider",outerContent:h("td",{role:"presentation",className:"fc-resource-timeline-divider "+n.theme.getClass("tableCellShaded")})},{key:"timeline",content:e.timeHeaderContent}]},{type:"body",key:"body",syncRowHeights:!0,liquid:!0,expandRows:!!i.expandRows,chunks:[{key:"datagrid",tableClassName:"fc-datagrid-body",rowContent:e.spreadsheetBodyRows},{key:"divider",outerContent:h("td",{role:"presentation",className:"fc-resource-timeline-divider "+n.theme.getClass("tableCellShaded")})},{key:"timeline",scrollerElRef:this.timeBodyScrollerElRef,content:e.timeBodyContent}]}];l&&o.push({type:"footer",key:"footer",isSticky:!0,chunks:[{key:"datagrid",content:zt},{key:"divider",outerContent:h("td",{role:"presentation",className:"fc-resource-timeline-divider "+n.theme.getClass("tableCellShaded")})},{key:"timeline",content:zt}]});let a=r.resourceAreaWidthOverride!=null?r.resourceAreaWidthOverride:i.resourceAreaWidth;return h(br,{ref:this.scrollGridRef,elRef:this.rootElRef,liquid:!e.isHeightAuto&&!e.forPrint,forPrint:e.forPrint,collapsibleWidth:!1,colGroups:[{cols:e.spreadsheetCols,width:a},{cols:[]},{cols:e.timeCols}],sections:o})}forceTimeScroll(e){this.scrollGridRef.current.forceScrollLeft(2,e)}forceResourceScroll(e){this.scrollGridRef.current.forceScrollTop(1,e)}getResourceScroll(){return this.timeBodyScrollerElRef.current.scrollTop}componentDidMount(){this.initSpreadsheetResizing()}componentWillUnmount(){this.destroySpreadsheetResizing()}initSpreadsheetResizing(){let{isRtl:e,pluginHooks:r}=this.context,n=r.elementDraggingImpl,i=this.spreadsheetHeaderChunkElRef.current;if(n){let s=this.rootElRef.current,l=this.spreadsheetResizerDragging=new n(s,".fc-resource-timeline-divider"),o,a;l.emitter.on("dragstart",()=>{o=i.getBoundingClientRect().width,a=s.getBoundingClientRect().width}),l.emitter.on("dragmove",u=>{let c=o+u.deltaX*(e?-1:1);c=Math.max(c,Nn),c=Math.min(c,a-Nn),this.setState({resourceAreaWidthOverride:c},this.ensureScrollGridResize)}),l.setAutoScrollEnabled(!1)}}destroySpreadsheetResizing(){this.spreadsheetResizerDragging&&this.spreadsheetResizerDragging.destroy()}}class Ms extends R{constructor(e,r){super(e,r),this.processColOptions=A(If),this.buildTimelineDateProfile=A(ds),this.hasNesting=A(Df),this.buildRowNodes=A(dd),this.layoutRef=N(),this.rowNodes=[],this.renderedRowNodes=[],this.buildRowIndex=A(wf),this.handleSlatCoords=n=>{this.setState({slatCoords:n})},this.handleRowCoords=n=>{this.rowCoords=n,this.scrollResponder.update(!1)},this.handleMaxCushionWidth=n=>{this.setState({slotCushionMaxWidth:Math.ceil(n)})},this.handleScrollLeftRequest=n=>{this.layoutRef.current.forceTimeScroll(n)},this.handleScrollRequest=n=>{let{rowCoords:i}=this,s=this.layoutRef.current,l=n.rowId||n.resourceId;if(i){if(l){let a=this.buildRowIndex(this.renderedRowNodes)[l];if(a!=null){let u=n.fromBottom!=null?i.bottoms[a]-n.fromBottom:i.tops[a];s.forceResourceScroll(u)}}return!0}return null},this.handleColWidthChange=n=>{this.setState({spreadsheetColWidths:n})},this.state={resourceAreaWidth:r.options.resourceAreaWidth,spreadsheetColWidths:[]}}render(){let{props:e,state:r,context:n}=this,{options:i,viewSpec:s}=n,{superHeaderRendering:l,groupSpecs:o,orderSpecs:a,isVGrouping:u,colSpecs:c}=this.processColOptions(n.options),d=this.buildTimelineDateProfile(e.dateProfile,n.dateEnv,i,n.dateProfileGenerator),f=this.rowNodes=this.buildRowNodes(e.resourceStore,o,a,u,e.resourceEntityExpansions,i.resourcesInitiallyExpanded),{slotMinWidth:p}=i,m=ys(d,p||this.computeFallbackSlotMinWidth(d));return h(vi,{elClasses:["fc-resource-timeline",!this.hasNesting(f)&&"fc-resource-timeline-flat","fc-timeline",i.eventOverlap===!1?"fc-timeline-overlap-disabled":"fc-timeline-overlap-enabled"],viewSpec:s},h(Rf,{ref:this.layoutRef,forPrint:e.forPrint,isHeightAuto:e.isHeightAuto,spreadsheetCols:_f(c,r.spreadsheetColWidths,""),spreadsheetHeaderRows:v=>h(mf,{superHeaderRendering:l,colSpecs:c,onColWidthChange:this.handleColWidthChange,rowInnerHeights:v.rowSyncHeights}),spreadsheetBodyRows:v=>h(I,null,this.renderSpreadsheetRows(f,c,v.rowSyncHeights)),timeCols:m,timeHeaderContent:v=>h(hs,{clientWidth:v.clientWidth,clientHeight:v.clientHeight,tableMinWidth:v.tableMinWidth,tableColGroupNode:v.tableColGroupNode,dateProfile:e.dateProfile,tDateProfile:d,slatCoords:r.slatCoords,rowInnerHeights:v.rowSyncHeights,onMaxCushionWidth:p?null:this.handleMaxCushionWidth}),timeBodyContent:v=>h(Cf,{dateProfile:e.dateProfile,clientWidth:v.clientWidth,clientHeight:v.clientHeight,tableMinWidth:v.tableMinWidth,tableColGroupNode:v.tableColGroupNode,expandRows:v.expandRows,tDateProfile:d,rowNodes:f,businessHours:e.businessHours,dateSelection:e.dateSelection,eventStore:e.eventStore,eventUiBases:e.eventUiBases,eventSelection:e.eventSelection,eventDrag:e.eventDrag,eventResize:e.eventResize,resourceStore:e.resourceStore,nextDayThreshold:n.options.nextDayThreshold,rowInnerHeights:v.rowSyncHeights,onSlatCoords:this.handleSlatCoords,onRowCoords:this.handleRowCoords,onScrollLeftRequest:this.handleScrollLeftRequest,onRowHeightChange:v.reportRowHeightChange})}))}renderSpreadsheetRows(e,r,n){return e.map((i,s)=>i.group?h(ks,{key:i.id,id:i.id,spreadsheetColCnt:r.length,isExpanded:i.isExpanded,group:i.group,innerHeight:n[s]||""}):i.resource?h(Ts,{key:i.id,colSpecs:r,rowSpans:i.rowSpans,depth:i.depth,isExpanded:i.isExpanded,hasChildren:i.hasChildren,resource:i.resource,innerHeight:n[s]||""}):null)}componentDidMount(){this.renderedRowNodes=this.rowNodes,this.scrollResponder=this.context.createScrollResponder(this.handleScrollRequest)}getSnapshotBeforeUpdate(){return this.props.forPrint?{}:{resourceScroll:this.queryResourceScroll()}}componentDidUpdate(e,r,n){this.renderedRowNodes=this.rowNodes,this.scrollResponder.update(e.dateProfile!==this.props.dateProfile),n.resourceScroll&&this.handleScrollRequest(n.resourceScroll)}componentWillUnmount(){this.scrollResponder.detach()}computeFallbackSlotMinWidth(e){return Math.max(30,(this.state.slotCushionMaxWidth||0)/e.slotsPerLabel)}queryResourceScroll(){let{rowCoords:e,renderedRowNodes:r}=this;if(e){let n=this.layoutRef.current,i=e.bottoms,s=n.getResourceScroll(),l={};for(let o=0;o<i.length;o+=1){let a=r[o],u=i[o]-s;if(u>0){l.rowId=a.id,l.fromBottom=u;break}}return l}return null}}Ms.addStateEquality({spreadsheetColWidths:F});function wf(t){let e={};for(let r=0;r<t.length;r+=1)e[t[r].id]=r;return e}function _f(t,e,r=""){return t.map((n,i)=>({className:n.isMain?"fc-main-col":"",width:e[i]||n.width||r}))}function Df(t){for(let e of t)if(e.group||e.resource&&e.hasChildren)return!0;return!1}function If(t){let e=t.resourceAreaColumns||[],r=null;e.length?t.resourceAreaHeaderContent&&(r={headerClassNames:t.resourceAreaHeaderClassNames,headerContent:t.resourceAreaHeaderContent,headerDidMount:t.resourceAreaHeaderDidMount,headerWillUnmount:t.resourceAreaHeaderWillUnmount}):e.push({headerClassNames:t.resourceAreaHeaderClassNames,headerContent:t.resourceAreaHeaderContent,headerDefault:()=>"Resources",headerDidMount:t.resourceAreaHeaderDidMount,headerWillUnmount:t.resourceAreaHeaderWillUnmount});let n=[],i=[],s=[],l=!1;for(let c of e)c.group?i.push(Object.assign(Object.assign({},c),{cellClassNames:c.cellClassNames||t.resourceGroupLabelClassNames,cellContent:c.cellContent||t.resourceGroupLabelContent,cellDidMount:c.cellDidMount||t.resourceGroupLabelDidMount,cellWillUnmount:c.cellWillUnmount||t.resourceGroupLaneWillUnmount})):n.push(c);let o=n[0];if(o.isMain=!0,o.cellClassNames=o.cellClassNames||t.resourceLabelClassNames,o.cellContent=o.cellContent||t.resourceLabelContent,o.cellDidMount=o.cellDidMount||t.resourceLabelDidMount,o.cellWillUnmount=o.cellWillUnmount||t.resourceLabelWillUnmount,i.length)s=i,l=!0;else{let c=t.resourceGroupField;c&&s.push({field:c,labelClassNames:t.resourceGroupLabelClassNames,labelContent:t.resourceGroupLabelContent,labelDidMount:t.resourceGroupLabelDidMount,labelWillUnmount:t.resourceGroupLabelWillUnmount,laneClassNames:t.resourceGroupLaneClassNames,laneContent:t.resourceGroupLaneContent,laneDidMount:t.resourceGroupLaneDidMount,laneWillUnmount:t.resourceGroupLaneWillUnmount})}let a=t.resourceOrder||ad,u=[];for(let c of a){let d=!1;for(let f of s)if(f.field===c.field){f.order=c.order,d=!0;break}d||u.push(c)}return{superHeaderRendering:r,isVGrouping:l,groupSpecs:s,colSpecs:i.concat(n),orderSpecs:u}}var Tf=".fc .fc-resource-timeline-divider{cursor:col-resize;width:3px}.fc .fc-resource-group{font-weight:inherit;text-align:inherit}.fc .fc-resource-timeline .fc-resource-group:not([rowspan]){background:var(--fc-neutral-bg-color)}.fc .fc-timeline-lane-frame{position:relative}.fc .fc-timeline-overlap-enabled .fc-timeline-lane-frame .fc-timeline-events{box-sizing:content-box;padding-bottom:10px}.fc-timeline-body-expandrows td.fc-timeline-lane{position:relative}.fc-timeline-body-expandrows .fc-timeline-lane-frame{position:static}.fc-datagrid-cell-frame-liquid{height:100%}.fc-liquid-hack .fc-datagrid-cell-frame-liquid{bottom:0;height:auto;left:0;position:absolute;right:0;top:0}.fc .fc-datagrid-header .fc-datagrid-cell-frame{align-items:center;display:flex;justify-content:flex-start;position:relative}.fc .fc-datagrid-cell-resizer{bottom:0;cursor:col-resize;position:absolute;top:0;width:5px;z-index:1}.fc .fc-datagrid-cell-cushion{overflow:hidden;padding:8px;white-space:nowrap}.fc .fc-datagrid-expander{cursor:pointer;opacity:.65}.fc .fc-datagrid-expander .fc-icon{display:inline-block;width:1em}.fc .fc-datagrid-expander-placeholder{cursor:auto}.fc .fc-resource-timeline-flat .fc-datagrid-expander-placeholder{display:none}.fc-direction-ltr .fc-datagrid-cell-resizer{right:-3px}.fc-direction-rtl .fc-datagrid-cell-resizer{left:-3px}.fc-direction-ltr .fc-datagrid-expander{margin-right:3px}.fc-direction-rtl .fc-datagrid-expander{margin-left:3px}";qt(Tf);var kf=X({name:"@fullcalendar/resource-timeline",premiumReleaseDate:"2025-04-02",deps:[vr,af,sd],initialView:"resourceTimelineDay",views:{resourceTimeline:{type:"timeline",component:Ms,needsResourceData:!0,resourceAreaWidth:"30%",resourcesInitiallyExpanded:!0,eventResizableFromStart:!0},resourceTimelineDay:{type:"resourceTimeline",duration:{days:1}},resourceTimelineWeek:{type:"resourceTimeline",duration:{weeks:1}},resourceTimelineMonth:{type:"resourceTimeline",duration:{months:1}},resourceTimelineYear:{type:"resourceTimeline",duration:{years:1}}}});document.addEventListener("DOMContentLoaded",function(){var t=document.getElementById("timelineCalendar");new au(t,{plugins:[kf],initialView:"resourceTimelineDay",timeZone:"UTC",aspectRatio:1.5,headerToolbar:{left:"prev,next",center:"title",right:"resourceTimelineDay,resourceTimelineWeek,resourceTimelineMonth"},editable:!0,resourceAreaHeaderContent:"Rooms",resources:"https://fullcalendar.io/api/demo-feeds/resources.json?with-nesting&with-colors",events:"https://fullcalendar.io/api/demo-feeds/events.json?single-day&for-resource-timeline&start=2025-08-01&end=2026-01-12&timezone=UTC"}).render()});
