import{c as v,i as w}from"../../admin.bundle-DI9_jvUJ.js";/* empty css                               */import"../../main-Cyta4iCA.js";VirtualSelect.init({ele:"#statusSelect2",options:[{label:"Customer",value:"1"},{label:"Personal",value:"2"},{label:"Employee",value:"3"},{label:"Marketing",value:"4"}],selectedValue:0});class k{constructor(){this.elements={table:document.querySelector("table tbody"),tableHeaders:document.querySelectorAll("table th.table-row"),searchInput:document.getElementById("searchContactInput"),checkAllBox:document.getElementById("checkAllData"),pagination:document.querySelector(".pagination"),resultsInfo:document.querySelector("#paginationInfo"),addContactForm:document.querySelector("#createContactModal form"),deleteBtn:document.querySelector(".btn-danger.btn-icon"),exportBtn:document.querySelector(".btn-light:nth-child(2)"),sortBtn:document.getElementById("filterDropdown")},this.contacts=[],this.filteredContacts=[],this.currentPage=1,this.itemsPerPage=10,this.selectedContacts=new Set,this.currentSortColumn=null,this.sortDirection="asc",this.init(),this.initializeEventListeners()}setupSortableTable(){Array.from(this.elements.tableHeaders).slice(1,-1).forEach((t,s)=>{t.classList.add("cursor-pointer");const a=t.textContent;t.innerHTML=`
        ${a}
        <span class="sort-icon ms-1 opacity-0">
          <i class="ri-arrow-up-line sort-up"></i>
          <i class="ri-arrow-down-line sort-down d-none"></i>
        </span>
      `;const o=this.getColumnNameByIndex(s);t.dataset.column=o,t.addEventListener("click",()=>this.handleColumnSort(o,t))})}getColumnNameByIndex(e){return["id","name","company","role","email","website","status"][e]}handleColumnSort(e,t){this.currentSortColumn===e?this.sortDirection=this.sortDirection==="asc"?"desc":"asc":(this.currentSortColumn=e,this.sortDirection="asc"),document.querySelectorAll(".sort-icon").forEach(a=>{a.classList.add("opacity-0"),a.querySelector(".sort-up").classList.remove("d-none"),a.querySelector(".sort-down").classList.add("d-none")});const s=t.querySelector(".sort-icon");s.classList.remove("opacity-0"),this.sortDirection==="asc"?(s.querySelector(".sort-up").classList.remove("d-none"),s.querySelector(".sort-down").classList.add("d-none")):(s.querySelector(".sort-up").classList.add("d-none"),s.querySelector(".sort-down").classList.remove("d-none")),this.sortContacts(e,this.sortDirection),this.currentPage=1,this.renderContacts(),this.setupPagination()}sortContacts(e,t){this.filteredContacts.sort((s,a)=>{let o=s[e],n=a[e];if(typeof o=="string"&&(o=o.toLowerCase()),typeof n=="string"&&(n=n.toLowerCase()),e==="id"){const i=parseInt(o.replace(/\D/g,"")),c=parseInt(n.replace(/\D/g,""));return t==="asc"?i-c:c-i}return e==="name"&&(o=s.name.toLowerCase(),n=a.name.toLowerCase()),o<n?t==="asc"?-1:1:o>n?t==="asc"?1:-1:0})}init(){this.loadContacts(),this.setupEventListeners(),this.setupSortableTable()}initializeEventListeners(){const e=this;this.elements.checkAllBox.addEventListener("change",function(){e.handleSelectAll()});const t=document.querySelector(".btn-danger.btn-icon");t&&t.addEventListener("click",function(){e.deleteSelectedContacts()}),document.querySelectorAll(".contact-check").forEach(s=>{s.addEventListener("change",function(a){const o=this.id.replace("check-","");this.checked?e.selectedContacts.add(o):e.selectedContacts.delete(o),e.updateSelectAllCheckbox(),e.updateDeleteButton()})})}loadContacts(){this.contacts=S,this.filteredContacts=[...this.contacts],this.renderContacts(),this.setupPagination()}setupEventListeners(){this.elements.searchInput.addEventListener("input",()=>this.handleSearch()),this.elements.checkAllBox.addEventListener("change",()=>this.toggleSelectAll()),this.elements.deleteBtn.addEventListener("click",()=>this.deleteSelectedContacts()),this.elements.exportBtn.addEventListener("click",()=>this.exportContacts()),this.elements.addContactForm.addEventListener("submit",a=>this.handleAddContact(a));const e=document.querySelector('[data-bs-target="#createContactModal"]');e&&e.addEventListener("click",()=>this.resetContactForm()),document.getElementById("createContactModal").addEventListener("hidden.bs.modal",()=>this.resetContactForm());const s=document.getElementById("imageInput");s.addEventListener("change",()=>this.validateImage(s)),document.querySelectorAll("#filterDropdown + .dropdown-menu .dropdown-item").forEach(a=>{a.addEventListener("click",o=>this.handleSort(o))})}renderContacts(){const e=(this.currentPage-1)*this.itemsPerPage,t=e+this.itemsPerPage,s=this.filteredContacts.slice(e,t);for(;this.elements.table.children.length>1;)this.elements.table.removeChild(this.elements.table.lastChild);if(this.filteredContacts.length===0){const a=document.createElement("tr"),o=this.elements.table.querySelector("tr").children.length;a.innerHTML=`
        <td colspan="${o}" class="text-center py-4">
          <div class="d-flex flex-column align-items-center">
            <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" class="mx-auto size-12" viewBox="0 0 48 48">
              <linearGradient id="SVGID_1__h35ynqzIJzH4_gr1" x1="34.598" x2="15.982" y1="15.982" y2="34.598" gradientUnits="userSpaceOnUse">
                <stop offset="0" stop-color="#60e8fe"></stop>
                <stop offset=".033" stop-color="#6ae9fe"></stop>
                <stop offset=".197" stop-color="#97f0fe"></stop>
                <stop offset=".362" stop-color="#bdf5ff"></stop>
                <stop offset=".525" stop-color="#dafaff"></stop>
                <stop offset=".687" stop-color="#eefdff"></stop>
                <stop offset=".846" stop-color="#fbfeff"></stop>
                <stop offset="1" stop-color="#fff"></stop>
              </linearGradient>
              <path fill="url(#SVGID_1__h35ynqzIJzH4_gr1)" d="M40.036,33.826L31.68,25.6c0.847-1.739,1.335-3.684,1.335-5.748c0-7.27-5.894-13.164-13.164-13.164 S6.688,12.582,6.688,19.852c0,7.27,5.894,13.164,13.164,13.164c2.056,0,3.995-0.485,5.728-1.326l3.914,4.015l4.331,4.331 c1.715,1.715,4.496,1.715,6.211,0C41.751,38.321,41.751,35.541,40.036,33.826z"></path>
              <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M31.95,25.739l8.086,8.086c1.715,1.715,1.715,4.496,0,6.211l0,0c-1.715,1.715-4.496,1.715-6.211,0 l-4.331-4.331"></path>
              <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M7.525,24.511c-1.771-4.694-0.767-10.196,3.011-13.975c3.847-3.847,9.48-4.817,14.228-2.912"></path>
              <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M30.856,12.603c3.376,5.114,2.814,12.063-1.688,16.565c-4.858,4.858-12.565,5.129-17.741,0.814"></path>
            </svg>
            <p class="mt-2 text-center text-gray-500 dark:text-dark-500">No matching records found</p>
            <p class="text-muted mb-0">We couldn't find any contacts matching your search.</p>
          </div>
        </td>
      `,this.elements.table.appendChild(a),this.elements.resultsInfo.innerHTML='Showing <b class="me-1">0-0</b> of <b class="ms-1">0</b> Results';return}s.forEach(a=>{const o=this.createContactRow(a);this.elements.table.appendChild(o)}),this.selectedContacts.size===0?this.elements.resultsInfo.innerHTML=`Showing <b class="me-1">${e+1}-${Math.min(t,this.filteredContacts.length)}</b> of <b class="ms-1">${this.filteredContacts.length}</b> Results`:this.updateSelectionUI(),this.updateSelectAllCheckbox()}createContactRow(e){const t=document.createElement("tr");return t.dataset.id=e.id,t.innerHTML=`
      <td>
        <div class="form-check check-primary">
          <input class="form-check-input contact-check" type="checkbox" id="check-${e.id}" 
            ${this.selectedContacts.has(e.id)?"checked":""}>
          <label class="form-check-label d-none" for="check-${e.id}">Check Data</label>
        </div>
      </td>
      <td>${e.id}</td>
      <td>
        <div class="d-flex align-items-center gap-2">
          <img src="${e.avatar}" loading="lazy" alt="" class="border-2 border border-2 border-light-subtle rounded-circle size-9">
          <div>
            <h6 class="mb-0"><a class="link link-custom flex-grow-1" href="#!">${e.name}</a></h6>
            <p class="fs-sm text-muted">${e.phone}</p>
          </div>
        </div>
      </td>
      <td>${e.company}</td>
      <td>${e.role}</td>
      <td>${e.email}</td>
      <td><span class="badge bg-light-subtle text-muted border border-light-subtle">${e.website}</span></td>
      <td><span class="badge bg-${e.statusColor}-subtle text-${e.statusColor} border border-${e.statusColor}-subtle">${e.status}</span></td>
      <td class="whitespace-nowrap">
        <div class="dropdown">
          <a href="#!" class="link link-custom-primary" type="button" data-bs-toggle="dropdown" aria-expanded="false" title="dropdown-button">
            <i class="ri-more-2-fill"></i>
          </a>
          <ul class="dropdown-menu dropdown-menu-end">
            <li><a href="#!" class="dropdown-item d-flex gap-3 align-items-center view-contact" data-id="${e.id}">
              <i class="ri-eye-line"></i><span>Overview</span></a></li>
            <li><a href="#!" class="dropdown-item d-flex gap-3 align-items-center edit-contact" data-id="${e.id}">
              <i class="ri-pencil-line"></i>Edit</a></li>
            <li><a href="#!" class="dropdown-item d-flex gap-3 align-items-center delete-contact" data-id="${e.id}">
              <i class="ri-delete-bin-line"></i><span>Delete</span></a></li>
          </ul>
        </div>
      </td>
    `,this.setupRowEventListeners(t,e.id),t}setupRowEventListeners(e,t){const s=e.querySelector(".contact-check");s.addEventListener("change",()=>{s.checked?this.selectedContacts.add(t):this.selectedContacts.delete(t),this.updateSelectAllCheckbox()}),e.querySelector(".edit-contact").addEventListener("click",()=>this.editContact(t)),e.querySelector(".delete-contact").addEventListener("click",()=>this.confirmDeleteContact(t))}setupPagination(){const e=Math.ceil(this.filteredContacts.length/this.itemsPerPage);let t="";t+=`
      <li class="page-item ${this.currentPage===1?"disabled":""}">
        <a class="page-link" href="#!" data-page="${this.currentPage-1}">
          <i data-lucide="chevron-left" class="size-4"></i> Previous
        </a>
      </li>
    `;for(let s=1;s<=e;s++)t+=`
        <li class="page-item ${this.currentPage===s?"active":""}">
          <a class="page-link" href="#!" data-page="${s}">${s}</a>
        </li>
      `;t+=`
      <li class="page-item ${this.currentPage===e?"disabled":""}">
        <a class="page-link" href="#!" data-page="${this.currentPage+1}">
          Next <i data-lucide="chevron-right" class="size-4"></i>
        </a>
      </li>
    `,this.elements.pagination.innerHTML=t,this.elements.pagination.querySelectorAll(".page-link").forEach(s=>{s.addEventListener("click",a=>{a.preventDefault();const o=parseInt(a.currentTarget.dataset.page);!isNaN(o)&&o!==this.currentPage&&o>0&&o<=e&&(this.currentPage=o,this.renderContacts(),this.setupPagination())})}),v({icons:w})}validateImage(e){const t=document.getElementById("imageError");if(t.textContent="",e.files&&e.files[0]){const s=e.files[0];if(!s.type.match("image.*"))return t.textContent="Please select a valid image file",e.value="",!1;if(s.size>2*1024*1024)return t.textContent="Image size should be less than 2MB",e.value="",!1;const a=document.querySelector("#imagePreview");if(a){const o=new FileReader;o.onload=function(n){a.style.backgroundImage=`url(${n.target.result})`,a.style.backgroundSize="cover",a.style.backgroundPosition="center",a.querySelector("i").style.display="none"},o.readAsDataURL(s)}return!0}return!1}handleSearch(){const e=this.elements.searchInput.value.toLowerCase().trim();e===""?this.filteredContacts=[...this.contacts]:this.filteredContacts=this.contacts.filter(t=>t.name.toLowerCase().includes(e)||t.email.toLowerCase().includes(e)||t.company.toLowerCase().includes(e)||t.role.toLowerCase().includes(e)),this.currentPage=1,this.renderContacts(),this.setupPagination()}toggleSelectAll(){this.elements.checkAllBox.checked?this.filteredContacts.forEach(s=>{this.selectedContacts.add(s.id)}):this.selectedContacts.clear(),document.querySelectorAll(".contact-check").forEach(s=>{const a=s.id.replace("check-","");s.checked=this.selectedContacts.has(a)}),this.updateSelectionUI()}updateSelectionUI(){if(this.selectedContacts.size>0)this.elements.resultsInfo.innerHTML=`Selected <b class="me-1">${this.selectedContacts.size}</b> of <b class="ms-1">${this.filteredContacts.length}</b> Results`;else{const e=(this.currentPage-1)*this.itemsPerPage,t=Math.min(e+this.itemsPerPage,this.filteredContacts.length);this.elements.resultsInfo.innerHTML=`Showing <b class="me-1">${e+1}-${t}</b> of <b class="ms-1">${this.filteredContacts.length}</b> Results`}this.elements.deleteBtn&&(this.elements.deleteBtn.disabled=this.selectedContacts.size===0),this.elements.exportBtn&&(this.elements.exportBtn.disabled=this.selectedContacts.size===0)}handleSelectAll(){const t=this.elements.checkAllBox.checked;document.querySelectorAll(".contact-check").forEach(a=>{const o=a.id.replace("check-","");a.checked=t,t?this.selectedContacts.add(o):this.selectedContacts.delete(o)}),this.updateDeleteButton()}updateDeleteButton(){const e=document.querySelector(".btn-danger.btn-icon");this.selectedContacts.size>0?e.classList.remove("d-none"):e.classList.add("d-none")}updateSelectAllCheckbox(){const e=document.querySelectorAll(".contact-check");let t=!0;e.forEach(s=>{const a=s.id.replace("check-","");this.selectedContacts.has(a)||(t=!1)}),this.elements.checkAllBox.checked=e.length>0&&t,this.updateDeleteButton()}deleteSelectedContacts(){this.contacts=this.contacts.filter(e=>!this.selectedContacts.has(e.id)),this.filteredContacts=this.filteredContacts.filter(e=>!this.selectedContacts.has(e.id)),this.selectedContacts.clear(),this.currentPage=1,this.renderContacts(),this.setupPagination()}confirmDeleteContact(e){const t=document.getElementById("deleteModal"),s=t.querySelector(".btn-danger");t.dataset.contactId=e;const a=()=>{const n=t.dataset.contactId;this.deleteContact(n),s.removeEventListener("click",a),window.bootstrap.Modal.getInstance(t).hide()};s.removeEventListener("click",a),s.addEventListener("click",a),new window.bootstrap.Modal(t).show()}deleteContact(e){this.contacts=this.contacts.filter(t=>t.id!==e),this.filteredContacts=this.filteredContacts.filter(t=>t.id!==e),this.selectedContacts.delete(e),this.renderContacts(),this.setupPagination()}exportContacts(){const e=this.selectedContacts.size>0?this.contacts.filter(i=>this.selectedContacts.has(i.id)):this.filteredContacts;let s=["ID","Name","Company","Role","Email","Phone","Website","Status"].join(",")+`
`;e.forEach(i=>{const c=[i.id,`"${i.name}"`,`"${i.company}"`,`"${i.role}"`,i.email,i.phone,i.website,i.status].join(",");s+=c+`
`});const a=new Blob([s],{type:"text/csv;charset=utf-8;"}),o=URL.createObjectURL(a),n=document.createElement("a");n.setAttribute("href",o),n.setAttribute("download","contacts.csv"),n.style.visibility="hidden",document.body.appendChild(n),n.click(),document.body.removeChild(n)}handleAddContact(e){e.preventDefault();const t=document.getElementById("contactId").value.trim(),s=document.getElementById("fullName").value.trim(),a=document.getElementById("email").value.trim(),o=document.getElementById("phone").value.trim(),n=document.getElementById("companyName").value.trim(),i=document.getElementById("role").value.trim(),c=document.getElementById("website").value.trim(),m=document.getElementById("imageInput");if(!s||!a||!o){alert("Please fill in all required fields");return}const u=t!=="";let r=null;if(u&&(r=this.contacts.find(l=>l.id===t),!r)){alert("Contact not found");return}const h={1:{label:"Customer",color:"pink"},2:{label:"Personal",color:"warning"},3:{label:"Employee",color:"success"},4:{label:"Marketing",color:"info"}},p=l=>{const g=document.querySelector("#statusSelect2").virtualSelect.getValue(),f=h[g]||h[1],C=f.label,y=f.color;if(u)r.name=s,r.email=a,r.phone=o,r.company=n||"Not specified",r.role=i||"Not specified",r.website=c||"example.com",r.avatar=l,r.status=C,r.statusColor=y;else{const b={id:`PEC-${Math.floor(1e4+Math.random()*9e4)}`,name:s,email:a,phone:o,company:n||"Not specified",role:i||"Not specified",website:c||"example.com",status:C,statusColor:y,avatar:l};this.contacts.unshift(b)}this.filteredContacts=[...this.contacts],this.resetContactForm(),window.bootstrap.Modal.getInstance(document.getElementById("createContactModal")).hide(),this.renderContacts(),this.setupPagination()};let d=u?r.avatar:"assets/images/avatar/user-1.png";if(m.files&&m.files[0]){const l=new FileReader;l.onload=g=>{d=g.target.result,p(d)},l.readAsDataURL(m.files[0])}else p(d)}resetContactForm(){const e=document.getElementById("createContactModal"),t=this.elements.addContactForm;e.querySelector(".modal-title"),t.querySelector('button[type="submit"]'),document.getElementById("contactId").value="",t.reset(),document.getElementById("imageError").textContent="";const s=document.querySelector("#imageLabel");s&&(s.style.backgroundImage="",s.style.backgroundSize="",s.style.backgroundPosition="",s.querySelector("i").style.display="block")}editContact(e){const t=this.contacts.find(l=>l.id===e);if(!t)return;const s=document.getElementById("createContactModal"),a=this.elements.addContactForm;if(!s||!a){console.error("Modal or form not found in DOM");return}const o=s.querySelector(".modal-title"),n=a.querySelector('button[type="submit"]');o&&(o.textContent="Edit Contact"),n&&(n.textContent="Update Contact");const i=document.getElementById("contactId");i&&(i.value=t.id);const c=document.getElementById("fullName"),m=document.getElementById("email"),u=document.getElementById("phone"),r=document.getElementById("companyName"),h=document.getElementById("role"),p=document.getElementById("website");c&&(c.value=t.name),m&&(m.value=t.email),u&&(u.value=t.phone),r&&(r.value=t.company),h&&(h.value=t.role),p&&(p.value=t.website);const d=document.querySelector("#imageLabel");if(d&&t.avatar){d.style.backgroundImage=`url(${t.avatar})`,d.style.backgroundSize="cover",d.style.backgroundPosition="center";const l=d.querySelector("i");l&&(l.style.display="none")}try{new window.bootstrap.Modal(s).show()}catch(l){console.error("Failed to show modal:",l),alert("Could not open edit form. Please try again.")}}handleSort(e){switch(e.target.textContent.trim()){case"No Sorting":this.filteredContacts=[...this.contacts],this.currentSortColumn=null;break;case"Alphabetical (A -> Z)":this.handleColumnSort("name",document.querySelector('[data-column="name"]'));break;case"Reverse Alphabetical (Z -> A)":this.currentSortColumn="name",this.sortDirection="desc",this.sortContacts("name","desc");break;case"Status":this.handleColumnSort("status",document.querySelector('[data-column="status"]'));break}this.currentPage=1,this.renderContacts(),this.setupPagination()}}const S=[{id:"PEC-24151",name:"Pat Martinez",company:"StarTech Dynamics",role:"Web Designer",email:"<EMAIL>",phone:"+890 1829 15781",website:"patmartizen.com",status:"Customer",statusColor:"pink",avatar:"assets/images/avatar/user-1.png"},{id:"PEC-24152",name:"Jane Brown",company:"StarTech Dynamics",role:"Web Designer",email:"<EMAIL>",phone:"+957 6326 78821",website:"janebrown.com",status:"Personal",statusColor:"warning",avatar:"assets/images/avatar/user-2.png"},{id:"PEC-24153",name:"John Davis",company:"StarTech Dynamics",role:"UI / UX Designer",email:"<EMAIL>",phone:"+264 1427 33002",website:"johndavis.com",status:"Customer",statusColor:"pink",avatar:"assets/images/avatar/user-3.png"},{id:"PEC-24154",name:"Jordan Davis",company:"BrightFuture Tech",role:"Graphic Designer",email:"<EMAIL>",phone:"+688 9444 65363",website:"jordandavis.com",status:"Customer",statusColor:"pink",avatar:"assets/images/avatar/user-4.png"},{id:"PEC-24155",name:"Alex Lee",company:"Quantum Innovations",role:"Co Founder",email:"<EMAIL>",phone:"+300 8108 69119",website:"alexlee.com",status:"Personal",statusColor:"warning",avatar:"assets/images/avatar/user-5.png"},{id:"PEC-24156",name:"Casey Martinez",company:"BrightFuture Tech",role:"Graphic Designer",email:"<EMAIL>",phone:"+646 9347 84543",website:"casetmartinez.com",status:"Personal",statusColor:"warning",avatar:"assets/images/avatar/user-6.png"},{id:"PEC-24157",name:"Taylor Wilson",company:"BrightFuture Tech",role:"ASP.Net Developer",email:"<EMAIL>",phone:"+749 6102 50325",website:"taylor.com",status:"Customer",statusColor:"pink",avatar:"assets/images/avatar/user-7.png"},{id:"PEC-24158",name:"Chris Smith",company:"BlueSky Enterprises",role:"Product Manager",email:"<EMAIL>",phone:"+829 5728 93265",website:"chrissmith.com",status:"Employee",statusColor:"info",avatar:"assets/images/avatar/user-8.png"},{id:"PEC-24159",name:"Jane Brown",company:"BrightFuture Tech",role:"web Developer",email:"<EMAIL>",phone:"+213 9689 10505",website:"jane.brigth.com",status:"Personal",statusColor:"warning",avatar:"assets/images/avatar/user-9.png"},{id:"PEC-24160",name:"John Garcia",company:"Quantum Innovations",role:"Product Manager",email:"<EMAIL>",phone:"+846 9274 23870",website:"johangarcia.com",status:"Personal",statusColor:"warning",avatar:"assets/images/avatar/user-10.png"},{id:"PEC-24161",name:"Chris Wilson",company:"BlueSky Enterprises",role:"React Developer",email:"<EMAIL>",phone:"+285 1994 96029",website:"chriswilson.com",status:"Customer",statusColor:"pink",avatar:"assets/images/avatar/user-11.png"},{id:"PEC-24162",name:"Alex Lee",company:"BrightFuture Tech",role:"Software Engineer",email:"<EMAIL>",phone:"+695 2025 51582",website:"alextech.com",status:"Customer",statusColor:"pink",avatar:"assets/images/avatar/user-12.png"},{id:"PEC-24163",name:"Cameron Wilson",company:"BlueSky Enterprises",role:"Laravel Developer",email:"<EMAIL>",phone:"+840 4447 94334",website:"cameron.com",status:"Customer",statusColor:"pink",avatar:"assets/images/avatar/user-13.png"},{id:"PEC-24164",name:"Sam Brown",company:"BlueSky Enterprises",role:"Marketing Specialist",email:"<EMAIL>",phone:"+438 6305 33828",website:"sambrown.com",status:"Marketing",statusColor:"warning",avatar:"assets/images/avatar/user-14.png"},{id:"PEC-24165",name:"Pat Martinez",company:"Synergy Solutions",role:"Laravel Developer",email:"<EMAIL>",phone:"+356 8229 92921",website:"patmartiz.com",status:"Employee",statusColor:"info",avatar:"assets/images/avatar/user-15.png"},{id:"PEC-24166",name:"Chris Smith",company:"BrightFuture Tech",role:"UI / UX Designer",email:"<EMAIL>",phone:"+880 8152 56315",website:"chrissmith.com",status:"Personal",statusColor:"warning",avatar:"assets/images/avatar/user-16.png"},{id:"PEC-24167",name:"Cameron Wilson",company:"BlueSky Enterprises",role:"Co Founder",email:"<EMAIL>",phone:"+599 4447 23760",website:"cameronwilson.com",status:"Employee",statusColor:"info",avatar:"assets/images/avatar/user-17.png"},{id:"PEC-24168",name:"Casey Martinez",company:"StarPath Dynamics",role:"UI / UX Designer",email:"<EMAIL>",phone:"+590 5863 84911",website:"caseystarpath.com",status:"Customer",statusColor:"pink",avatar:"assets/images/avatar/user-18.png"}];document.addEventListener("DOMContentLoaded",()=>{new k});
