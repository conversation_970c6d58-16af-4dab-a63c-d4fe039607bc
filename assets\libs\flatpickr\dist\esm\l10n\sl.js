var fp = typeof window !== "undefined" && window.flatpickr !== undefined
    ? window.flatpickr
    : {
        l10ns: {},
    };
export var Slovenian = {
    weekdays: {
        shorthand: ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"],
        longhand: [
            "<PERSON><PERSON><PERSON>",
            "Ponedeljek",
            "<PERSON><PERSON>",
            "<PERSON><PERSON>",
            "<PERSON><PERSON>rte<PERSON>",
            "<PERSON><PERSON>",
            "Sobot<PERSON>",
        ],
    },
    months: {
        shorthand: [
            "<PERSON>",
            "Feb",
            "Mar",
            "Apr",
            "Maj",
            "Jun",
            "Jul",
            "Avg",
            "Sep",
            "Okt",
            "Nov",
            "Dec",
        ],
        longhand: [
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON>",
            "April",
            "<PERSON>",
            "<PERSON>ij",
            "Julij",
            "Avgust",
            "September",
            "Oktober",
            "November",
            "December",
        ],
    },
    firstDayOfWeek: 1,
    rangeSeparator: " do ",
    time_24hr: true,
    ordinal: function () {
        return ".";
    },
};
fp.l10ns.sl = Slovenian;
export default fp.l10ns;
