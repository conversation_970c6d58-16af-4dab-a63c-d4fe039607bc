var cp=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports);import"../../admin.bundle-DI9_jvUJ.js";import"../../main-Cyta4iCA.js";var zw=cp((ut,ct)=>{const ta={},Xh=[];function O(e,t){if(Array.isArray(e)){for(const r of e)O(r,t);return}if(typeof e=="object"){for(const r in e)O(r,e[r]);return}Gh(Object.getOwnPropertyNames(t)),ta[e]=Object.assign(ta[e]||{},t)}function Z(e){return ta[e]||{}}function lp(){return[...new Set(Xh)]}function Gh(e){Xh.push(...e)}function ba(e,t){let r;const n=e.length,i=[];for(r=0;r<n;r++)i.push(t(e[r]));return i}function hp(e,t){let r;const n=e.length,i=[];for(r=0;r<n;r++)t(e[r])&&i.push(e[r]);return i}function pr(e){return e%360*Math.PI/180}function fp(e){return e.replace(/([A-Z])/g,function(t,r){return"-"+r.toLowerCase()})}function Qh(e){return e.charAt(0).toUpperCase()+e.slice(1)}function Zt(e,t,r,n){return(t==null||r==null)&&(n=n||e.bbox(),t==null?t=n.width/n.height*r:r==null&&(r=n.height/n.width*t)),{width:t,height:r}}function ea(e,t){const r=e.origin;let n=e.ox!=null?e.ox:e.originX!=null?e.originX:"center",i=e.oy!=null?e.oy:e.originY!=null?e.originY:"center";r!=null&&([n,i]=Array.isArray(r)?r:typeof r=="object"?[r.x,r.y]:[r,r]);const s=typeof n=="string",a=typeof i=="string";if(s||a){const{height:o,width:u,x:c,y:l}=t.bbox();s&&(n=n.includes("left")?c:n.includes("right")?c+u:c+u/2),a&&(i=i.includes("top")?l:i.includes("bottom")?l+o:l+o/2)}return[n,i]}const dp=new Set(["desc","metadata","title"]),ra=e=>dp.has(e.nodeName),tf=(e,t,r={})=>{const n={...t};for(const i in n)n[i].valueOf()===r[i]&&delete n[i];Object.keys(n).length?e.node.setAttribute("data-svgjs",JSON.stringify(n)):(e.node.removeAttribute("data-svgjs"),e.node.removeAttribute("svgjs:data"))},wa="http://www.w3.org/2000/svg",pp="http://www.w3.org/1999/xhtml",gr="http://www.w3.org/2000/xmlns/",ve="http://www.w3.org/1999/xlink",P={window:typeof window>"u"?null:window,document:typeof document>"u"?null:document};function gp(){return P.window}class xa{}const Ct={},Aa="___SYMBOL___ROOT___";function le(e,t=wa){return P.document.createElementNS(t,e)}function Y(e,t=!1){if(e instanceof xa)return e;if(typeof e=="object")return vr(e);if(e==null)return new Ct[Aa];if(typeof e=="string"&&e.charAt(0)!=="<")return vr(P.document.querySelector(e));const r=t?P.document.createElement("div"):le("svg");return r.innerHTML=e,e=vr(r.firstChild),r.removeChild(r.firstChild),e}function B(e,t){return t&&(t instanceof P.window.Node||t.ownerDocument&&t instanceof t.ownerDocument.defaultView.Node)?t:le(e)}function tt(e){if(!e)return null;if(e.instance instanceof xa)return e.instance;if(e.nodeName==="#document-fragment")return new Ct.Fragment(e);let t=Qh(e.nodeName||"Dom");return t==="LinearGradient"||t==="RadialGradient"?t="Gradient":Ct[t]||(t="Dom"),new Ct[t](e)}let vr=tt;function L(e,t=e.name,r=!1){return Ct[t]=e,r&&(Ct[Aa]=e),Gh(Object.getOwnPropertyNames(e.prototype)),e}function vp(e){return Ct[e]}let yp=1e3;function ef(e){return"Svgjs"+Qh(e)+yp++}function rf(e){for(let t=e.children.length-1;t>=0;t--)rf(e.children[t]);return e.id&&(e.id=ef(e.nodeName)),e}function M(e,t){let r,n;for(e=Array.isArray(e)?e:[e],n=e.length-1;n>=0;n--)for(r in t)e[n].prototype[r]=t[r]}function z(e){return function(...t){const r=t[t.length-1];return r&&r.constructor===Object&&!(r instanceof Array)?e.apply(this,t.slice(0,-1)).attr(r):e.apply(this,t)}}function _p(){return this.parent().children()}function mp(){return this.parent().index(this)}function bp(){return this.siblings()[this.position()+1]}function wp(){return this.siblings()[this.position()-1]}function xp(){const e=this.position();return this.parent().add(this.remove(),e+1),this}function Ap(){const e=this.position();return this.parent().add(this.remove(),e?e-1:0),this}function Sp(){return this.parent().add(this.remove()),this}function Tp(){return this.parent().add(this.remove(),0),this}function Cp(e){e=Y(e),e.remove();const t=this.position();return this.parent().add(e,t),this}function Ep(e){e=Y(e),e.remove();const t=this.position();return this.parent().add(e,t+1),this}function qp(e){return e=Y(e),e.before(this),this}function Rp(e){return e=Y(e),e.after(this),this}O("Dom",{siblings:_p,position:mp,next:bp,prev:wp,forward:xp,backward:Ap,front:Sp,back:Tp,before:Cp,after:Ep,insertBefore:qp,insertAfter:Rp});const nf=/^([+-]?(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?)([a-z%]*)$/i,Op=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,Ip=/rgb\((\d+),(\d+),(\d+)\)/,Mp=/(#[a-z_][a-z0-9\-_]*)/i,Pp=/\)\s*,?\s*/,Lp=/\s/g,go=/^#[a-f0-9]{3}$|^#[a-f0-9]{6}$/i,vo=/^rgb\(/,yo=/^(\s+)?$/,_o=/^[+-]?(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,jp=/\.(jpg|jpeg|png|gif|svg)(\?[^=]+.*)?/i,ht=/[\s,]+/,Sa=/[MLHVCSQTAZ]/i;function Np(){const e=this.attr("class");return e==null?[]:e.trim().split(ht)}function Dp(e){return this.classes().indexOf(e)!==-1}function Fp(e){if(!this.hasClass(e)){const t=this.classes();t.push(e),this.attr("class",t.join(" "))}return this}function kp(e){return this.hasClass(e)&&this.attr("class",this.classes().filter(function(t){return t!==e}).join(" ")),this}function $p(e){return this.hasClass(e)?this.removeClass(e):this.addClass(e)}O("Dom",{classes:Np,hasClass:Dp,addClass:Fp,removeClass:kp,toggleClass:$p});function zp(e,t){const r={};if(arguments.length===0)return this.node.style.cssText.split(/\s*;\s*/).filter(function(n){return!!n.length}).forEach(function(n){const i=n.split(/\s*:\s*/);r[i[0]]=i[1]}),r;if(arguments.length<2){if(Array.isArray(e)){for(const n of e){const i=n;r[n]=this.node.style.getPropertyValue(i)}return r}if(typeof e=="string")return this.node.style.getPropertyValue(e);if(typeof e=="object")for(const n in e)this.node.style.setProperty(n,e[n]==null||yo.test(e[n])?"":e[n])}return arguments.length===2&&this.node.style.setProperty(e,t==null||yo.test(t)?"":t),this}function Bp(){return this.css("display","")}function Hp(){return this.css("display","none")}function Up(){return this.css("display")!=="none"}O("Dom",{css:zp,show:Bp,hide:Hp,visible:Up});function Kp(e,t,r){if(e==null)return this.data(ba(hp(this.node.attributes,n=>n.nodeName.indexOf("data-")===0),n=>n.nodeName.slice(5)));if(e instanceof Array){const n={};for(const i of e)n[i]=this.data(i);return n}else if(typeof e=="object")for(t in e)this.data(t,e[t]);else if(arguments.length<2)try{return JSON.parse(this.attr("data-"+e))}catch{return this.attr("data-"+e)}else this.attr("data-"+e,t===null?null:r===!0||typeof t=="string"||typeof t=="number"?t:JSON.stringify(t));return this}O("Dom",{data:Kp});function Wp(e,t){if(typeof arguments[0]=="object")for(const r in e)this.remember(r,e[r]);else{if(arguments.length===1)return this.memory()[e];this.memory()[e]=t}return this}function Vp(){if(arguments.length===0)this._memory={};else for(let e=arguments.length-1;e>=0;e--)delete this.memory()[arguments[e]];return this}function Yp(){return this._memory=this._memory||{}}O("Dom",{remember:Wp,forget:Vp,memory:Yp});function Jp(e){return e.length===4?["#",e.substring(1,2),e.substring(1,2),e.substring(2,3),e.substring(2,3),e.substring(3,4),e.substring(3,4)].join(""):e}function Zp(e){const t=Math.round(e),n=Math.max(0,Math.min(255,t)).toString(16);return n.length===1?"0"+n:n}function Ut(e,t){for(let r=t.length;r--;)if(e[t[r]]==null)return!1;return!0}function Xp(e,t){const r=Ut(e,"rgb")?{_a:e.r,_b:e.g,_c:e.b,_d:0,space:"rgb"}:Ut(e,"xyz")?{_a:e.x,_b:e.y,_c:e.z,_d:0,space:"xyz"}:Ut(e,"hsl")?{_a:e.h,_b:e.s,_c:e.l,_d:0,space:"hsl"}:Ut(e,"lab")?{_a:e.l,_b:e.a,_c:e.b,_d:0,space:"lab"}:Ut(e,"lch")?{_a:e.l,_b:e.c,_c:e.h,_d:0,space:"lch"}:Ut(e,"cmyk")?{_a:e.c,_b:e.m,_c:e.y,_d:e.k,space:"cmyk"}:{_a:0,_b:0,_c:0,space:"rgb"};return r.space=t||r.space,r}function Gp(e){return e==="lab"||e==="xyz"||e==="lch"}function yr(e,t,r){return r<0&&(r+=1),r>1&&(r-=1),r<1/6?e+(t-e)*6*r:r<1/2?t:r<2/3?e+(t-e)*(2/3-r)*6:e}class D{constructor(...t){this.init(...t)}static isColor(t){return t&&(t instanceof D||this.isRgb(t)||this.test(t))}static isRgb(t){return t&&typeof t.r=="number"&&typeof t.g=="number"&&typeof t.b=="number"}static random(t="vibrant",r){const{random:n,round:i,sin:s,PI:a}=Math;if(t==="vibrant"){const o=24*n()+57,u=38*n()+45,c=360*n();return new D(o,u,c,"lch")}else if(t==="sine"){r=r??n();const o=i(80*s(2*a*r/.5+.01)+150),u=i(50*s(2*a*r/.5****)+200),c=i(100*s(2*a*r/.5****)+150);return new D(o,u,c)}else if(t==="pastel"){const o=8*n()+86,u=17*n()+9,c=360*n();return new D(o,u,c,"lch")}else if(t==="dark"){const o=10+10*n(),u=50*n()+86,c=360*n();return new D(o,u,c,"lch")}else if(t==="rgb"){const o=255*n(),u=255*n(),c=255*n();return new D(o,u,c)}else if(t==="lab"){const o=100*n(),u=256*n()-128,c=256*n()-128;return new D(o,u,c,"lab")}else if(t==="grey"){const o=255*n();return new D(o,o,o)}else throw new Error("Unsupported random color mode")}static test(t){return typeof t=="string"&&(go.test(t)||vo.test(t))}cmyk(){const{_a:t,_b:r,_c:n}=this.rgb(),[i,s,a]=[t,r,n].map(f=>f/255),o=Math.min(1-i,1-s,1-a);if(o===1)return new D(0,0,0,1,"cmyk");const u=(1-i-o)/(1-o),c=(1-s-o)/(1-o),l=(1-a-o)/(1-o);return new D(u,c,l,o,"cmyk")}hsl(){const{_a:t,_b:r,_c:n}=this.rgb(),[i,s,a]=[t,r,n].map(p=>p/255),o=Math.max(i,s,a),u=Math.min(i,s,a),c=(o+u)/2,l=o===u,h=o-u,f=l?0:c>.5?h/(2-o-u):h/(o+u),d=l?0:o===i?((s-a)/h+(s<a?6:0))/6:o===s?((a-i)/h+2)/6:o===a?((i-s)/h+4)/6:0;return new D(360*d,100*f,100*c,"hsl")}init(t=0,r=0,n=0,i=0,s="rgb"){if(t=t||0,this.space)for(const h in this.space)delete this[this.space[h]];if(typeof t=="number")s=typeof i=="string"?i:s,i=typeof i=="string"?0:i,Object.assign(this,{_a:t,_b:r,_c:n,_d:i,space:s});else if(t instanceof Array)this.space=r||(typeof t[3]=="string"?t[3]:t[4])||"rgb",Object.assign(this,{_a:t[0],_b:t[1],_c:t[2],_d:t[3]||0});else if(t instanceof Object){const h=Xp(t,r);Object.assign(this,h)}else if(typeof t=="string")if(vo.test(t)){const h=t.replace(Lp,""),[f,d,g]=Ip.exec(h).slice(1,4).map(p=>parseInt(p));Object.assign(this,{_a:f,_b:d,_c:g,_d:0,space:"rgb"})}else if(go.test(t)){const h=p=>parseInt(p,16),[,f,d,g]=Op.exec(Jp(t)).map(h);Object.assign(this,{_a:f,_b:d,_c:g,_d:0,space:"rgb"})}else throw Error("Unsupported string format, can't construct Color");const{_a:a,_b:o,_c:u,_d:c}=this,l=this.space==="rgb"?{r:a,g:o,b:u}:this.space==="xyz"?{x:a,y:o,z:u}:this.space==="hsl"?{h:a,s:o,l:u}:this.space==="lab"?{l:a,a:o,b:u}:this.space==="lch"?{l:a,c:o,h:u}:this.space==="cmyk"?{c:a,m:o,y:u,k:c}:{};Object.assign(this,l)}lab(){const{x:t,y:r,z:n}=this.xyz(),i=116*r-16,s=500*(t-r),a=200*(r-n);return new D(i,s,a,"lab")}lch(){const{l:t,a:r,b:n}=this.lab(),i=Math.sqrt(r**2+n**2);let s=180*Math.atan2(n,r)/Math.PI;return s<0&&(s*=-1,s=360-s),new D(t,i,s,"lch")}rgb(){if(this.space==="rgb")return this;if(Gp(this.space)){let{x:t,y:r,z:n}=this;if(this.space==="lab"||this.space==="lch"){let{l:d,a:g,b:p}=this;if(this.space==="lch"){const{c:w,h:x}=this,R=Math.PI/180;g=w*Math.cos(R*x),p=w*Math.sin(R*x)}const v=(d+16)/116,y=g/500+v,_=v-p/200,b=16/116,m=.008856,S=7.787;t=.95047*(y**3>m?y**3:(y-b)/S),r=1*(v**3>m?v**3:(v-b)/S),n=1.08883*(_**3>m?_**3:(_-b)/S)}const i=t*3.2406+r*-1.5372+n*-.4986,s=t*-.9689+r*1.8758+n*.0415,a=t*.0557+r*-.204+n*1.057,o=Math.pow,u=.0031308,c=i>u?1.055*o(i,1/2.4)-.055:12.92*i,l=s>u?1.055*o(s,1/2.4)-.055:12.92*s,h=a>u?1.055*o(a,1/2.4)-.055:12.92*a;return new D(255*c,255*l,255*h)}else if(this.space==="hsl"){let{h:t,s:r,l:n}=this;if(t/=360,r/=100,n/=100,r===0)return n*=255,new D(n,n,n);const i=n<.5?n*(1+r):n+r-n*r,s=2*n-i,a=255*yr(s,i,t+1/3),o=255*yr(s,i,t),u=255*yr(s,i,t-1/3);return new D(a,o,u)}else if(this.space==="cmyk"){const{c:t,m:r,y:n,k:i}=this,s=255*(1-Math.min(1,t*(1-i)+i)),a=255*(1-Math.min(1,r*(1-i)+i)),o=255*(1-Math.min(1,n*(1-i)+i));return new D(s,a,o)}else return this}toArray(){const{_a:t,_b:r,_c:n,_d:i,space:s}=this;return[t,r,n,i,s]}toHex(){const[t,r,n]=this._clamped().map(Zp);return`#${t}${r}${n}`}toRgb(){const[t,r,n]=this._clamped();return`rgb(${t},${r},${n})`}toString(){return this.toHex()}xyz(){const{_a:t,_b:r,_c:n}=this.rgb(),[i,s,a]=[t,r,n].map(y=>y/255),o=i>.04045?Math.pow((i+.055)/1.055,2.4):i/12.92,u=s>.04045?Math.pow((s+.055)/1.055,2.4):s/12.92,c=a>.04045?Math.pow((a+.055)/1.055,2.4):a/12.92,l=(o*.4124+u*.3576+c*.1805)/.95047,h=(o*.2126+u*.7152+c*.0722)/1,f=(o*.0193+u*.1192+c*.9505)/1.08883,d=l>.008856?Math.pow(l,1/3):7.787*l+16/116,g=h>.008856?Math.pow(h,1/3):7.787*h+16/116,p=f>.008856?Math.pow(f,1/3):7.787*f+16/116;return new D(d,g,p,"xyz")}_clamped(){const{_a:t,_b:r,_c:n}=this.rgb(),{max:i,min:s,round:a}=Math,o=u=>i(0,s(a(u),255));return[t,r,n].map(o)}}class H{constructor(...t){this.init(...t)}clone(){return new H(this)}init(t,r){const n={x:0,y:0},i=Array.isArray(t)?{x:t[0],y:t[1]}:typeof t=="object"?{x:t.x,y:t.y}:{x:t,y:r};return this.x=i.x==null?n.x:i.x,this.y=i.y==null?n.y:i.y,this}toArray(){return[this.x,this.y]}transform(t){return this.clone().transformO(t)}transformO(t){T.isMatrixLike(t)||(t=new T(t));const{x:r,y:n}=this;return this.x=t.a*r+t.c*n+t.e,this.y=t.b*r+t.d*n+t.f,this}}function Qp(e,t){return new H(e,t).transformO(this.screenCTM().inverseO())}function Kt(e,t,r){return Math.abs(t-e)<1e-6}class T{constructor(...t){this.init(...t)}static formatTransforms(t){const r=t.flip==="both"||t.flip===!0,n=t.flip&&(r||t.flip==="x")?-1:1,i=t.flip&&(r||t.flip==="y")?-1:1,s=t.skew&&t.skew.length?t.skew[0]:isFinite(t.skew)?t.skew:isFinite(t.skewX)?t.skewX:0,a=t.skew&&t.skew.length?t.skew[1]:isFinite(t.skew)?t.skew:isFinite(t.skewY)?t.skewY:0,o=t.scale&&t.scale.length?t.scale[0]*n:isFinite(t.scale)?t.scale*n:isFinite(t.scaleX)?t.scaleX*n:n,u=t.scale&&t.scale.length?t.scale[1]*i:isFinite(t.scale)?t.scale*i:isFinite(t.scaleY)?t.scaleY*i:i,c=t.shear||0,l=t.rotate||t.theta||0,h=new H(t.origin||t.around||t.ox||t.originX,t.oy||t.originY),f=h.x,d=h.y,g=new H(t.position||t.px||t.positionX||NaN,t.py||t.positionY||NaN),p=g.x,v=g.y,y=new H(t.translate||t.tx||t.translateX,t.ty||t.translateY),_=y.x,b=y.y,m=new H(t.relative||t.rx||t.relativeX,t.ry||t.relativeY),S=m.x,w=m.y;return{scaleX:o,scaleY:u,skewX:s,skewY:a,shear:c,theta:l,rx:S,ry:w,tx:_,ty:b,ox:f,oy:d,px:p,py:v}}static fromArray(t){return{a:t[0],b:t[1],c:t[2],d:t[3],e:t[4],f:t[5]}}static isMatrixLike(t){return t.a!=null||t.b!=null||t.c!=null||t.d!=null||t.e!=null||t.f!=null}static matrixMultiply(t,r,n){const i=t.a*r.a+t.c*r.b,s=t.b*r.a+t.d*r.b,a=t.a*r.c+t.c*r.d,o=t.b*r.c+t.d*r.d,u=t.e+t.a*r.e+t.c*r.f,c=t.f+t.b*r.e+t.d*r.f;return n.a=i,n.b=s,n.c=a,n.d=o,n.e=u,n.f=c,n}around(t,r,n){return this.clone().aroundO(t,r,n)}aroundO(t,r,n){const i=t||0,s=r||0;return this.translateO(-i,-s).lmultiplyO(n).translateO(i,s)}clone(){return new T(this)}decompose(t=0,r=0){const n=this.a,i=this.b,s=this.c,a=this.d,o=this.e,u=this.f,c=n*a-i*s,l=c>0?1:-1,h=l*Math.sqrt(n*n+i*i),f=Math.atan2(l*i,l*n),d=180/Math.PI*f,g=Math.cos(f),p=Math.sin(f),v=(n*s+i*a)/c,y=s*h/(v*n-i)||a*h/(v*i+n),_=o-t+t*g*h+r*(v*g*h-p*y),b=u-r+t*p*h+r*(v*p*h+g*y);return{scaleX:h,scaleY:y,shear:v,rotate:d,translateX:_,translateY:b,originX:t,originY:r,a:this.a,b:this.b,c:this.c,d:this.d,e:this.e,f:this.f}}equals(t){if(t===this)return!0;const r=new T(t);return Kt(this.a,r.a)&&Kt(this.b,r.b)&&Kt(this.c,r.c)&&Kt(this.d,r.d)&&Kt(this.e,r.e)&&Kt(this.f,r.f)}flip(t,r){return this.clone().flipO(t,r)}flipO(t,r){return t==="x"?this.scaleO(-1,1,r,0):t==="y"?this.scaleO(1,-1,0,r):this.scaleO(-1,-1,t,r||t)}init(t){const r=T.fromArray([1,0,0,1,0,0]);return t=t instanceof at?t.matrixify():typeof t=="string"?T.fromArray(t.split(ht).map(parseFloat)):Array.isArray(t)?T.fromArray(t):typeof t=="object"&&T.isMatrixLike(t)?t:typeof t=="object"?new T().transform(t):arguments.length===6?T.fromArray([].slice.call(arguments)):r,this.a=t.a!=null?t.a:r.a,this.b=t.b!=null?t.b:r.b,this.c=t.c!=null?t.c:r.c,this.d=t.d!=null?t.d:r.d,this.e=t.e!=null?t.e:r.e,this.f=t.f!=null?t.f:r.f,this}inverse(){return this.clone().inverseO()}inverseO(){const t=this.a,r=this.b,n=this.c,i=this.d,s=this.e,a=this.f,o=t*i-r*n;if(!o)throw new Error("Cannot invert "+this);const u=i/o,c=-r/o,l=-n/o,h=t/o,f=-(u*s+l*a),d=-(c*s+h*a);return this.a=u,this.b=c,this.c=l,this.d=h,this.e=f,this.f=d,this}lmultiply(t){return this.clone().lmultiplyO(t)}lmultiplyO(t){const r=this,n=t instanceof T?t:new T(t);return T.matrixMultiply(n,r,this)}multiply(t){return this.clone().multiplyO(t)}multiplyO(t){const r=this,n=t instanceof T?t:new T(t);return T.matrixMultiply(r,n,this)}rotate(t,r,n){return this.clone().rotateO(t,r,n)}rotateO(t,r=0,n=0){t=pr(t);const i=Math.cos(t),s=Math.sin(t),{a,b:o,c:u,d:c,e:l,f:h}=this;return this.a=a*i-o*s,this.b=o*i+a*s,this.c=u*i-c*s,this.d=c*i+u*s,this.e=l*i-h*s+n*s-r*i+r,this.f=h*i+l*s-r*s-n*i+n,this}scale(){return this.clone().scaleO(...arguments)}scaleO(t,r=t,n=0,i=0){arguments.length===3&&(i=n,n=r,r=t);const{a:s,b:a,c:o,d:u,e:c,f:l}=this;return this.a=s*t,this.b=a*r,this.c=o*t,this.d=u*r,this.e=c*t-n*t+n,this.f=l*r-i*r+i,this}shear(t,r,n){return this.clone().shearO(t,r,n)}shearO(t,r=0,n=0){const{a:i,b:s,c:a,d:o,e:u,f:c}=this;return this.a=i+s*t,this.c=a+o*t,this.e=u+c*t-n*t,this}skew(){return this.clone().skewO(...arguments)}skewO(t,r=t,n=0,i=0){arguments.length===3&&(i=n,n=r,r=t),t=pr(t),r=pr(r);const s=Math.tan(t),a=Math.tan(r),{a:o,b:u,c,d:l,e:h,f}=this;return this.a=o+u*s,this.b=u+o*a,this.c=c+l*s,this.d=l+c*a,this.e=h+f*s-i*s,this.f=f+h*a-n*a,this}skewX(t,r,n){return this.skew(t,0,r,n)}skewY(t,r,n){return this.skew(0,t,r,n)}toArray(){return[this.a,this.b,this.c,this.d,this.e,this.f]}toString(){return"matrix("+this.a+","+this.b+","+this.c+","+this.d+","+this.e+","+this.f+")"}transform(t){if(T.isMatrixLike(t))return new T(t).multiplyO(this);const r=T.formatTransforms(t),n=this,{x:i,y:s}=new H(r.ox,r.oy).transform(n),a=new T().translateO(r.rx,r.ry).lmultiplyO(n).translateO(-i,-s).scaleO(r.scaleX,r.scaleY).skewO(r.skewX,r.skewY).shearO(r.shear).rotateO(r.theta).translateO(i,s);if(isFinite(r.px)||isFinite(r.py)){const o=new H(i,s).transform(a),u=isFinite(r.px)?r.px-o.x:0,c=isFinite(r.py)?r.py-o.y:0;a.translateO(u,c)}return a.translateO(r.tx,r.ty),a}translate(t,r){return this.clone().translateO(t,r)}translateO(t,r){return this.e+=t||0,this.f+=r||0,this}valueOf(){return{a:this.a,b:this.b,c:this.c,d:this.d,e:this.e,f:this.f}}}function tg(){return new T(this.node.getCTM())}function eg(){try{if(typeof this.isRoot=="function"&&!this.isRoot()){const e=this.rect(1,1),t=e.node.getScreenCTM();return e.remove(),new T(t)}return new T(this.node.getScreenCTM())}catch{return console.warn(`Cannot get CTM from SVG node ${this.node.nodeName}. Is the element rendered?`),new T}}L(T,"Matrix");function mt(){if(!mt.nodes){const e=Y().size(2,0);e.node.style.cssText=["opacity: 0","position: absolute","left: -100%","top: -100%","overflow: hidden"].join(";"),e.attr("focusable","false"),e.attr("aria-hidden","true");const t=e.path().node;mt.nodes={svg:e,path:t}}if(!mt.nodes.svg.node.parentNode){const e=P.document.body||P.document.documentElement;mt.nodes.svg.addTo(e)}return mt.nodes}function sf(e){return!e.width&&!e.height&&!e.x&&!e.y}function rg(e){return e===P.document||(P.document.documentElement.contains||function(t){for(;t.parentNode;)t=t.parentNode;return t===P.document}).call(P.document.documentElement,e)}class V{constructor(...t){this.init(...t)}addOffset(){return this.x+=P.window.pageXOffset,this.y+=P.window.pageYOffset,new V(this)}init(t){const r=[0,0,0,0];return t=typeof t=="string"?t.split(ht).map(parseFloat):Array.isArray(t)?t:typeof t=="object"?[t.left!=null?t.left:t.x,t.top!=null?t.top:t.y,t.width,t.height]:arguments.length===4?[].slice.call(arguments):r,this.x=t[0]||0,this.y=t[1]||0,this.width=this.w=t[2]||0,this.height=this.h=t[3]||0,this.x2=this.x+this.w,this.y2=this.y+this.h,this.cx=this.x+this.w/2,this.cy=this.y+this.h/2,this}isNulled(){return sf(this)}merge(t){const r=Math.min(this.x,t.x),n=Math.min(this.y,t.y),i=Math.max(this.x+this.width,t.x+t.width)-r,s=Math.max(this.y+this.height,t.y+t.height)-n;return new V(r,n,i,s)}toArray(){return[this.x,this.y,this.width,this.height]}toString(){return this.x+" "+this.y+" "+this.width+" "+this.height}transform(t){t instanceof T||(t=new T(t));let r=1/0,n=-1/0,i=1/0,s=-1/0;return[new H(this.x,this.y),new H(this.x2,this.y),new H(this.x,this.y2),new H(this.x2,this.y2)].forEach(function(o){o=o.transform(t),r=Math.min(r,o.x),n=Math.max(n,o.x),i=Math.min(i,o.y),s=Math.max(s,o.y)}),new V(r,i,n-r,s-i)}}function af(e,t,r){let n;try{if(n=t(e.node),sf(n)&&!rg(e.node))throw new Error("Element not in the dom")}catch{n=r(e)}return n}function ng(){const r=af(this,i=>i.getBBox(),i=>{try{const s=i.clone().addTo(mt().svg).show(),a=s.node.getBBox();return s.remove(),a}catch(s){throw new Error(`Getting bbox of element "${i.node.nodeName}" is not possible: ${s.toString()}`)}});return new V(r)}function ig(e){const n=af(this,s=>s.getBoundingClientRect(),s=>{throw new Error(`Getting rbox of element "${s.node.nodeName}" is not possible`)}),i=new V(n);return e?i.transform(e.screenCTM().inverseO()):i.addOffset()}function sg(e,t){const r=this.bbox();return e>r.x&&t>r.y&&e<r.x+r.width&&t<r.y+r.height}O({viewbox:{viewbox(e,t,r,n){return e==null?new V(this.attr("viewBox")):this.attr("viewBox",new V(e,t,r,n))},zoom(e,t){let{width:r,height:n}=this.attr(["width","height"]);if((!r&&!n||typeof r=="string"||typeof n=="string")&&(r=this.node.clientWidth,n=this.node.clientHeight),!r||!n)throw new Error("Impossible to get absolute width and height. Please provide an absolute width and height attribute on the zooming element");const i=this.viewbox(),s=r/i.width,a=n/i.height,o=Math.min(s,a);if(e==null)return o;let u=o/e;u===1/0&&(u=Number.MAX_SAFE_INTEGER/100),t=t||new H(r/2/s+i.x,n/2/a+i.y);const c=new V(i).transform(new T({scale:u,origin:t}));return this.viewbox(c)}}});L(V,"Box");class Et extends Array{constructor(t=[],...r){if(super(t,...r),typeof t=="number")return this;this.length=0,this.push(...t)}}M([Et],{each(e,...t){return typeof e=="function"?this.map((r,n,i)=>e.call(r,r,n,i)):this.map(r=>r[e](...t))},toArray(){return Array.prototype.concat.apply([],this)}});const ag=["toArray","constructor","each"];Et.extend=function(e){e=e.reduce((t,r)=>(ag.includes(r)||r[0]==="_"||(r in Array.prototype&&(t["$"+r]=Array.prototype[r]),t[r]=function(...n){return this.each(r,...n)}),t),{}),M([Et],e)};function Xt(e,t){return new Et(ba((t||P.document).querySelectorAll(e),function(r){return tt(r)}))}function og(e){return Xt(e,this.node)}function ug(e){return tt(this.node.querySelector(e))}let cg=0;const of={};function uf(e){let t=e.getEventHolder();return t===P.window&&(t=of),t.events||(t.events={}),t.events}function Ta(e){return e.getEventTarget()}function lg(e){let t=e.getEventHolder();t===P.window&&(t=of),t.events&&(t.events={})}function na(e,t,r,n,i){const s=r.bind(n||e),a=Y(e),o=uf(a),u=Ta(a);t=Array.isArray(t)?t:t.split(ht),r._svgjsListenerId||(r._svgjsListenerId=++cg),t.forEach(function(c){const l=c.split(".")[0],h=c.split(".")[1]||"*";o[l]=o[l]||{},o[l][h]=o[l][h]||{},o[l][h][r._svgjsListenerId]=s,u.addEventListener(l,s,i||!1)})}function Vt(e,t,r,n){const i=Y(e),s=uf(i),a=Ta(i);typeof r=="function"&&(r=r._svgjsListenerId,!r)||(t=Array.isArray(t)?t:(t||"").split(ht),t.forEach(function(o){const u=o&&o.split(".")[0],c=o&&o.split(".")[1];let l,h;if(r)s[u]&&s[u][c||"*"]&&(a.removeEventListener(u,s[u][c||"*"][r],n||!1),delete s[u][c||"*"][r]);else if(u&&c){if(s[u]&&s[u][c]){for(h in s[u][c])Vt(a,[u,c].join("."),h);delete s[u][c]}}else if(c)for(o in s)for(l in s[o])c===l&&Vt(a,[o,c].join("."));else if(u){if(s[u]){for(l in s[u])Vt(a,[u,l].join("."));delete s[u]}}else{for(o in s)Vt(a,o);lg(i)}}))}function hg(e,t,r,n){const i=Ta(e);return t instanceof P.window.Event||(t=new P.window.CustomEvent(t,{detail:r,cancelable:!0,...n})),i.dispatchEvent(t),t}class ye extends xa{addEventListener(){}dispatch(t,r,n){return hg(this,t,r,n)}dispatchEvent(t){const r=this.getEventHolder().events;if(!r)return!0;const n=r[t.type];for(const i in n)for(const s in n[i])n[i][s](t);return!t.defaultPrevented}fire(t,r,n){return this.dispatch(t,r,n),this}getEventHolder(){return this}getEventTarget(){return this}off(t,r,n){return Vt(this,t,r,n),this}on(t,r,n,i){return na(this,t,r,n,i),this}removeEventListener(){}}L(ye,"EventTarget");function mo(){}const se={duration:400,ease:">",delay:0},fg={"fill-opacity":1,"stroke-opacity":1,"stroke-width":0,"stroke-linejoin":"miter","stroke-linecap":"butt",fill:"#000000",stroke:"#000000",opacity:1,x:0,y:0,cx:0,cy:0,width:0,height:0,r:0,rx:0,ry:0,offset:0,"stop-opacity":1,"stop-color":"#000000","text-anchor":"start"};class Jt extends Array{constructor(...t){super(...t),this.init(...t)}clone(){return new this.constructor(this)}init(t){return typeof t=="number"?this:(this.length=0,this.push(...this.parse(t)),this)}parse(t=[]){return t instanceof Array?t:t.trim().split(ht).map(parseFloat)}toArray(){return Array.prototype.concat.apply([],this)}toSet(){return new Set(this)}toString(){return this.join(" ")}valueOf(){const t=[];return t.push(...this),t}}class E{constructor(...t){this.init(...t)}convert(t){return new E(this.value,t)}divide(t){return t=new E(t),new E(this/t,this.unit||t.unit)}init(t,r){return r=Array.isArray(t)?t[1]:r,t=Array.isArray(t)?t[0]:t,this.value=0,this.unit=r||"",typeof t=="number"?this.value=isNaN(t)?0:isFinite(t)?t:t<0?-34e37:34e37:typeof t=="string"?(r=t.match(nf),r&&(this.value=parseFloat(r[1]),r[5]==="%"?this.value/=100:r[5]==="s"&&(this.value*=1e3),this.unit=r[5])):t instanceof E&&(this.value=t.valueOf(),this.unit=t.unit),this}minus(t){return t=new E(t),new E(this-t,this.unit||t.unit)}plus(t){return t=new E(t),new E(this+t,this.unit||t.unit)}times(t){return t=new E(t),new E(this*t,this.unit||t.unit)}toArray(){return[this.value,this.unit]}toJSON(){return this.toString()}toString(){return(this.unit==="%"?~~(this.value*1e8)/1e6:this.unit==="s"?this.value/1e3:this.value)+this.unit}valueOf(){return this.value}}const dg=new Set(["fill","stroke","color","bgcolor","stop-color","flood-color","lighting-color"]),cf=[];function pg(e){cf.push(e)}function gg(e,t,r){if(e==null){e={},t=this.node.attributes;for(const n of t)e[n.nodeName]=_o.test(n.nodeValue)?parseFloat(n.nodeValue):n.nodeValue;return e}else{if(e instanceof Array)return e.reduce((n,i)=>(n[i]=this.attr(i),n),{});if(typeof e=="object"&&e.constructor===Object)for(t in e)this.attr(t,e[t]);else if(t===null)this.node.removeAttribute(e);else{if(t==null)return t=this.node.getAttribute(e),t==null?fg[e]:_o.test(t)?parseFloat(t):t;t=cf.reduce((n,i)=>i(e,n,this),t),typeof t=="number"?t=new E(t):dg.has(e)&&D.isColor(t)?t=new D(t):t.constructor===Array&&(t=new Jt(t)),e==="leading"?this.leading&&this.leading(t):typeof r=="string"?this.node.setAttributeNS(r,e,t.toString()):this.node.setAttribute(e,t.toString()),this.rebuild&&(e==="font-size"||e==="x")&&this.rebuild()}}return this}class bt extends ye{constructor(t,r){super(),this.node=t,this.type=t.nodeName,r&&t!==r&&this.attr(r)}add(t,r){return t=Y(t),t.removeNamespace&&this.node instanceof P.window.SVGElement&&t.removeNamespace(),r==null?this.node.appendChild(t.node):t.node!==this.node.childNodes[r]&&this.node.insertBefore(t.node,this.node.childNodes[r]),this}addTo(t,r){return Y(t).put(this,r)}children(){return new Et(ba(this.node.children,function(t){return tt(t)}))}clear(){for(;this.node.hasChildNodes();)this.node.removeChild(this.node.lastChild);return this}clone(t=!0,r=!0){this.writeDataToDom();let n=this.node.cloneNode(t);return r&&(n=rf(n)),new this.constructor(n)}each(t,r){const n=this.children();let i,s;for(i=0,s=n.length;i<s;i++)t.apply(n[i],[i,n]),r&&n[i].each(t,r);return this}element(t,r){return this.put(new bt(le(t),r))}first(){return tt(this.node.firstChild)}get(t){return tt(this.node.childNodes[t])}getEventHolder(){return this.node}getEventTarget(){return this.node}has(t){return this.index(t)>=0}html(t,r){return this.xml(t,r,pp)}id(t){return typeof t>"u"&&!this.node.id&&(this.node.id=ef(this.type)),this.attr("id",t)}index(t){return[].slice.call(this.node.childNodes).indexOf(t.node)}last(){return tt(this.node.lastChild)}matches(t){const r=this.node,n=r.matches||r.matchesSelector||r.msMatchesSelector||r.mozMatchesSelector||r.webkitMatchesSelector||r.oMatchesSelector||null;return n&&n.call(r,t)}parent(t){let r=this;if(!r.node.parentNode)return null;if(r=tt(r.node.parentNode),!t)return r;do if(typeof t=="string"?r.matches(t):r instanceof t)return r;while(r=tt(r.node.parentNode));return r}put(t,r){return t=Y(t),this.add(t,r),t}putIn(t,r){return Y(t).add(this,r)}remove(){return this.parent()&&this.parent().removeElement(this),this}removeElement(t){return this.node.removeChild(t.node),this}replace(t){return t=Y(t),this.node.parentNode&&this.node.parentNode.replaceChild(t.node,this.node),t}round(t=2,r=null){const n=10**t,i=this.attr(r);for(const s in i)typeof i[s]=="number"&&(i[s]=Math.round(i[s]*n)/n);return this.attr(i),this}svg(t,r){return this.xml(t,r,wa)}toString(){return this.id()}words(t){return this.node.textContent=t,this}wrap(t){const r=this.parent();if(!r)return this.addTo(t);const n=r.index(this);return r.put(t,n).put(this)}writeDataToDom(){return this.each(function(){this.writeDataToDom()}),this}xml(t,r,n){if(typeof t=="boolean"&&(n=r,r=t,t=null),t==null||typeof t=="function"){r=r??!0,this.writeDataToDom();let o=this;if(t!=null){if(o=tt(o.node.cloneNode(!0)),r){const u=t(o);if(o=u||o,u===!1)return""}o.each(function(){const u=t(this),c=u||this;u===!1?this.remove():u&&this!==c&&this.replace(c)},!0)}return r?o.node.outerHTML:o.node.innerHTML}r=r??!1;const i=le("wrapper",n),s=P.document.createDocumentFragment();i.innerHTML=t;for(let o=i.children.length;o--;)s.appendChild(i.firstElementChild);const a=this.parent();return r?this.replace(s)&&a:this.add(s)}}M(bt,{attr:gg,find:og,findOne:ug});L(bt,"Dom");class at extends bt{constructor(t,r){super(t,r),this.dom={},this.node.instance=this,(t.hasAttribute("data-svgjs")||t.hasAttribute("svgjs:data"))&&this.setData(JSON.parse(t.getAttribute("data-svgjs"))??JSON.parse(t.getAttribute("svgjs:data"))??{})}center(t,r){return this.cx(t).cy(r)}cx(t){return t==null?this.x()+this.width()/2:this.x(t-this.width()/2)}cy(t){return t==null?this.y()+this.height()/2:this.y(t-this.height()/2)}defs(){const t=this.root();return t&&t.defs()}dmove(t,r){return this.dx(t).dy(r)}dx(t=0){return this.x(new E(t).plus(this.x()))}dy(t=0){return this.y(new E(t).plus(this.y()))}getEventHolder(){return this}height(t){return this.attr("height",t)}move(t,r){return this.x(t).y(r)}parents(t=this.root()){const r=typeof t=="string";r||(t=Y(t));const n=new Et;let i=this;for(;(i=i.parent())&&i.node!==P.document&&i.nodeName!=="#document-fragment"&&(n.push(i),!(!r&&i.node===t.node||r&&i.matches(t)));)if(i.node===this.root().node)return null;return n}reference(t){if(t=this.attr(t),!t)return null;const r=(t+"").match(Mp);return r?Y(r[1]):null}root(){const t=this.parent(vp(Aa));return t&&t.root()}setData(t){return this.dom=t,this}size(t,r){const n=Zt(this,t,r);return this.width(new E(n.width)).height(new E(n.height))}width(t){return this.attr("width",t)}writeDataToDom(){return tf(this,this.dom),super.writeDataToDom()}x(t){return this.attr("x",t)}y(t){return this.attr("y",t)}}M(at,{bbox:ng,rbox:ig,inside:sg,point:Qp,ctm:tg,screenCTM:eg});L(at,"Element");const re={stroke:["color","width","opacity","linecap","linejoin","miterlimit","dasharray","dashoffset"],fill:["color","opacity","rule"],prefix:function(e,t){return t==="color"?e:e+"-"+t}};["fill","stroke"].forEach(function(e){const t={};let r;t[e]=function(n){if(typeof n>"u")return this.attr(e);if(typeof n=="string"||n instanceof D||D.isRgb(n)||n instanceof at)this.attr(e,n);else for(r=re[e].length-1;r>=0;r--)n[re[e][r]]!=null&&this.attr(re.prefix(e,re[e][r]),n[re[e][r]]);return this},O(["Element","Runner"],t)});O(["Element","Runner"],{matrix:function(e,t,r,n,i,s){return e==null?new T(this):this.attr("transform",new T(e,t,r,n,i,s))},rotate:function(e,t,r){return this.transform({rotate:e,ox:t,oy:r},!0)},skew:function(e,t,r,n){return arguments.length===1||arguments.length===3?this.transform({skew:e,ox:t,oy:r},!0):this.transform({skew:[e,t],ox:r,oy:n},!0)},shear:function(e,t,r){return this.transform({shear:e,ox:t,oy:r},!0)},scale:function(e,t,r,n){return arguments.length===1||arguments.length===3?this.transform({scale:e,ox:t,oy:r},!0):this.transform({scale:[e,t],ox:r,oy:n},!0)},translate:function(e,t){return this.transform({translate:[e,t]},!0)},relative:function(e,t){return this.transform({relative:[e,t]},!0)},flip:function(e="both",t="center"){return"xybothtrue".indexOf(e)===-1&&(t=e,e="both"),this.transform({flip:e,origin:t},!0)},opacity:function(e){return this.attr("opacity",e)}});O("radius",{radius:function(e,t=e){return(this._element||this).type==="radialGradient"?this.attr("r",new E(e)):this.rx(e).ry(t)}});O("Path",{length:function(){return this.node.getTotalLength()},pointAt:function(e){return new H(this.node.getPointAtLength(e))}});O(["Element","Runner"],{font:function(e,t){if(typeof e=="object"){for(t in e)this.font(t,e[t]);return this}return e==="leading"?this.leading(t):e==="anchor"?this.attr("text-anchor",t):e==="size"||e==="family"||e==="weight"||e==="stretch"||e==="variant"||e==="style"?this.attr("font-"+e,t):this.attr(e,t)}});const vg=["click","dblclick","mousedown","mouseup","mouseover","mouseout","mousemove","mouseenter","mouseleave","touchstart","touchmove","touchleave","touchend","touchcancel","contextmenu","wheel","pointerdown","pointermove","pointerup","pointerleave","pointercancel"].reduce(function(e,t){const r=function(n){return n===null?this.off(t):this.on(t,n),this};return e[t]=r,e},{});O("Element",vg);function yg(){return this.attr("transform",null)}function _g(){return(this.attr("transform")||"").split(Pp).slice(0,-1).map(function(t){const r=t.trim().split("(");return[r[0],r[1].split(ht).map(function(n){return parseFloat(n)})]}).reverse().reduce(function(t,r){return r[0]==="matrix"?t.lmultiply(T.fromArray(r[1])):t[r[0]].apply(t,r[1])},new T)}function mg(e,t){if(this===e)return this;if(ra(this.node))return this.addTo(e,t);const r=this.screenCTM(),n=e.screenCTM().inverse();return this.addTo(e,t).untransform().transform(n.multiply(r)),this}function bg(e){return this.toParent(this.root(),e)}function wg(e,t){if(e==null||typeof e=="string"){const i=new T(this).decompose();return e==null?i:i[e]}T.isMatrixLike(e)||(e={...e,origin:ea(e,this)});const r=t===!0?this:t||!1,n=new T(r).transform(e);return this.attr("transform",n)}O("Element",{untransform:yg,matrixify:_g,toParent:mg,toRoot:bg,transform:wg});class X extends at{flatten(){return this.each(function(){if(this instanceof X)return this.flatten().ungroup()}),this}ungroup(t=this.parent(),r=t.index(this)){return r=r===-1?t.children().length:r,this.each(function(n,i){return i[i.length-n-1].toParent(t,r)}),this.remove()}}L(X,"Container");class Ca extends X{constructor(t,r=t){super(B("defs",t),r)}flatten(){return this}ungroup(){return this}}L(Ca,"Defs");class Q extends at{}L(Q,"Shape");function Ea(e){return this.attr("rx",e)}function qa(e){return this.attr("ry",e)}function lf(e){return e==null?this.cx()-this.rx():this.cx(e+this.rx())}function hf(e){return e==null?this.cy()-this.ry():this.cy(e+this.ry())}function ff(e){return this.attr("cx",e)}function df(e){return this.attr("cy",e)}function pf(e){return e==null?this.rx()*2:this.rx(new E(e).divide(2))}function gf(e){return e==null?this.ry()*2:this.ry(new E(e).divide(2))}const xg=Object.freeze(Object.defineProperty({__proto__:null,cx:ff,cy:df,height:gf,rx:Ea,ry:qa,width:pf,x:lf,y:hf},Symbol.toStringTag,{value:"Module"}));class Ke extends Q{constructor(t,r=t){super(B("ellipse",t),r)}size(t,r){const n=Zt(this,t,r);return this.rx(new E(n.width).divide(2)).ry(new E(n.height).divide(2))}}M(Ke,xg);O("Container",{ellipse:z(function(e=0,t=e){return this.put(new Ke).size(e,t).move(0,0)})});L(Ke,"Ellipse");class vf extends bt{constructor(t=P.document.createDocumentFragment()){super(t)}xml(t,r,n){if(typeof t=="boolean"&&(n=r,r=t,t=null),t==null||typeof t=="function"){const i=new bt(le("wrapper",n));return i.add(this.node.cloneNode(!0)),i.xml(!1,n)}return super.xml(t,!1,n)}}L(vf,"Fragment");function yf(e,t){return(this._element||this).type==="radialGradient"?this.attr({fx:new E(e),fy:new E(t)}):this.attr({x1:new E(e),y1:new E(t)})}function _f(e,t){return(this._element||this).type==="radialGradient"?this.attr({cx:new E(e),cy:new E(t)}):this.attr({x2:new E(e),y2:new E(t)})}const Ag=Object.freeze(Object.defineProperty({__proto__:null,from:yf,to:_f},Symbol.toStringTag,{value:"Module"}));class _e extends X{constructor(t,r){super(B(t+"Gradient",typeof t=="string"?null:t),r)}attr(t,r,n){return t==="transform"&&(t="gradientTransform"),super.attr(t,r,n)}bbox(){return new V}targets(){return Xt("svg [fill*="+this.id()+"]")}toString(){return this.url()}update(t){return this.clear(),typeof t=="function"&&t.call(this,this),this}url(){return"url(#"+this.id()+")"}}M(_e,Ag);O({Container:{gradient(...e){return this.defs().gradient(...e)}},Defs:{gradient:z(function(e,t){return this.put(new _e(e)).update(t)})}});L(_e,"Gradient");class he extends X{constructor(t,r=t){super(B("pattern",t),r)}attr(t,r,n){return t==="transform"&&(t="patternTransform"),super.attr(t,r,n)}bbox(){return new V}targets(){return Xt("svg [fill*="+this.id()+"]")}toString(){return this.url()}update(t){return this.clear(),typeof t=="function"&&t.call(this,this),this}url(){return"url(#"+this.id()+")"}}O({Container:{pattern(...e){return this.defs().pattern(...e)}},Defs:{pattern:z(function(e,t,r){return this.put(new he).update(r).attr({x:0,y:0,width:e,height:t,patternUnits:"userSpaceOnUse"})})}});L(he,"Pattern");let We=class extends Q{constructor(t,r=t){super(B("image",t),r)}load(t,r){if(!t)return this;const n=new P.window.Image;return na(n,"load",function(i){const s=this.parent(he);this.width()===0&&this.height()===0&&this.size(n.width,n.height),s instanceof he&&s.width()===0&&s.height()===0&&s.size(this.width(),this.height()),typeof r=="function"&&r.call(this,i)},this),na(n,"load error",function(){Vt(n)}),this.attr("href",n.src=t,ve)}};pg(function(e,t,r){return(e==="fill"||e==="stroke")&&jp.test(t)&&(t=r.root().defs().image(t)),t instanceof We&&(t=r.root().defs().pattern(0,0,n=>{n.add(t)})),t});O({Container:{image:z(function(e,t){return this.put(new We).size(0,0).load(e,t)})}});L(We,"Image");class wt extends Jt{bbox(){let t=-1/0,r=-1/0,n=1/0,i=1/0;return this.forEach(function(s){t=Math.max(s[0],t),r=Math.max(s[1],r),n=Math.min(s[0],n),i=Math.min(s[1],i)}),new V(n,i,t-n,r-i)}move(t,r){const n=this.bbox();if(t-=n.x,r-=n.y,!isNaN(t)&&!isNaN(r))for(let i=this.length-1;i>=0;i--)this[i]=[this[i][0]+t,this[i][1]+r];return this}parse(t=[0,0]){const r=[];t instanceof Array?t=Array.prototype.concat.apply([],t):t=t.trim().split(ht).map(parseFloat),t.length%2!==0&&t.pop();for(let n=0,i=t.length;n<i;n=n+2)r.push([t[n],t[n+1]]);return r}size(t,r){let n;const i=this.bbox();for(n=this.length-1;n>=0;n--)i.width&&(this[n][0]=(this[n][0]-i.x)*t/i.width+i.x),i.height&&(this[n][1]=(this[n][1]-i.y)*r/i.height+i.y);return this}toLine(){return{x1:this[0][0],y1:this[0][1],x2:this[1][0],y2:this[1][1]}}toString(){const t=[];for(let r=0,n=this.length;r<n;r++)t.push(this[r].join(","));return t.join(" ")}transform(t){return this.clone().transformO(t)}transformO(t){T.isMatrixLike(t)||(t=new T(t));for(let r=this.length;r--;){const[n,i]=this[r];this[r][0]=t.a*n+t.c*i+t.e,this[r][1]=t.b*n+t.d*i+t.f}return this}}const Sg=wt;function Tg(e){return e==null?this.bbox().x:this.move(e,this.bbox().y)}function Cg(e){return e==null?this.bbox().y:this.move(this.bbox().x,e)}function Eg(e){const t=this.bbox();return e==null?t.width:this.size(e,t.height)}function qg(e){const t=this.bbox();return e==null?t.height:this.size(t.width,e)}const Ra=Object.freeze(Object.defineProperty({__proto__:null,MorphArray:Sg,height:qg,width:Eg,x:Tg,y:Cg},Symbol.toStringTag,{value:"Module"}));class fe extends Q{constructor(t,r=t){super(B("line",t),r)}array(){return new wt([[this.attr("x1"),this.attr("y1")],[this.attr("x2"),this.attr("y2")]])}move(t,r){return this.attr(this.array().move(t,r).toLine())}plot(t,r,n,i){return t==null?this.array():(typeof r<"u"?t={x1:t,y1:r,x2:n,y2:i}:t=new wt(t).toLine(),this.attr(t))}size(t,r){const n=Zt(this,t,r);return this.attr(this.array().size(n.width,n.height).toLine())}}M(fe,Ra);O({Container:{line:z(function(...e){return fe.prototype.plot.apply(this.put(new fe),e[0]!=null?e:[0,0,0,0])})}});L(fe,"Line");class je extends X{constructor(t,r=t){super(B("marker",t),r)}height(t){return this.attr("markerHeight",t)}orient(t){return this.attr("orient",t)}ref(t,r){return this.attr("refX",t).attr("refY",r)}toString(){return"url(#"+this.id()+")"}update(t){return this.clear(),typeof t=="function"&&t.call(this,this),this}width(t){return this.attr("markerWidth",t)}}O({Container:{marker(...e){return this.defs().marker(...e)}},Defs:{marker:z(function(e,t,r){return this.put(new je).size(e,t).ref(e/2,t/2).viewbox(0,0,e,t).attr("orient","auto").update(r)})},marker:{marker(e,t,r,n){let i=["marker"];return e!=="all"&&i.push(e),i=i.join("-"),e=arguments[1]instanceof je?arguments[1]:this.defs().marker(t,r,n),this.attr(i,e)}}});L(je,"Marker");function Yt(e,t){return function(r){return r==null?this[e]:(this[e]=r,t&&t.call(this),this)}}const Rg={"-":function(e){return e},"<>":function(e){return-Math.cos(e*Math.PI)/2+.5},">":function(e){return Math.sin(e*Math.PI/2)},"<":function(e){return-Math.cos(e*Math.PI/2)+1},bezier:function(e,t,r,n){return function(i){return i<0?e>0?t/e*i:r>0?n/r*i:0:i>1?r<1?(1-n)/(1-r)*i+(n-r)/(1-r):e<1?(1-t)/(1-e)*i+(t-e)/(1-e):1:3*i*(1-i)**2*t+3*i**2*(1-i)*n+i**3}},steps:function(e,t="end"){t=t.split("-").reverse()[0];let r=e;return t==="none"?--r:t==="both"&&++r,(n,i=!1)=>{let s=Math.floor(n*e);const a=n*s%1===0;return(t==="start"||t==="both")&&++s,i&&a&&--s,n>=0&&s<0&&(s=0),n<=1&&s>r&&(s=r),s/r}}};class Oa{done(){return!1}}class ia extends Oa{constructor(t=se.ease){super(),this.ease=Rg[t]||t}step(t,r,n){return typeof t!="number"?n<1?t:r:t+(r-t)*this.ease(n)}}class Ne extends Oa{constructor(t){super(),this.stepper=t}done(t){return t.done}step(t,r,n,i){return this.stepper(t,r,n,i)}}function bo(){const e=(this._duration||500)/1e3,t=this._overshoot||0,r=1e-10,n=Math.PI,i=Math.log(t/100+r),s=-i/Math.sqrt(n*n+i*i),a=3.9/(s*e);this.d=2*s*a,this.k=a*a}class Og extends Ne{constructor(t=500,r=0){super(),this.duration(t).overshoot(r)}step(t,r,n,i){if(typeof t=="string")return t;if(i.done=n===1/0,n===1/0)return r;if(n===0)return t;n>100&&(n=16),n/=1e3;const s=i.velocity||0,a=-this.d*s-this.k*(t-r),o=t+s*n+a*n*n/2;return i.velocity=s+a*n,i.done=Math.abs(r-o)+Math.abs(s)<.002,i.done?r:o}}M(Og,{duration:Yt("_duration",bo),overshoot:Yt("_overshoot",bo)});class Ig extends Ne{constructor(t=.1,r=.01,n=0,i=1e3){super(),this.p(t).i(r).d(n).windup(i)}step(t,r,n,i){if(typeof t=="string")return t;if(i.done=n===1/0,n===1/0)return r;if(n===0)return t;const s=r-t;let a=(i.integral||0)+s*n;const o=(s-(i.error||0))/n,u=this._windup;return u!==!1&&(a=Math.max(-u,Math.min(a,u))),i.error=s,i.integral=a,i.done=Math.abs(s)<.001,i.done?r:t+(this.P*s+this.I*a+this.D*o)}}M(Ig,{windup:Yt("_windup"),p:Yt("P"),i:Yt("I"),d:Yt("D")});const Mg={M:2,L:2,H:1,V:1,C:6,S:4,Q:4,T:2,A:7,Z:0},sa={M:function(e,t,r){return t.x=r.x=e[0],t.y=r.y=e[1],["M",t.x,t.y]},L:function(e,t){return t.x=e[0],t.y=e[1],["L",e[0],e[1]]},H:function(e,t){return t.x=e[0],["H",e[0]]},V:function(e,t){return t.y=e[0],["V",e[0]]},C:function(e,t){return t.x=e[4],t.y=e[5],["C",e[0],e[1],e[2],e[3],e[4],e[5]]},S:function(e,t){return t.x=e[2],t.y=e[3],["S",e[0],e[1],e[2],e[3]]},Q:function(e,t){return t.x=e[2],t.y=e[3],["Q",e[0],e[1],e[2],e[3]]},T:function(e,t){return t.x=e[0],t.y=e[1],["T",e[0],e[1]]},Z:function(e,t,r){return t.x=r.x,t.y=r.y,["Z"]},A:function(e,t){return t.x=e[5],t.y=e[6],["A",e[0],e[1],e[2],e[3],e[4],e[5],e[6]]}},_r="mlhvqtcsaz".split("");for(let e=0,t=_r.length;e<t;++e)sa[_r[e]]=function(r){return function(n,i,s){if(r==="H")n[0]=n[0]+i.x;else if(r==="V")n[0]=n[0]+i.y;else if(r==="A")n[5]=n[5]+i.x,n[6]=n[6]+i.y;else for(let a=0,o=n.length;a<o;++a)n[a]=n[a]+(a%2?i.y:i.x);return sa[r](n,i,s)}}(_r[e].toUpperCase());function Pg(e){const t=e.segment[0];return sa[t](e.segment.slice(1),e.p,e.p0)}function aa(e){return e.segment.length&&e.segment.length-1===Mg[e.segment[0].toUpperCase()]}function Lg(e,t){e.inNumber&&St(e,!1);const r=Sa.test(t);if(r)e.segment=[t];else{const n=e.lastCommand,i=n.toLowerCase(),s=n===i;e.segment=[i==="m"?s?"l":"L":n]}return e.inSegment=!0,e.lastCommand=e.segment[0],r}function St(e,t){if(!e.inNumber)throw new Error("Parser Error");e.number&&e.segment.push(parseFloat(e.number)),e.inNumber=t,e.number="",e.pointSeen=!1,e.hasExponent=!1,aa(e)&&oa(e)}function oa(e){e.inSegment=!1,e.absolute&&(e.segment=Pg(e)),e.segments.push(e.segment)}function jg(e){if(!e.segment.length)return!1;const t=e.segment[0].toUpperCase()==="A",r=e.segment.length;return t&&(r===4||r===5)}function Ng(e){return e.lastToken.toUpperCase()==="E"}const Dg=new Set([" ",",","	",`
`,"\r","\f"]);function Fg(e,t=!0){let r=0,n="";const i={segment:[],inNumber:!1,number:"",lastToken:"",inSegment:!1,segments:[],pointSeen:!1,hasExponent:!1,absolute:t,p0:new H,p:new H};for(;i.lastToken=n,n=e.charAt(r++);)if(!(!i.inSegment&&Lg(i,n))){if(n==="."){if(i.pointSeen||i.hasExponent){St(i,!1),--r;continue}i.inNumber=!0,i.pointSeen=!0,i.number+=n;continue}if(!isNaN(parseInt(n))){if(i.number==="0"||jg(i)){i.inNumber=!0,i.number=n,St(i,!0);continue}i.inNumber=!0,i.number+=n;continue}if(Dg.has(n)){i.inNumber&&St(i,!1);continue}if(n==="-"||n==="+"){if(i.inNumber&&!Ng(i)){St(i,!1),--r;continue}i.number+=n,i.inNumber=!0;continue}if(n.toUpperCase()==="E"){i.number+=n,i.hasExponent=!0;continue}if(Sa.test(n)){if(i.inNumber)St(i,!1);else if(aa(i))oa(i);else throw new Error("parser Error");--r}}return i.inNumber&&St(i,!1),i.inSegment&&aa(i)&&oa(i),i.segments}function kg(e){let t="";for(let r=0,n=e.length;r<n;r++)t+=e[r][0],e[r][1]!=null&&(t+=e[r][1],e[r][2]!=null&&(t+=" ",t+=e[r][2],e[r][3]!=null&&(t+=" ",t+=e[r][3],t+=" ",t+=e[r][4],e[r][5]!=null&&(t+=" ",t+=e[r][5],t+=" ",t+=e[r][6],e[r][7]!=null&&(t+=" ",t+=e[r][7])))));return t+" "}class qt extends Jt{bbox(){return mt().path.setAttribute("d",this.toString()),new V(mt.nodes.path.getBBox())}move(t,r){const n=this.bbox();if(t-=n.x,r-=n.y,!isNaN(t)&&!isNaN(r))for(let i,s=this.length-1;s>=0;s--)i=this[s][0],i==="M"||i==="L"||i==="T"?(this[s][1]+=t,this[s][2]+=r):i==="H"?this[s][1]+=t:i==="V"?this[s][1]+=r:i==="C"||i==="S"||i==="Q"?(this[s][1]+=t,this[s][2]+=r,this[s][3]+=t,this[s][4]+=r,i==="C"&&(this[s][5]+=t,this[s][6]+=r)):i==="A"&&(this[s][6]+=t,this[s][7]+=r);return this}parse(t="M0 0"){return Array.isArray(t)&&(t=Array.prototype.concat.apply([],t).toString()),Fg(t)}size(t,r){const n=this.bbox();let i,s;for(n.width=n.width===0?1:n.width,n.height=n.height===0?1:n.height,i=this.length-1;i>=0;i--)s=this[i][0],s==="M"||s==="L"||s==="T"?(this[i][1]=(this[i][1]-n.x)*t/n.width+n.x,this[i][2]=(this[i][2]-n.y)*r/n.height+n.y):s==="H"?this[i][1]=(this[i][1]-n.x)*t/n.width+n.x:s==="V"?this[i][1]=(this[i][1]-n.y)*r/n.height+n.y:s==="C"||s==="S"||s==="Q"?(this[i][1]=(this[i][1]-n.x)*t/n.width+n.x,this[i][2]=(this[i][2]-n.y)*r/n.height+n.y,this[i][3]=(this[i][3]-n.x)*t/n.width+n.x,this[i][4]=(this[i][4]-n.y)*r/n.height+n.y,s==="C"&&(this[i][5]=(this[i][5]-n.x)*t/n.width+n.x,this[i][6]=(this[i][6]-n.y)*r/n.height+n.y)):s==="A"&&(this[i][1]=this[i][1]*t/n.width,this[i][2]=this[i][2]*r/n.height,this[i][6]=(this[i][6]-n.x)*t/n.width+n.x,this[i][7]=(this[i][7]-n.y)*r/n.height+n.y);return this}toString(){return kg(this)}}const mf=e=>{const t=typeof e;return t==="number"?E:t==="string"?D.isColor(e)?D:ht.test(e)?Sa.test(e)?qt:Jt:nf.test(e)?E:ua:Ia.indexOf(e.constructor)>-1?e.constructor:Array.isArray(e)?Jt:t==="object"?de:ua};class Tt{constructor(t){this._stepper=t||new ia("-"),this._from=null,this._to=null,this._type=null,this._context=null,this._morphObj=null}at(t){return this._morphObj.morph(this._from,this._to,t,this._stepper,this._context)}done(){return this._context.map(this._stepper.done).reduce(function(r,n){return r&&n},!0)}from(t){return t==null?this._from:(this._from=this._set(t),this)}stepper(t){return t==null?this._stepper:(this._stepper=t,this)}to(t){return t==null?this._to:(this._to=this._set(t),this)}type(t){return t==null?this._type:(this._type=t,this)}_set(t){this._type||this.type(mf(t));let r=new this._type(t);return this._type===D&&(r=this._to?r[this._to[4]]():this._from?r[this._from[4]]():r),this._type===de&&(r=this._to?r.align(this._to):this._from?r.align(this._from):r),r=r.toConsumable(),this._morphObj=this._morphObj||new this._type,this._context=this._context||Array.apply(null,Array(r.length)).map(Object).map(function(n){return n.done=!0,n}),r}}class ua{constructor(...t){this.init(...t)}init(t){return t=Array.isArray(t)?t[0]:t,this.value=t,this}toArray(){return[this.value]}valueOf(){return this.value}}class me{constructor(...t){this.init(...t)}init(t){return Array.isArray(t)&&(t={scaleX:t[0],scaleY:t[1],shear:t[2],rotate:t[3],translateX:t[4],translateY:t[5],originX:t[6],originY:t[7]}),Object.assign(this,me.defaults,t),this}toArray(){const t=this;return[t.scaleX,t.scaleY,t.shear,t.rotate,t.translateX,t.translateY,t.originX,t.originY]}}me.defaults={scaleX:1,scaleY:1,shear:0,rotate:0,translateX:0,translateY:0,originX:0,originY:0};const $g=(e,t)=>e[0]<t[0]?-1:e[0]>t[0]?1:0;class de{constructor(...t){this.init(...t)}align(t){const r=this.values;for(let n=0,i=r.length;n<i;++n){if(r[n+1]===t[n+1]){if(r[n+1]===D&&t[n+7]!==r[n+7]){const o=t[n+7],u=new D(this.values.splice(n+3,5))[o]().toArray();this.values.splice(n+3,0,...u)}n+=r[n+2]+2;continue}if(!t[n+1])return this;const s=new t[n+1]().toArray(),a=r[n+2]+3;r.splice(n,a,t[n],t[n+1],t[n+2],...s),n+=r[n+2]+2}return this}init(t){if(this.values=[],Array.isArray(t)){this.values=t.slice();return}t=t||{};const r=[];for(const n in t){const i=mf(t[n]),s=new i(t[n]).toArray();r.push([n,i,s.length,...s])}return r.sort($g),this.values=r.reduce((n,i)=>n.concat(i),[]),this}toArray(){return this.values}valueOf(){const t={},r=this.values;for(;r.length;){const n=r.shift(),i=r.shift(),s=r.shift(),a=r.splice(0,s);t[n]=new i(a)}return t}}const Ia=[ua,me,de];function zg(e=[]){Ia.push(...[].concat(e))}function Bg(){M(Ia,{to(e){return new Tt().type(this.constructor).from(this.toArray()).to(e)},fromArray(e){return this.init(e),this},toConsumable(){return this.toArray()},morph(e,t,r,n,i){const s=function(a,o){return n.step(a,t[o],r,i[o],i)};return this.fromArray(e.map(s))}})}class Mt extends Q{constructor(t,r=t){super(B("path",t),r)}array(){return this._array||(this._array=new qt(this.attr("d")))}clear(){return delete this._array,this}height(t){return t==null?this.bbox().height:this.size(this.bbox().width,t)}move(t,r){return this.attr("d",this.array().move(t,r))}plot(t){return t==null?this.array():this.clear().attr("d",typeof t=="string"?t:this._array=new qt(t))}size(t,r){const n=Zt(this,t,r);return this.attr("d",this.array().size(n.width,n.height))}width(t){return t==null?this.bbox().width:this.size(t,this.bbox().height)}x(t){return t==null?this.bbox().x:this.move(t,this.bbox().y)}y(t){return t==null?this.bbox().y:this.move(this.bbox().x,t)}}Mt.prototype.MorphArray=qt;O({Container:{path:z(function(e){return this.put(new Mt).plot(e||new qt)})}});L(Mt,"Path");function Hg(){return this._array||(this._array=new wt(this.attr("points")))}function Ug(){return delete this._array,this}function Kg(e,t){return this.attr("points",this.array().move(e,t))}function Wg(e){return e==null?this.array():this.clear().attr("points",typeof e=="string"?e:this._array=new wt(e))}function Vg(e,t){const r=Zt(this,e,t);return this.attr("points",this.array().size(r.width,r.height))}const bf=Object.freeze(Object.defineProperty({__proto__:null,array:Hg,clear:Ug,move:Kg,plot:Wg,size:Vg},Symbol.toStringTag,{value:"Module"}));class be extends Q{constructor(t,r=t){super(B("polygon",t),r)}}O({Container:{polygon:z(function(e){return this.put(new be).plot(e||new wt)})}});M(be,Ra);M(be,bf);L(be,"Polygon");class we extends Q{constructor(t,r=t){super(B("polyline",t),r)}}O({Container:{polyline:z(function(e){return this.put(new we).plot(e||new wt)})}});M(we,Ra);M(we,bf);L(we,"Polyline");class xe extends Q{constructor(t,r=t){super(B("rect",t),r)}}M(xe,{rx:Ea,ry:qa});O({Container:{rect:z(function(e,t){return this.put(new xe).size(e,t)})}});L(xe,"Rect");class mr{constructor(){this._first=null,this._last=null}first(){return this._first&&this._first.value}last(){return this._last&&this._last.value}push(t){const r=typeof t.next<"u"?t:{value:t,next:null,prev:null};return this._last?(r.prev=this._last,this._last.next=r,this._last=r):(this._last=r,this._first=r),r}remove(t){t.prev&&(t.prev.next=t.next),t.next&&(t.next.prev=t.prev),t===this._last&&(this._last=t.prev),t===this._first&&(this._first=t.next),t.prev=null,t.next=null}shift(){const t=this._first;return t?(this._first=t.next,this._first&&(this._first.prev=null),this._last=this._first?this._last:null,t.value):null}}const j={nextDraw:null,frames:new mr,timeouts:new mr,immediates:new mr,timer:()=>P.window.performance||P.window.Date,transforms:[],frame(e){const t=j.frames.push({run:e});return j.nextDraw===null&&(j.nextDraw=P.window.requestAnimationFrame(j._draw)),t},timeout(e,t){t=t||0;const r=j.timer().now()+t,n=j.timeouts.push({run:e,time:r});return j.nextDraw===null&&(j.nextDraw=P.window.requestAnimationFrame(j._draw)),n},immediate(e){const t=j.immediates.push(e);return j.nextDraw===null&&(j.nextDraw=P.window.requestAnimationFrame(j._draw)),t},cancelFrame(e){e!=null&&j.frames.remove(e)},clearTimeout(e){e!=null&&j.timeouts.remove(e)},cancelImmediate(e){e!=null&&j.immediates.remove(e)},_draw(e){let t=null;const r=j.timeouts.last();for(;(t=j.timeouts.shift())&&(e>=t.time?t.run():j.timeouts.push(t),t!==r););let n=null;const i=j.frames.last();for(;n!==i&&(n=j.frames.shift());)n.run(e);let s=null;for(;s=j.immediates.shift();)s();j.nextDraw=j.timeouts.first()||j.frames.first()?P.window.requestAnimationFrame(j._draw):null}},Yg=function(e){const t=e.start,r=e.runner.duration(),n=t+r;return{start:t,duration:r,end:n,runner:e.runner}},Jg=function(){const e=P.window;return(e.performance||e.Date).now()};class wf extends ye{constructor(t=Jg){super(),this._timeSource=t,this.terminate()}active(){return!!this._nextFrame}finish(){return this.time(this.getEndTimeOfTimeline()+1),this.pause()}getEndTime(){const t=this.getLastRunnerInfo(),r=t?t.runner.duration():0;return(t?t.start:this._time)+r}getEndTimeOfTimeline(){const t=this._runners.map(r=>r.start+r.runner.duration());return Math.max(0,...t)}getLastRunnerInfo(){return this.getRunnerInfoById(this._lastRunnerId)}getRunnerInfoById(t){return this._runners[this._runnerIds.indexOf(t)]||null}pause(){return this._paused=!0,this._continue()}persist(t){return t==null?this._persist:(this._persist=t,this)}play(){return this._paused=!1,this.updateTime()._continue()}reverse(t){const r=this.speed();if(t==null)return this.speed(-r);const n=Math.abs(r);return this.speed(t?-n:n)}schedule(t,r,n){if(t==null)return this._runners.map(Yg);let i=0;const s=this.getEndTime();if(r=r||0,n==null||n==="last"||n==="after")i=s;else if(n==="absolute"||n==="start")i=r,r=0;else if(n==="now")i=this._time;else if(n==="relative"){const u=this.getRunnerInfoById(t.id);u&&(i=u.start+r,r=0)}else if(n==="with-last"){const u=this.getLastRunnerInfo();i=u?u.start:this._time}else throw new Error('Invalid value for the "when" parameter');t.unschedule(),t.timeline(this);const a=t.persist(),o={persist:a===null?this._persist:a,start:i+r,runner:t};return this._lastRunnerId=t.id,this._runners.push(o),this._runners.sort((u,c)=>u.start-c.start),this._runnerIds=this._runners.map(u=>u.runner.id),this.updateTime()._continue(),this}seek(t){return this.time(this._time+t)}source(t){return t==null?this._timeSource:(this._timeSource=t,this)}speed(t){return t==null?this._speed:(this._speed=t,this)}stop(){return this.time(0),this.pause()}time(t){return t==null?this._time:(this._time=t,this._continue(!0))}unschedule(t){const r=this._runnerIds.indexOf(t.id);return r<0?this:(this._runners.splice(r,1),this._runnerIds.splice(r,1),t.timeline(null),this)}updateTime(){return this.active()||(this._lastSourceTime=this._timeSource()),this}_continue(t=!1){return j.cancelFrame(this._nextFrame),this._nextFrame=null,t?this._stepImmediate():this._paused?this:(this._nextFrame=j.frame(this._step),this)}_stepFn(t=!1){const r=this._timeSource();let n=r-this._lastSourceTime;t&&(n=0);const i=this._speed*n+(this._time-this._lastStepTime);this._lastSourceTime=r,t||(this._time+=i,this._time=this._time<0?0:this._time),this._lastStepTime=this._time,this.fire("time",this._time);for(let a=this._runners.length;a--;){const o=this._runners[a],u=o.runner;this._time-o.start<=0&&u.reset()}let s=!1;for(let a=0,o=this._runners.length;a<o;a++){const u=this._runners[a],c=u.runner;let l=i;const h=this._time-u.start;if(h<=0){s=!0;continue}else h<l&&(l=h);if(!c.active())continue;c.step(l).done?u.persist!==!0&&c.duration()-c.time()+this._time+u.persist<this._time&&(c.unschedule(),--a,--o):s=!0}return s&&!(this._speed<0&&this._time===0)||this._runnerIds.length&&this._speed<0&&this._time>0?this._continue():(this.pause(),this.fire("finished")),this}terminate(){this._startTime=0,this._speed=1,this._persist=0,this._nextFrame=null,this._paused=!0,this._runners=[],this._runnerIds=[],this._lastRunnerId=-1,this._time=0,this._lastSourceTime=0,this._lastStepTime=0,this._step=this._stepFn.bind(this,!1),this._stepImmediate=this._stepFn.bind(this,!0)}}O({Element:{timeline:function(e){return e==null?(this._timeline=this._timeline||new wf,this._timeline):(this._timeline=e,this)}}});class G extends ye{constructor(t){super(),this.id=G.id++,t=t??se.duration,t=typeof t=="function"?new Ne(t):t,this._element=null,this._timeline=null,this.done=!1,this._queue=[],this._duration=typeof t=="number"&&t,this._isDeclarative=t instanceof Ne,this._stepper=this._isDeclarative?t:new ia,this._history={},this.enabled=!0,this._time=0,this._lastTime=0,this._reseted=!0,this.transforms=new T,this.transformId=1,this._haveReversed=!1,this._reverse=!1,this._loopsDone=0,this._swing=!1,this._wait=0,this._times=1,this._frameId=null,this._persist=this._isDeclarative?!0:null}static sanitise(t,r,n){let i=1,s=!1,a=0;return t=t??se.duration,r=r??se.delay,n=n||"last",typeof t=="object"&&!(t instanceof Oa)&&(r=t.delay??r,n=t.when??n,s=t.swing||s,i=t.times??i,a=t.wait??a,t=t.duration??se.duration),{duration:t,delay:r,swing:s,times:i,wait:a,when:n}}active(t){return t==null?this.enabled:(this.enabled=t,this)}addTransform(t){return this.transforms.lmultiplyO(t),this}after(t){return this.on("finished",t)}animate(t,r,n){const i=G.sanitise(t,r,n),s=new G(i.duration);return this._timeline&&s.timeline(this._timeline),this._element&&s.element(this._element),s.loop(i).schedule(i.delay,i.when)}clearTransform(){return this.transforms=new T,this}clearTransformsFromQueue(){(!this.done||!this._timeline||!this._timeline._runnerIds.includes(this.id))&&(this._queue=this._queue.filter(t=>!t.isTransform))}delay(t){return this.animate(0,t)}duration(){return this._times*(this._wait+this._duration)-this._wait}during(t){return this.queue(null,t)}ease(t){return this._stepper=new ia(t),this}element(t){return t==null?this._element:(this._element=t,t._prepareRunner(),this)}finish(){return this.step(1/0)}loop(t,r,n){return typeof t=="object"&&(r=t.swing,n=t.wait,t=t.times),this._times=t||1/0,this._swing=r||!1,this._wait=n||0,this._times===!0&&(this._times=1/0),this}loops(t){const r=this._duration+this._wait;if(t==null){const a=Math.floor(this._time/r),u=(this._time-a*r)/this._duration;return Math.min(a+u,this._times)}const n=Math.floor(t),i=t%1,s=r*n+this._duration*i;return this.time(s)}persist(t){return t==null?this._persist:(this._persist=t,this)}position(t){const r=this._time,n=this._duration,i=this._wait,s=this._times,a=this._swing,o=this._reverse;let u;if(t==null){const f=function(g){const p=a*Math.floor(g%(2*(i+n))/(i+n)),v=p&&!o||!p&&o,y=Math.pow(-1,v)*(g%(i+n))/n+v;return Math.max(Math.min(y,1),0)},d=s*(i+n)-i;return u=r<=0?Math.round(f(1e-5)):r<d?f(r):Math.round(f(d-1e-5)),u}const c=Math.floor(this.loops()),l=a&&c%2===0;return u=c+(l&&!o||o&&l?t:1-t),this.loops(u)}progress(t){return t==null?Math.min(1,this._time/this.duration()):this.time(t*this.duration())}queue(t,r,n,i){return this._queue.push({initialiser:t||mo,runner:r||mo,retarget:n,isTransform:i,initialised:!1,finished:!1}),this.timeline()&&this.timeline()._continue(),this}reset(){return this._reseted?this:(this.time(0),this._reseted=!0,this)}reverse(t){return this._reverse=t??!this._reverse,this}schedule(t,r,n){if(t instanceof wf||(n=r,r=t,t=this.timeline()),!t)throw Error("Runner cannot be scheduled without timeline");return t.schedule(this,r,n),this}step(t){if(!this.enabled)return this;t=t??16,this._time+=t;const r=this.position(),n=this._lastPosition!==r&&this._time>=0;this._lastPosition=r;const i=this.duration(),s=this._lastTime<=0&&this._time>0,a=this._lastTime<i&&this._time>=i;this._lastTime=this._time,s&&this.fire("start",this);const o=this._isDeclarative;this.done=!o&&!a&&this._time>=i,this._reseted=!1;let u=!1;return(n||o)&&(this._initialise(n),this.transforms=new T,u=this._run(o?t:r),this.fire("step",this)),this.done=this.done||u&&o,a&&this.fire("finished",this),this}time(t){if(t==null)return this._time;const r=t-this._time;return this.step(r),this}timeline(t){return typeof t>"u"?this._timeline:(this._timeline=t,this)}unschedule(){const t=this.timeline();return t&&t.unschedule(this),this}_initialise(t){if(!(!t&&!this._isDeclarative))for(let r=0,n=this._queue.length;r<n;++r){const i=this._queue[r],s=this._isDeclarative||!i.initialised&&t;t=!i.finished,s&&t&&(i.initialiser.call(this),i.initialised=!0)}}_rememberMorpher(t,r){if(this._history[t]={morpher:r,caller:this._queue[this._queue.length-1]},this._isDeclarative){const n=this.timeline();n&&n.play()}}_run(t){let r=!0;for(let n=0,i=this._queue.length;n<i;++n){const s=this._queue[n],a=s.runner.call(this,t);s.finished=s.finished||a===!0,r=r&&s.finished}return r}_tryRetarget(t,r,n){if(this._history[t]){if(!this._history[t].caller.initialised){const s=this._queue.indexOf(this._history[t].caller);return this._queue.splice(s,1),!1}this._history[t].caller.retarget?this._history[t].caller.retarget.call(this,r,n):this._history[t].morpher.to(r),this._history[t].caller.finished=!1;const i=this.timeline();return i&&i.play(),!0}return!1}}G.id=0;class De{constructor(t=new T,r=-1,n=!0){this.transforms=t,this.id=r,this.done=n}clearTransformsFromQueue(){}}M([G,De],{mergeWith(e){return new De(e.transforms.lmultiply(this.transforms),e.id)}});const xf=(e,t)=>e.lmultiplyO(t),Af=e=>e.transforms;function Zg(){const t=this._transformationRunners.runners.map(Af).reduce(xf,new T);this.transform(t),this._transformationRunners.merge(),this._transformationRunners.length()===1&&(this._frameId=null)}class Xg{constructor(){this.runners=[],this.ids=[]}add(t){if(this.runners.includes(t))return;const r=t.id+1;return this.runners.push(t),this.ids.push(r),this}clearBefore(t){const r=this.ids.indexOf(t+1)||1;return this.ids.splice(0,r,0),this.runners.splice(0,r,new De).forEach(n=>n.clearTransformsFromQueue()),this}edit(t,r){const n=this.ids.indexOf(t+1);return this.ids.splice(n,1,t+1),this.runners.splice(n,1,r),this}getByID(t){return this.runners[this.ids.indexOf(t+1)]}length(){return this.ids.length}merge(){let t=null;for(let r=0;r<this.runners.length;++r){const n=this.runners[r];if(t&&n.done&&t.done&&(!n._timeline||!n._timeline._runnerIds.includes(n.id))&&(!t._timeline||!t._timeline._runnerIds.includes(t.id))){this.remove(n.id);const s=n.mergeWith(t);this.edit(t.id,s),t=s,--r}else t=n}return this}remove(t){const r=this.ids.indexOf(t+1);return this.ids.splice(r,1),this.runners.splice(r,1),this}}O({Element:{animate(e,t,r){const n=G.sanitise(e,t,r),i=this.timeline();return new G(n.duration).loop(n).element(this).timeline(i.play()).schedule(n.delay,n.when)},delay(e,t){return this.animate(0,e,t)},_clearTransformRunnersBefore(e){this._transformationRunners.clearBefore(e.id)},_currentTransform(e){return this._transformationRunners.runners.filter(t=>t.id<=e.id).map(Af).reduce(xf,new T)},_addRunner(e){this._transformationRunners.add(e),j.cancelImmediate(this._frameId),this._frameId=j.immediate(Zg.bind(this))},_prepareRunner(){this._frameId==null&&(this._transformationRunners=new Xg().add(new De(new T(this))))}}});const Gg=(e,t)=>e.filter(r=>!t.includes(r));M(G,{attr(e,t){return this.styleAttr("attr",e,t)},css(e,t){return this.styleAttr("css",e,t)},styleAttr(e,t,r){if(typeof t=="string")return this.styleAttr(e,{[t]:r});let n=t;if(this._tryRetarget(e,n))return this;let i=new Tt(this._stepper).to(n),s=Object.keys(n);return this.queue(function(){i=i.from(this.element()[e](s))},function(a){return this.element()[e](i.at(a).valueOf()),i.done()},function(a){const o=Object.keys(a),u=Gg(o,s);if(u.length){const l=this.element()[e](u),h=new de(i.from()).valueOf();Object.assign(h,l),i.from(h)}const c=new de(i.to()).valueOf();Object.assign(c,a),i.to(c),s=o,n=a}),this._rememberMorpher(e,i),this},zoom(e,t){if(this._tryRetarget("zoom",e,t))return this;let r=new Tt(this._stepper).to(new E(e));return this.queue(function(){r=r.from(this.element().zoom())},function(n){return this.element().zoom(r.at(n),t),r.done()},function(n,i){t=i,r.to(n)}),this._rememberMorpher("zoom",r),this},transform(e,t,r){if(t=e.relative||t,this._isDeclarative&&!t&&this._tryRetarget("transform",e))return this;const n=T.isMatrixLike(e);r=e.affine!=null?e.affine:r??!n;const i=new Tt(this._stepper).type(r?me:T);let s,a,o,u,c;function l(){a=a||this.element(),s=s||ea(e,a),c=new T(t?void 0:a),a._addRunner(this),t||a._clearTransformRunnersBefore(this)}function h(d){t||this.clearTransform();const{x:g,y:p}=new H(s).transform(a._currentTransform(this));let v=new T({...e,origin:[g,p]}),y=this._isDeclarative&&o?o:c;if(r){v=v.decompose(g,p),y=y.decompose(g,p);const b=v.rotate,m=y.rotate,S=[b-360,b,b+360],w=S.map(C=>Math.abs(C-m)),x=Math.min(...w),R=w.indexOf(x);v.rotate=S[R]}t&&(n||(v.rotate=e.rotate||0),this._isDeclarative&&u&&(y.rotate=u)),i.from(y),i.to(v);const _=i.at(d);return u=_.rotate,o=new T(_),this.addTransform(o),a._addRunner(this),i.done()}function f(d){(d.origin||"center").toString()!==(e.origin||"center").toString()&&(s=ea(d,a)),e={...d,origin:s}}return this.queue(l,h,f,!0),this._isDeclarative&&this._rememberMorpher("transform",i),this},x(e){return this._queueNumber("x",e)},y(e){return this._queueNumber("y",e)},ax(e){return this._queueNumber("ax",e)},ay(e){return this._queueNumber("ay",e)},dx(e=0){return this._queueNumberDelta("x",e)},dy(e=0){return this._queueNumberDelta("y",e)},dmove(e,t){return this.dx(e).dy(t)},_queueNumberDelta(e,t){if(t=new E(t),this._tryRetarget(e,t))return this;const r=new Tt(this._stepper).to(t);let n=null;return this.queue(function(){n=this.element()[e](),r.from(n),r.to(n+t)},function(i){return this.element()[e](r.at(i)),r.done()},function(i){r.to(n+new E(i))}),this._rememberMorpher(e,r),this},_queueObject(e,t){if(this._tryRetarget(e,t))return this;const r=new Tt(this._stepper).to(t);return this.queue(function(){r.from(this.element()[e]())},function(n){return this.element()[e](r.at(n)),r.done()}),this._rememberMorpher(e,r),this},_queueNumber(e,t){return this._queueObject(e,new E(t))},cx(e){return this._queueNumber("cx",e)},cy(e){return this._queueNumber("cy",e)},move(e,t){return this.x(e).y(t)},amove(e,t){return this.ax(e).ay(t)},center(e,t){return this.cx(e).cy(t)},size(e,t){let r;return(!e||!t)&&(r=this._element.bbox()),e||(e=r.width/r.height*t),t||(t=r.height/r.width*e),this.width(e).height(t)},width(e){return this._queueNumber("width",e)},height(e){return this._queueNumber("height",e)},plot(e,t,r,n){if(arguments.length===4)return this.plot([e,t,r,n]);if(this._tryRetarget("plot",e))return this;const i=new Tt(this._stepper).type(this._element.MorphArray).to(e);return this.queue(function(){i.from(this._element.array())},function(s){return this._element.plot(i.at(s)),i.done()}),this._rememberMorpher("plot",i),this},leading(e){return this._queueNumber("leading",e)},viewbox(e,t,r,n){return this._queueObject("viewbox",new V(e,t,r,n))},update(e){return typeof e!="object"?this.update({offset:arguments[0],color:arguments[1],opacity:arguments[2]}):(e.opacity!=null&&this.attr("stop-opacity",e.opacity),e.color!=null&&this.attr("stop-color",e.color),e.offset!=null&&this.attr("offset",e.offset),this)}});M(G,{rx:Ea,ry:qa,from:yf,to:_f});L(G,"Runner");class Ma extends X{constructor(t,r=t){super(B("svg",t),r),this.namespace()}defs(){return this.isRoot()?tt(this.node.querySelector("defs"))||this.put(new Ca):this.root().defs()}isRoot(){return!this.node.parentNode||!(this.node.parentNode instanceof P.window.SVGElement)&&this.node.parentNode.nodeName!=="#document-fragment"}namespace(){return this.isRoot()?this.attr({xmlns:wa,version:"1.1"}).attr("xmlns:xlink",ve,gr):this.root().namespace()}removeNamespace(){return this.attr({xmlns:null,version:null}).attr("xmlns:xlink",null,gr).attr("xmlns:svgjs",null,gr)}root(){return this.isRoot()?this:super.root()}}O({Container:{nested:z(function(){return this.put(new Ma)})}});L(Ma,"Svg",!0);let Pa=class extends X{constructor(t,r=t){super(B("symbol",t),r)}};O({Container:{symbol:z(function(){return this.put(new Pa)})}});L(Pa,"Symbol");function Qg(e){return this._build===!1&&this.clear(),this.node.appendChild(P.document.createTextNode(e)),this}function tv(){return this.node.getComputedTextLength()}function ev(e,t=this.bbox()){return e==null?t.x:this.attr("x",this.attr("x")+e-t.x)}function rv(e,t=this.bbox()){return e==null?t.y:this.attr("y",this.attr("y")+e-t.y)}function nv(e,t,r=this.bbox()){return this.x(e,r).y(t,r)}function iv(e,t=this.bbox()){return e==null?t.cx:this.attr("x",this.attr("x")+e-t.cx)}function sv(e,t=this.bbox()){return e==null?t.cy:this.attr("y",this.attr("y")+e-t.cy)}function av(e,t,r=this.bbox()){return this.cx(e,r).cy(t,r)}function ov(e){return this.attr("x",e)}function uv(e){return this.attr("y",e)}function cv(e,t){return this.ax(e).ay(t)}function lv(e){return this._build=!!e,this}const Sf=Object.freeze(Object.defineProperty({__proto__:null,amove:cv,ax:ov,ay:uv,build:lv,center:av,cx:iv,cy:sv,length:tv,move:nv,plain:Qg,x:ev,y:rv},Symbol.toStringTag,{value:"Module"}));class et extends Q{constructor(t,r=t){super(B("text",t),r),this.dom.leading=this.dom.leading??new E(1.3),this._rebuild=!0,this._build=!1}leading(t){return t==null?this.dom.leading:(this.dom.leading=new E(t),this.rebuild())}rebuild(t){if(typeof t=="boolean"&&(this._rebuild=t),this._rebuild){const r=this;let n=0;const i=this.dom.leading;this.each(function(s){if(ra(this.node))return;const a=P.window.getComputedStyle(this.node).getPropertyValue("font-size"),o=i*new E(a);this.dom.newLined&&(this.attr("x",r.attr("x")),this.text()===`
`?n+=o:(this.attr("dy",s?o+n:0),n=0))}),this.fire("rebuild")}return this}setData(t){return this.dom=t,this.dom.leading=new E(t.leading||1.3),this}writeDataToDom(){return tf(this,this.dom,{leading:1.3}),this}text(t){if(t===void 0){const r=this.node.childNodes;let n=0;t="";for(let i=0,s=r.length;i<s;++i){if(r[i].nodeName==="textPath"||ra(r[i])){i===0&&(n=i+1);continue}i!==n&&r[i].nodeType!==3&&tt(r[i]).dom.newLined===!0&&(t+=`
`),t+=r[i].textContent}return t}if(this.clear().build(!0),typeof t=="function")t.call(this,this);else{t=(t+"").split(`
`);for(let r=0,n=t.length;r<n;r++)this.newLine(t[r])}return this.build(!1).rebuild()}}M(et,Sf);O({Container:{text:z(function(e=""){return this.put(new et).text(e)}),plain:z(function(e=""){return this.put(new et).plain(e)})}});L(et,"Text");class Ve extends Q{constructor(t,r=t){super(B("tspan",t),r),this._build=!1}dx(t){return this.attr("dx",t)}dy(t){return this.attr("dy",t)}newLine(){this.dom.newLined=!0;const t=this.parent();if(!(t instanceof et))return this;const r=t.index(this),n=P.window.getComputedStyle(this.node).getPropertyValue("font-size"),i=t.dom.leading*new E(n);return this.dy(r?i:0).attr("x",t.x())}text(t){return t==null?this.node.textContent+(this.dom.newLined?`
`:""):(typeof t=="function"?(this.clear().build(!0),t.call(this,this),this.build(!1)):this.plain(t),this)}}M(Ve,Sf);O({Tspan:{tspan:z(function(e=""){const t=new Ve;return this._build||this.clear(),this.put(t).text(e)})},Text:{newLine:function(e=""){return this.tspan(e).newLine()}}});L(Ve,"Tspan");class La extends Q{constructor(t,r=t){super(B("circle",t),r)}radius(t){return this.attr("r",t)}rx(t){return this.attr("r",t)}ry(t){return this.rx(t)}size(t){return this.radius(new E(t).divide(2))}}M(La,{x:lf,y:hf,cx:ff,cy:df,width:pf,height:gf});O({Container:{circle:z(function(e=0){return this.put(new La).size(e).move(0,0)})}});L(La,"Circle");class ca extends X{constructor(t,r=t){super(B("clipPath",t),r)}remove(){return this.targets().forEach(function(t){t.unclip()}),super.remove()}targets(){return Xt("svg [clip-path*="+this.id()+"]")}}O({Container:{clip:z(function(){return this.defs().put(new ca)})},Element:{clipper(){return this.reference("clip-path")},clipWith(e){const t=e instanceof ca?e:this.parent().clip().add(e);return this.attr("clip-path","url(#"+t.id()+")")},unclip(){return this.attr("clip-path",null)}}});L(ca,"ClipPath");class Tf extends at{constructor(t,r=t){super(B("foreignObject",t),r)}}O({Container:{foreignObject:z(function(e,t){return this.put(new Tf).size(e,t)})}});L(Tf,"ForeignObject");function hv(e,t){return this.children().forEach(r=>{let n;try{n=r.node instanceof gp().SVGSVGElement?new V(r.attr(["x","y","width","height"])):r.bbox()}catch{return}const i=new T(r),s=i.translate(e,t).transform(i.inverse()),a=new H(n.x,n.y).transform(s);r.move(a.x,a.y)}),this}function fv(e){return this.dmove(e,0)}function dv(e){return this.dmove(0,e)}function pv(e,t=this.bbox()){return e==null?t.height:this.size(t.width,e,t)}function gv(e=0,t=0,r=this.bbox()){const n=e-r.x,i=t-r.y;return this.dmove(n,i)}function vv(e,t,r=this.bbox()){const n=Zt(this,e,t,r),i=n.width/r.width,s=n.height/r.height;return this.children().forEach(a=>{const o=new H(r).transform(new T(a).inverse());a.scale(i,s,o.x,o.y)}),this}function yv(e,t=this.bbox()){return e==null?t.width:this.size(e,t.height,t)}function _v(e,t=this.bbox()){return e==null?t.x:this.move(e,t.y,t)}function mv(e,t=this.bbox()){return e==null?t.y:this.move(t.x,e,t)}const Cf=Object.freeze(Object.defineProperty({__proto__:null,dmove:hv,dx:fv,dy:dv,height:pv,move:gv,size:vv,width:yv,x:_v,y:mv},Symbol.toStringTag,{value:"Module"}));class Ae extends X{constructor(t,r=t){super(B("g",t),r)}}M(Ae,Cf);O({Container:{group:z(function(){return this.put(new Ae)})}});L(Ae,"G");class Fe extends X{constructor(t,r=t){super(B("a",t),r)}target(t){return this.attr("target",t)}to(t){return this.attr("href",t,ve)}}M(Fe,Cf);O({Container:{link:z(function(e){return this.put(new Fe).to(e)})},Element:{unlink(){const e=this.linker();if(!e)return this;const t=e.parent();if(!t)return this.remove();const r=t.index(e);return t.add(this,r),e.remove(),this},linkTo(e){let t=this.linker();return t||(t=new Fe,this.wrap(t)),typeof e=="function"?e.call(t,t):t.to(e),this},linker(){const e=this.parent();return e&&e.node.nodeName.toLowerCase()==="a"?e:null}}});L(Fe,"A");class la extends X{constructor(t,r=t){super(B("mask",t),r)}remove(){return this.targets().forEach(function(t){t.unmask()}),super.remove()}targets(){return Xt("svg [mask*="+this.id()+"]")}}O({Container:{mask:z(function(){return this.defs().put(new la)})},Element:{masker(){return this.reference("mask")},maskWith(e){const t=e instanceof la?e:this.parent().mask().add(e);return this.attr("mask","url(#"+t.id()+")")},unmask(){return this.attr("mask",null)}}});L(la,"Mask");class Ef extends at{constructor(t,r=t){super(B("stop",t),r)}update(t){return(typeof t=="number"||t instanceof E)&&(t={offset:arguments[0],color:arguments[1],opacity:arguments[2]}),t.opacity!=null&&this.attr("stop-opacity",t.opacity),t.color!=null&&this.attr("stop-color",t.color),t.offset!=null&&this.attr("offset",new E(t.offset)),this}}O({Gradient:{stop:function(e,t,r){return this.put(new Ef).update(e,t,r)}}});L(Ef,"Stop");function bv(e,t){if(!e)return"";if(!t)return e;let r=e+"{";for(const n in t)r+=fp(n)+":"+t[n]+";";return r+="}",r}class ha extends at{constructor(t,r=t){super(B("style",t),r)}addText(t=""){return this.node.textContent+=t,this}font(t,r,n={}){return this.rule("@font-face",{fontFamily:t,src:r,...n})}rule(t,r){return this.addText(bv(t,r))}}O("Dom",{style(e,t){return this.put(new ha).rule(e,t)},fontface(e,t,r){return this.put(new ha).font(e,t,r)}});L(ha,"Style");class ja extends et{constructor(t,r=t){super(B("textPath",t),r)}array(){const t=this.track();return t?t.array():null}plot(t){const r=this.track();let n=null;return r&&(n=r.plot(t)),t==null?n:this}track(){return this.reference("href")}}O({Container:{textPath:z(function(e,t){return e instanceof et||(e=this.text(e)),e.path(t)})},Text:{path:z(function(e,t=!0){const r=new ja;e instanceof Mt||(e=this.defs().path(e)),r.attr("href","#"+e,ve);let n;if(t)for(;n=this.node.firstChild;)r.node.appendChild(n);return this.put(r)}),textPath(){return this.findOne("textPath")}},Path:{text:z(function(e){return e instanceof et||(e=new et().addTo(this.parent()).text(e)),e.path(this)}),targets(){return Xt("svg textPath").filter(e=>(e.attr("href")||"").includes(this.id()))}}});ja.prototype.MorphArray=qt;L(ja,"TextPath");class qf extends Q{constructor(t,r=t){super(B("use",t),r)}use(t,r){return this.attr("href",(r||"")+"#"+t,ve)}}O({Container:{use:z(function(e,t){return this.put(new qf).use(e,t)})}});L(qf,"Use");const fa=Y;M([Ma,Pa,We,he,je],Z("viewbox"));M([fe,we,be,Mt],Z("marker"));M(et,Z("Text"));M(Mt,Z("Path"));M(Ca,Z("Defs"));M([et,Ve],Z("Tspan"));M([xe,Ke,_e,G],Z("radius"));M(ye,Z("EventTarget"));M(bt,Z("Dom"));M(at,Z("Element"));M(Q,Z("Shape"));M([X,vf],Z("Container"));M(_e,Z("Gradient"));M(G,Z("Runner"));Et.extend(lp());zg([E,D,V,T,Jt,wt,qt,H]);Bg();class wv{constructor(t,r="apex"){this.canvas=t,this.prefix=r}getSvgString(){return this.canvas.svg().replace(/(<img [\w\W]+?)(>)/g,"$1 />").replace(/(<br)(>)/g,"$1 />").replace(/(<hr)(>)/g,"$1 />")}svgUrl(){const t=this.getSvgString(),r=new Blob([t],{type:"image/svg+xml;charset=utf-8"});return URL.createObjectURL(r)}triggerDownload(t,r){const n=document.createElement("a");n.href=t,n.download=r,document.body.appendChild(n),n.click(),document.body.removeChild(n)}exportToSVG(){this.triggerDownload(this.svgUrl(),`${this.prefix}-${new Date().getTime()}.svg`)}}function xv(e,t,r,n){for(var i=-1,s=e==null?0:e.length;++i<s;)r=t(r,e[i],i,e);return r}function Av(e){return function(t){return e==null?void 0:e[t]}}var Sv={À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",Ç:"C",ç:"c",Ð:"D",ð:"d",È:"E",É:"E",Ê:"E",Ë:"E",è:"e",é:"e",ê:"e",ë:"e",Ì:"I",Í:"I",Î:"I",Ï:"I",ì:"i",í:"i",î:"i",ï:"i",Ñ:"N",ñ:"n",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",Ù:"U",Ú:"U",Û:"U",Ü:"U",ù:"u",ú:"u",û:"u",ü:"u",Ý:"Y",ý:"y",ÿ:"y",Æ:"Ae",æ:"ae",Þ:"Th",þ:"th",ß:"ss",Ā:"A",Ă:"A",Ą:"A",ā:"a",ă:"a",ą:"a",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",ć:"c",ĉ:"c",ċ:"c",č:"c",Ď:"D",Đ:"D",ď:"d",đ:"d",Ē:"E",Ĕ:"E",Ė:"E",Ę:"E",Ě:"E",ē:"e",ĕ:"e",ė:"e",ę:"e",ě:"e",Ĝ:"G",Ğ:"G",Ġ:"G",Ģ:"G",ĝ:"g",ğ:"g",ġ:"g",ģ:"g",Ĥ:"H",Ħ:"H",ĥ:"h",ħ:"h",Ĩ:"I",Ī:"I",Ĭ:"I",Į:"I",İ:"I",ĩ:"i",ī:"i",ĭ:"i",į:"i",ı:"i",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",ĸ:"k",Ĺ:"L",Ļ:"L",Ľ:"L",Ŀ:"L",Ł:"L",ĺ:"l",ļ:"l",ľ:"l",ŀ:"l",ł:"l",Ń:"N",Ņ:"N",Ň:"N",Ŋ:"N",ń:"n",ņ:"n",ň:"n",ŋ:"n",Ō:"O",Ŏ:"O",Ő:"O",ō:"o",ŏ:"o",ő:"o",Ŕ:"R",Ŗ:"R",Ř:"R",ŕ:"r",ŗ:"r",ř:"r",Ś:"S",Ŝ:"S",Ş:"S",Š:"S",ś:"s",ŝ:"s",ş:"s",š:"s",Ţ:"T",Ť:"T",Ŧ:"T",ţ:"t",ť:"t",ŧ:"t",Ũ:"U",Ū:"U",Ŭ:"U",Ů:"U",Ű:"U",Ų:"U",ũ:"u",ū:"u",ŭ:"u",ů:"u",ű:"u",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",Ż:"Z",Ž:"Z",ź:"z",ż:"z",ž:"z",Ĳ:"IJ",ĳ:"ij",Œ:"Oe",œ:"oe",ŉ:"'n",ſ:"s"},Tv=Av(Sv),Rf=typeof global=="object"&&global&&global.Object===Object&&global,Cv=typeof self=="object"&&self&&self.Object===Object&&self,ft=Rf||Cv||Function("return this")(),xt=ft.Symbol;function Ev(e,t){for(var r=-1,n=e==null?0:e.length,i=Array(n);++r<n;)i[r]=t(e[r],r,e);return i}var st=Array.isArray,Of=Object.prototype,qv=Of.hasOwnProperty,Rv=Of.toString,ne=xt?xt.toStringTag:void 0;function Ov(e){var t=qv.call(e,ne),r=e[ne];try{e[ne]=void 0;var n=!0}catch{}var i=Rv.call(e);return n&&(t?e[ne]=r:delete e[ne]),i}var Iv=Object.prototype,Mv=Iv.toString;function Pv(e){return Mv.call(e)}var Lv="[object Null]",jv="[object Undefined]",wo=xt?xt.toStringTag:void 0;function Pt(e){return e==null?e===void 0?jv:Lv:wo&&wo in Object(e)?Ov(e):Pv(e)}function Rt(e){return e!=null&&typeof e=="object"}var Nv="[object Symbol]";function Ye(e){return typeof e=="symbol"||Rt(e)&&Pt(e)==Nv}var xo=xt?xt.prototype:void 0,Ao=xo?xo.toString:void 0;function If(e){if(typeof e=="string")return e;if(st(e))return Ev(e,If)+"";if(Ye(e))return Ao?Ao.call(e):"";var t=e+"";return t=="0"&&1/e==-1/0?"-0":t}function Na(e){return e==null?"":If(e)}var Dv=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,Fv="\\u0300-\\u036f",kv="\\ufe20-\\ufe2f",$v="\\u20d0-\\u20ff",zv=Fv+kv+$v,Bv="["+zv+"]",Hv=RegExp(Bv,"g");function Uv(e){return e=Na(e),e&&e.replace(Dv,Tv).replace(Hv,"")}var Kv=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g;function Wv(e){return e.match(Kv)||[]}var Vv=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/;function Yv(e){return Vv.test(e)}var Mf="\\ud800-\\udfff",Jv="\\u0300-\\u036f",Zv="\\ufe20-\\ufe2f",Xv="\\u20d0-\\u20ff",Gv=Jv+Zv+Xv,Pf="\\u2700-\\u27bf",Lf="a-z\\xdf-\\xf6\\xf8-\\xff",Qv="\\xac\\xb1\\xd7\\xf7",ty="\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf",ey="\\u2000-\\u206f",ry=" \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",jf="A-Z\\xc0-\\xd6\\xd8-\\xde",ny="\\ufe0e\\ufe0f",Nf=Qv+ty+ey+ry,Df="['’]",So="["+Nf+"]",iy="["+Gv+"]",Ff="\\d+",sy="["+Pf+"]",kf="["+Lf+"]",$f="[^"+Mf+Nf+Ff+Pf+Lf+jf+"]",ay="\\ud83c[\\udffb-\\udfff]",oy="(?:"+iy+"|"+ay+")",uy="[^"+Mf+"]",zf="(?:\\ud83c[\\udde6-\\uddff]){2}",Bf="[\\ud800-\\udbff][\\udc00-\\udfff]",Wt="["+jf+"]",cy="\\u200d",To="(?:"+kf+"|"+$f+")",ly="(?:"+Wt+"|"+$f+")",Co="(?:"+Df+"(?:d|ll|m|re|s|t|ve))?",Eo="(?:"+Df+"(?:D|LL|M|RE|S|T|VE))?",Hf=oy+"?",Uf="["+ny+"]?",hy="(?:"+cy+"(?:"+[uy,zf,Bf].join("|")+")"+Uf+Hf+")*",fy="\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",dy="\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])",py=Uf+Hf+hy,gy="(?:"+[sy,zf,Bf].join("|")+")"+py,vy=RegExp([Wt+"?"+kf+"+"+Co+"(?="+[So,Wt,"$"].join("|")+")",ly+"+"+Eo+"(?="+[So,Wt+To,"$"].join("|")+")",Wt+"?"+To+"+"+Co,Wt+"+"+Eo,dy,fy,Ff,gy].join("|"),"g");function yy(e){return e.match(vy)||[]}function _y(e,t,r){return e=Na(e),t=t,t===void 0?Yv(e)?yy(e):Wv(e):e.match(t)||[]}var my="['’]",by=RegExp(my,"g");function wy(e){return function(t){return xv(_y(Uv(t).replace(by,"")),e,"")}}var xy=wy(function(e,t,r){return e+(r?"-":"")+t.toLowerCase()});const Ay=(e="apex-tooltip-container")=>{const t=document.getElementById(e)||document.createElement("div");return t.id=e,t},qo=(e="",t,r="")=>{const n=document.getElementById(e);t?n==null||n.setAttribute("style",t):n==null||n.removeAttribute("style"),(n==null?void 0:n.innerHTML.replaceAll("'",'"'))!==r.replaceAll("'",'"')&&n&&(n.innerHTML=r)},Sy=({bgColor:e,borderColor:t,maxWidth:r,padding:n,x:i,y:s})=>{const a=["position: absolute;",`left: ${i+20}px;`,`top: ${s+20}px;`,`border: 1px solid ${t};`,"border-radius: 5px;"];return e&&a.push(`background-color: ${e};`),r&&a.push(`max-width: ${r}px`),n!==void 0&&a.push(`padding: ${n}px`),a.join(" ")},ke=(e={})=>{const t=[];for(const r in e){const n=`${xy(r)}: ${e[r]};`;t.push(n)}return t.join(" ")};function Ty({borderColor:e="#caced0",color:t="#97cbe6",height:r=0,opacity:n=1,radius:i=0,width:s=0,x:a=0,y:o=0}={}){const u=new xe;return u.attr({height:r,opacity:n,rx:i,ry:i,width:s,x:a,y:o}),u.fill(t),u.stroke({color:e,width:1}),u}function Cy(e="",{fontColor:t="#666767",pointerEvents:r="all",textAnchor:n="start",x:i,y:s}){const a=new et;return a.font({fill:t}),a.tspan(e),i!==void 0&&s!==void 0&&a.move(i,s),a.attr({"pointer-events":r,"text-anchor":n}),a}function br(e,t,r,n){const i=new Ae;return i.attr({"data-parent":n,"data-self":r,transform:e!==void 0&&t!==void 0?`translate(${e}, ${t})`:void 0}),i}function Ey(e,{edgeColor:t,id:r="",opacity:n=1}={}){const i=new Mt({d:e});return i.id(r),i.fill(t).opacity(n),i}function Ro(){}const qy="data:image/svg+xml,%3csvg%20fill='%23000000'%20width='20px'%20height='20px'%20viewBox='0%200%2024%2024'%20id='export-2'%20xmlns='http://www.w3.org/2000/svg'%20class='icon%20line'%3e%3cpolyline%20id='primary'%20points='15%203%2021%203%2021%209'%20style='fill:%20none;%20stroke:%20rgb(0,%200,%200);%20stroke-linecap:%20round;%20stroke-linejoin:%20round;%20stroke-width:%201.5;'%3e%3c/polyline%3e%3cpath%20id='primary-2'%20data-name='primary'%20d='M21,13v7a1,1,0,0,1-1,1H4a1,1,0,0,1-1-1V4A1,1,0,0,1,4,3h7'%20style='fill:%20none;%20stroke:%20rgb(0,%200,%200);%20stroke-linecap:%20round;%20stroke-linejoin:%20round;%20stroke-width:%201.5;'%3e%3c/path%3e%3cline%20id='primary-3'%20data-name='primary'%20x1='11'%20y1='13'%20x2='21'%20y2='3'%20style='fill:%20none;%20stroke:%20rgb(0,%200,%200);%20stroke-linecap:%20round;%20stroke-linejoin:%20round;%20stroke-width:%201.5;'%3e%3c/line%3e%3c/svg%3e",Ry="data:image/svg+xml,%3csvg%20width='20px'%20height='20px'%20viewBox='0%200%2032%2032'%20id='icon'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpolygon%20points='8%202%202%202%202%208%204%208%204%204%208%204%208%202'/%3e%3cpolygon%20points='24%202%2030%202%2030%208%2028%208%2028%204%2024%204%2024%202'/%3e%3cpolygon%20points='8%2030%202%2030%202%2024%204%2024%204%2028%208%2028%208%2030'/%3e%3cpolygon%20points='24%2030%2030%2030%2030%2024%2028%2024%2028%2028%2024%2028%2024%2030'/%3e%3cpath%20d='M24,24H8a2.0023,2.0023,0,0,1-2-2V10A2.0023,2.0023,0,0,1,8,8H24a2.0023,2.0023,0,0,1,2,2V22A2.0023,2.0023,0,0,1,24,24ZM8,10V22H24V10Z'/%3e%3crect%20fill='none'%20width='32'%20height='32'/%3e%3c/svg%3e",Oy="data:image/svg+xml,%3csvg%20width='20px'%20height='20px'%20viewBox='0%200%2032%2032'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20xmlns:sketch='http://www.bohemiancoding.com/sketch/ns'%3e%3cg%20stroke='none'%20stroke-width='1'%20fill='none'%20fill-rule='evenodd'%3e%3cg%20transform='translate(-308.000000,%20-1139.000000)'%20fill='%23000000'%3e%3cpath%20d='M321.46,1163.45%20C315.17,1163.45%20310.07,1158.44%20310.07,1152.25%20C310.07,1146.06%20315.17,1141.04%20321.46,1141.04%20C327.75,1141.04%20332.85,1146.06%20332.85,1152.25%20C332.85,1158.44%20327.75,1163.45%20321.46,1163.45%20L321.46,1163.45%20Z%20M339.688,1169.25%20L331.429,1161.12%20C333.592,1158.77%20334.92,1155.67%20334.92,1152.25%20C334.92,1144.93%20328.894,1139%20321.46,1139%20C314.026,1139%20308,1144.93%20308,1152.25%20C308,1159.56%20314.026,1165.49%20321.46,1165.49%20C324.672,1165.49%20327.618,1164.38%20329.932,1162.53%20L338.225,1170.69%20C338.629,1171.09%20339.284,1171.09%20339.688,1170.69%20C340.093,1170.3%20340.093,1169.65%20339.688,1169.25%20L339.688,1169.25%20Z%20M326.519,1151.41%20L322.522,1151.41%20L322.522,1147.41%20C322.522,1146.85%20322.075,1146.41%20321.523,1146.41%20C320.972,1146.41%20320.524,1146.85%20320.524,1147.41%20L320.524,1151.41%20L316.529,1151.41%20C315.978,1151.41%20315.53,1151.59%20315.53,1152.14%20C315.53,1152.7%20315.978,1153.41%20316.529,1153.41%20L320.524,1153.41%20L320.524,1157.41%20C320.524,1157.97%20320.972,1158.41%20321.523,1158.41%20C322.075,1158.41%20322.522,1157.97%20322.522,1157.41%20L322.522,1153.41%20L326.519,1153.41%20C327.07,1153.41%20327.518,1152.96%20327.518,1152.41%20C327.518,1151.86%20327.07,1151.41%20326.519,1151.41%20L326.519,1151.41%20Z'%20/%3e%3c/g%3e%3c/g%3e%3c/svg%3e",Iy="data:image/svg+xml,%3csvg%20width='20px'%20height='20px'%20viewBox='0%200%2032%2032'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%3e%3cg%20stroke='none'%20stroke-width='1'%20fill='none'%20fill-rule='evenodd'%20%3e%3cg%20transform='translate(-360.000000,%20-1139.000000)'%20fill='%23000000'%3e%3cpath%20d='M373.46,1163.45%20C367.17,1163.45%20362.071,1158.44%20362.071,1152.25%20C362.071,1146.06%20367.17,1141.04%20373.46,1141.04%20C379.75,1141.04%20384.85,1146.06%20384.85,1152.25%20C384.85,1158.44%20379.75,1163.45%20373.46,1163.45%20L373.46,1163.45%20Z%20M391.688,1169.25%20L383.429,1161.12%20C385.592,1158.77%20386.92,1155.67%20386.92,1152.25%20C386.92,1144.93%20380.894,1139%20373.46,1139%20C366.026,1139%20360,1144.93%20360,1152.25%20C360,1159.56%20366.026,1165.49%20373.46,1165.49%20C376.672,1165.49%20379.618,1164.38%20381.932,1162.53%20L390.225,1170.69%20C390.629,1171.09%20391.284,1171.09%20391.688,1170.69%20C392.093,1170.3%20392.093,1169.65%20391.688,1169.25%20L391.688,1169.25%20Z%20M378.689,1151.41%20L368.643,1151.41%20C368.102,1151.41%20367.663,1151.84%20367.663,1152.37%20C367.663,1152.9%20368.102,1153.33%20368.643,1153.33%20L378.689,1153.33%20C379.23,1153.33%20379.669,1152.9%20379.669,1152.37%20C379.669,1151.84%20379.23,1151.41%20378.689,1151.41%20L378.689,1151.41%20Z'%20/%3e%3c/g%3e%3c/g%3e%3c/svg%3e",Ie={export:qy,"fit-screen":Ry,"zoom-in":Oy,"zoom-out":Iy},My=.1;class Py{constructor(t,r,n="apex"){this.element=t,this.canvas=r,this.prefix=n,this.export=new wv(r,n)}createToolbarItem(t,r){const n=document.createElement("div"),i=new Image;i.src=r,n.id=t,n.append(i);const s=ke({alignItems:"center",backgroundColor:"#FFFFFF",border:"1px solid #BCBCBC",cursor:"pointer",display:"flex",height:"30px",justifyContent:"center",width:"30px"});return n.setAttribute("style",s),n}render({enableExport:t=!1,enableFitscreen:r=!1,enableZoom:n=!1,onFitscreen:i=Ro,onZoom:s=Ro}){var a;const o=document.createElement("div");o.id="toolbar";const u=ke({display:"flex",gap:"5px",position:"absolute",right:"10px",top:"10px"});if(o.setAttribute("style",u),t){const c=this.createToolbarItem("export",Ie.export);c.addEventListener("click",()=>{this.export.exportToSVG()}),o.append(c)}if(n){const c=this.createToolbarItem("zoom-in",Ie["zoom-in"]),l=this.createToolbarItem("zoom-out",Ie["zoom-out"]);c.addEventListener("click",()=>{s(My)}),l.addEventListener("click",()=>{s(-.1)}),o.append(c,l)}if(r){const c=this.createToolbarItem("fit-screen",Ie["fit-screen"]);c.addEventListener("click",()=>{i()}),o.append(c)}(a=this.element)==null||a.append(o)}}function $e(e,t){if(t.w===null||t.v===null)return 0;if(t.w===t.v)return Math.PI/2;const r=e.node(t.v),n=e.node(t.w);return Math.atan2(n.y-r.y,n.x-r.x)}function Ly(){this.__data__=[],this.size=0}function Kf(e,t){return e===t||e!==e&&t!==t}function Je(e,t){for(var r=e.length;r--;)if(Kf(e[r][0],t))return r;return-1}var jy=Array.prototype,Ny=jy.splice;function Dy(e){var t=this.__data__,r=Je(t,e);if(r<0)return!1;var n=t.length-1;return r==n?t.pop():Ny.call(t,r,1),--this.size,!0}function Fy(e){var t=this.__data__,r=Je(t,e);return r<0?void 0:t[r][1]}function ky(e){return Je(this.__data__,e)>-1}function $y(e,t){var r=this.__data__,n=Je(r,e);return n<0?(++this.size,r.push([e,t])):r[n][1]=t,this}function dt(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}dt.prototype.clear=Ly;dt.prototype.delete=Dy;dt.prototype.get=Fy;dt.prototype.has=ky;dt.prototype.set=$y;function zy(){this.__data__=new dt,this.size=0}function By(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r}function Hy(e){return this.__data__.get(e)}function Uy(e){return this.__data__.has(e)}function Da(e){var t=typeof e;return e!=null&&(t=="object"||t=="function")}var Ky="[object AsyncFunction]",Wy="[object Function]",Vy="[object GeneratorFunction]",Yy="[object Proxy]";function Fa(e){if(!Da(e))return!1;var t=Pt(e);return t==Wy||t==Vy||t==Ky||t==Yy}var wr=ft["__core-js_shared__"],Oo=function(){var e=/[^.]+$/.exec(wr&&wr.keys&&wr.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();function Jy(e){return!!Oo&&Oo in e}var Zy=Function.prototype,Xy=Zy.toString;function Lt(e){if(e!=null){try{return Xy.call(e)}catch{}try{return e+""}catch{}}return""}var Gy=/[\\^$.*+?()[\]{}|]/g,Qy=/^\[object .+?Constructor\]$/,t0=Function.prototype,e0=Object.prototype,r0=t0.toString,n0=e0.hasOwnProperty,i0=RegExp("^"+r0.call(n0).replace(Gy,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function s0(e){if(!Da(e)||Jy(e))return!1;var t=Fa(e)?i0:Qy;return t.test(Lt(e))}function a0(e,t){return e==null?void 0:e[t]}function Gt(e,t){var r=a0(e,t);return s0(r)?r:void 0}var pe=Gt(ft,"Map"),ge=Gt(Object,"create");function o0(){this.__data__=ge?ge(null):{},this.size=0}function u0(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}var c0="__lodash_hash_undefined__",l0=Object.prototype,h0=l0.hasOwnProperty;function f0(e){var t=this.__data__;if(ge){var r=t[e];return r===c0?void 0:r}return h0.call(t,e)?t[e]:void 0}var d0=Object.prototype,p0=d0.hasOwnProperty;function g0(e){var t=this.__data__;return ge?t[e]!==void 0:p0.call(t,e)}var v0="__lodash_hash_undefined__";function y0(e,t){var r=this.__data__;return this.size+=this.has(e)?0:1,r[e]=ge&&t===void 0?v0:t,this}function Ot(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}Ot.prototype.clear=o0;Ot.prototype.delete=u0;Ot.prototype.get=f0;Ot.prototype.has=g0;Ot.prototype.set=y0;function _0(){this.size=0,this.__data__={hash:new Ot,map:new(pe||dt),string:new Ot}}function m0(e){var t=typeof e;return t=="string"||t=="number"||t=="symbol"||t=="boolean"?e!=="__proto__":e===null}function Ze(e,t){var r=e.__data__;return m0(t)?r[typeof t=="string"?"string":"hash"]:r.map}function b0(e){var t=Ze(this,e).delete(e);return this.size-=t?1:0,t}function w0(e){return Ze(this,e).get(e)}function x0(e){return Ze(this,e).has(e)}function A0(e,t){var r=Ze(this,e),n=r.size;return r.set(e,t),this.size+=r.size==n?0:1,this}function pt(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}pt.prototype.clear=_0;pt.prototype.delete=b0;pt.prototype.get=w0;pt.prototype.has=x0;pt.prototype.set=A0;var S0=200;function T0(e,t){var r=this.__data__;if(r instanceof dt){var n=r.__data__;if(!pe||n.length<S0-1)return n.push([e,t]),this.size=++r.size,this;r=this.__data__=new pt(n)}return r.set(e,t),this.size=r.size,this}function lt(e){var t=this.__data__=new dt(e);this.size=t.size}lt.prototype.clear=zy;lt.prototype.delete=By;lt.prototype.get=Hy;lt.prototype.has=Uy;lt.prototype.set=T0;var C0="__lodash_hash_undefined__";function E0(e){return this.__data__.set(e,C0),this}function q0(e){return this.__data__.has(e)}function ze(e){var t=-1,r=e==null?0:e.length;for(this.__data__=new pt;++t<r;)this.add(e[t])}ze.prototype.add=ze.prototype.push=E0;ze.prototype.has=q0;function R0(e,t){for(var r=-1,n=e==null?0:e.length;++r<n;)if(t(e[r],r,e))return!0;return!1}function O0(e,t){return e.has(t)}var I0=1,M0=2;function Wf(e,t,r,n,i,s){var a=r&I0,o=e.length,u=t.length;if(o!=u&&!(a&&u>o))return!1;var c=s.get(e),l=s.get(t);if(c&&l)return c==t&&l==e;var h=-1,f=!0,d=r&M0?new ze:void 0;for(s.set(e,t),s.set(t,e);++h<o;){var g=e[h],p=t[h];if(n)var v=a?n(p,g,h,t,e,s):n(g,p,h,e,t,s);if(v!==void 0){if(v)continue;f=!1;break}if(d){if(!R0(t,function(y,_){if(!O0(d,_)&&(g===y||i(g,y,r,n,s)))return d.push(_)})){f=!1;break}}else if(!(g===p||i(g,p,r,n,s))){f=!1;break}}return s.delete(e),s.delete(t),f}var Io=ft.Uint8Array;function P0(e){var t=-1,r=Array(e.size);return e.forEach(function(n,i){r[++t]=[i,n]}),r}function L0(e){var t=-1,r=Array(e.size);return e.forEach(function(n){r[++t]=n}),r}var j0=1,N0=2,D0="[object Boolean]",F0="[object Date]",k0="[object Error]",$0="[object Map]",z0="[object Number]",B0="[object RegExp]",H0="[object Set]",U0="[object String]",K0="[object Symbol]",W0="[object ArrayBuffer]",V0="[object DataView]",Mo=xt?xt.prototype:void 0,xr=Mo?Mo.valueOf:void 0;function Y0(e,t,r,n,i,s,a){switch(r){case V0:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case W0:return!(e.byteLength!=t.byteLength||!s(new Io(e),new Io(t)));case D0:case F0:case z0:return Kf(+e,+t);case k0:return e.name==t.name&&e.message==t.message;case B0:case U0:return e==t+"";case $0:var o=P0;case H0:var u=n&j0;if(o||(o=L0),e.size!=t.size&&!u)return!1;var c=a.get(e);if(c)return c==t;n|=N0,a.set(e,t);var l=Wf(o(e),o(t),n,i,s,a);return a.delete(e),l;case K0:if(xr)return xr.call(e)==xr.call(t)}return!1}function J0(e,t){for(var r=-1,n=t.length,i=e.length;++r<n;)e[i+r]=t[r];return e}function Z0(e,t,r){var n=t(e);return st(e)?n:J0(n,r(e))}function X0(e,t){for(var r=-1,n=e==null?0:e.length,i=0,s=[];++r<n;){var a=e[r];t(a,r,e)&&(s[i++]=a)}return s}function G0(){return[]}var Q0=Object.prototype,t1=Q0.propertyIsEnumerable,Po=Object.getOwnPropertySymbols,e1=Po?function(e){return e==null?[]:(e=Object(e),X0(Po(e),function(t){return t1.call(e,t)}))}:G0;function r1(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n}var n1="[object Arguments]";function Lo(e){return Rt(e)&&Pt(e)==n1}var Vf=Object.prototype,i1=Vf.hasOwnProperty,s1=Vf.propertyIsEnumerable,Yf=Lo(function(){return arguments}())?Lo:function(e){return Rt(e)&&i1.call(e,"callee")&&!s1.call(e,"callee")};function a1(){return!1}var Jf=typeof ut=="object"&&ut&&!ut.nodeType&&ut,jo=Jf&&typeof ct=="object"&&ct&&!ct.nodeType&&ct,o1=jo&&jo.exports===Jf,No=o1?ft.Buffer:void 0,u1=No?No.isBuffer:void 0,da=u1||a1,c1=9007199254740991,l1=/^(?:0|[1-9]\d*)$/;function Zf(e,t){var r=typeof e;return t=t??c1,!!t&&(r=="number"||r!="symbol"&&l1.test(e))&&e>-1&&e%1==0&&e<t}var h1=9007199254740991;function ka(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=h1}var f1="[object Arguments]",d1="[object Array]",p1="[object Boolean]",g1="[object Date]",v1="[object Error]",y1="[object Function]",_1="[object Map]",m1="[object Number]",b1="[object Object]",w1="[object RegExp]",x1="[object Set]",A1="[object String]",S1="[object WeakMap]",T1="[object ArrayBuffer]",C1="[object DataView]",E1="[object Float32Array]",q1="[object Float64Array]",R1="[object Int8Array]",O1="[object Int16Array]",I1="[object Int32Array]",M1="[object Uint8Array]",P1="[object Uint8ClampedArray]",L1="[object Uint16Array]",j1="[object Uint32Array]",$={};$[E1]=$[q1]=$[R1]=$[O1]=$[I1]=$[M1]=$[P1]=$[L1]=$[j1]=!0;$[f1]=$[d1]=$[T1]=$[p1]=$[C1]=$[g1]=$[v1]=$[y1]=$[_1]=$[m1]=$[b1]=$[w1]=$[x1]=$[A1]=$[S1]=!1;function N1(e){return Rt(e)&&ka(e.length)&&!!$[Pt(e)]}function D1(e){return function(t){return e(t)}}var Xf=typeof ut=="object"&&ut&&!ut.nodeType&&ut,ce=Xf&&typeof ct=="object"&&ct&&!ct.nodeType&&ct,F1=ce&&ce.exports===Xf,Ar=F1&&Rf.process,Do=function(){try{var e=ce&&ce.require&&ce.require("util").types;return e||Ar&&Ar.binding&&Ar.binding("util")}catch{}}(),Fo=Do&&Do.isTypedArray,Gf=Fo?D1(Fo):N1,k1=Object.prototype,$1=k1.hasOwnProperty;function z1(e,t){var r=st(e),n=!r&&Yf(e),i=!r&&!n&&da(e),s=!r&&!n&&!i&&Gf(e),a=r||n||i||s,o=a?r1(e.length,String):[],u=o.length;for(var c in e)$1.call(e,c)&&!(a&&(c=="length"||i&&(c=="offset"||c=="parent")||s&&(c=="buffer"||c=="byteLength"||c=="byteOffset")||Zf(c,u)))&&o.push(c);return o}var B1=Object.prototype;function H1(e){var t=e&&e.constructor,r=typeof t=="function"&&t.prototype||B1;return e===r}function U1(e,t){return function(r){return e(t(r))}}var K1=U1(Object.keys,Object),W1=Object.prototype,V1=W1.hasOwnProperty;function Y1(e){if(!H1(e))return K1(e);var t=[];for(var r in Object(e))V1.call(e,r)&&r!="constructor"&&t.push(r);return t}function J1(e){return e!=null&&ka(e.length)&&!Fa(e)}function Qf(e){return J1(e)?z1(e):Y1(e)}function ko(e){return Z0(e,Qf,e1)}var Z1=1,X1=Object.prototype,G1=X1.hasOwnProperty;function Q1(e,t,r,n,i,s){var a=r&Z1,o=ko(e),u=o.length,c=ko(t),l=c.length;if(u!=l&&!a)return!1;for(var h=u;h--;){var f=o[h];if(!(a?f in t:G1.call(t,f)))return!1}var d=s.get(e),g=s.get(t);if(d&&g)return d==t&&g==e;var p=!0;s.set(e,t),s.set(t,e);for(var v=a;++h<u;){f=o[h];var y=e[f],_=t[f];if(n)var b=a?n(_,y,f,t,e,s):n(y,_,f,e,t,s);if(!(b===void 0?y===_||i(y,_,r,n,s):b)){p=!1;break}v||(v=f=="constructor")}if(p&&!v){var m=e.constructor,S=t.constructor;m!=S&&"constructor"in e&&"constructor"in t&&!(typeof m=="function"&&m instanceof m&&typeof S=="function"&&S instanceof S)&&(p=!1)}return s.delete(e),s.delete(t),p}var pa=Gt(ft,"DataView"),ga=Gt(ft,"Promise"),va=Gt(ft,"Set"),ya=Gt(ft,"WeakMap"),$o="[object Map]",t_="[object Object]",zo="[object Promise]",Bo="[object Set]",Ho="[object WeakMap]",Uo="[object DataView]",e_=Lt(pa),r_=Lt(pe),n_=Lt(ga),i_=Lt(va),s_=Lt(ya),_t=Pt;(pa&&_t(new pa(new ArrayBuffer(1)))!=Uo||pe&&_t(new pe)!=$o||ga&&_t(ga.resolve())!=zo||va&&_t(new va)!=Bo||ya&&_t(new ya)!=Ho)&&(_t=function(e){var t=Pt(e),r=t==t_?e.constructor:void 0,n=r?Lt(r):"";if(n)switch(n){case e_:return Uo;case r_:return $o;case n_:return zo;case i_:return Bo;case s_:return Ho}return t});var a_=1,Ko="[object Arguments]",Wo="[object Array]",Me="[object Object]",o_=Object.prototype,Vo=o_.hasOwnProperty;function u_(e,t,r,n,i,s){var a=st(e),o=st(t),u=a?Wo:_t(e),c=o?Wo:_t(t);u=u==Ko?Me:u,c=c==Ko?Me:c;var l=u==Me,h=c==Me,f=u==c;if(f&&da(e)){if(!da(t))return!1;a=!0,l=!1}if(f&&!l)return s||(s=new lt),a||Gf(e)?Wf(e,t,r,n,i,s):Y0(e,t,u,r,n,i,s);if(!(r&a_)){var d=l&&Vo.call(e,"__wrapped__"),g=h&&Vo.call(t,"__wrapped__");if(d||g){var p=d?e.value():e,v=g?t.value():t;return s||(s=new lt),i(p,v,r,n,s)}}return f?(s||(s=new lt),Q1(e,t,r,n,i,s)):!1}function $a(e,t,r,n,i){return e===t?!0:e==null||t==null||!Rt(e)&&!Rt(t)?e!==e&&t!==t:u_(e,t,r,n,$a,i)}var c_=1,l_=2;function h_(e,t,r,n){var i=r.length,s=i;if(e==null)return!s;for(e=Object(e);i--;){var a=r[i];if(a[2]?a[1]!==e[a[0]]:!(a[0]in e))return!1}for(;++i<s;){a=r[i];var o=a[0],u=e[o],c=a[1];if(a[2]){if(u===void 0&&!(o in e))return!1}else{var l=new lt,h;if(!(h===void 0?$a(c,u,c_|l_,n,l):h))return!1}}return!0}function td(e){return e===e&&!Da(e)}function f_(e){for(var t=Qf(e),r=t.length;r--;){var n=t[r],i=e[n];t[r]=[n,i,td(i)]}return t}function ed(e,t){return function(r){return r==null?!1:r[e]===t&&(t!==void 0||e in Object(r))}}function d_(e){var t=f_(e);return t.length==1&&t[0][2]?ed(t[0][0],t[0][1]):function(r){return r===e||h_(r,e,t)}}var p_=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,g_=/^\w*$/;function za(e,t){if(st(e))return!1;var r=typeof e;return r=="number"||r=="symbol"||r=="boolean"||e==null||Ye(e)?!0:g_.test(e)||!p_.test(e)||t!=null&&e in Object(t)}var v_="Expected a function";function Ba(e,t){if(typeof e!="function"||t!=null&&typeof t!="function")throw new TypeError(v_);var r=function(){var n=arguments,i=t?t.apply(this,n):n[0],s=r.cache;if(s.has(i))return s.get(i);var a=e.apply(this,n);return r.cache=s.set(i,a)||s,a};return r.cache=new(Ba.Cache||pt),r}Ba.Cache=pt;var y_=500;function __(e){var t=Ba(e,function(n){return r.size===y_&&r.clear(),n}),r=t.cache;return t}var m_=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,b_=/\\(\\)?/g,w_=__(function(e){var t=[];return e.charCodeAt(0)===46&&t.push(""),e.replace(m_,function(r,n,i,s){t.push(i?s.replace(b_,"$1"):n||r)}),t});function rd(e,t){return st(e)?e:za(e,t)?[e]:w_(Na(e))}function Xe(e){if(typeof e=="string"||Ye(e))return e;var t=e+"";return t=="0"&&1/e==-1/0?"-0":t}function nd(e,t){t=rd(t,e);for(var r=0,n=t.length;e!=null&&r<n;)e=e[Xe(t[r++])];return r&&r==n?e:void 0}function x_(e,t,r){var n=e==null?void 0:nd(e,t);return n===void 0?r:n}function A_(e,t){return e!=null&&t in Object(e)}function S_(e,t,r){t=rd(t,e);for(var n=-1,i=t.length,s=!1;++n<i;){var a=Xe(t[n]);if(!(s=e!=null&&r(e,a)))break;e=e[a]}return s||++n!=i?s:(i=e==null?0:e.length,!!i&&ka(i)&&Zf(a,i)&&(st(e)||Yf(e)))}function T_(e,t){return e!=null&&S_(e,t,A_)}var C_=1,E_=2;function q_(e,t){return za(e)&&td(t)?ed(Xe(e),t):function(r){var n=x_(r,e);return n===void 0&&n===t?T_(r,e):$a(t,n,C_|E_)}}function id(e){return e}function R_(e){return function(t){return t==null?void 0:t[e]}}function O_(e){return function(t){return nd(t,e)}}function I_(e){return za(e)?R_(Xe(e)):O_(e)}function M_(e){return typeof e=="function"?e:e==null?id:typeof e=="object"?st(e)?q_(e[0],e[1]):d_(e):I_(e)}function P_(e,t){for(var r,n=-1,i=e.length;++n<i;){var s=t(e[n]);s!==void 0&&(r=r===void 0?s:r+s)}return r}function nt(e,t){return e&&e.length?P_(e,M_(t)):0}function L_(e,t){const r=e.inEdges(t),n=e.outEdges(t),i=new Map;[...r,...n].forEach(u=>{i.has(u.name)||i.set(u.name,[]),i.get(u.name).push(u)});const s=new Map(Array.from(i.entries()).map(([u,c])=>{const l=nt(c,f=>e.edge(f).value),h=nt(c,f=>e.edge(f).value*o(f));return[u,h/l]})),a=Array.from(s.keys());return a.sort(),a;function o(u){if(u.v===t)return e.node(u.w).y;if(u.w===t)return e.node(u.v).y;throw new Error}}function j_(e,{alignLinkTypes:t=!1}={}){e.nodes().forEach(r=>{const n=e.node(r);if(n.incoming=e.inEdges(r),n.outgoing=e.outEdges(r),t){const i=L_(e,r);n.incoming.sort(Jo(e,i,!1)),n.outgoing.sort(Jo(e,i,!0))}else n.incoming.sort(Yo(e,!1)),n.outgoing.sort(Yo(e,!0))})}function Yo(e,t=!0){return function(r,n){const i=$e(e,r),s=$e(e,n),a=t?1:-1;return r.v===n.v&&r.w===n.w?typeof r.name=="number"&&typeof n.name=="number"?r.name-n.name:typeof r.name=="string"&&typeof n.name=="string"?r.name.localeCompare(n.name):0:Math.abs(i-s)<.001?r.v&&r.w&&n.v&&n.w?r.w===n.w?-1*(e.node(r.v).y-e.node(n.v).y):r.v===n.v?-1*(e.node(r.w).y-e.node(n.w).y):0:0:a*(i-s)}}function Jo(e,t,r=!0){return function(n,i){if(n.name!==i.name)return t.indexOf(n.name)-t.indexOf(i.name);const s=$e(e,n),a=$e(e,i),o=r?1:-1;return Math.abs(s-a)<.001?n.v&&n.w&&i.v&&i.w?n.w===i.w?o*(s>0?-1:1)*(e.node(n.v).y-e.node(i.v).y):n.v===i.v?o*(s>0?-1:1)*(e.node(n.w).y-e.node(i.w).y):0:0:o*(s-a)}}function Zo(e,t){let r=null;for(let n=0;n<e.length;++n)if(t(e[n])){r=n;break}return r}function Xo(e,t){if(e.length===0)return;let r,n=0;for(let s=e.length-1;s>=0;--s)r=e[s].dy/2,e[s][t]-r<n&&(e[s][t]=Math.min(e[s].Rmax,n+r)),n=e[s][t]+r;let i=e[0].Rmax+e[0].dy/2;for(let s=0;s<e.length;++s)r=e[s].dy/2,e[s][t]+r>i&&(e[s][t]=Math.max(r,i-r)),i=e[s][t]-r}function N_(e){e.edges().forEach(t=>{const r=e.edge(t);r.points.unshift({d:r.d0,ro:r.r0,x:r.x0,y:r.y0}),r.points.push({d:r.d1,ri:r.r1,x:r.x1,y:r.y1})})}function D_(e){function t(r){F_(r,e),k_(r);const n=[];return r.edges().forEach(i=>{const s=r.edge(i);s.id=`${i.v}-${i.w}-${i.name}`,n.push(s)}),n}return t}function F_(e,t){e.nodes().forEach(r=>{const n=e.node(r);let i=0,s=0;n.outgoing.forEach(a=>{const o=e.edge(a);o.x0=n.x+t.nodeWidth/2,o.y0=n.y+i+o.dy/2,o.d0=n.direction||"r",i+=o.dy}),n.incoming.forEach(a=>{const o=e.edge(a);o.x1=n.x-t.nodeWidth/2,o.y1=n.y+s+o.dy/2,o.d1=n.direction||"r",s+=o.dy})})}function k_(e){e.nodes().forEach(t=>{const r=e.node(t),n=r.outgoing.map(s=>e.edge(s)),i=r.incoming.map(s=>e.edge(s));n.sort((s,a)=>s.y0-a.y0),i.sort((s,a)=>s.y1-a.y1),Go(n,"r0"),Go(i,"r1")})}function $_(e){const t=e.x1-e.x0,r=e.y1-e.y0;return e.d0!==e.d1?Math.abs(r)/2.1:r!==0?(t*t+r*r)/Math.abs(4*r):1/0}function Go(e,t){e.forEach(n=>{n.Rmax=$_(n),n[t]=Math.max(n.dy/2,n.d0===n.d1?n.Rmax*.6:5+n.dy/2)});let r=t==="r0"?Zo(e,n=>n.y1>n.y0):Zo(e,n=>n.y0>n.y1);if(r===null&&(r=e.length),Xo(e.slice(r),t),r>0){const n=[];for(let i=r-1;i>=0;i--)n.push(e[i]);Xo(n,t)}}function z_(e){return e.edges().forEach(t=>{const r=e.node(t.v),n=e.node(t.w),i=e.edge(t);let s=r.rank;const a=[];if(s+1<=n.rank){for(r.direction==="l"&&a.push(s);++s<n.rank;)a.push(s);n.direction==="l"&&a.push(s),Qo(e,t,i,a,"r")}else if(s>n.rank){for(r.direction!=="l"&&a.push(s);s-- >n.rank+1;)a.push(s);n.direction!=="l"&&a.push(s),Qo(e,t,i,a,"l")}}),e}function Qo(e,t,r,n,i){if(n.length===0)return;const s=n.map(o=>{const u=`__${t.v}_${t.w}_${o}`;return e.setNode(u,{data:null,direction:i,dummy:!0,rank:o}),u}),a=[t.v,...s,t.w];a.forEach((o,u)=>{u+1<a.length&&e.setEdge(a[u],a[u+1],{data:e.edge(t).data,origEdge:t,origLabel:r,source:e.node(t.v),target:e.node(t.w),value:e.edge(t).value},t.name)}),e.removeEdge(t)}function B_(e){e.nodes().filter(r=>r.startsWith("__")).forEach(r=>{var n;let i=e.node(r);if(!i)return;let s=((n=e.inEdges(r))==null?void 0:n.map(u=>e.edge(u)))||[];s.forEach(u=>{u.origLabel.dy=u.dy,u.origLabel.x0=u.x0,u.origLabel.y0=u.y0,u.origLabel.r0=u.r0,u.origLabel.d0=u.d0,u.origLabel.value=u.value,e.setEdge(u.origEdge,u.origLabel)});let a=s.map(u=>u.r1),o;for(;i.dummy;)s=e.outEdges(r).map(u=>e.edge(u)),s.forEach((u,c)=>{u.origLabel.points||(u.origLabel.points=[]),u.origLabel.points.push({d:u.d0,r0:u.r0,r1:a[c],x:i.x,y:u.y0})}),a=s.map(u=>u.r1),o=e.successors(r)[0],e.removeNode(r),i=e.node(r=o);s.forEach(u=>{u.origLabel.x1=u.x1,u.origLabel.y1=u.y1,u.origLabel.r1=u.r1,u.origLabel.d1=u.d1})})}function _a(e,t,r){return H_(e,t,r)}function H_(e,t,r){let n,i,s;t.length>r.length?(n=t,s=r):(n=r,s=t);const a=[];n.forEach(h=>{s.forEach((f,d)=>{(e.hasEdge(h,f)||e.hasEdge(f,h))&&a.push(d)})});let o=1;for(;o<i;)o*=2;const u=2*o-1;o-=1;const c=new Array(u);for(let h=0;h<u;h++)c[h]=0;let l=0;return a.forEach(h=>{let f=h+o;for(c[f]++;f>0;)f%2&&(l+=c[f+1]),f=Math.floor((f-1)/2),c[f]++}),l}var Pe=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function U_(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function K_(e){throw new Error('Could not dynamically require "'+e+'". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.')}var Sr,tu;function W_(){if(tu)return Sr;tu=1;function e(){this.__data__=[],this.size=0}return Sr=e,Sr}var Tr,eu;function Ha(){if(eu)return Tr;eu=1;function e(t,r){return t===r||t!==t&&r!==r}return Tr=e,Tr}var Cr,ru;function Ge(){if(ru)return Cr;ru=1;var e=Ha();function t(r,n){for(var i=r.length;i--;)if(e(r[i][0],n))return i;return-1}return Cr=t,Cr}var Er,nu;function V_(){if(nu)return Er;nu=1;var e=Ge(),t=Array.prototype,r=t.splice;function n(i){var s=this.__data__,a=e(s,i);if(a<0)return!1;var o=s.length-1;return a==o?s.pop():r.call(s,a,1),--this.size,!0}return Er=n,Er}var qr,iu;function Y_(){if(iu)return qr;iu=1;var e=Ge();function t(r){var n=this.__data__,i=e(n,r);return i<0?void 0:n[i][1]}return qr=t,qr}var Rr,su;function J_(){if(su)return Rr;su=1;var e=Ge();function t(r){return e(this.__data__,r)>-1}return Rr=t,Rr}var Or,au;function Z_(){if(au)return Or;au=1;var e=Ge();function t(r,n){var i=this.__data__,s=e(i,r);return s<0?(++this.size,i.push([r,n])):i[s][1]=n,this}return Or=t,Or}var Ir,ou;function Qe(){if(ou)return Ir;ou=1;var e=W_(),t=V_(),r=Y_(),n=J_(),i=Z_();function s(a){var o=-1,u=a==null?0:a.length;for(this.clear();++o<u;){var c=a[o];this.set(c[0],c[1])}}return s.prototype.clear=e,s.prototype.delete=t,s.prototype.get=r,s.prototype.has=n,s.prototype.set=i,Ir=s,Ir}var Mr,uu;function X_(){if(uu)return Mr;uu=1;var e=Qe();function t(){this.__data__=new e,this.size=0}return Mr=t,Mr}var Pr,cu;function G_(){if(cu)return Pr;cu=1;function e(t){var r=this.__data__,n=r.delete(t);return this.size=r.size,n}return Pr=e,Pr}var Lr,lu;function Q_(){if(lu)return Lr;lu=1;function e(t){return this.__data__.get(t)}return Lr=e,Lr}var jr,hu;function t2(){if(hu)return jr;hu=1;function e(t){return this.__data__.has(t)}return jr=e,jr}var Nr,fu;function sd(){if(fu)return Nr;fu=1;var e=typeof Pe=="object"&&Pe&&Pe.Object===Object&&Pe;return Nr=e,Nr}var Dr,du;function ot(){if(du)return Dr;du=1;var e=sd(),t=typeof self=="object"&&self&&self.Object===Object&&self,r=e||t||Function("return this")();return Dr=r,Dr}var Fr,pu;function Qt(){if(pu)return Fr;pu=1;var e=ot(),t=e.Symbol;return Fr=t,Fr}var kr,gu;function e2(){if(gu)return kr;gu=1;var e=Qt(),t=Object.prototype,r=t.hasOwnProperty,n=t.toString,i=e?e.toStringTag:void 0;function s(a){var o=r.call(a,i),u=a[i];try{a[i]=void 0;var c=!0}catch{}var l=n.call(a);return c&&(o?a[i]=u:delete a[i]),l}return kr=s,kr}var $r,vu;function r2(){if(vu)return $r;vu=1;var e=Object.prototype,t=e.toString;function r(n){return t.call(n)}return $r=r,$r}var zr,yu;function te(){if(yu)return zr;yu=1;var e=Qt(),t=e2(),r=r2(),n="[object Null]",i="[object Undefined]",s=e?e.toStringTag:void 0;function a(o){return o==null?o===void 0?i:n:s&&s in Object(o)?t(o):r(o)}return zr=a,zr}var Br,_u;function jt(){if(_u)return Br;_u=1;function e(t){var r=typeof t;return t!=null&&(r=="object"||r=="function")}return Br=e,Br}var Hr,mu;function tr(){if(mu)return Hr;mu=1;var e=te(),t=jt(),r="[object AsyncFunction]",n="[object Function]",i="[object GeneratorFunction]",s="[object Proxy]";function a(o){if(!t(o))return!1;var u=e(o);return u==n||u==i||u==r||u==s}return Hr=a,Hr}var Ur,bu;function n2(){if(bu)return Ur;bu=1;var e=ot(),t=e["__core-js_shared__"];return Ur=t,Ur}var Kr,wu;function i2(){if(wu)return Kr;wu=1;var e=n2(),t=function(){var n=/[^.]+$/.exec(e&&e.keys&&e.keys.IE_PROTO||"");return n?"Symbol(src)_1."+n:""}();function r(n){return!!t&&t in n}return Kr=r,Kr}var Wr,xu;function ad(){if(xu)return Wr;xu=1;var e=Function.prototype,t=e.toString;function r(n){if(n!=null){try{return t.call(n)}catch{}try{return n+""}catch{}}return""}return Wr=r,Wr}var Vr,Au;function s2(){if(Au)return Vr;Au=1;var e=tr(),t=i2(),r=jt(),n=ad(),i=/[\\^$.*+?()[\]{}|]/g,s=/^\[object .+?Constructor\]$/,a=Function.prototype,o=Object.prototype,u=a.toString,c=o.hasOwnProperty,l=RegExp("^"+u.call(c).replace(i,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function h(f){if(!r(f)||t(f))return!1;var d=e(f)?l:s;return d.test(n(f))}return Vr=h,Vr}var Yr,Su;function a2(){if(Su)return Yr;Su=1;function e(t,r){return t==null?void 0:t[r]}return Yr=e,Yr}var Jr,Tu;function Nt(){if(Tu)return Jr;Tu=1;var e=s2(),t=a2();function r(n,i){var s=t(n,i);return e(s)?s:void 0}return Jr=r,Jr}var Zr,Cu;function Ua(){if(Cu)return Zr;Cu=1;var e=Nt(),t=ot(),r=e(t,"Map");return Zr=r,Zr}var Xr,Eu;function er(){if(Eu)return Xr;Eu=1;var e=Nt(),t=e(Object,"create");return Xr=t,Xr}var Gr,qu;function o2(){if(qu)return Gr;qu=1;var e=er();function t(){this.__data__=e?e(null):{},this.size=0}return Gr=t,Gr}var Qr,Ru;function u2(){if(Ru)return Qr;Ru=1;function e(t){var r=this.has(t)&&delete this.__data__[t];return this.size-=r?1:0,r}return Qr=e,Qr}var tn,Ou;function c2(){if(Ou)return tn;Ou=1;var e=er(),t="__lodash_hash_undefined__",r=Object.prototype,n=r.hasOwnProperty;function i(s){var a=this.__data__;if(e){var o=a[s];return o===t?void 0:o}return n.call(a,s)?a[s]:void 0}return tn=i,tn}var en,Iu;function l2(){if(Iu)return en;Iu=1;var e=er(),t=Object.prototype,r=t.hasOwnProperty;function n(i){var s=this.__data__;return e?s[i]!==void 0:r.call(s,i)}return en=n,en}var rn,Mu;function h2(){if(Mu)return rn;Mu=1;var e=er(),t="__lodash_hash_undefined__";function r(n,i){var s=this.__data__;return this.size+=this.has(n)?0:1,s[n]=e&&i===void 0?t:i,this}return rn=r,rn}var nn,Pu;function f2(){if(Pu)return nn;Pu=1;var e=o2(),t=u2(),r=c2(),n=l2(),i=h2();function s(a){var o=-1,u=a==null?0:a.length;for(this.clear();++o<u;){var c=a[o];this.set(c[0],c[1])}}return s.prototype.clear=e,s.prototype.delete=t,s.prototype.get=r,s.prototype.has=n,s.prototype.set=i,nn=s,nn}var sn,Lu;function d2(){if(Lu)return sn;Lu=1;var e=f2(),t=Qe(),r=Ua();function n(){this.size=0,this.__data__={hash:new e,map:new(r||t),string:new e}}return sn=n,sn}var an,ju;function p2(){if(ju)return an;ju=1;function e(t){var r=typeof t;return r=="string"||r=="number"||r=="symbol"||r=="boolean"?t!=="__proto__":t===null}return an=e,an}var on,Nu;function rr(){if(Nu)return on;Nu=1;var e=p2();function t(r,n){var i=r.__data__;return e(n)?i[typeof n=="string"?"string":"hash"]:i.map}return on=t,on}var un,Du;function g2(){if(Du)return un;Du=1;var e=rr();function t(r){var n=e(this,r).delete(r);return this.size-=n?1:0,n}return un=t,un}var cn,Fu;function v2(){if(Fu)return cn;Fu=1;var e=rr();function t(r){return e(this,r).get(r)}return cn=t,cn}var ln,ku;function y2(){if(ku)return ln;ku=1;var e=rr();function t(r){return e(this,r).has(r)}return ln=t,ln}var hn,$u;function _2(){if($u)return hn;$u=1;var e=rr();function t(r,n){var i=e(this,r),s=i.size;return i.set(r,n),this.size+=i.size==s?0:1,this}return hn=t,hn}var fn,zu;function Ka(){if(zu)return fn;zu=1;var e=d2(),t=g2(),r=v2(),n=y2(),i=_2();function s(a){var o=-1,u=a==null?0:a.length;for(this.clear();++o<u;){var c=a[o];this.set(c[0],c[1])}}return s.prototype.clear=e,s.prototype.delete=t,s.prototype.get=r,s.prototype.has=n,s.prototype.set=i,fn=s,fn}var dn,Bu;function m2(){if(Bu)return dn;Bu=1;var e=Qe(),t=Ua(),r=Ka(),n=200;function i(s,a){var o=this.__data__;if(o instanceof e){var u=o.__data__;if(!t||u.length<n-1)return u.push([s,a]),this.size=++o.size,this;o=this.__data__=new r(u)}return o.set(s,a),this.size=o.size,this}return dn=i,dn}var pn,Hu;function Wa(){if(Hu)return pn;Hu=1;var e=Qe(),t=X_(),r=G_(),n=Q_(),i=t2(),s=m2();function a(o){var u=this.__data__=new e(o);this.size=u.size}return a.prototype.clear=t,a.prototype.delete=r,a.prototype.get=n,a.prototype.has=i,a.prototype.set=s,pn=a,pn}var gn,Uu;function Va(){if(Uu)return gn;Uu=1;function e(t,r){for(var n=-1,i=t==null?0:t.length;++n<i&&r(t[n],n,t)!==!1;);return t}return gn=e,gn}var vn,Ku;function od(){if(Ku)return vn;Ku=1;var e=Nt(),t=function(){try{var r=e(Object,"defineProperty");return r({},"",{}),r}catch{}}();return vn=t,vn}var yn,Wu;function ud(){if(Wu)return yn;Wu=1;var e=od();function t(r,n,i){n=="__proto__"&&e?e(r,n,{configurable:!0,enumerable:!0,value:i,writable:!0}):r[n]=i}return yn=t,yn}var _n,Vu;function cd(){if(Vu)return _n;Vu=1;var e=ud(),t=Ha(),r=Object.prototype,n=r.hasOwnProperty;function i(s,a,o){var u=s[a];(!(n.call(s,a)&&t(u,o))||o===void 0&&!(a in s))&&e(s,a,o)}return _n=i,_n}var mn,Yu;function nr(){if(Yu)return mn;Yu=1;var e=cd(),t=ud();function r(n,i,s,a){var o=!s;s||(s={});for(var u=-1,c=i.length;++u<c;){var l=i[u],h=a?a(s[l],n[l],l,s,n):void 0;h===void 0&&(h=n[l]),o?t(s,l,h):e(s,l,h)}return s}return mn=r,mn}var bn,Ju;function b2(){if(Ju)return bn;Ju=1;function e(t,r){for(var n=-1,i=Array(t);++n<t;)i[n]=r(n);return i}return bn=e,bn}var wn,Zu;function gt(){if(Zu)return wn;Zu=1;function e(t){return t!=null&&typeof t=="object"}return wn=e,wn}var xn,Xu;function w2(){if(Xu)return xn;Xu=1;var e=te(),t=gt(),r="[object Arguments]";function n(i){return t(i)&&e(i)==r}return xn=n,xn}var An,Gu;function ir(){if(Gu)return An;Gu=1;var e=w2(),t=gt(),r=Object.prototype,n=r.hasOwnProperty,i=r.propertyIsEnumerable,s=e(function(){return arguments}())?e:function(a){return t(a)&&n.call(a,"callee")&&!i.call(a,"callee")};return An=s,An}var Sn,Qu;function K(){if(Qu)return Sn;Qu=1;var e=Array.isArray;return Sn=e,Sn}var ae={exports:{}},Tn,tc;function x2(){if(tc)return Tn;tc=1;function e(){return!1}return Tn=e,Tn}ae.exports;var ec;function Se(){return ec||(ec=1,function(e,t){var r=ot(),n=x2(),i=t&&!t.nodeType&&t,s=i&&!0&&e&&!e.nodeType&&e,a=s&&s.exports===i,o=a?r.Buffer:void 0,u=o?o.isBuffer:void 0,c=u||n;e.exports=c}(ae,ae.exports)),ae.exports}var Cn,rc;function ld(){if(rc)return Cn;rc=1;var e=9007199254740991,t=/^(?:0|[1-9]\d*)$/;function r(n,i){var s=typeof n;return i=i??e,!!i&&(s=="number"||s!="symbol"&&t.test(n))&&n>-1&&n%1==0&&n<i}return Cn=r,Cn}var En,nc;function Ya(){if(nc)return En;nc=1;var e=9007199254740991;function t(r){return typeof r=="number"&&r>-1&&r%1==0&&r<=e}return En=t,En}var qn,ic;function A2(){if(ic)return qn;ic=1;var e=te(),t=Ya(),r=gt(),n="[object Arguments]",i="[object Array]",s="[object Boolean]",a="[object Date]",o="[object Error]",u="[object Function]",c="[object Map]",l="[object Number]",h="[object Object]",f="[object RegExp]",d="[object Set]",g="[object String]",p="[object WeakMap]",v="[object ArrayBuffer]",y="[object DataView]",_="[object Float32Array]",b="[object Float64Array]",m="[object Int8Array]",S="[object Int16Array]",w="[object Int32Array]",x="[object Uint8Array]",R="[object Uint8ClampedArray]",C="[object Uint16Array]",F="[object Uint32Array]",A={};A[_]=A[b]=A[m]=A[S]=A[w]=A[x]=A[R]=A[C]=A[F]=!0,A[n]=A[i]=A[v]=A[s]=A[y]=A[a]=A[o]=A[u]=A[c]=A[l]=A[h]=A[f]=A[d]=A[g]=A[p]=!1;function U(W){return r(W)&&t(W.length)&&!!A[e(W)]}return qn=U,qn}var Rn,sc;function Ja(){if(sc)return Rn;sc=1;function e(t){return function(r){return t(r)}}return Rn=e,Rn}var oe={exports:{}};oe.exports;var ac;function Za(){return ac||(ac=1,function(e,t){var r=sd(),n=t&&!t.nodeType&&t,i=n&&!0&&e&&!e.nodeType&&e,s=i&&i.exports===n,a=s&&r.process,o=function(){try{var u=i&&i.require&&i.require("util").types;return u||a&&a.binding&&a.binding("util")}catch{}}();e.exports=o}(oe,oe.exports)),oe.exports}var On,oc;function sr(){if(oc)return On;oc=1;var e=A2(),t=Ja(),r=Za(),n=r&&r.isTypedArray,i=n?t(n):e;return On=i,On}var In,uc;function hd(){if(uc)return In;uc=1;var e=b2(),t=ir(),r=K(),n=Se(),i=ld(),s=sr(),a=Object.prototype,o=a.hasOwnProperty;function u(c,l){var h=r(c),f=!h&&t(c),d=!h&&!f&&n(c),g=!h&&!f&&!d&&s(c),p=h||f||d||g,v=p?e(c.length,String):[],y=v.length;for(var _ in c)(l||o.call(c,_))&&!(p&&(_=="length"||d&&(_=="offset"||_=="parent")||g&&(_=="buffer"||_=="byteLength"||_=="byteOffset")||i(_,y)))&&v.push(_);return v}return In=u,In}var Mn,cc;function ar(){if(cc)return Mn;cc=1;var e=Object.prototype;function t(r){var n=r&&r.constructor,i=typeof n=="function"&&n.prototype||e;return r===i}return Mn=t,Mn}var Pn,lc;function fd(){if(lc)return Pn;lc=1;function e(t,r){return function(n){return t(r(n))}}return Pn=e,Pn}var Ln,hc;function S2(){if(hc)return Ln;hc=1;var e=fd(),t=e(Object.keys,Object);return Ln=t,Ln}var jn,fc;function Xa(){if(fc)return jn;fc=1;var e=ar(),t=S2(),r=Object.prototype,n=r.hasOwnProperty;function i(s){if(!e(s))return t(s);var a=[];for(var o in Object(s))n.call(s,o)&&o!="constructor"&&a.push(o);return a}return jn=i,jn}var Nn,dc;function Dt(){if(dc)return Nn;dc=1;var e=tr(),t=Ya();function r(n){return n!=null&&t(n.length)&&!e(n)}return Nn=r,Nn}var Dn,pc;function Ft(){if(pc)return Dn;pc=1;var e=hd(),t=Xa(),r=Dt();function n(i){return r(i)?e(i):t(i)}return Dn=n,Dn}var Fn,gc;function T2(){if(gc)return Fn;gc=1;var e=nr(),t=Ft();function r(n,i){return n&&e(i,t(i),n)}return Fn=r,Fn}var kn,vc;function C2(){if(vc)return kn;vc=1;function e(t){var r=[];if(t!=null)for(var n in Object(t))r.push(n);return r}return kn=e,kn}var $n,yc;function E2(){if(yc)return $n;yc=1;var e=jt(),t=ar(),r=C2(),n=Object.prototype,i=n.hasOwnProperty;function s(a){if(!e(a))return r(a);var o=t(a),u=[];for(var c in a)c=="constructor"&&(o||!i.call(a,c))||u.push(c);return u}return $n=s,$n}var zn,_c;function Ga(){if(_c)return zn;_c=1;var e=hd(),t=E2(),r=Dt();function n(i){return r(i)?e(i,!0):t(i)}return zn=n,zn}var Bn,mc;function q2(){if(mc)return Bn;mc=1;var e=nr(),t=Ga();function r(n,i){return n&&e(i,t(i),n)}return Bn=r,Bn}var ue={exports:{}};ue.exports;var bc;function R2(){return bc||(bc=1,function(e,t){var r=ot(),n=t&&!t.nodeType&&t,i=n&&!0&&e&&!e.nodeType&&e,s=i&&i.exports===n,a=s?r.Buffer:void 0,o=a?a.allocUnsafe:void 0;function u(c,l){if(l)return c.slice();var h=c.length,f=o?o(h):new c.constructor(h);return c.copy(f),f}e.exports=u}(ue,ue.exports)),ue.exports}var Hn,wc;function O2(){if(wc)return Hn;wc=1;function e(t,r){var n=-1,i=t.length;for(r||(r=Array(i));++n<i;)r[n]=t[n];return r}return Hn=e,Hn}var Un,xc;function dd(){if(xc)return Un;xc=1;function e(t,r){for(var n=-1,i=t==null?0:t.length,s=0,a=[];++n<i;){var o=t[n];r(o,n,t)&&(a[s++]=o)}return a}return Un=e,Un}var Kn,Ac;function pd(){if(Ac)return Kn;Ac=1;function e(){return[]}return Kn=e,Kn}var Wn,Sc;function Qa(){if(Sc)return Wn;Sc=1;var e=dd(),t=pd(),r=Object.prototype,n=r.propertyIsEnumerable,i=Object.getOwnPropertySymbols,s=i?function(a){return a==null?[]:(a=Object(a),e(i(a),function(o){return n.call(a,o)}))}:t;return Wn=s,Wn}var Vn,Tc;function I2(){if(Tc)return Vn;Tc=1;var e=nr(),t=Qa();function r(n,i){return e(n,t(n),i)}return Vn=r,Vn}var Yn,Cc;function to(){if(Cc)return Yn;Cc=1;function e(t,r){for(var n=-1,i=r.length,s=t.length;++n<i;)t[s+n]=r[n];return t}return Yn=e,Yn}var Jn,Ec;function eo(){if(Ec)return Jn;Ec=1;var e=fd(),t=e(Object.getPrototypeOf,Object);return Jn=t,Jn}var Zn,qc;function gd(){if(qc)return Zn;qc=1;var e=to(),t=eo(),r=Qa(),n=pd(),i=Object.getOwnPropertySymbols,s=i?function(a){for(var o=[];a;)e(o,r(a)),a=t(a);return o}:n;return Zn=s,Zn}var Xn,Rc;function M2(){if(Rc)return Xn;Rc=1;var e=nr(),t=gd();function r(n,i){return e(n,t(n),i)}return Xn=r,Xn}var Gn,Oc;function vd(){if(Oc)return Gn;Oc=1;var e=to(),t=K();function r(n,i,s){var a=i(n);return t(n)?a:e(a,s(n))}return Gn=r,Gn}var Qn,Ic;function yd(){if(Ic)return Qn;Ic=1;var e=vd(),t=Qa(),r=Ft();function n(i){return e(i,r,t)}return Qn=n,Qn}var ti,Mc;function P2(){if(Mc)return ti;Mc=1;var e=vd(),t=gd(),r=Ga();function n(i){return e(i,r,t)}return ti=n,ti}var ei,Pc;function L2(){if(Pc)return ei;Pc=1;var e=Nt(),t=ot(),r=e(t,"DataView");return ei=r,ei}var ri,Lc;function j2(){if(Lc)return ri;Lc=1;var e=Nt(),t=ot(),r=e(t,"Promise");return ri=r,ri}var ni,jc;function _d(){if(jc)return ni;jc=1;var e=Nt(),t=ot(),r=e(t,"Set");return ni=r,ni}var ii,Nc;function N2(){if(Nc)return ii;Nc=1;var e=Nt(),t=ot(),r=e(t,"WeakMap");return ii=r,ii}var si,Dc;function ee(){if(Dc)return si;Dc=1;var e=L2(),t=Ua(),r=j2(),n=_d(),i=N2(),s=te(),a=ad(),o="[object Map]",u="[object Object]",c="[object Promise]",l="[object Set]",h="[object WeakMap]",f="[object DataView]",d=a(e),g=a(t),p=a(r),v=a(n),y=a(i),_=s;return(e&&_(new e(new ArrayBuffer(1)))!=f||t&&_(new t)!=o||r&&_(r.resolve())!=c||n&&_(new n)!=l||i&&_(new i)!=h)&&(_=function(b){var m=s(b),S=m==u?b.constructor:void 0,w=S?a(S):"";if(w)switch(w){case d:return f;case g:return o;case p:return c;case v:return l;case y:return h}return m}),si=_,si}var ai,Fc;function D2(){if(Fc)return ai;Fc=1;var e=Object.prototype,t=e.hasOwnProperty;function r(n){var i=n.length,s=new n.constructor(i);return i&&typeof n[0]=="string"&&t.call(n,"index")&&(s.index=n.index,s.input=n.input),s}return ai=r,ai}var oi,kc;function md(){if(kc)return oi;kc=1;var e=ot(),t=e.Uint8Array;return oi=t,oi}var ui,$c;function ro(){if($c)return ui;$c=1;var e=md();function t(r){var n=new r.constructor(r.byteLength);return new e(n).set(new e(r)),n}return ui=t,ui}var ci,zc;function F2(){if(zc)return ci;zc=1;var e=ro();function t(r,n){var i=n?e(r.buffer):r.buffer;return new r.constructor(i,r.byteOffset,r.byteLength)}return ci=t,ci}var li,Bc;function k2(){if(Bc)return li;Bc=1;var e=/\w*$/;function t(r){var n=new r.constructor(r.source,e.exec(r));return n.lastIndex=r.lastIndex,n}return li=t,li}var hi,Hc;function $2(){if(Hc)return hi;Hc=1;var e=Qt(),t=e?e.prototype:void 0,r=t?t.valueOf:void 0;function n(i){return r?Object(r.call(i)):{}}return hi=n,hi}var fi,Uc;function z2(){if(Uc)return fi;Uc=1;var e=ro();function t(r,n){var i=n?e(r.buffer):r.buffer;return new r.constructor(i,r.byteOffset,r.length)}return fi=t,fi}var di,Kc;function B2(){if(Kc)return di;Kc=1;var e=ro(),t=F2(),r=k2(),n=$2(),i=z2(),s="[object Boolean]",a="[object Date]",o="[object Map]",u="[object Number]",c="[object RegExp]",l="[object Set]",h="[object String]",f="[object Symbol]",d="[object ArrayBuffer]",g="[object DataView]",p="[object Float32Array]",v="[object Float64Array]",y="[object Int8Array]",_="[object Int16Array]",b="[object Int32Array]",m="[object Uint8Array]",S="[object Uint8ClampedArray]",w="[object Uint16Array]",x="[object Uint32Array]";function R(C,F,A){var U=C.constructor;switch(F){case d:return e(C);case s:case a:return new U(+C);case g:return t(C,A);case p:case v:case y:case _:case b:case m:case S:case w:case x:return i(C,A);case o:return new U;case u:case h:return new U(C);case c:return r(C);case l:return new U;case f:return n(C)}}return di=R,di}var pi,Wc;function bd(){if(Wc)return pi;Wc=1;var e=jt(),t=Object.create,r=function(){function n(){}return function(i){if(!e(i))return{};if(t)return t(i);n.prototype=i;var s=new n;return n.prototype=void 0,s}}();return pi=r,pi}var gi,Vc;function H2(){if(Vc)return gi;Vc=1;var e=bd(),t=eo(),r=ar();function n(i){return typeof i.constructor=="function"&&!r(i)?e(t(i)):{}}return gi=n,gi}var vi,Yc;function U2(){if(Yc)return vi;Yc=1;var e=ee(),t=gt(),r="[object Map]";function n(i){return t(i)&&e(i)==r}return vi=n,vi}var yi,Jc;function K2(){if(Jc)return yi;Jc=1;var e=U2(),t=Ja(),r=Za(),n=r&&r.isMap,i=n?t(n):e;return yi=i,yi}var _i,Zc;function W2(){if(Zc)return _i;Zc=1;var e=ee(),t=gt(),r="[object Set]";function n(i){return t(i)&&e(i)==r}return _i=n,_i}var mi,Xc;function V2(){if(Xc)return mi;Xc=1;var e=W2(),t=Ja(),r=Za(),n=r&&r.isSet,i=n?t(n):e;return mi=i,mi}var bi,Gc;function Y2(){if(Gc)return bi;Gc=1;var e=Wa(),t=Va(),r=cd(),n=T2(),i=q2(),s=R2(),a=O2(),o=I2(),u=M2(),c=yd(),l=P2(),h=ee(),f=D2(),d=B2(),g=H2(),p=K(),v=Se(),y=K2(),_=jt(),b=V2(),m=Ft(),S=Ga(),w=1,x=2,R=4,C="[object Arguments]",F="[object Array]",A="[object Boolean]",U="[object Date]",W="[object Error]",kt="[object Function]",$t="[object GeneratorFunction]",dr="[object Map]",Hd="[object Number]",co="[object Object]",Ud="[object RegExp]",Kd="[object Set]",Wd="[object String]",Vd="[object Symbol]",Yd="[object WeakMap]",Jd="[object ArrayBuffer]",Zd="[object DataView]",Xd="[object Float32Array]",Gd="[object Float64Array]",Qd="[object Int8Array]",tp="[object Int16Array]",ep="[object Int32Array]",rp="[object Uint8Array]",np="[object Uint8ClampedArray]",ip="[object Uint16Array]",sp="[object Uint32Array]",k={};k[C]=k[F]=k[Jd]=k[Zd]=k[A]=k[U]=k[Xd]=k[Gd]=k[Qd]=k[tp]=k[ep]=k[dr]=k[Hd]=k[co]=k[Ud]=k[Kd]=k[Wd]=k[Vd]=k[rp]=k[np]=k[ip]=k[sp]=!0,k[W]=k[kt]=k[Yd]=!1;function Ee(N,zt,Bt,ap,qe,vt){var J,Re=zt&w,Oe=zt&x,op=zt&R;if(Bt&&(J=qe?Bt(N,ap,qe,vt):Bt(N)),J!==void 0)return J;if(!_(N))return N;var lo=p(N);if(lo){if(J=f(N),!Re)return a(N,J)}else{var Ht=h(N),ho=Ht==kt||Ht==$t;if(v(N))return s(N,Re);if(Ht==co||Ht==C||ho&&!qe){if(J=Oe||ho?{}:g(N),!Re)return Oe?u(N,i(J,N)):o(N,n(J,N))}else{if(!k[Ht])return qe?N:{};J=d(N,Ht,Re)}}vt||(vt=new e);var fo=vt.get(N);if(fo)return fo;vt.set(N,J),b(N)?N.forEach(function(yt){J.add(Ee(yt,zt,Bt,yt,N,vt))}):y(N)&&N.forEach(function(yt,At){J.set(At,Ee(yt,zt,Bt,At,N,vt))});var up=op?Oe?l:c:Oe?S:m,po=lo?void 0:up(N);return t(po||N,function(yt,At){po&&(At=yt,yt=N[At]),r(J,At,Ee(yt,zt,Bt,At,N,vt))}),J}return bi=Ee,bi}var wi,Qc;function J2(){if(Qc)return wi;Qc=1;var e=Y2(),t=4;function r(n){return e(n,t)}return wi=r,wi}var xi,tl;function wd(){if(tl)return xi;tl=1;function e(t){return function(){return t}}return xi=e,xi}var Ai,el;function Z2(){if(el)return Ai;el=1;function e(t){return function(r,n,i){for(var s=-1,a=Object(r),o=i(r),u=o.length;u--;){var c=o[t?u:++s];if(n(a[c],c,a)===!1)break}return r}}return Ai=e,Ai}var Si,rl;function X2(){if(rl)return Si;rl=1;var e=Z2(),t=e();return Si=t,Si}var Ti,nl;function xd(){if(nl)return Ti;nl=1;var e=X2(),t=Ft();function r(n,i){return n&&e(n,i,t)}return Ti=r,Ti}var Ci,il;function G2(){if(il)return Ci;il=1;var e=Dt();function t(r,n){return function(i,s){if(i==null)return i;if(!e(i))return r(i,s);for(var a=i.length,o=n?a:-1,u=Object(i);(n?o--:++o<a)&&s(u[o],o,u)!==!1;);return i}}return Ci=t,Ci}var Ei,sl;function or(){if(sl)return Ei;sl=1;var e=xd(),t=G2(),r=t(e);return Ei=r,Ei}var qi,al;function ur(){if(al)return qi;al=1;function e(t){return t}return qi=e,qi}var Ri,ol;function Q2(){if(ol)return Ri;ol=1;var e=ur();function t(r){return typeof r=="function"?r:e}return Ri=t,Ri}var Oi,ul;function tm(){if(ul)return Oi;ul=1;var e=Va(),t=or(),r=Q2(),n=K();function i(s,a){var o=n(s)?e:t;return o(s,r(a))}return Oi=i,Oi}var Ii,cl;function em(){return cl||(cl=1,Ii=tm()),Ii}var Mi,ll;function rm(){if(ll)return Mi;ll=1;var e=or();function t(r,n){var i=[];return e(r,function(s,a,o){n(s,a,o)&&i.push(s)}),i}return Mi=t,Mi}var Pi,hl;function nm(){if(hl)return Pi;hl=1;var e="__lodash_hash_undefined__";function t(r){return this.__data__.set(r,e),this}return Pi=t,Pi}var Li,fl;function im(){if(fl)return Li;fl=1;function e(t){return this.__data__.has(t)}return Li=e,Li}var ji,dl;function Ad(){if(dl)return ji;dl=1;var e=Ka(),t=nm(),r=im();function n(i){var s=-1,a=i==null?0:i.length;for(this.__data__=new e;++s<a;)this.add(i[s])}return n.prototype.add=n.prototype.push=t,n.prototype.has=r,ji=n,ji}var Ni,pl;function sm(){if(pl)return Ni;pl=1;function e(t,r){for(var n=-1,i=t==null?0:t.length;++n<i;)if(r(t[n],n,t))return!0;return!1}return Ni=e,Ni}var Di,gl;function Sd(){if(gl)return Di;gl=1;function e(t,r){return t.has(r)}return Di=e,Di}var Fi,vl;function Td(){if(vl)return Fi;vl=1;var e=Ad(),t=sm(),r=Sd(),n=1,i=2;function s(a,o,u,c,l,h){var f=u&n,d=a.length,g=o.length;if(d!=g&&!(f&&g>d))return!1;var p=h.get(a),v=h.get(o);if(p&&v)return p==o&&v==a;var y=-1,_=!0,b=u&i?new e:void 0;for(h.set(a,o),h.set(o,a);++y<d;){var m=a[y],S=o[y];if(c)var w=f?c(S,m,y,o,a,h):c(m,S,y,a,o,h);if(w!==void 0){if(w)continue;_=!1;break}if(b){if(!t(o,function(x,R){if(!r(b,R)&&(m===x||l(m,x,u,c,h)))return b.push(R)})){_=!1;break}}else if(!(m===S||l(m,S,u,c,h))){_=!1;break}}return h.delete(a),h.delete(o),_}return Fi=s,Fi}var ki,yl;function am(){if(yl)return ki;yl=1;function e(t){var r=-1,n=Array(t.size);return t.forEach(function(i,s){n[++r]=[s,i]}),n}return ki=e,ki}var $i,_l;function no(){if(_l)return $i;_l=1;function e(t){var r=-1,n=Array(t.size);return t.forEach(function(i){n[++r]=i}),n}return $i=e,$i}var zi,ml;function om(){if(ml)return zi;ml=1;var e=Qt(),t=md(),r=Ha(),n=Td(),i=am(),s=no(),a=1,o=2,u="[object Boolean]",c="[object Date]",l="[object Error]",h="[object Map]",f="[object Number]",d="[object RegExp]",g="[object Set]",p="[object String]",v="[object Symbol]",y="[object ArrayBuffer]",_="[object DataView]",b=e?e.prototype:void 0,m=b?b.valueOf:void 0;function S(w,x,R,C,F,A,U){switch(R){case _:if(w.byteLength!=x.byteLength||w.byteOffset!=x.byteOffset)return!1;w=w.buffer,x=x.buffer;case y:return!(w.byteLength!=x.byteLength||!A(new t(w),new t(x)));case u:case c:case f:return r(+w,+x);case l:return w.name==x.name&&w.message==x.message;case d:case p:return w==x+"";case h:var W=i;case g:var kt=C&a;if(W||(W=s),w.size!=x.size&&!kt)return!1;var $t=U.get(w);if($t)return $t==x;C|=o,U.set(w,x);var dr=n(W(w),W(x),C,F,A,U);return U.delete(w),dr;case v:if(m)return m.call(w)==m.call(x)}return!1}return zi=S,zi}var Bi,bl;function um(){if(bl)return Bi;bl=1;var e=yd(),t=1,r=Object.prototype,n=r.hasOwnProperty;function i(s,a,o,u,c,l){var h=o&t,f=e(s),d=f.length,g=e(a),p=g.length;if(d!=p&&!h)return!1;for(var v=d;v--;){var y=f[v];if(!(h?y in a:n.call(a,y)))return!1}var _=l.get(s),b=l.get(a);if(_&&b)return _==a&&b==s;var m=!0;l.set(s,a),l.set(a,s);for(var S=h;++v<d;){y=f[v];var w=s[y],x=a[y];if(u)var R=h?u(x,w,y,a,s,l):u(w,x,y,s,a,l);if(!(R===void 0?w===x||c(w,x,o,u,l):R)){m=!1;break}S||(S=y=="constructor")}if(m&&!S){var C=s.constructor,F=a.constructor;C!=F&&"constructor"in s&&"constructor"in a&&!(typeof C=="function"&&C instanceof C&&typeof F=="function"&&F instanceof F)&&(m=!1)}return l.delete(s),l.delete(a),m}return Bi=i,Bi}var Hi,wl;function cm(){if(wl)return Hi;wl=1;var e=Wa(),t=Td(),r=om(),n=um(),i=ee(),s=K(),a=Se(),o=sr(),u=1,c="[object Arguments]",l="[object Array]",h="[object Object]",f=Object.prototype,d=f.hasOwnProperty;function g(p,v,y,_,b,m){var S=s(p),w=s(v),x=S?l:i(p),R=w?l:i(v);x=x==c?h:x,R=R==c?h:R;var C=x==h,F=R==h,A=x==R;if(A&&a(p)){if(!a(v))return!1;S=!0,C=!1}if(A&&!C)return m||(m=new e),S||o(p)?t(p,v,y,_,b,m):r(p,v,x,y,_,b,m);if(!(y&u)){var U=C&&d.call(p,"__wrapped__"),W=F&&d.call(v,"__wrapped__");if(U||W){var kt=U?p.value():p,$t=W?v.value():v;return m||(m=new e),b(kt,$t,y,_,m)}}return A?(m||(m=new e),n(p,v,y,_,b,m)):!1}return Hi=g,Hi}var Ui,xl;function Cd(){if(xl)return Ui;xl=1;var e=cm(),t=gt();function r(n,i,s,a,o){return n===i?!0:n==null||i==null||!t(n)&&!t(i)?n!==n&&i!==i:e(n,i,s,a,r,o)}return Ui=r,Ui}var Ki,Al;function lm(){if(Al)return Ki;Al=1;var e=Wa(),t=Cd(),r=1,n=2;function i(s,a,o,u){var c=o.length,l=c,h=!u;if(s==null)return!l;for(s=Object(s);c--;){var f=o[c];if(h&&f[2]?f[1]!==s[f[0]]:!(f[0]in s))return!1}for(;++c<l;){f=o[c];var d=f[0],g=s[d],p=f[1];if(h&&f[2]){if(g===void 0&&!(d in s))return!1}else{var v=new e;if(u)var y=u(g,p,d,s,a,v);if(!(y===void 0?t(p,g,r|n,u,v):y))return!1}}return!0}return Ki=i,Ki}var Wi,Sl;function Ed(){if(Sl)return Wi;Sl=1;var e=jt();function t(r){return r===r&&!e(r)}return Wi=t,Wi}var Vi,Tl;function hm(){if(Tl)return Vi;Tl=1;var e=Ed(),t=Ft();function r(n){for(var i=t(n),s=i.length;s--;){var a=i[s],o=n[a];i[s]=[a,o,e(o)]}return i}return Vi=r,Vi}var Yi,Cl;function qd(){if(Cl)return Yi;Cl=1;function e(t,r){return function(n){return n==null?!1:n[t]===r&&(r!==void 0||t in Object(n))}}return Yi=e,Yi}var Ji,El;function fm(){if(El)return Ji;El=1;var e=lm(),t=hm(),r=qd();function n(i){var s=t(i);return s.length==1&&s[0][2]?r(s[0][0],s[0][1]):function(a){return a===i||e(a,i,s)}}return Ji=n,Ji}var Zi,ql;function io(){if(ql)return Zi;ql=1;var e=te(),t=gt(),r="[object Symbol]";function n(i){return typeof i=="symbol"||t(i)&&e(i)==r}return Zi=n,Zi}var Xi,Rl;function so(){if(Rl)return Xi;Rl=1;var e=K(),t=io(),r=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,n=/^\w*$/;function i(s,a){if(e(s))return!1;var o=typeof s;return o=="number"||o=="symbol"||o=="boolean"||s==null||t(s)?!0:n.test(s)||!r.test(s)||a!=null&&s in Object(a)}return Xi=i,Xi}var Gi,Ol;function dm(){if(Ol)return Gi;Ol=1;var e=Ka(),t="Expected a function";function r(n,i){if(typeof n!="function"||i!=null&&typeof i!="function")throw new TypeError(t);var s=function(){var a=arguments,o=i?i.apply(this,a):a[0],u=s.cache;if(u.has(o))return u.get(o);var c=n.apply(this,a);return s.cache=u.set(o,c)||u,c};return s.cache=new(r.Cache||e),s}return r.Cache=e,Gi=r,Gi}var Qi,Il;function pm(){if(Il)return Qi;Il=1;var e=dm(),t=500;function r(n){var i=e(n,function(a){return s.size===t&&s.clear(),a}),s=i.cache;return i}return Qi=r,Qi}var ts,Ml;function gm(){if(Ml)return ts;Ml=1;var e=pm(),t=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,r=/\\(\\)?/g,n=e(function(i){var s=[];return i.charCodeAt(0)===46&&s.push(""),i.replace(t,function(a,o,u,c){s.push(u?c.replace(r,"$1"):o||a)}),s});return ts=n,ts}var es,Pl;function ao(){if(Pl)return es;Pl=1;function e(t,r){for(var n=-1,i=t==null?0:t.length,s=Array(i);++n<i;)s[n]=r(t[n],n,t);return s}return es=e,es}var rs,Ll;function vm(){if(Ll)return rs;Ll=1;var e=Qt(),t=ao(),r=K(),n=io(),i=e?e.prototype:void 0,s=i?i.toString:void 0;function a(o){if(typeof o=="string")return o;if(r(o))return t(o,a)+"";if(n(o))return s?s.call(o):"";var u=o+"";return u=="0"&&1/o==-1/0?"-0":u}return rs=a,rs}var ns,jl;function ym(){if(jl)return ns;jl=1;var e=vm();function t(r){return r==null?"":e(r)}return ns=t,ns}var is,Nl;function Rd(){if(Nl)return is;Nl=1;var e=K(),t=so(),r=gm(),n=ym();function i(s,a){return e(s)?s:t(s,a)?[s]:r(n(s))}return is=i,is}var ss,Dl;function cr(){if(Dl)return ss;Dl=1;var e=io();function t(r){if(typeof r=="string"||e(r))return r;var n=r+"";return n=="0"&&1/r==-1/0?"-0":n}return ss=t,ss}var as,Fl;function Od(){if(Fl)return as;Fl=1;var e=Rd(),t=cr();function r(n,i){i=e(i,n);for(var s=0,a=i.length;n!=null&&s<a;)n=n[t(i[s++])];return s&&s==a?n:void 0}return as=r,as}var os,kl;function _m(){if(kl)return os;kl=1;var e=Od();function t(r,n,i){var s=r==null?void 0:e(r,n);return s===void 0?i:s}return os=t,os}var us,$l;function mm(){if($l)return us;$l=1;function e(t,r){return t!=null&&r in Object(t)}return us=e,us}var cs,zl;function Id(){if(zl)return cs;zl=1;var e=Rd(),t=ir(),r=K(),n=ld(),i=Ya(),s=cr();function a(o,u,c){u=e(u,o);for(var l=-1,h=u.length,f=!1;++l<h;){var d=s(u[l]);if(!(f=o!=null&&c(o,d)))break;o=o[d]}return f||++l!=h?f:(h=o==null?0:o.length,!!h&&i(h)&&n(d,h)&&(r(o)||t(o)))}return cs=a,cs}var ls,Bl;function bm(){if(Bl)return ls;Bl=1;var e=mm(),t=Id();function r(n,i){return n!=null&&t(n,i,e)}return ls=r,ls}var hs,Hl;function wm(){if(Hl)return hs;Hl=1;var e=Cd(),t=_m(),r=bm(),n=so(),i=Ed(),s=qd(),a=cr(),o=1,u=2;function c(l,h){return n(l)&&i(h)?s(a(l),h):function(f){var d=t(f,l);return d===void 0&&d===h?r(f,l):e(h,d,o|u)}}return hs=c,hs}var fs,Ul;function Md(){if(Ul)return fs;Ul=1;function e(t){return function(r){return r==null?void 0:r[t]}}return fs=e,fs}var ds,Kl;function xm(){if(Kl)return ds;Kl=1;var e=Od();function t(r){return function(n){return e(n,r)}}return ds=t,ds}var ps,Wl;function Am(){if(Wl)return ps;Wl=1;var e=Md(),t=xm(),r=so(),n=cr();function i(s){return r(s)?e(n(s)):t(s)}return ps=i,ps}var gs,Vl;function lr(){if(Vl)return gs;Vl=1;var e=fm(),t=wm(),r=ur(),n=K(),i=Am();function s(a){return typeof a=="function"?a:a==null?r:typeof a=="object"?n(a)?t(a[0],a[1]):e(a):i(a)}return gs=s,gs}var vs,Yl;function Sm(){if(Yl)return vs;Yl=1;var e=dd(),t=rm(),r=lr(),n=K();function i(s,a){var o=n(s)?e:t;return o(s,r(a,3))}return vs=i,vs}var ys,Jl;function Tm(){if(Jl)return ys;Jl=1;var e=Object.prototype,t=e.hasOwnProperty;function r(n,i){return n!=null&&t.call(n,i)}return ys=r,ys}var _s,Zl;function Cm(){if(Zl)return _s;Zl=1;var e=Tm(),t=Id();function r(n,i){return n!=null&&t(n,i,e)}return _s=r,_s}var ms,Xl;function Em(){if(Xl)return ms;Xl=1;var e=Xa(),t=ee(),r=ir(),n=K(),i=Dt(),s=Se(),a=ar(),o=sr(),u="[object Map]",c="[object Set]",l=Object.prototype,h=l.hasOwnProperty;function f(d){if(d==null)return!0;if(i(d)&&(n(d)||typeof d=="string"||typeof d.splice=="function"||s(d)||o(d)||r(d)))return!d.length;var g=t(d);if(g==u||g==c)return!d.size;if(a(d))return!e(d).length;for(var p in d)if(h.call(d,p))return!1;return!0}return ms=f,ms}var bs,Gl;function qm(){if(Gl)return bs;Gl=1;function e(t){return t===void 0}return bs=e,bs}var ws,Ql;function Rm(){if(Ql)return ws;Ql=1;var e=or(),t=Dt();function r(n,i){var s=-1,a=t(n)?Array(n.length):[];return e(n,function(o,u,c){a[++s]=i(o,u,c)}),a}return ws=r,ws}var xs,th;function Om(){if(th)return xs;th=1;var e=ao(),t=lr(),r=Rm(),n=K();function i(s,a){var o=n(s)?e:r;return o(s,t(a,3))}return xs=i,xs}var As,eh;function Im(){if(eh)return As;eh=1;function e(t,r,n,i){var s=-1,a=t==null?0:t.length;for(i&&a&&(n=t[++s]);++s<a;)n=r(n,t[s],s,t);return n}return As=e,As}var Ss,rh;function Mm(){if(rh)return Ss;rh=1;function e(t,r,n,i,s){return s(t,function(a,o,u){n=i?(i=!1,a):r(n,a,o,u)}),n}return Ss=e,Ss}var Ts,nh;function Pm(){if(nh)return Ts;nh=1;var e=Im(),t=or(),r=lr(),n=Mm(),i=K();function s(a,o,u){var c=i(a)?e:n,l=arguments.length<3;return c(a,r(o,4),u,l,t)}return Ts=s,Ts}var Cs,ih;function Lm(){if(ih)return Cs;ih=1;var e=te(),t=K(),r=gt(),n="[object String]";function i(s){return typeof s=="string"||!t(s)&&r(s)&&e(s)==n}return Cs=i,Cs}var Es,sh;function jm(){if(sh)return Es;sh=1;var e=Md(),t=e("length");return Es=t,Es}var qs,ah;function Nm(){if(ah)return qs;ah=1;var e="\\ud800-\\udfff",t="\\u0300-\\u036f",r="\\ufe20-\\ufe2f",n="\\u20d0-\\u20ff",i=t+r+n,s="\\ufe0e\\ufe0f",a="\\u200d",o=RegExp("["+a+e+i+s+"]");function u(c){return o.test(c)}return qs=u,qs}var Rs,oh;function Dm(){if(oh)return Rs;oh=1;var e="\\ud800-\\udfff",t="\\u0300-\\u036f",r="\\ufe20-\\ufe2f",n="\\u20d0-\\u20ff",i=t+r+n,s="\\ufe0e\\ufe0f",a="["+e+"]",o="["+i+"]",u="\\ud83c[\\udffb-\\udfff]",c="(?:"+o+"|"+u+")",l="[^"+e+"]",h="(?:\\ud83c[\\udde6-\\uddff]){2}",f="[\\ud800-\\udbff][\\udc00-\\udfff]",d="\\u200d",g=c+"?",p="["+s+"]?",v="(?:"+d+"(?:"+[l,h,f].join("|")+")"+p+g+")*",y=p+g+v,_="(?:"+[l+o+"?",o,h,f,a].join("|")+")",b=RegExp(u+"(?="+u+")|"+_+y,"g");function m(S){for(var w=b.lastIndex=0;b.test(S);)++w;return w}return Rs=m,Rs}var Os,uh;function Fm(){if(uh)return Os;uh=1;var e=jm(),t=Nm(),r=Dm();function n(i){return t(i)?r(i):e(i)}return Os=n,Os}var Is,ch;function km(){if(ch)return Is;ch=1;var e=Xa(),t=ee(),r=Dt(),n=Lm(),i=Fm(),s="[object Map]",a="[object Set]";function o(u){if(u==null)return 0;if(r(u))return n(u)?i(u):u.length;var c=t(u);return c==s||c==a?u.size:e(u).length}return Is=o,Is}var Ms,lh;function $m(){if(lh)return Ms;lh=1;var e=Va(),t=bd(),r=xd(),n=lr(),i=eo(),s=K(),a=Se(),o=tr(),u=jt(),c=sr();function l(h,f,d){var g=s(h),p=g||a(h)||c(h);if(f=n(f,4),d==null){var v=h&&h.constructor;p?d=g?new v:[]:u(h)?d=o(v)?t(i(h)):{}:d={}}return(p?e:r)(h,function(y,_,b){return f(d,y,_,b)}),d}return Ms=l,Ms}var Ps,hh;function zm(){if(hh)return Ps;hh=1;var e=Qt(),t=ir(),r=K(),n=e?e.isConcatSpreadable:void 0;function i(s){return r(s)||t(s)||!!(n&&s&&s[n])}return Ps=i,Ps}var Ls,fh;function Bm(){if(fh)return Ls;fh=1;var e=to(),t=zm();function r(n,i,s,a,o){var u=-1,c=n.length;for(s||(s=t),o||(o=[]);++u<c;){var l=n[u];i>0&&s(l)?i>1?r(l,i-1,s,a,o):e(o,l):a||(o[o.length]=l)}return o}return Ls=r,Ls}var js,dh;function Hm(){if(dh)return js;dh=1;function e(t,r,n){switch(n.length){case 0:return t.call(r);case 1:return t.call(r,n[0]);case 2:return t.call(r,n[0],n[1]);case 3:return t.call(r,n[0],n[1],n[2])}return t.apply(r,n)}return js=e,js}var Ns,ph;function Um(){if(ph)return Ns;ph=1;var e=Hm(),t=Math.max;function r(n,i,s){return i=t(i===void 0?n.length-1:i,0),function(){for(var a=arguments,o=-1,u=t(a.length-i,0),c=Array(u);++o<u;)c[o]=a[i+o];o=-1;for(var l=Array(i+1);++o<i;)l[o]=a[o];return l[i]=s(c),e(n,this,l)}}return Ns=r,Ns}var Ds,gh;function Km(){if(gh)return Ds;gh=1;var e=wd(),t=od(),r=ur(),n=t?function(i,s){return t(i,"toString",{configurable:!0,enumerable:!1,value:e(s),writable:!0})}:r;return Ds=n,Ds}var Fs,vh;function Wm(){if(vh)return Fs;vh=1;var e=800,t=16,r=Date.now;function n(i){var s=0,a=0;return function(){var o=r(),u=t-(o-a);if(a=o,u>0){if(++s>=e)return arguments[0]}else s=0;return i.apply(void 0,arguments)}}return Fs=n,Fs}var ks,yh;function Vm(){if(yh)return ks;yh=1;var e=Km(),t=Wm(),r=t(e);return ks=r,ks}var $s,_h;function Ym(){if(_h)return $s;_h=1;var e=ur(),t=Um(),r=Vm();function n(i,s){return r(t(i,s,e),i+"")}return $s=n,$s}var zs,mh;function Jm(){if(mh)return zs;mh=1;function e(t,r,n,i){for(var s=t.length,a=n+(i?1:-1);i?a--:++a<s;)if(r(t[a],a,t))return a;return-1}return zs=e,zs}var Bs,bh;function Zm(){if(bh)return Bs;bh=1;function e(t){return t!==t}return Bs=e,Bs}var Hs,wh;function Xm(){if(wh)return Hs;wh=1;function e(t,r,n){for(var i=n-1,s=t.length;++i<s;)if(t[i]===r)return i;return-1}return Hs=e,Hs}var Us,xh;function Gm(){if(xh)return Us;xh=1;var e=Jm(),t=Zm(),r=Xm();function n(i,s,a){return s===s?r(i,s,a):e(i,t,a)}return Us=n,Us}var Ks,Ah;function Qm(){if(Ah)return Ks;Ah=1;var e=Gm();function t(r,n){var i=r==null?0:r.length;return!!i&&e(r,n,0)>-1}return Ks=t,Ks}var Ws,Sh;function tb(){if(Sh)return Ws;Sh=1;function e(t,r,n){for(var i=-1,s=t==null?0:t.length;++i<s;)if(n(r,t[i]))return!0;return!1}return Ws=e,Ws}var Vs,Th;function eb(){if(Th)return Vs;Th=1;function e(){}return Vs=e,Vs}var Ys,Ch;function rb(){if(Ch)return Ys;Ch=1;var e=_d(),t=eb(),r=no(),n=1/0,i=e&&1/r(new e([,-0]))[1]==n?function(s){return new e(s)}:t;return Ys=i,Ys}var Js,Eh;function nb(){if(Eh)return Js;Eh=1;var e=Ad(),t=Qm(),r=tb(),n=Sd(),i=rb(),s=no(),a=200;function o(u,c,l){var h=-1,f=t,d=u.length,g=!0,p=[],v=p;if(l)g=!1,f=r;else if(d>=a){var y=c?null:i(u);if(y)return s(y);g=!1,f=n,v=new e}else v=c?[]:p;t:for(;++h<d;){var _=u[h],b=c?c(_):_;if(_=l||_!==0?_:0,g&&b===b){for(var m=v.length;m--;)if(v[m]===b)continue t;c&&v.push(b),p.push(_)}else f(v,b,l)||(v!==p&&v.push(b),p.push(_))}return p}return Js=o,Js}var Zs,qh;function ib(){if(qh)return Zs;qh=1;var e=Dt(),t=gt();function r(n){return t(n)&&e(n)}return Zs=r,Zs}var Xs,Rh;function sb(){if(Rh)return Xs;Rh=1;var e=Bm(),t=Ym(),r=nb(),n=ib(),i=t(function(s){return r(e(s,1,n,!0))});return Xs=i,Xs}var Gs,Oh;function ab(){if(Oh)return Gs;Oh=1;var e=ao();function t(r,n){return e(n,function(i){return r[i]})}return Gs=t,Gs}var Qs,Ih;function ob(){if(Ih)return Qs;Ih=1;var e=ab(),t=Ft();function r(n){return n==null?[]:e(n,t(n))}return Qs=r,Qs}var Be;if(typeof K_=="function")try{Be={clone:J2(),constant:wd(),each:em(),filter:Sm(),has:Cm(),isArray:K(),isEmpty:Em(),isFunction:tr(),isUndefined:qm(),keys:Ft(),map:Om(),reduce:Pm(),size:km(),transform:$m(),union:sb(),values:ob()}}catch{}Be||(Be=window._);var it=Be,q=it,Pd=I,ub="\0",It="\0",Mh="";function I(e){this._isDirected=q.has(e,"directed")?e.directed:!0,this._isMultigraph=q.has(e,"multigraph")?e.multigraph:!1,this._isCompound=q.has(e,"compound")?e.compound:!1,this._label=void 0,this._defaultNodeLabelFn=q.constant(void 0),this._defaultEdgeLabelFn=q.constant(void 0),this._nodes={},this._isCompound&&(this._parent={},this._children={},this._children[It]={}),this._in={},this._preds={},this._out={},this._sucs={},this._edgeObjs={},this._edgeLabels={}}I.prototype._nodeCount=0;I.prototype._edgeCount=0;I.prototype.isDirected=function(){return this._isDirected};I.prototype.isMultigraph=function(){return this._isMultigraph};I.prototype.isCompound=function(){return this._isCompound};I.prototype.setGraph=function(e){return this._label=e,this};I.prototype.graph=function(){return this._label};I.prototype.setDefaultNodeLabel=function(e){return q.isFunction(e)||(e=q.constant(e)),this._defaultNodeLabelFn=e,this};I.prototype.nodeCount=function(){return this._nodeCount};I.prototype.nodes=function(){return q.keys(this._nodes)};I.prototype.sources=function(){var e=this;return q.filter(this.nodes(),function(t){return q.isEmpty(e._in[t])})};I.prototype.sinks=function(){var e=this;return q.filter(this.nodes(),function(t){return q.isEmpty(e._out[t])})};I.prototype.setNodes=function(e,t){var r=arguments,n=this;return q.each(e,function(i){r.length>1?n.setNode(i,t):n.setNode(i)}),this};I.prototype.setNode=function(e,t){return q.has(this._nodes,e)?(arguments.length>1&&(this._nodes[e]=t),this):(this._nodes[e]=arguments.length>1?t:this._defaultNodeLabelFn(e),this._isCompound&&(this._parent[e]=It,this._children[e]={},this._children[It][e]=!0),this._in[e]={},this._preds[e]={},this._out[e]={},this._sucs[e]={},++this._nodeCount,this)};I.prototype.node=function(e){return this._nodes[e]};I.prototype.hasNode=function(e){return q.has(this._nodes,e)};I.prototype.removeNode=function(e){var t=this;if(q.has(this._nodes,e)){var r=function(n){t.removeEdge(t._edgeObjs[n])};delete this._nodes[e],this._isCompound&&(this._removeFromParentsChildList(e),delete this._parent[e],q.each(this.children(e),function(n){t.setParent(n)}),delete this._children[e]),q.each(q.keys(this._in[e]),r),delete this._in[e],delete this._preds[e],q.each(q.keys(this._out[e]),r),delete this._out[e],delete this._sucs[e],--this._nodeCount}return this};I.prototype.setParent=function(e,t){if(!this._isCompound)throw new Error("Cannot set parent in a non-compound graph");if(q.isUndefined(t))t=It;else{t+="";for(var r=t;!q.isUndefined(r);r=this.parent(r))if(r===e)throw new Error("Setting "+t+" as parent of "+e+" would create a cycle");this.setNode(t)}return this.setNode(e),this._removeFromParentsChildList(e),this._parent[e]=t,this._children[t][e]=!0,this};I.prototype._removeFromParentsChildList=function(e){delete this._children[this._parent[e]][e]};I.prototype.parent=function(e){if(this._isCompound){var t=this._parent[e];if(t!==It)return t}};I.prototype.children=function(e){if(q.isUndefined(e)&&(e=It),this._isCompound){var t=this._children[e];if(t)return q.keys(t)}else{if(e===It)return this.nodes();if(this.hasNode(e))return[]}};I.prototype.predecessors=function(e){var t=this._preds[e];if(t)return q.keys(t)};I.prototype.successors=function(e){var t=this._sucs[e];if(t)return q.keys(t)};I.prototype.neighbors=function(e){var t=this.predecessors(e);if(t)return q.union(t,this.successors(e))};I.prototype.isLeaf=function(e){var t;return this.isDirected()?t=this.successors(e):t=this.neighbors(e),t.length===0};I.prototype.filterNodes=function(e){var t=new this.constructor({directed:this._isDirected,multigraph:this._isMultigraph,compound:this._isCompound});t.setGraph(this.graph());var r=this;q.each(this._nodes,function(s,a){e(a)&&t.setNode(a,s)}),q.each(this._edgeObjs,function(s){t.hasNode(s.v)&&t.hasNode(s.w)&&t.setEdge(s,r.edge(s))});var n={};function i(s){var a=r.parent(s);return a===void 0||t.hasNode(a)?(n[s]=a,a):a in n?n[a]:i(a)}return this._isCompound&&q.each(t.nodes(),function(s){t.setParent(s,i(s))}),t};I.prototype.setDefaultEdgeLabel=function(e){return q.isFunction(e)||(e=q.constant(e)),this._defaultEdgeLabelFn=e,this};I.prototype.edgeCount=function(){return this._edgeCount};I.prototype.edges=function(){return q.values(this._edgeObjs)};I.prototype.setPath=function(e,t){var r=this,n=arguments;return q.reduce(e,function(i,s){return n.length>1?r.setEdge(i,s,t):r.setEdge(i,s),s}),this};I.prototype.setEdge=function(){var e,t,r,n,i=!1,s=arguments[0];typeof s=="object"&&s!==null&&"v"in s?(e=s.v,t=s.w,r=s.name,arguments.length===2&&(n=arguments[1],i=!0)):(e=s,t=arguments[1],r=arguments[3],arguments.length>2&&(n=arguments[2],i=!0)),e=""+e,t=""+t,q.isUndefined(r)||(r=""+r);var a=Te(this._isDirected,e,t,r);if(q.has(this._edgeLabels,a))return i&&(this._edgeLabels[a]=n),this;if(!q.isUndefined(r)&&!this._isMultigraph)throw new Error("Cannot set a named edge when isMultigraph = false");this.setNode(e),this.setNode(t),this._edgeLabels[a]=i?n:this._defaultEdgeLabelFn(e,t,r);var o=cb(this._isDirected,e,t,r);return e=o.v,t=o.w,Object.freeze(o),this._edgeObjs[a]=o,Ph(this._preds[t],e),Ph(this._sucs[e],t),this._in[t][a]=o,this._out[e][a]=o,this._edgeCount++,this};I.prototype.edge=function(e,t,r){var n=arguments.length===1?oo(this._isDirected,arguments[0]):Te(this._isDirected,e,t,r);return this._edgeLabels[n]};I.prototype.hasEdge=function(e,t,r){var n=arguments.length===1?oo(this._isDirected,arguments[0]):Te(this._isDirected,e,t,r);return q.has(this._edgeLabels,n)};I.prototype.removeEdge=function(e,t,r){var n=arguments.length===1?oo(this._isDirected,arguments[0]):Te(this._isDirected,e,t,r),i=this._edgeObjs[n];return i&&(e=i.v,t=i.w,delete this._edgeLabels[n],delete this._edgeObjs[n],Lh(this._preds[t],e),Lh(this._sucs[e],t),delete this._in[t][n],delete this._out[e][n],this._edgeCount--),this};I.prototype.inEdges=function(e,t){var r=this._in[e];if(r){var n=q.values(r);return t?q.filter(n,function(i){return i.v===t}):n}};I.prototype.outEdges=function(e,t){var r=this._out[e];if(r){var n=q.values(r);return t?q.filter(n,function(i){return i.w===t}):n}};I.prototype.nodeEdges=function(e,t){var r=this.inEdges(e,t);if(r)return r.concat(this.outEdges(e,t))};function Ph(e,t){e[t]?e[t]++:e[t]=1}function Lh(e,t){--e[t]||delete e[t]}function Te(e,t,r,n){var i=""+t,s=""+r;if(!e&&i>s){var a=i;i=s,s=a}return i+Mh+s+Mh+(q.isUndefined(n)?ub:n)}function cb(e,t,r,n){var i=""+t,s=""+r;if(!e&&i>s){var a=i;i=s,s=a}var o={v:i,w:s};return n&&(o.name=n),o}function oo(e,t){return Te(e,t.v,t.w,t.name)}var lb={Graph:Pd},Le=it,hb=fb;function fb(e){var t={},r=[],n;function i(s){Le.has(t,s)||(t[s]=!0,n.push(s),Le.each(e.successors(s),i),Le.each(e.predecessors(s),i))}return Le.each(e.nodes(),function(s){n=[],i(s),n.length&&r.push(n)}),r}var Ld=it,jd=rt;function rt(){this._arr=[],this._keyIndices={}}rt.prototype.size=function(){return this._arr.length};rt.prototype.keys=function(){return this._arr.map(function(e){return e.key})};rt.prototype.has=function(e){return Ld.has(this._keyIndices,e)};rt.prototype.priority=function(e){var t=this._keyIndices[e];if(t!==void 0)return this._arr[t].priority};rt.prototype.min=function(){if(this.size()===0)throw new Error("Queue underflow");return this._arr[0].key};rt.prototype.add=function(e,t){var r=this._keyIndices;if(e=String(e),!Ld.has(r,e)){var n=this._arr,i=n.length;return r[e]=i,n.push({key:e,priority:t}),this._decrease(i),!0}return!1};rt.prototype.removeMin=function(){this._swap(0,this._arr.length-1);var e=this._arr.pop();return delete this._keyIndices[e.key],this._heapify(0),e.key};rt.prototype.decrease=function(e,t){var r=this._keyIndices[e];if(t>this._arr[r].priority)throw new Error("New priority is greater than current priority. Key: "+e+" Old: "+this._arr[r].priority+" New: "+t);this._arr[r].priority=t,this._decrease(r)};rt.prototype._heapify=function(e){var t=this._arr,r=2*e,n=r+1,i=e;r<t.length&&(i=t[r].priority<t[i].priority?r:i,n<t.length&&(i=t[n].priority<t[i].priority?n:i),i!==e&&(this._swap(e,i),this._heapify(i)))};rt.prototype._decrease=function(e){for(var t=this._arr,r=t[e].priority,n;e!==0&&(n=e>>1,!(t[n].priority<r));)this._swap(e,n),e=n};rt.prototype._swap=function(e,t){var r=this._arr,n=this._keyIndices,i=r[e],s=r[t];r[e]=s,r[t]=i,n[s.key]=e,n[i.key]=t};var db=it,pb=jd,Nd=vb,gb=db.constant(1);function vb(e,t,r,n){return yb(e,String(t),r||gb,n||function(i){return e.outEdges(i)})}function yb(e,t,r,n){var i={},s=new pb,a,o,u=function(c){var l=c.v!==a?c.v:c.w,h=i[l],f=r(c),d=o.distance+f;if(f<0)throw new Error("dijkstra does not allow negative edge weights. Bad edge: "+c+" Weight: "+f);d<h.distance&&(h.distance=d,h.predecessor=a,s.decrease(l,d))};for(e.nodes().forEach(function(c){var l=c===t?0:Number.POSITIVE_INFINITY;i[c]={distance:l},s.add(c,l)});s.size()>0&&(a=s.removeMin(),o=i[a],o.distance!==Number.POSITIVE_INFINITY);)n(a).forEach(u);return i}var _b=Nd,mb=it,bb=wb;function wb(e,t,r){return mb.transform(e.nodes(),function(n,i){n[i]=_b(e,i,t,r)},{})}var jh=it,Dd=xb;function xb(e){var t=0,r=[],n={},i=[];function s(a){var o=n[a]={onStack:!0,lowlink:t,index:t++};if(r.push(a),e.successors(a).forEach(function(l){jh.has(n,l)?n[l].onStack&&(o.lowlink=Math.min(o.lowlink,n[l].index)):(s(l),o.lowlink=Math.min(o.lowlink,n[l].lowlink))}),o.lowlink===o.index){var u=[],c;do c=r.pop(),n[c].onStack=!1,u.push(c);while(a!==c);i.push(u)}}return e.nodes().forEach(function(a){jh.has(n,a)||s(a)}),i}var Ab=it,Sb=Dd,Tb=Cb;function Cb(e){return Ab.filter(Sb(e),function(t){return t.length>1||t.length===1&&e.hasEdge(t[0],t[0])})}var Eb=it,qb=Ob,Rb=Eb.constant(1);function Ob(e,t,r){return Ib(e,t||Rb,r||function(n){return e.outEdges(n)})}function Ib(e,t,r){var n={},i=e.nodes();return i.forEach(function(s){n[s]={},n[s][s]={distance:0},i.forEach(function(a){s!==a&&(n[s][a]={distance:Number.POSITIVE_INFINITY})}),r(s).forEach(function(a){var o=a.v===s?a.w:a.v,u=t(a);n[s][o]={distance:u,predecessor:s}})}),i.forEach(function(s){var a=n[s];i.forEach(function(o){var u=n[o];i.forEach(function(c){var l=u[s],h=a[c],f=u[c],d=l.distance+h.distance;d<f.distance&&(f.distance=d,f.predecessor=h.predecessor)})})}),n}var ie=it,Fd=kd;kd.CycleException=He;function kd(e){var t={},r={},n=[];function i(s){if(ie.has(r,s))throw new He;ie.has(t,s)||(r[s]=!0,t[s]=!0,ie.each(e.predecessors(s),i),delete r[s],n.push(s))}if(ie.each(e.sinks(),i),ie.size(t)!==e.nodeCount())throw new He;return n}function He(){}He.prototype=new Error;var Nh=Fd,Mb=Pb;function Pb(e){try{Nh(e)}catch(t){if(t instanceof Nh.CycleException)return!1;throw t}return!0}var Ue=it,$d=Lb;function Lb(e,t,r){Ue.isArray(t)||(t=[t]);var n=(e.isDirected()?e.successors:e.neighbors).bind(e),i=[],s={};return Ue.each(t,function(a){if(!e.hasNode(a))throw new Error("Graph does not have node: "+a);zd(e,a,r==="post",s,n,i)}),i}function zd(e,t,r,n,i,s){Ue.has(n,t)||(n[t]=!0,r||s.push(t),Ue.each(i(t),function(a){zd(e,a,r,n,i,s)}),r&&s.push(t))}var jb=$d,Nb=Db;function Db(e,t){return jb(e,t,"post")}var Fb=$d,kb=$b;function $b(e,t){return Fb(e,t,"pre")}var Dh=it,zb=Pd,Bb=jd,Hb=Ub;function Ub(e,t){var r=new zb,n={},i=new Bb,s;function a(u){var c=u.v===s?u.w:u.v,l=i.priority(c);if(l!==void 0){var h=t(u);h<l&&(n[c]=s,i.decrease(c,h))}}if(e.nodeCount()===0)return r;Dh.each(e.nodes(),function(u){i.add(u,Number.POSITIVE_INFINITY),r.setNode(u)}),i.decrease(e.nodes()[0],0);for(var o=!1;i.size()>0;){if(s=i.removeMin(),Dh.has(n,s))r.setEdge(s,n[s]);else{if(o)throw new Error("Input graph is not connected: "+e);o=!0}e.nodeEdges(s).forEach(a)}return r}var Kb={components:hb,dijkstra:Nd,dijkstraAll:bb,findCycles:Tb,floydWarshall:qb,isAcyclic:Mb,postorder:Nb,preorder:kb,prim:Hb,tarjan:Dd,topsort:Fd},Wb=lb,hr={Graph:Wb.Graph,alg:Kb};function Vb(e){const t=[];let r=e.nodes().filter(n=>e.node(n).rank===0).concat(e.sources());return r.length===0&&(r=e.nodes().slice(0,1)),hr.alg.preorder(e,r).forEach(n=>{const i=e.node(n).rank;for(;i>=t.length;)t.push([]);t[i].push(n)}),t}function Yb(e,t){let r=!0;for(;r;){r=!1;for(let n=0;n<t.length;++n)for(let i=0;i<t[n].length-1;++i){const s=Fh(e,t,n);kh(t[n],i,i+1),Fh(e,t,n)<s?r=!0:kh(t[n],i,i+1)}}}function Fh(e,t,r){let n=0;return r>0&&(n+=_a(e,t[r-1],t[r])),r+1<t.length&&(n+=_a(e,t[r],t[r+1])),n}function kh(e,t,r){const n=e[t];e[t]=e[r],e[r]=n}function $h(e){const t=Math.floor(e.length/2);if(e.length===0)return-1;if(e.length%2===1)return e[t];if(e.length===2)return(e[0]+e[1])/2;const r=e[t-1]-e[0],n=e[e.length-1]-e[t];return(e[t-1]*n+e[t]*r)/(r+n)}function Jb(e,t,r=1,n=!1){if(r>0)for(let i=1;i<t.length;++i){const s=new Map;t[i].forEach(a=>{const o=$h(zh(e,t,i,i-1,a,n));s.set(a,o)}),Bh(t[i],s)}else for(let i=t.length-2;i>=0;--i){const s=new Map;t[i].forEach(a=>{const o=$h(zh(e,t,i,i+1,a,n));s.set(a,o)}),Bh(t[i],s)}}function zh(e,t,r,n,i,s=!1){const a=t[r],o=t[n],u=[];return o.forEach((c,l)=>{e.nodeEdges(c,i).length>0&&u.push(l)}),u.length===0&&s&&a.forEach((c,l)=>{e.nodeEdges(c,i).length>0&&u.push(l+.5)}),u.sort((c,l)=>c-l),u}function Bh(e,t){const r=new Map(e.map((n,i)=>[n,i]));for(let n=1;n<e.length;++n)for(let i=n;i>0;--i){let s=i-1,a=t.get(e[s]),o=t.get(e[i]);for(;(a=t.get(e[s]))===-1&&s>0;)s--;if(o===-1||a===-1||(a===o&&(a=r.get(e[s]),o=r.get(e[i])),o>=a))break;[e[i],e[s]]=[e[s],e[i]]}}function Zb(e,t=25){const r=Vb(e);let n=r,i=0;for(;i++<t;)Jb(e,r,i%2===0?1:0),Yb(e,r),Hh(e,r)<Hh(e,n)&&(n=Xb(r));return n=n.map(s=>[s]),n}function Hh(e,t){let r=0;for(let n=0;n<t.length-1;++n)r+=_a(e,t[n],t[n+1]);return r}function Xb(e){const t=[];for(const r of e){const n=[];t.push(n);for(const i of r)n.push(i)}return t}function Gb(e,t){const r=[];return t.forEach((n,i)=>{if(i===t.length-1)return;let s=0;n.forEach(a=>{a.forEach(o=>{e.outEdges(o).forEach(u=>{const c=e.edge(u).dy,l=e.node(u.w).y-e.node(u.v).y,h=Math.abs(l)-c,f=c*c-h*h,d=f>=0?Math.sqrt(f):c;d>s&&(s=d)})})}),r.push(s)}),r}function Qb(e,t,r,n){const i=nt(n);let s;if(i>r)s=n.map(o=>r*o/i);else{const o=r-i;s=n.map(u=>u+o/(t.length-1))}let a=0;t.forEach((o,u)=>{o.forEach(c=>{c.forEach(l=>{const h=e.node(l);h.x=a})}),a+=s[u]})}function tw(){let e=[100,100],t=null,r=.5,n=function(o,u,c){return 1};const i=function(o){return o.data.value},s=function(o){return o.data?o.data.direction:"r"};function a(o,u){t===null&&a.scaleToFit(o,u),Uh(o,i,s),ew(o,i,t);const c=Kh(o,u);u.forEach(f=>{let d=0;f.forEach((g,p)=>{const v=c[p]/nt(c)*e[1],y=d===0?0:r*v/5,_=v-2*y,b=nt(g,C=>o.node(C).dy),m=g.map((C,F)=>o.node(C).value&&g[F+1]?n(g[F],g[F+1],o):0),S=Math.max(0,_-b),w=nt(m)?S/nt(m):0;let x=d+y;g.length===1&&(x+=(_-o.node(g[0]).dy)/2);let R=Number.MAX_VALUE;g.forEach((C,F)=>{const A=o.node(C);A.y=x,A.spaceAbove=R,A.spaceBelow=m[F]*w,x+=A.dy+A.spaceBelow,R=A.spaceBelow,A.data&&A.data.forceY!==void 0&&(A.y=y+A.data.forceY*(_-A.dy))}),g.length>0&&(o.node(g[g.length-1]).spaceBelow=Number.MAX_VALUE),d+=v})});const l=Gb(o,u);Qb(o,u,e[0],l);const h=[];return o.nodes().forEach(f=>{const d=o.node(f);d.id=f,h.push(d)}),h}return a.scaleToFit=function(o,u){Uh(o,i,s);const c=nt(Kh(o,u));if(c<=0){t=1;return}t=e[1]/c,r!==1&&(t*=1-r)},a.size=function(o){return arguments.length?(e=o,a):e},a.separation=function(o){return arguments.length?(n=Fa(o)?o:()=>o,a):n},a.whitespace=function(o){return arguments.length?(r=o,a):r},a.scale=function(o){return arguments.length?(t=o,a):t},a}function Uh(e,t,r){e.nodes().forEach(n=>{const i=nt(e.inEdges(n),o=>t(e.edge(o))),s=nt(e.outEdges(n),o=>t(e.edge(o)));let a=e.node(n);a||e.setNode(n,a={}),a.value=Math.max(i,s),a.direction===void 0&&(a.direction=r(a)||"r")})}function ew(e,t,r){e.nodes().forEach(n=>{const i=e.node(n);i.dy=i.value*r}),e.edges().forEach(n=>{const i=e.edge(n);i.value=t(i),i.dy=i.value*r})}function Kh(e,t){if(t.length===0||t[0].length===0)return[];const r=t[0].length,n=new Array(r);for(let i=0;i<r;i++)n[i]=0;return t.forEach(i=>{i.forEach((s,a)=>{const o=nt(s,u=>e.node(u).value);n[a]=Math.max(n[a],o)})}),n}function rw(e,t,r){for(var n=-1,i=e.length;++n<i;){var s=e[n],a=t(s);if(a!=null&&(o===void 0?a===a&&!Ye(a):r(a,o)))var o=a,u=s}return u}function nw(e,t){return e<t}function iw(e){return e&&e.length?rw(e,id,nw):void 0}function sw(e,t){const r=new hr.Graph({directed:!0});let n=!1;for(const a of t)if(a.type==="min"){n=!0;break}if(!n){const a=e.sources();a.length>0?t=[{nodes:[a[0]],type:"min"},...t]:t=[{nodes:[e.nodes()[0]],type:"min"},...t]}const i=new Map;let s=0;return t.forEach(a=>{if(!a.nodes||a.nodes.length===0)return;const o=`${s++}`;a.nodes.forEach(u=>{i.set(u,o)}),r.setNode(o,{nodes:a.nodes,type:a.type})}),e.nodes().forEach(a=>{if(!i.has(a)){const o=`${s++}`,u={nodes:[a],type:"same"};i.set(a,o),r.setNode(o,u)}}),e.edges().forEach(a=>{const o=i.has(a.v)?i.get(a.v):a.v,u=i.has(a.w)?i.get(a.w):a.w,c=e.node(a.v)||{},l=e.node(a.w)||{},h=r.edge(o,u)||{delta:0};o===u?(h.delta=0,r.setEdge(o,u,h)):c.direction==="l"?(h.delta=Math.max(h.delta,l.direction==="l"?1:0),r.setEdge(u,o,h)):(h.delta=Math.max(h.delta,l.direction==="l"?0:1),r.setEdge(o,u,h))}),r}var aw=function(){for(var t=0;t<arguments.length;t++)if(typeof arguments[t]<"u")return arguments[t]};const ow=U_(aw);function uw(e){var t;const r=e.sources(),n=new Set,i=new Set;for(e.edges().forEach(s=>{s.v===s.w&&i.add(s)});r.length>0;){const s=r.shift();n.add(s);let a=e.node(s);a||e.setNode(s,a={}),a.rank=0;for(const o of e.inEdges(s)){const u=ow((t=e.edge(o)||{})==null?void 0:t.delta,1);a.rank=Math.max(a.rank,e.node(o.v).rank+u)}for(const o of e.outEdges(s))i.add(o);for(const o of e.nodes())r.indexOf(o)<0&&!n.has(o)&&!e.inEdges(o).some(u=>!i.has(u))&&r.push(o)}}function cw(e,t){const r=lw(e,t);return e.edges().forEach(n=>{if(hw(r,n.v,n.w)<0){const s=e.edge(n)||{};s.reversed=!0,e.removeEdge(n),e.setEdge(n.w,n.v,s)}}),e}function lw(e,t){const r=new Set,n=new hr.Graph({directed:!0}),i=[];if(!e.hasNode(t))throw Error("node not in graph");return ma(e,t,r,n,i),e.nodes().forEach(s=>{r.has(s)||ma(e,s,r,n,i)}),i.forEach((s,a)=>{n.node(s).thread=a+1<i.length?i[a+1]:i[0]}),n}function hw(e,t,r){const n=e.node(t),i=e.node(r);if(n.depth<i.depth){let s=n.thread;for(;e.node(s).depth>n.depth;){if(s===r)return 1;s=e.node(s).thread}}else if(i.depth<n.depth){let s=i.thread;for(;e.node(s).depth>i.depth;){if(s===t)return-1;s=e.node(s).thread}}return 0}function ma(e,t,r,n,i,s=0){r.has(t)||(r.add(t),i.push(t),n.setNode(t,{depth:s}),e.outEdges(t).map(o=>o.w).forEach(o=>{r.has(o)||n.setEdge(t,o,{delta:1}),ma(e,o,r,n,i,s+1)}))}function Wh(e,t){if(e.nodes().length===0)return;const r=sw(e,t);fw(r),cw(r,"0"),uw(r),dw(r),e.nodes().forEach(n=>{e.node(n)||e.setNode(n,{})}),r.nodes().forEach(n=>{const i=r.node(n);i.nodes.forEach(s=>{e.node(s).rank=i.rank})})}function fw(e){e.sources().forEach(t=>{t!=="0"&&e.setEdge("0",t,{delta:0,temp:!0})})}function dw(e){e.edges().forEach(r=>{e.edge(r).temp&&t(r.w)});function t(r){const n=e.node(r),i=iw(e.outEdges(r).map(s=>e.node(s.w).rank-e.edge(s).delta));i!==void 0&&(n.rank=i)}}function pw(e=[],t=[]){const r=new hr.Graph({directed:!0,multigraph:!0});return t.forEach(n=>{r.setEdge(n.source,n.target,{data:n,points:[]},n.type)}),r.nodes().forEach(n=>r.setNode(n,{data:{}})),e.forEach(n=>{r.hasNode(n.id)&&(r.node(n.id).data=n,n.direction!==void 0&&(r.node(n.id).direction=n.direction))}),r.edges().forEach(n=>{r.edge(n).source=r.node(n.v),r.edge(n).target=r.node(n.w)}),r}function gw(e){const t=tw(),r=D_(e);let n=[],i=[],s=null;function a(o=[],u=[],c={}){const l=pw(u,o);return c.order?(Wh(l,c.rankSets||[]),s=c.order,s.length>0&&s[0].length>0&&!st(s[0][0])&&(s=s.map(h=>[h])),s=s.map(h=>h.map(f=>f.filter(d=>l.node(d)!==void 0)))):(Wh(l,c.rankSets||[]),z_(l),s=Zb(l)),n=t(l,s),j_(l,{alignLinkTypes:c.alignLinkTypes||!1}),i=r(l),B_(l),N_(l),l}return a.nodes=function(){return n},a.links=function(){return i},a.order=function(){return s},a.size=function(o){return arguments.length?(t.size(o),a):t.size()},a.separation=function(o){return arguments.length?(t.separation(o),a):t.separation()},a.whitespace=function(o){return arguments.length?(t.whitespace(o),a):t.whitespace()},a.edgeValue=function(o){return a},a.scale=function(o){return arguments.length?(t.scale(o),a):t.scale()},a}const Vh=["#ACD5FC","#E37816","#07CC80","#0871D4","#CB8E55","#9FAE29","#427565","#6D55A6","#FEF445","#E177C2","#F48B7B","#D74B05","#AA896F","#14A14D","#C5114D"];var vw="[object Number]";function yw(e){return typeof e=="number"||Rt(e)&&Pt(e)==vw}function _w(e){return yw(e)&&e!=+e}const Bd=e=>e.dy===0?0:2;function mw(e){const t=e.x1-e.x0,r=e.y1-e.y0,n=e.dy/2,i=(t*t+r*r)/Math.abs(4*r);return[n,i]}function bw(e){const t=Math.max(Bd(e),e.dy)/2;let{x0:r}=e,{x1:n}=e,{y0:i}=e,{y1:s}=e;n<r&&([r,n]=[n,r],[i,s]=[s,i]);const a=s>i?1:-1,o=1,u=mw(e),c=Math.max(u[0],Math.min(u[1],(n-r)/3));let l=Math.max(u[0],Math.min(u[1],e.r0||c)),h=Math.max(u[0],Math.min(u[1],e.r1||c));const f=n-r,d=s-i-a*(l+h),g=Math.sqrt(f*f+d*d),p=-a*Math.acos(Math.min(1,(l+h)/g)),v=Math.atan2(d,f);let y=Math.PI/2+a*(v+p),_=t*a*Math.sin(y),b=t*Math.cos(y),m=r+o*l*Math.sin(Math.abs(y)),S=n-o*h*Math.sin(Math.abs(y)),w=i+l*a*(1-Math.cos(y)),x=s-h*a*(1-Math.cos(y));(_w(y)||Math.abs(y)<.001)&&(y=0,l=0,h=0,m=r,S=n,w=i,x=s,_=0,b=t);function R(F,A){const U=F*(s-i)>0?1:0;let W=o*F*(s-i)>0?A+t:A-t;return y===0&&(W=A),`A${W} ${W} ${Math.abs(y)} 0 ${U} `}return`M${[r,i-t]} ${R(1,l)}${[m+_,w-b]} L${[S+_,x-b]} ${R(-1,h)}${[n,s-t]} L${[n,s+t]} ${R(1,h)}${[S-_,x+b]} L${[m-_,w+b]} ${R(-1,l)}${[r,i+t]} Z`}function ww(e){const t=e.points[0],r=Math.max(Bd(e),e.dy)/2;return t.style==="down-right"?`M${[t.x-20,t.y-r]} L${[t.x,t.y-r]} L${[t.x,t.y+r]} L${[t.x-20,t.y+r]} Z`:t.style==="right-down"?`M${[t.x,t.y-r]} L${[t.x+20,t.y-r]} L${[t.x+20,t.y+r]} L${[t.x,t.y+r]} Z`:null}function xw(e){var t;if(((t=e.points)==null?void 0:t.length)===1)return ww(e);let r="",n;for(let i=0;i<e.points.length-1;++i)n={d0:e.points[i].d,d1:e.points[i+1].d,dy:e.dy,r0:e.points[i].ro,r1:e.points[i+1].ri,x0:e.points[i].x,x1:e.points[i+1].x,y0:e.points[i].y,y1:e.points[i+1].y},r+=bw(n);return r}const Aw=(e,t)=>{const r=e.title!==void 0?e.title:e.id;return t&&r.length>t?`${r.substr(0,15)}...`:r};function Sw(e,t){let r=!1,n=!1;return e.rank<t/2&&(n=!0,r=!1),{right:n,top:r}}function Tw(e,t,r){const n=t.top?-25:e.dy/2-10;let i;return t.top?i=t.right?4:-4:i=t.right?10+r/2:-10+-r/2,{x:e.x+i,y:e.y+n}}function Cw(e,{fontColor:t,maxRank:r,nodeWidth:n}){const{data:i}=e,s=Sw(e,r),{x:a,y:o}=Tw(e,s,n);return Cy(Aw(i,s.top?25:15),{fontColor:t,pointerEvents:"none",textAnchor:s.top?"middle":s.right?"start":"end",x:a,y:o})}function Ew(e,t,r){var n,i;const{dy:s,x:a,y:o}=e,{fontColor:u,fontSize:c,nodeBorderColor:l,nodeBorderWidth:h,nodeWidth:f,onNodeClick:d}=r,g=new Ae({class:"node"}),p=Ty({height:s,width:f,x:a-f/2,y:o});if(p.fill((n=e.data)==null?void 0:n.color),p.stroke({color:l??((i=e.data)==null?void 0:i.color),width:h}),p.on("click",()=>{typeof d=="function"&&d(e)}),g.add(p),e.data&&s>Number.parseInt(c,10)){const v=Cw(e,{fontColor:u,maxRank:t,nodeWidth:f});g.add(v)}return g}class qw{constructor(t,r){this.element=t,this.options=r,this.canvas=fa().addTo(t).size("100%","100%").viewbox(`-${this.spacing} -${this.spacing+this.getYShift()} ${this.width+this.spacing*2} ${this.height+this.spacing+this.getYShift()}`).attr({style:this.options.canvasStyle})}getYShift(){return this.options.enableToolbar?30:0}add(t){this.canvas.add(t)}clear(){this.canvas.clear().viewbox(`-${this.spacing} -${this.spacing+this.getYShift()} ${this.width+this.spacing*2} ${this.height+this.spacing+this.getYShift()}`)}resetViewBox(){this.canvas.viewbox(`-${this.spacing} -${this.spacing+this.getYShift()} ${this.width+this.spacing*2} ${this.height+this.spacing+this.getYShift()}`)}updateViewBox(t,r,n,i){this.canvas.viewbox(`${t} ${r} ${n} ${i}`)}get height(){return this.options.viewPortHeight}get spacing(){return this.options.spacing}get width(){return this.options.viewPortWidth}}class Rw extends qw{construct(t){const{nodeWidth:r,spacing:n,viewPortHeight:i,viewPortWidth:s}=this.options,a=gw({nodeWidth:r});a.size([s-r,i-n]);const o=t.nodes.map((c,l)=>{const h=l%Vh.length;return{...c,color:c.color??Vh[h],colorIndex:h}}),u=a(t.edges,o,t.options);this.maxRank=a.order().length-1,this.graph=u}render({keepOldPosition:t=!1}={}){var r,n;const{enableTooltip:i,fontColor:s,fontFamily:a,fontSize:o,fontWeight:u,nodeBorderColor:c,nodeBorderWidth:l,nodeWidth:h,onNodeClick:f,tooltipId:d}=this.options,g=this.canvas.viewbox();this.clear();const p=ke({color:s,fontFamily:a,fontSize:o,fontWeight:u}),v=br(h/2,0);v.attr("style",p);const y=((r=this.graph)==null?void 0:r.nodes())||[],_=((n=this.graph)==null?void 0:n.edges())||[],b=br();_.forEach(S=>{this.renderEdge(S,b)}),v.add(b);const m=br();if(y.forEach(S=>{const w=this.graph.node(S);if(w.dummy)return;const x=Ew(w,this.maxRank,{fontColor:s,fontSize:o,nodeBorderColor:c,nodeBorderWidth:l,nodeWidth:h,onNodeClick:f});m.add(x)}),v.add(m),this.add(v),t&&this.updateViewBox(g.x,g.y,g.width,g.height),i){const S=Ay(d);(document.body||document.getElementsByTagName("body")[0]).append(S)}}renderEdge(t,r){const{edgeGradientFill:n,edgeOpacity:i,enableTooltip:s,fontColor:a,fontFamily:o,fontSize:u,fontWeight:c,nodeWidth:l,tooltipBGColor:h,tooltipBorderColor:f,tooltipId:d,tooltipTemplate:g}=this.options,p=this.graph.edge(t.v,t.w,t.name),v=this.graph.node(t.v),y=this.graph.node(t.w),_=p.source,b=p.target;v.dummy&&(p.x0-=l/2),y.dummy&&(p.x1+=l/2);const m=xw(p),S=this.canvas.gradient("linear",x=>{var R,C,F;x.stop(0,(R=_.data)==null?void 0:R.color),x.stop(1,n?(C=b.data)==null?void 0:C.color:(F=_.data)==null?void 0:F.color)}),w=Ey(m,{edgeColor:S,id:`${t.v}-${t.w}`,opacity:i});if(s&&_.data&&b.data){const x=g?g({source:{..._.data,value:_.value},target:{...b.data,value:b.value},value:p.value}):null;w.on("mousemove",R=>{var C;const F=ke({color:a,fontFamily:o,fontSize:u,fontWeight:c}),A=Sy({bgColor:h,borderColor:f,x:R.pageX,y:R.pageY});(C=fa(`[id="${R.target.id}"]`))==null||C.front().opacity(1),qo(d,A+F,x)}),w.on("mouseout",R=>{var C;qo(d),(C=fa(`[id="${R.target.id}"]`))==null||C.front().opacity(i)})}r.add(w)}}const Ow={canvasStyle:"border: 1px solid #caced0; box-sizing: border-box",edgeGradientFill:!0,edgeOpacity:.4,enableToolbar:!0,enableTooltip:!0,fontColor:"#212121",fontFamily:"",fontSize:"14px",fontWeight:"400",height:"auto",nodeBorderColor:null,nodeBorderWidth:1,nodeWidth:20,onNodeClick:void 0,spacing:20,tooltipBGColor:"#FFFFFF",tooltipBorderColor:"#BCBCBC",tooltipId:"apexsankey-tooltip-container",tooltipTemplate({source:e,target:t,value:r}){return`
      <div style='display:flex;align-items:center;gap:5px;padding:5px 10px;'>
        <div style='width:12px;height:12px;background-color:${e.color}'></div>
        <div>${e.title}</div>
        <div>=></div>
        <div style='width:12px;height:12px;background-color:${t.color}'></div>
        <div>${t.title}</div>
        <div>: ${r}</div> 
      </div>
    `},viewPortHeight:500,viewPortWidth:800,width:"100%"};class Iw{constructor(t,r){this.element=t,this.options=r,this.options={...Ow,...r};const{height:n,width:i}=this.options;this.graph=new Rw(this.element,this.options);let s=!1,a=0;typeof i=="string"&&/^\d+(\.\d+)?%$/.test(i)?(a=Number(i.substring(0,i.length-1)),s=!0):a=Number(i);const o=n==="auto"?a/1.6:n;this.element.style.width=`${a}${s?"%":"px"}`,this.element.style.height=`${o}${s?"%":"px"}`,this.element.style.position="relative"}render(t){if(!this.element)throw new Error("Element not found");return this.graph.construct(t),this.graph.render(),this.options.enableToolbar&&new Py(this.element,this.graph.canvas,"apex-sankey").render({enableExport:!0}),this.graph}}function Mw(e){const t=getComputedStyle(document.documentElement).getPropertyValue(e).trim();return/^\d{1,3},\s*\d{1,3},\s*\d{1,3}$/.test(t)?`rgb(${t})`:t}var Ce=[];const Yh=e=>{const t=JSON.parse(JSON.stringify(e)),r=n=>{for(const i in n)typeof n[i]=="string"&&n[i].startsWith("--dx-")?n[i]=Mw(n[i]):typeof n[i]=="object"&&n[i]!==null&&r(n[i])};return r(t),t};function fr(e=""){e&&document.documentElement.setAttribute("data-colors",e),Ce.forEach(t=>{const r=JSON.parse(JSON.stringify(t[0].data)),n=Yh(structuredClone(r)),i=JSON.parse(JSON.stringify(t[0].renderData)),s=Yh(structuredClone(i));t[0].chart&&t[0].chart.dispose(),n.nodeTemplate=t[0].data.nodeTemplate;var a=new Iw(document.getElementById(t[0].id),n);a.render(s),t[0].chart=a})}document.querySelectorAll('input[name="data-colors"]').forEach(e=>{e.addEventListener("change",function(){uo(this.value)})});document.querySelectorAll('input[name="data-bs-theme"]').forEach(e=>{e.addEventListener("change",function(){uo(this.value)})});var Jh;(Jh=document.getElementById("darkModeButton"))==null||Jh.addEventListener("click",function(){uo(this.value)});function uo(e){setTimeout(()=>{fr(e)},0)}var Zh;(Zh=document.getElementById("darkModeButton"))==null||Zh.addEventListener("click",function(){setTimeout(()=>{fr()},0)});const Pw={nodes:[{id:"Oil",title:"Oil"},{id:"Natural Gas",title:"Natural Gas"},{id:"Coal",title:"Coal"},{id:"Fossil Fuels",title:"Fossil Fuels"},{id:"Electricity",title:"Electricity"},{id:"Energy",title:"Energy"}],edges:[{source:"Oil",target:"Fossil Fuels",value:15},{source:"Natural Gas",target:"Fossil Fuels",value:20},{source:"Coal",target:"Fossil Fuels",value:25},{source:"Coal",target:"Electricity",value:25},{source:"Fossil Fuels",target:"Energy",value:60},{source:"Electricity",target:"Energy",value:25}]},Lw={nodeWidth:20,fontWeight:"500",fontSize:"10px",height:"200px",enableToolbar:!0,fontColor:"--dx-body-color",tooltipBGColor:"--dx-secondary-bg",tooltipBorderColor:"--dx-border-color",canvasStyle:"border: 1px solid var(--dx-border-color); background: var(--dx-secondary-bg);"};Ce.push([{id:"basicSankey",data:Lw,renderData:Pw}]);const jw={nodes:[{id:"Applications",title:"Applications"},{id:"Accepted",title:"Accepted"},{id:"Rejected",title:"Rejected"},{id:"In Progress",title:"In Progress"},{id:"Software Engineering",title:"Software Engineering"},{id:"Data Science",title:"Data Science"},{id:"Marketing",title:"Marketing"},{id:"Sales",title:"Sales"},{id:"HR",title:"HR"},{id:"Finance",title:"Finance"},{id:"Internship",title:"Internship"},{id:"Junior",title:"Junior"},{id:"Mid-level",title:"Mid-level"},{id:"Senior",title:"Senior"},{id:"Entry Level",title:"Entry Level"},{id:"Full-time",title:"Full-time"},{id:"Part-time",title:"Part-time"}],edges:[{source:"Applications",target:"Accepted",value:10},{source:"Applications",target:"Rejected",value:15},{source:"Applications",target:"In Progress",value:10},{source:"Accepted",target:"Software Engineering",value:4},{source:"Accepted",target:"Data Science",value:2},{source:"Accepted",target:"Marketing",value:1},{source:"Accepted",target:"Sales",value:1},{source:"Accepted",target:"HR",value:1},{source:"Accepted",target:"Finance",value:1},{source:"Rejected",target:"Software Engineering",value:5},{source:"Rejected",target:"Data Science",value:3},{source:"Rejected",target:"Marketing",value:2},{source:"Rejected",target:"Sales",value:2},{source:"Rejected",target:"HR",value:2},{source:"Rejected",target:"Finance",value:1},{source:"In Progress",target:"Software Engineering",value:3},{source:"In Progress",target:"Data Science",value:2},{source:"In Progress",target:"Marketing",value:2},{source:"In Progress",target:"Sales",value:1},{source:"In Progress",target:"HR",value:1},{source:"In Progress",target:"Finance",value:1},{source:"Software Engineering",target:"Internship",value:1},{source:"Software Engineering",target:"Junior",value:1},{source:"Software Engineering",target:"Mid-level",value:1},{source:"Software Engineering",target:"Senior",value:1},{source:"Software Engineering",target:"Entry Level",value:1},{source:"Data Science",target:"Internship",value:1},{source:"Data Science",target:"Junior",value:1},{source:"Data Science",target:"Mid-level",value:1},{source:"Data Science",target:"Senior",value:1},{source:"Data Science",target:"Entry Level",value:1},{source:"Marketing",target:"Internship",value:1},{source:"Marketing",target:"Junior",value:1},{source:"Marketing",target:"Mid-level",value:1},{source:"Marketing",target:"Senior",value:1},{source:"Marketing",target:"Entry Level",value:1},{source:"Sales",target:"Internship",value:1},{source:"Sales",target:"Junior",value:1},{source:"Sales",target:"Mid-level",value:1},{source:"Sales",target:"Senior",value:1},{source:"Sales",target:"Entry Level",value:1},{source:"HR",target:"Internship",value:1},{source:"HR",target:"Junior",value:1},{source:"HR",target:"Mid-level",value:1},{source:"HR",target:"Senior",value:1},{source:"HR",target:"Entry Level",value:1},{source:"Finance",target:"Internship",value:1},{source:"Finance",target:"Junior",value:1},{source:"Finance",target:"Mid-level",value:1},{source:"Finance",target:"Senior",value:1},{source:"Finance",target:"Entry Level",value:1},{source:"Internship",target:"Full-time",value:1},{source:"Internship",target:"Part-time",value:1},{source:"Junior",target:"Full-time",value:1},{source:"Junior",target:"Part-time",value:1},{source:"Mid-level",target:"Full-time",value:1},{source:"Mid-level",target:"Part-time",value:1},{source:"Senior",target:"Full-time",value:1},{source:"Senior",target:"Part-time",value:1},{source:"Entry Level",target:"Full-time",value:1},{source:"Entry Level",target:"Part-time",value:1}]},Nw={height:"250px",fontSize:"10px",fontWeight:"500",nodeWidth:5,nodeBorderWidth:2,nodeBorderColor:"--dx-border-color",edgeGradientFill:!1,fontColor:"--dx-body-color",tooltipBGColor:"--dx-secondary-bg",tooltipBorderColor:"--dx-border-color",canvasStyle:"border: 1px solid var(--dx-border-color); background: var(--dx-secondary-bg);"};Ce.push([{id:"nodeCustomization",data:Nw,renderData:jw}]);const Dw={nodes:[{id:"England",title:"england"},{id:"Wales",title:"wales"},{id:"Level 4",title:"level 4"},{id:"Level 3",title:"level 3"},{id:"Level 2",title:"level 2"},{id:"Level 1 and entry level",title:"level 1 and entry level"},{id:"No qualifications",title:"no qualifications"},{id:"Other",title:"other"},{id:"Wholesale & retail",title:"wholesale & retail"},{id:"Health & social work",title:"health & social work"},{id:"Education",title:"education"},{id:"Construction",title:"construction"},{id:"Manufacturing",title:"manufacturing"},{id:"Transport & storage",title:"transport & storage"},{id:"Finance & insurance",title:"finance & insurance"}],edges:[{source:"England",target:"Level 4",value:13},{source:"England",target:"Level 3",value:8},{source:"England",target:"Level 2",value:8},{source:"England",target:"Level 1 and entry level",value:6},{source:"England",target:"No qualifications",value:3},{source:"Wales",target:"Level 4",value:7},{source:"Wales",target:"Level 3",value:8},{source:"Wales",target:"Level 2",value:4},{source:"Wales",target:"Level 1 and entry level",value:5},{source:"Wales",target:"No qualifications",value:5},{source:"Level 4",target:"Wholesale & retail",value:4},{source:"Level 4",target:"Health & social work",value:3},{source:"Level 4",target:"Education",value:2},{source:"Level 4",target:"Construction",value:1},{source:"Level 4",target:"Manufacturing",value:2},{source:"Level 4",target:"Other",value:3},{source:"Level 4",target:"Transport & storage",value:2},{source:"Level 4",target:"Finance & insurance",value:3},{source:"Level 3",target:"Wholesale & retail",value:3},{source:"Level 3",target:"Health & social work",value:2},{source:"Level 3",target:"Education",value:1},{source:"Level 3",target:"Construction",value:2},{source:"Level 3",target:"Manufacturing",value:1},{source:"Level 3",target:"Other",value:3},{source:"Level 3",target:"Transport & storage",value:2},{source:"Level 3",target:"Finance & insurance",value:2},{source:"Level 2",target:"Wholesale & retail",value:2},{source:"Level 2",target:"Health & social work",value:1},{source:"Level 2",target:"Education",value:2},{source:"Level 2",target:"Construction",value:1},{source:"Level 2",target:"Manufacturing",value:2},{source:"Level 2",target:"Other",value:2},{source:"Level 2",target:"Transport & storage",value:1},{source:"Level 2",target:"Finance & insurance",value:1},{source:"Level 1 and entry level",target:"Wholesale & retail",value:1},{source:"Level 1 and entry level",target:"Health & social work",value:2},{source:"Level 1 and entry level",target:"Education",value:1},{source:"Level 1 and entry level",target:"Construction",value:2},{source:"Level 1 and entry level",target:"Manufacturing",value:1},{source:"Level 1 and entry level",target:"Other",value:2},{source:"Level 1 and entry level",target:"Transport & storage",value:1},{source:"Level 1 and entry level",target:"Finance & insurance",value:1},{source:"No qualifications",target:"Wholesale & retail",value:1},{source:"No qualifications",target:"Health & social work",value:1},{source:"No qualifications",target:"Education",value:1},{source:"No qualifications",target:"Construction",value:1},{source:"No qualifications",target:"Manufacturing",value:1},{source:"No qualifications",target:"Other",value:1},{source:"No qualifications",target:"Transport & storage",value:1},{source:"No qualifications",target:"Finance & insurance",value:1}]},Fw={nodeWidth:20,fontSize:"10px",height:"100%",fontWeight:"500",edgeOpacity:.2,fontColor:"--dx-body-color",tooltipBGColor:"--dx-secondary-bg",tooltipBorderColor:"--dx-border-color",canvasStyle:"border: 1px solid var(--dx-border-color); background: var(--dx-secondary-bg);"};Ce.push([{id:"edgeCustomization",data:Fw,renderData:Dw}]);const kw={nodes:[{id:"Berlin",title:"Berlin"},{id:"Job Applications",title:"Job Applications"},{id:"Barcelona",title:"Barcelona"},{id:"Madrid",title:"Madrid"},{id:"Amsterdam",title:"Amsterdam"},{id:"Paris",title:"Paris"},{id:"London",title:"London"},{id:"Munich",title:"Munich"},{id:"Brussels",title:"Brussels"},{id:"Dubai",title:"Dubai"},{id:"Dublin",title:"Dublin"},{id:"Other Cities",title:"Other Cities"},{id:"No Response",title:"No Response"},{id:"Responded",title:"Responded"},{id:"Rejected",title:"Rejected"},{id:"Interviewed",title:"Interviewed"},{id:"No Offer",title:"No Offer"},{id:"Declined Offer",title:"Declined Offer"},{id:"Accepted Offer",title:"Accepted Offer"}],edges:[{source:"Berlin",target:"Job Applications",value:102,color:"#dddddd"},{source:"Barcelona",target:"Job Applications",value:39,color:"#dddddd"},{source:"Madrid",target:"Job Applications",value:35,color:"#dddddd"},{source:"Amsterdam",target:"Job Applications",value:15,color:"#dddddd"},{source:"Paris",target:"Job Applications",value:14,color:"#dddddd"},{source:"London",target:"Job Applications",value:6,color:"#dddddd"},{source:"Munich",target:"Job Applications",value:5,color:"#dddddd"},{source:"Brussels",target:"Job Applications",value:4,color:"#dddddd"},{source:"Dubai",target:"Job Applications",value:3,color:"#dddddd"},{source:"Dublin",target:"Job Applications",value:3,color:"#dddddd"},{source:"Other Cities",target:"Job Applications",value:12,color:"#dddddd"},{source:"Job Applications",target:"No Response",value:189,color:"#dddddd"},{source:"Job Applications",target:"Responded",value:49,color:"orange"},{source:"Responded",target:"Rejected",value:38,color:"#dddddd"},{source:"Responded",target:"Interviewed",value:11,color:"orange"},{source:"Interviewed",target:"No Offer",value:8,color:"#dddddd"},{source:"Interviewed",target:"Declined Offer",value:2,color:"#dddddd"},{source:"Interviewed",target:"Accepted Offer",value:1,color:"orange"}],options:{order:[[["Berlin","Barcelona","Madrid","Amsterdam","Paris","London"],["Munich","Brussels","Dubai","Dublin","Other Cities"]],[["Job Applications"]],[["Responded"],["No Response"]],[["Interviewed"],["Rejected"]],[["Accepted Offer","Declined Offer","No Offer"],[]]]}},$w={nodeWidth:10,fontSize:"10px",height:"100%",fontFamily:'Satisfy, "cursive"',fontWeight:"500",fontColor:"--dx-orange",tooltipBGColor:"--dx-secondary-bg",tooltipBorderColor:"--dx-border-color",canvasStyle:"border: 1px solid var(--dx-border-color); background: var(--dx-secondary-bg);"};Ce.push([{id:"fontOptions",data:$w,renderData:kw}]);window.addEventListener("DOMContentLoaded",()=>{fr()});window.addEventListener("resize",()=>{setTimeout(()=>{fr()},0)})});export default zw();
