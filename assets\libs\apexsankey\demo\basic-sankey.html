<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Quicksand:wght@400;600&display=swap"
      rel="stylesheet"
    />
    <script src="https://cdn.jsdelivr.net/npm/@svgdotjs/svg.js"></script>
    <script src="../apexsankey.min.js"></script>
  </head>
  <body>
    <div
      id="svg-sankey"
      style="margin: 0 auto; width: 800px; height: 600px"
    ></div>
    <script>
      const data = {
        nodes: [
          {
            id: "Oil",
            title: "Oil",
          },
          {
            id: "Natural Gas",
            title: "Natural Gas",
          },
          {
            id: "Coal",
            title: "Coal",
          },
          {
            id: "Fossil Fuels",
            title: "Fossil Fuels",
          },
          {
            id: "Electricity",
            title: "Electricity",
          },
          {
            id: "Energy",
            title: "Energy",
          },
        ],
        edges: [
          {
            source: "Oil",
            target: "Fossil Fuels",
            value: 15,
          },
          {
            source: "Natural Gas",
            target: "Fossil Fuels",
            value: 20,
          },
          {
            source: "Coal",
            target: "Fossil Fuels",
            value: 25,
          },
          {
            source: "Coal",
            target: "Electricity",
            value: 25,
          },
          {
            source: "Fossil Fuels",
            target: "Energy",
            value: 60,
          },
          {
            source: "Electricity",
            target: "Energy",
            value: 25,
          },
        ],
      };
      const graphOptions = {
        nodeWidth: 20,
        fontFamily: "Quicksand, sans-serif",
        fontWeight: "600",
        height: 600,
        enableToolbar: true,
      };
      const s = new ApexSankey(
        document.getElementById("svg-sankey"),
        graphOptions
      );
      s.render(data);
    </script>
  </body>
</html>
