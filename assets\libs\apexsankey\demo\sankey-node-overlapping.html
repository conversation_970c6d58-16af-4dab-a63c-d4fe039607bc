<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
    <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@400;600&display=swap" rel="stylesheet" />
    <script src="https://cdn.jsdelivr.net/npm/@svgdotjs/svg.js"></script>
    <script src="../apexsankey.min.js"></script>
  </head>
  <body>
    <div id="svg-sankey" style="margin: 0 auto; width: 800px; height: 600px"></div>
    <script>
      const data = {
        nodes: [
          {
            id: 'Beverages',
            title: 'Beverages',
          },
          {
            id: 'Condiments',
            title: 'Condiments',
          },
          {
            id: 'Produce',
            title: 'Produce',
          },
          {
            id: 'Seafood',
            title: 'Seafood',
          },
          {
            id: 'Dairy Products',
            title: 'Dairy Products',
          },
          {
            id: 'Confections',
            title: 'Confections',
          },
          {
            id: 'Grains-Cereals',
            title: 'Grains-Cereals',
          },
          {
            id: 'Meat-Poultry',
            title: 'Meat-Poultry',
          },
          {
            id: 'Chai',
            title: 'Chai',
          },
          {
            id: 'Chang',
            title: 'Chang',
          },
          {
            id: 'Aniseed Syrup',
            title: 'Aniseed Syrup',
          },
          {
            id: "Chef Anton's Cajun Seasoning",
            title: "Chef Anton's Cajun Seasoning",
          },
          {
            id: "Grandma's Boysenberry Spread",
            title: "Grandma's Boysenberry Spread",
          },
          {
            id: "Uncle Bob's Organic Dried Pears",
            title: "Uncle Bob's Organic Dried Pears",
          },
          {
            id: 'Northwoods Cranberry Sauce',
            title: 'Northwoods Cranberry Sauce',
          },
          {
            id: 'Ikura',
            title: 'Ikura',
          },
          {
            id: 'Queso Cabrales',
            title: 'Queso Cabrales',
          },
          {
            id: 'Queso Manchego La Pastora',
            title: 'Queso Manchego La Pastora',
          },
          {
            id: 'Konbu',
            title: 'Konbu',
          },
          {
            id: 'Tofu',
            title: 'Tofu',
          },
          {
            id: 'Genen Shouyu',
            title: 'Genen Shouyu',
          },
          {
            id: 'Pavlova',
            title: 'Pavlova',
          },
          {
            id: 'Carnarvon Tigers',
            title: 'Carnarvon Tigers',
          },
          {
            id: 'Teatime Chocolate Biscuits',
            title: 'Teatime Chocolate Biscuits',
          },
          {
            id: "Sir Rodney's Marmalade",
            title: "Sir Rodney's Marmalade",
          },
          {
            id: "Sir Rodney's Scones",
            title: "Sir Rodney's Scones",
          },
          {
            id: "Gustaf's Knäckebröd",
            title: "Gustaf's Knäckebröd",
          },
          {
            id: 'Tunnbröd',
            title: 'Tunnbröd',
          },
          {
            id: 'NuNuCa Nuß-Nougat-Creme',
            title: 'NuNuCa Nuß-Nougat-Creme',
          },
          {
            id: 'Gumbär Gummibärchen',
            title: 'Gumbär Gummibärchen',
          },
          {
            id: 'Schoggi Schokolade',
            title: 'Schoggi Schokolade',
          },
          {
            id: 'Nord-Ost Matjeshering',
            title: 'Nord-Ost Matjeshering',
          },
          {
            id: 'Gorgonzola Telino',
            title: 'Gorgonzola Telino',
          },
          {
            id: 'Mascarpone Fabioli',
            title: 'Mascarpone Fabioli',
          },
          {
            id: 'Geitost',
            title: 'Geitost',
          },
          {
            id: 'Sasquatch Ale',
            title: 'Sasquatch Ale',
          },
          {
            id: 'Steeleye Stout',
            title: 'Steeleye Stout',
          },
          {
            id: 'Inlagd Sill',
            title: 'Inlagd Sill',
          },
          {
            id: 'Gravad lax',
            title: 'Gravad lax',
          },
          {
            id: 'Côte de Blaye',
            title: 'Côte de Blaye',
          },
          {
            id: 'Chartreuse verte',
            title: 'Chartreuse verte',
          },
          {
            id: 'Boston Crab Meat',
            title: 'Boston Crab Meat',
          },
          {
            id: "Jack's New England Clam Chowder",
            title: "Jack's New England Clam Chowder",
          },
          {
            id: 'Ipoh Coffee',
            title: 'Ipoh Coffee',
          },
          {
            id: 'Gula Malacca',
            title: 'Gula Malacca',
          },
          {
            id: 'Rogede sild',
            title: 'Rogede sild',
          },
          {
            id: 'Spegesild',
            title: 'Spegesild',
          },
          {
            id: 'Zaanse koeken',
            title: 'Zaanse koeken',
          },
          {
            id: 'Chocolade',
            title: 'Chocolade',
          },
          {
            id: 'Maxilaku',
            title: 'Maxilaku',
          },
          {
            id: 'Valkoinen suklaa',
            title: 'Valkoinen suklaa',
          },
          {
            id: 'Manjimup Dried Apples',
            title: 'Manjimup Dried Apples',
          },
          {
            id: 'Filo Mix',
            title: 'Filo Mix',
          },
          {
            id: 'Tourtière',
            title: 'Tourtière',
          },
          {
            id: 'Pâté chinois',
            title: 'Pâté chinois',
          },
          {
            id: 'Gnocchi di nonna Alice',
            title: 'Gnocchi di nonna Alice',
          },
          {
            id: 'Ravioli Angelo',
            title: 'Ravioli Angelo',
          },
          {
            id: 'Escargots de Bourgogne',
            title: 'Escargots de Bourgogne',
          },
          {
            id: 'Raclette Courdavault',
            title: 'Raclette Courdavault',
          },
          {
            id: 'Camembert Pierrot',
            title: 'Camembert Pierrot',
          },
          {
            id: "Sirop d'érable",
            title: "Sirop d'érable",
          },
          {
            id: 'Tarte au sucre',
            title: 'Tarte au sucre',
          },
          {
            id: 'Vegie-spread',
            title: 'Vegie-spread',
          },
          {
            id: 'Wimmers gute Semmelknödel',
            title: 'Wimmers gute Semmelknödel',
          },
          {
            id: 'Louisiana Fiery Hot Pepper Sauce',
            title: 'Louisiana Fiery Hot Pepper Sauce',
          },
          {
            id: 'Louisiana Hot Spiced Okra',
            title: 'Louisiana Hot Spiced Okra',
          },
          {
            id: 'Laughing Lumberjack Lager',
            title: 'Laughing Lumberjack Lager',
          },
          {
            id: 'Scottish Longbreads',
            title: 'Scottish Longbreads',
          },
          {
            id: 'Gudbrandsdalsost',
            title: 'Gudbrandsdalsost',
          },
          {
            id: 'Outback Lager',
            title: 'Outback Lager',
          },
          {
            id: 'Flotemysost',
            title: 'Flotemysost',
          },
          {
            id: 'Mozzarella di Giovanni',
            title: 'Mozzarella di Giovanni',
          },
          {
            id: 'Röd Kaviar',
            title: 'Röd Kaviar',
          },
          {
            id: 'Longlife Tofu',
            title: 'Longlife Tofu',
          },
          {
            id: 'Rhönbräu Klosterbier',
            title: 'Rhönbräu Klosterbier',
          },
          {
            id: 'Lakkalikööri',
            title: 'Lakkalikööri',
          },
          {
            id: 'Original Frankfurter grüne Soße',
            title: 'Original Frankfurter grüne Soße',
          },
        ],
        edges: [
          {source: 'Beverages', target: 'Chai', value: 41},
          {source: 'Beverages', target: 'Chang', value: 17},
          {source: 'Condiments', target: 'Aniseed Syrup', value: 13},
          {source: 'Condiments', target: "Chef Anton's Cajun Seasoning", value: 53},
          {
            source: 'Condiments',
            target: "Grandma's Boysenberry Spread",
            value: 120,
          },
          {source: 'Produce', target: "Uncle Bob's Organic Dried Pears", value: 15},
          {source: 'Condiments', target: 'Northwoods Cranberry Sauce', value: 6},
          {source: 'Seafood', target: 'Ikura', value: 31},
          {source: 'Dairy Products', target: 'Queso Cabrales', value: 22},
          {
            source: 'Dairy Products',
            target: 'Queso Manchego La Pastora',
            value: 86,
          },
          {source: 'Seafood', target: 'Konbu', value: 24},
          {source: 'Produce', target: 'Tofu', value: 35},
          {source: 'Condiments', target: 'Genen Shouyu', value: 39},
          {source: 'Confections', target: 'Pavlova', value: 29},
          {source: 'Seafood', target: 'Carnarvon Tigers', value: 42},
          {source: 'Confections', target: 'Teatime Chocolate Biscuits', value: 25},
          {source: 'Confections', target: "Sir Rodney's Marmalade", value: 40},
          {source: 'Confections', target: "Sir Rodney's Scones", value: 3},
          {source: 'Grains-Cereals', target: "Gustaf's Knäckebröd", value: 104},
          {source: 'Grains-Cereals', target: 'Tunnbröd', value: 61},
          {source: 'Confections', target: 'NuNuCa Nuß-Nougat-Creme', value: 76},
          {source: 'Confections', target: 'Gumbär Gummibärchen', value: 15},
          {source: 'Confections', target: 'Schoggi Schokolade', value: 49},
          {source: 'Seafood', target: 'Nord-Ost Matjeshering', value: 10},
          {source: 'Dairy Products', target: 'Gorgonzola Telino', value: 0},
          {source: 'Dairy Products', target: 'Mascarpone Fabioli', value: 9},
          {source: 'Dairy Products', target: 'Geitost', value: 112},
          {source: 'Beverages', target: 'Sasquatch Ale', value: 111},
          {source: 'Beverages', target: 'Steeleye Stout', value: 20},
          {source: 'Seafood', target: 'Inlagd Sill', value: 112},
          {source: 'Seafood', target: 'Gravad lax', value: 11},
          {source: 'Beverages', target: 'Côte de Blaye', value: 17},
          {source: 'Beverages', target: 'Chartreuse verte', value: 69},
          {source: 'Seafood', target: 'Boston Crab Meat', value: 150},
          {source: 'Seafood', target: "Jack's New England Clam Chowder", value: 85},
          {source: 'Beverages', target: 'Ipoh Coffee', value: 17},
          {source: 'Condiments', target: 'Gula Malacca', value: 27},
          {source: 'Seafood', target: 'Rogede sild', value: 5},
          {source: 'Seafood', target: 'Spegesild', value: 95},
          {source: 'Confections', target: 'Zaanse koeken', value: 36},
          {source: 'Confections', target: 'Chocolade', value: 15},
          {source: 'Confections', target: 'Maxilaku', value: 10},
          {source: 'Confections', target: 'Valkoinen suklaa', value: 65},
          {source: 'Produce', target: 'Manjimup Dried Apples', value: 20},
          {source: 'Grains-Cereals', target: 'Filo Mix', value: 38},
          {source: 'Meat-Poultry', target: 'Tourtière', value: 21},
          {source: 'Meat-Poultry', target: 'Pâté chinois', value: 150},
          {source: 'Grains-Cereals', target: 'Gnocchi di nonna Alice', value: 21},
          {source: 'Grains-Cereals', target: 'Ravioli Angelo', value: 36},
          {source: 'Seafood', target: 'Escargots de Bourgogne', value: 62},
          {source: 'Dairy Products', target: 'Raclette Courdavault', value: 79},
          {source: 'Dairy Products', target: 'Camembert Pierrot', value: 19},
          {source: 'Condiments', target: "Sirop d'érable", value: 150},
          {source: 'Confections', target: 'Tarte au sucre', value: 17},
          {source: 'Condiments', target: 'Vegie-spread', value: 24},
          {
            source: 'Grains-Cereals',
            target: 'Wimmers gute Semmelknödel',
            value: 22,
          },
          {
            source: 'Condiments',
            target: 'Louisiana Fiery Hot Pepper Sauce',
            value: 76,
          },
          {source: 'Condiments', target: 'Louisiana Hot Spiced Okra', value: 4},
          {source: 'Beverages', target: 'Laughing Lumberjack Lager', value: 52},
          {source: 'Confections', target: 'Scottish Longbreads', value: 6},
          {source: 'Dairy Products', target: 'Gudbrandsdalsost', value: 26},
          {source: 'Beverages', target: 'Outback Lager', value: 15},
          {source: 'Dairy Products', target: 'Flotemysost', value: 26},
          {source: 'Dairy Products', target: 'Mozzarella di Giovanni', value: 14},
          {source: 'Seafood', target: 'Röd Kaviar', value: 101},
          {source: 'Produce', target: 'Longlife Tofu', value: 4},
          {source: 'Beverages', target: 'Rhönbräu Klosterbier', value: 150},
          {source: 'Beverages', target: 'Lakkalikööri', value: 57},
          {
            source: 'Condiments',
            target: 'Original Frankfurter grüne Soße',
            value: 32,
          },
        ],
      };
      const graphOptions = {
        nodeWidth: 20,
        fontFamily: 'Quicksand, sans-serif',
        fontWeight: '600',
        fontSize: '10px',
        spacing: 20,
      };
      const s = new ApexSankey(document.getElementById('svg-sankey'), graphOptions);
      s.render(data);
    </script>
  </body>
</html>
