import{c as d,i as c}from"../../admin.bundle-DI9_jvUJ.js";/* empty css                               */import{A as m}from"../../air-datepicker-DlUcrly3.js";import"../../main-Cyta4iCA.js";VirtualSelect.init({ele:"#departmentSelect",options:[{label:"Radiology",value:"Radiology"},{label:"Orthopedics",value:"Orthopedics"},{label:"Neurology",value:"Neurology"},{label:"Cardiology",value:"Cardiology"},{label:"Pediatrics",value:"Pediatrics"},{label:"Manager",value:"Manager"},{label:"Nurse",value:"Nurse"},{label:"Security Officer",value:"Security Officer"},{label:"Others",value:"Others"}]});VirtualSelect.init({ele:"#staffSelect",options:[{label:"Radiology",value:"Radiology"},{label:"Orthopedics",value:"Orthopedics"},{label:"Neurology",value:"Neurology"},{label:"Cardiology",value:"Cardiology"},{label:"Pediatrics",value:"Pediatrics"},{label:"Manager",value:"Manager"},{label:"Nurse",value:"Nurse"},{label:"Security Officer",value:"Security Officer"},{label:"Others",value:"Others"}],selectedValue:"Radiology"});class g{constructor(e){this.tableBodyId=e.tableBodyId,this.paginationId=e.paginationId,this.paginationInfoId=e.paginationInfoId,this.formId=e.formId,this.modalId=e.modalId,this.deleteModalId=e.deleteModalId,this.itemsPerPage=e.itemsPerPage||10,this.currentPage=1,this.currentSortColumn=null,this.currentSortDirection="asc",this.data=[{id:"PES-1595",name:"Linda Sharp",role:"Merchandiser, retail",department:"Radiology",email:"<EMAIL>",phone:"****** 123 4567",joiningDate:"2021-02-20",image:"assets/images/avatar/user-3.png"},{id:"PES-1596",name:"Lori Reynolds",role:"Clinical molecular geneticist",department:"Orthopedics",email:"<EMAIL>",phone:"+44 20 7946 0958",joiningDate:"2020-01-26",image:"assets/images/avatar/user-2.png"},{id:"PES-1597",name:"Paige Williamson",role:"Product manager",department:"Orthopedics",email:"<EMAIL>",phone:"+61 2 9374 4000",joiningDate:"2022-12-30",image:""},{id:"PES-1598",name:"Russell Hunt",role:"Ophthalmologist",department:"Neurology",email:"<EMAIL>",phone:"+49 30 123456",joiningDate:"2021-09-21",image:"assets/images/avatar/user-24.png"},{id:"PES-1599",name:"Theresa Thomas",role:"Cytogeneticist",department:"Orthopedics",email:"<EMAIL>",phone:"+33 1 42 68 53 00",joiningDate:"2020-09-04",image:"assets/images/avatar/user-25.png"},{id:"PES-1600",name:"Caitlin Werner",role:"Geneticist, molecular",department:"Orthopedics",email:"<EMAIL>",phone:"+34 91 123 45 67",joiningDate:"2020-03-07",image:"assets/images/avatar/user-26.png"},{id:"PES-1601",name:"Stephanie Nguyen",role:"Multimedia programmer",department:"Cardiology",email:"<EMAIL>",phone:"+49 40 123456",joiningDate:"2022-04-28",image:""},{id:"PES-1602",name:"Kelli Miller",role:"Mechanical engineer",department:"Cardiology",email:"<EMAIL>",phone:"+81 3 1234 5678",joiningDate:"2020-06-09",image:"assets/images/avatar/user-27.png"},{id:"PES-1603",name:"Nicholas Velasquez",role:"Accounting technician",department:"Radiology",email:"<EMAIL>",phone:"+91 22 1234 5678",joiningDate:"2021-02-19",image:""},{id:"PES-1604",name:"Lydia James",role:"Surveyor, commercial/residential",department:"Radiology",email:"<EMAIL>",phone:"+55 11 1234-5678",joiningDate:"2020-08-09",image:"assets/images/avatar/user-28.png"},{id:"PES-1605",name:"Robert Torres",role:"Nurse, children's",department:"Radiology",email:"<EMAIL>",phone:"****** 123-45-67",joiningDate:"2021-04-13",image:"assets/images/avatar/user-10.png"},{id:"PES-1606",name:"Samantha Mcdaniel",role:"Claims inspector/assessor",department:"Pediatrics",email:"<EMAIL>",phone:"+27 11 123 4567",joiningDate:"2021-11-27",image:"assets/images/avatar/user-16.png"},{id:"PES-1607",name:"Dr. Phillip Carr DDS",role:"Corporate investment banker",department:"Orthopedics",email:"<EMAIL>",phone:"+65 6 1234 567",joiningDate:"2023-05-27",image:""},{id:"PES-1608",name:"Kelly Schneider",role:"Social researcher",department:"Orthopedics",email:"<EMAIL>",phone:"+39 06 123 4567",joiningDate:"2022-03-25",image:""},{id:"PES-1609",name:"Brittany Arnold",role:"Waste management officer",department:"Orthopedics",email:"<EMAIL>",phone:"+52 55 1234 5678",joiningDate:"2020-08-28",image:"assets/images/avatar/user-19.png"},{id:"PES-1610",name:"Heather Myers",role:"Designer",department:"Pediatrics",email:"<EMAIL>",phone:"+86 10 1234 5678",joiningDate:"2024-05-22",image:"assets/images/avatar/user-15.png"},{id:"PES-1611",name:"Elizabeth Martinez",role:"Media buyer",department:"Pediatrics",email:"<EMAIL>",phone:"+31 20 123 4567",joiningDate:"2021-04-29",image:""}],this.bindEvents(),this.setupImageUpload(),this.renderTable(),this.setupSortableColumns()}bindEvents(){document.getElementById(this.formId).addEventListener("submit",e=>{e.preventDefault(),this.saveStaff()}),document.getElementById("confirmDeleteBtn").addEventListener("click",()=>{const e=document.getElementById("deleteStaffId").value;this.deleteStaff(e)}),document.getElementById("addStaffBtn").addEventListener("click",()=>{this.resetForm()})}setupImageUpload(){const e=document.getElementById("imageInput"),t=document.getElementById("imagePreview"),n=document.getElementById("uploadIcon"),i=e.parentElement,a=document.getElementById("staffImagePath");i.addEventListener("click",()=>{e.click()}),e.addEventListener("change",o=>{if(o.target.files&&o.target.files[0]){const l=new FileReader;l.onload=r=>{t.src=r.target.result,t.style.display="block",n.style.display="none",a.value=r.target.result},l.readAsDataURL(o.target.files[0])}})}resetForm(){document.getElementById("staffId").value="",document.getElementById("staffImagePath").value="",document.getElementById("staffForm").reset(),document.getElementById("saveStaffBtn").textContent="Add Staff";const e=document.getElementById("imagePreview"),t=document.getElementById("uploadIcon");e.style.display="none",t.style.display="block",e.src="";const n=document.querySelector("#addStaffModal .card-title");n&&(n.textContent="Add New Staff")}setupSortableColumns(){document.querySelectorAll(".sortable").forEach(t=>{if(t.addEventListener("click",()=>{const n=t.getAttribute("data-column");this.sortData(n)}),!t.querySelector(".sort-icon")){const n=document.createElement("span");n.className="sort-icon ms-1",n.innerHTML='<i class="ri-arrow-up-down-line text-muted fs-sm"></i>',t.appendChild(n)}})}sortData(e){this.currentSortColumn===e?this.currentSortDirection=this.currentSortDirection==="asc"?"desc":"asc":(this.currentSortColumn=e,this.currentSortDirection="asc"),this.updateSortIcons(e),e==="joiningDate"?this.data.sort((t,n)=>{const i=new Date(t[e]),a=new Date(n[e]);return this.currentSortDirection==="asc"?i-a:a-i}):e==="name"?this.data.sort((t,n)=>{const i=t[e].toLowerCase(),a=n[e].toLowerCase();return this.currentSortDirection==="asc"?i.localeCompare(a):a.localeCompare(i)}):this.data.sort((t,n)=>{const i=t[e]?t[e].toString().toLowerCase():"",a=n[e]?n[e].toString().toLowerCase():"";return this.currentSortDirection==="asc"?i.localeCompare(a):a.localeCompare(i)}),this.currentPage=1,this.renderTable()}updateSortIcons(e){document.querySelectorAll(".sortable").forEach(n=>{const i=n.getAttribute("data-column"),a=n.querySelector(".sort-icon");a&&(a.innerHTML='<i class="ri-arrow-up-down-line text-muted fs-sm"></i>',i===e&&(this.currentSortDirection==="asc"?a.innerHTML='<i class="ri-arrow-up-line"></i>':a.innerHTML='<i class="ri-arrow-down-line"></i>'))})}renderTable(){const e=document.getElementById(this.tableBodyId);e.innerHTML="";const t=(this.currentPage-1)*this.itemsPerPage,n=Math.min(t+this.itemsPerPage,this.data.length),i=this.data.slice(t,n);document.getElementById(this.paginationInfoId).innerHTML=`Showing <b class="me-1">${t+1}-${n}</b> of <b class="ms-1">${this.data.length}</b> Results`,i.length===0?e.innerHTML='<tr><td colspan="7" class="text-center">No staff records found</td></tr>':(i.forEach(a=>{const o=document.createElement("tr");o.innerHTML=`
                    <td>${a.id}</td>
                    <td>
                        <div class="d-flex align-items-center gap-3">
                            ${this.renderStaffAvatar(a)}
                            <div>
                                <h6 class="mb-1"><a href="#!" class="text-reset">${a.name}</a></h6>
                                <p class="fs-sm text-muted">${a.role}</p>
                            </div>
                        </div>
                    </td>
                    <td>${a.department}</td>
                    <td>${a.email}</td>
                    <td>${a.phone}</td>
                    <td>${this.formatDate(a.joiningDate)}</td>
                    <td>
                        <div class="d-flex align-items-center gap-2">
                            <button class="btn btn-sub-primary size-8 btn-icon edit-btn" data-id="${a.id}">
                                <i class="ri-pencil-line"></i>
                            </button>
                            <button class="btn btn-sub-danger size-8 btn-icon delete-btn" data-id="${a.id}">
                                <i class="ri-delete-bin-line"></i>
                            </button>
                        </div>
                    </td>
                `,e.appendChild(o)}),this.addActionButtonListeners()),this.renderPagination()}renderStaffAvatar(e){return e.image?`<img src="${e.image}" loading="lazy" alt="${e.name}" class="size-10 rounded-circle">`:`<div class="size-10 bg-light-subtle rounded-circle text-muted fs-sm fw-semibold d-flex justify-content-center align-items-center">
                        ${this.getInitials(e.name)}
                    </div>`}getInitials(e){return e.split(" ").map(t=>t.charAt(0)).join("").toUpperCase()}formatDate(e){const t=new Date(e),n={day:"numeric",month:"short",year:"numeric"};return t.toLocaleDateString("en-US",n)}addActionButtonListeners(){document.querySelectorAll(".edit-btn").forEach(e=>{e.addEventListener("click",()=>{const t=e.getAttribute("data-id");this.editStaff(t)})}),document.querySelectorAll(".delete-btn").forEach(e=>{e.addEventListener("click",()=>{const t=e.getAttribute("data-id");document.getElementById("deleteStaffId").value=t,new window.bootstrap.Modal(document.getElementById(this.deleteModalId)).show()})})}renderPagination(){const e=document.getElementById(this.paginationId);e.innerHTML="";const t=Math.ceil(this.data.length/this.itemsPerPage),n=document.createElement("li");n.className=`page-item ${this.currentPage===1?"disabled":""}`,n.innerHTML='<a class="page-link" href="#!"><i data-lucide="chevron-left" class="size-4"></i> Previous</a>',n.addEventListener("click",()=>{this.currentPage>1&&(this.currentPage--,this.renderTable())}),e.appendChild(n);for(let a=1;a<=t;a++){const o=document.createElement("li");o.className=`page-item ${a===this.currentPage?"active":""}`,o.innerHTML=`<a class="page-link" href="#!">${a}</a>`,o.addEventListener("click",()=>{this.currentPage=a,this.renderTable()}),e.appendChild(o)}const i=document.createElement("li");i.className=`page-item ${this.currentPage===t?"disabled":""}`,i.innerHTML='<a class="page-link" href="#!">Next <i data-lucide="chevron-right" class="size-4"></i></a>',i.addEventListener("click",()=>{this.currentPage<t&&(this.currentPage++,this.renderTable())}),e.appendChild(i),d({icons:c})}editStaff(e){const t=this.data.find(r=>r.id===e);if(!t)return;document.getElementById("staffId").value=t.id,document.getElementById("staffImagePath").value=t.image||"",document.getElementById("fullName").value=t.name,document.getElementById("role").value=t.role;try{window.VirtualSelect?VirtualSelect.setValue(document.querySelector("#departmentSelect"),t.department):document.querySelector("#departmentSelect").setValue(t.department)}catch(r){console.log("Could not set department dropdown value:",r)}document.getElementById("email").value=t.email,document.getElementById("phone").value=t.phone;const i=new Date(t.joiningDate).toLocaleDateString("en-GB",{day:"2-digit",month:"short",year:"numeric"}).replace(/ /g," ");document.getElementById("joiningDate").value=i;const a=document.getElementById("imagePreview"),o=document.getElementById("uploadIcon");t.image?(a.src=t.image,a.style.display="block",o.style.display="none"):(a.style.display="none",o.style.display="block"),document.getElementById("saveStaffBtn").textContent="Update Staff",new window.bootstrap.Modal(document.getElementById(this.modalId)).show()}saveStaff(){const e=document.getElementById("staffId").value,t=e!=="",n=document.getElementById("staffImagePath"),i=document.getElementById("imagePreview");let a;i.style.display!=="none"?a=n.value||i.src:a="";const o={name:document.getElementById("fullName").value,role:document.getElementById("role").value,department:document.getElementById("department").value,email:document.getElementById("email").value,phone:document.getElementById("phone").value,joiningDate:document.getElementById("joiningDate").value,image:a};if(t){const r=this.data.findIndex(s=>s.id===e);r!==-1&&(this.data[r]={...this.data[r],...o})}else{const r=`PES-${1599+this.data.length}`;this.data.unshift({id:r,...o})}window.bootstrap.Modal.getInstance(document.getElementById(this.modalId)).hide(),this.resetForm(),this.renderTable()}deleteStaff(e){this.data=this.data.filter(i=>i.id!==e),window.bootstrap.Modal.getInstance(document.getElementById(this.deleteModalId)).hide();const n=Math.ceil(this.data.length/this.itemsPerPage);this.currentPage>n&&n>0&&(this.currentPage=n),this.renderTable()}}new m("#joiningDate",{dateFormat:"dd MMM, yyyy",autoClose:!0,position:"bottom left",locale:{days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],daysShort:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],daysMin:["Su","Mo","Tu","We","Th","Fr","Sa"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],monthsShort:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],today:"Today",clear:"Clear",firstDay:0}});document.addEventListener("DOMContentLoaded",()=>{new g({tableBodyId:"staffTableBody",paginationId:"pagination",paginationInfoId:"paginationInfo",formId:"staffForm",modalId:"addStaffModal",deleteModalId:"deleteModal",itemsPerPage:10})});
