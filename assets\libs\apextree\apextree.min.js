!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).ApexTree=e()}(this,(function(){"use strict";function t(t){var e=0,n=t.children,i=n&&n.length;if(i)for(;--i>=0;)e+=n[i].value;else e=1;t.value=e}function e(t,e){var i,o,h,a,l,u=new s(t),c=+t.value&&(u.value=t.value),d=[u];for(null==e&&(e=n);i=d.pop();)if(c&&(i.value=+i.data.value),(h=e(i.data))&&(l=h.length))for(i.children=new Array(l),a=l-1;a>=0;--a)d.push(o=i.children[a]=new s(h[a])),o.parent=i,o.depth=i.depth+1;return u.eachBefore(r)}function n(t){return t.children}function i(t){t.data=t.data.data}function r(t){var e=0;do{t.height=e}while((t=t.parent)&&t.height<++e)}function s(t){this.data=t,this.depth=this.height=0,this.parent=null}s.prototype=e.prototype={constructor:s,count:function(){return this.eachAfter(t)},each:function(t){var e,n,i,r,s=this,o=[s];do{for(e=o.reverse(),o=[];s=e.pop();)if(t(s),n=s.children)for(i=0,r=n.length;i<r;++i)o.push(n[i])}while(o.length);return this},eachAfter:function(t){for(var e,n,i,r=this,s=[r],o=[];r=s.pop();)if(o.push(r),e=r.children)for(n=0,i=e.length;n<i;++n)s.push(e[n]);for(;r=o.pop();)t(r);return this},eachBefore:function(t){for(var e,n,i=this,r=[i];i=r.pop();)if(t(i),e=i.children)for(n=e.length-1;n>=0;--n)r.push(e[n]);return this},sum:function(t){return this.eachAfter((function(e){for(var n=+t(e.data)||0,i=e.children,r=i&&i.length;--r>=0;)n+=i[r].value;e.value=n}))},sort:function(t){return this.eachBefore((function(e){e.children&&e.children.sort(t)}))},path:function(t){for(var e=this,n=function(t,e){if(t===e)return t;var n=t.ancestors(),i=e.ancestors(),r=null;t=n.pop(),e=i.pop();for(;t===e;)r=t,t=n.pop(),e=i.pop();return r}(e,t),i=[e];e!==n;)e=e.parent,i.push(e);for(var r=i.length;t!==n;)i.splice(r,0,t),t=t.parent;return i},ancestors:function(){for(var t=this,e=[t];t=t.parent;)e.push(t);return e},descendants:function(){var t=[];return this.each((function(e){t.push(e)})),t},leaves:function(){var t=[];return this.eachBefore((function(e){e.children||t.push(e)})),t},links:function(){var t=this,e=[];return t.each((function(n){n!==t&&e.push({source:n.parent,target:n})})),e},copy:function(){return e(this).eachBefore(i)}};const o={name:"d3-flextree",version:"2.1.2",main:"build/d3-flextree.js",module:"index","jsnext:main":"index",author:{name:"Chris Maloney",url:"http://chrismaloney.org"},description:"Flexible tree layout algorithm that allows for variable node sizes.",keywords:["d3","d3-module","layout","tree","hierarchy","d3-hierarchy","plugin","d3-plugin","infovis","visualization","2d"],homepage:"https://github.com/klortho/d3-flextree",license:"WTFPL",repository:{type:"git",url:"https://github.com/klortho/d3-flextree.git"},scripts:{clean:"rm -rf build demo test","build:demo":"rollup -c --environment BUILD:demo","build:dev":"rollup -c --environment BUILD:dev","build:prod":"rollup -c --environment BUILD:prod","build:test":"rollup -c --environment BUILD:test",build:"rollup -c",lint:"eslint index.js src","test:main":"node test/bundle.js","test:browser":"node test/browser-tests.js",test:"npm-run-all test:*",prepare:"npm-run-all clean build lint test"},dependencies:{"d3-hierarchy":"^1.1.5"},devDependencies:{"babel-plugin-external-helpers":"^6.22.0","babel-preset-es2015-rollup":"^3.0.0",d3:"^4.13.0","d3-selection-multi":"^1.0.1",eslint:"^4.19.1",jsdom:"^11.6.2","npm-run-all":"^4.1.2",rollup:"^0.55.3","rollup-plugin-babel":"^2.7.1","rollup-plugin-commonjs":"^8.0.2","rollup-plugin-copy":"^0.2.3","rollup-plugin-json":"^2.3.0","rollup-plugin-node-resolve":"^3.0.2","rollup-plugin-uglify":"^3.0.0","uglify-es":"^3.3.9"}},{version:h}=o,a=Object.freeze({children:t=>t.children,nodeSize:t=>t.data.size,spacing:0});function l(t){const n=Object.assign({},a,t);function i(t){const e=n[t];return"function"==typeof e?e:()=>e}function r(t){const e=o(function(){const t=s(),e=i("nodeSize"),n=i("spacing");return class extends t{constructor(t){super(t),Object.assign(this,{x:0,y:0,relX:0,prelim:0,shift:0,change:0,lExt:this,lExtRelX:0,lThr:null,rExt:this,rExtRelX:0,rThr:null})}get size(){return e(this.data)}spacing(t){return n(this.data,t.data)}get x(){return this.data.x}set x(t){this.data.x=t}get y(){return this.data.y}set y(t){this.data.y=t}update(){return u(this),c(this),this}}}(),t,(t=>t.children));return e.update(),e.data}function s(){const t=i("nodeSize"),n=i("spacing");return class i extends e.prototype.constructor{constructor(t){super(t)}copy(){const t=o(this.constructor,this,(t=>t.children));return t.each((t=>t.data=t.data.data)),t}get size(){return t(this)}spacing(t){return n(this,t)}get nodes(){return this.descendants()}get xSize(){return this.size[0]}get ySize(){return this.size[1]}get top(){return this.y}get bottom(){return this.y+this.ySize}get left(){return this.x-this.xSize/2}get right(){return this.x+this.xSize/2}get root(){const t=this.ancestors();return t[t.length-1]}get numChildren(){return this.hasChildren?this.children.length:0}get hasChildren(){return!this.noChildren}get noChildren(){return null===this.children}get firstChild(){return this.hasChildren?this.children[0]:null}get lastChild(){return this.hasChildren?this.children[this.numChildren-1]:null}get extents(){return(this.children||[]).reduce(((t,e)=>i.maxExtents(t,e.extents)),this.nodeExtents)}get nodeExtents(){return{top:this.top,bottom:this.bottom,left:this.left,right:this.right}}static maxExtents(t,e){return{top:Math.min(t.top,e.top),bottom:Math.max(t.bottom,e.bottom),left:Math.min(t.left,e.left),right:Math.max(t.right,e.right)}}}}function o(t,e,n){const i=(e,r)=>{const s=new t(e);Object.assign(s,{parent:r,depth:null===r?0:r.depth+1,height:0,length:1});const o=n(e)||[];return s.children=0===o.length?null:o.map((t=>i(t,s))),s.children&&Object.assign(s,s.children.reduce(((t,e)=>({height:Math.max(t.height,e.height+1),length:t.length+e.length})),s)),s};return i(e,null)}return Object.assign(r,{nodeSize(t){return arguments.length?(n.nodeSize=t,r):n.nodeSize},spacing(t){return arguments.length?(n.spacing=t,r):n.spacing},children(t){return arguments.length?(n.children=t,r):n.children},hierarchy(t,e){const i=void 0===e?n.children:e;return o(s(),t,i)},dump(t){const e=i("nodeSize"),n=t=>i=>{const r=t+"  ",s=t+"    ",{x:o,y:h}=i,a=e(i),l=i.children||[],u=0===l.length?" ":`,${r}children: [${s}${l.map(n(s)).join(s)}${r}],${t}`;return`{ size: [${a.join(", ")}],${r}x: ${o}, y: ${h}${u}},`};return n("\n")(t)}}),r}l.version=h;const u=(t,e=0)=>(t.y=e,(t.children||[]).reduce(((e,n)=>{const[i,r]=e;u(n,t.y+t.ySize);const s=(0===i?n.lExt:n.rExt).bottom;0!==i&&p(t,i,r);return[i+1,_(s,i,r)]}),[0,null]),d(t),b(t),t),c=(t,e,n)=>{void 0===e&&(e=-t.relX-t.prelim,n=0);const i=e+t.relX;return t.relX=i+t.prelim-n,t.prelim=0,t.x=n+t.relX,(t.children||[]).forEach((e=>c(e,i,t.x))),t},d=t=>{(t.children||[]).reduce(((t,e)=>{const[n,i]=t,r=n+e.shift,s=i+r+e.change;return e.relX+=s,[r,s]}),[0,0])},p=(t,e,n)=>{const i=t.children[e-1],r=t.children[e];let s=i,o=i.relX,h=r,a=r.relX,l=!0;for(;s&&h;){s.bottom>n.lowY&&(n=n.next);const i=o+s.prelim-(a+h.prelim)+s.xSize/2+h.xSize/2+s.spacing(h);(i>0||i<0&&l)&&(a+=i,f(r,i),m(t,e,n.index,i)),l=!1;const u=s.bottom,c=h.bottom;u<=c&&(s=y(s),s&&(o+=s.relX)),u>=c&&(h=g(h),h&&(a+=h.relX))}!s&&h?x(t,e,h,a):s&&!h&&w(t,e,s,o)},f=(t,e)=>{t.relX+=e,t.lExtRelX+=e,t.rExtRelX+=e},m=(t,e,n,i)=>{const r=t.children[e],s=e-n;if(s>1){const e=i/s;t.children[n+1].shift+=e,r.shift-=e,r.change-=i-e}},g=t=>t.hasChildren?t.firstChild:t.lThr,y=t=>t.hasChildren?t.lastChild:t.rThr,x=(t,e,n,i)=>{const r=t.firstChild,s=r.lExt,o=t.children[e];s.lThr=n;const h=i-n.relX-r.lExtRelX;s.relX+=h,s.prelim-=h,r.lExt=o.lExt,r.lExtRelX=o.lExtRelX},w=(t,e,n,i)=>{const r=t.children[e],s=r.rExt,o=t.children[e-1];s.rThr=n;const h=i-n.relX-r.rExtRelX;s.relX+=h,s.prelim-=h,r.rExt=o.rExt,r.rExtRelX=o.rExtRelX},b=t=>{if(t.hasChildren){const e=t.firstChild,n=t.lastChild,i=(e.prelim+e.relX-e.xSize/2+n.relX+n.prelim+n.xSize/2)/2;Object.assign(t,{prelim:i,lExt:e.lExt,lExtRelX:e.lExtRelX,rExt:n.rExt,rExtRelX:n.rExtRelX})}},_=(t,e,n)=>{for(;null!==n&&t>=n.lowY;)n=n.next;return{lowY:t,index:e,next:n}},v=(t,e,n)=>{const i=t.x,r=t.y,s=e.x,o=e.y,h=(null==n?void 0:n.x)??i,a=(null==n?void 0:n.y)??r,l=s-i<0?-1:1,u=o-r<0?-1:1;let c=Math.abs(s-i)/2<35?Math.abs(s-i)/2:35;c=Math.abs(o-r)/2<c?Math.abs(o-r)/2:c;const d=Math.abs(s-i)/2-c;return[`M ${h} ${a}`,`L ${h} ${r}`,`L ${i} ${r}`,`L ${i+d*l} ${r}`,`C ${i+d*l+c*l} ${r} ${i+d*l+c*l} ${r} ${i+d*l+c*l} ${r+c*u}`,`L ${i+d*l+c*l} ${o-c*u}`,`C ${i+d*l+c*l} ${o} ${i+d*l+c*l} ${o} ${s-d*l} ${o}`,`L ${s} ${o}`].join(" ")},M=(t,e,n,i={sy:0})=>{const r=t.x;let s=t.y;const o=e.x,h=e.y,a=(null==n?void 0:n.x)??r,l=(null==n?void 0:n.y)??s,u=o-r<0?-1:1,c=h-s<0?-1:1;s+=i.sy;let d=Math.abs(o-r)/2<35?Math.abs(o-r)/2:35;d=Math.abs(h-s)/2<d?Math.abs(h-s)/2:d;const p=Math.abs(h-s)/2-d;return[`M ${a} ${l}`,`L ${r} ${l}`,`L ${r} ${s}`,`L ${r} ${s+p*c}`,`C  ${r} ${s+p*c+d*c} ${r} ${s+p*c+d*c} ${r+d*u} ${s+p*c+d*c}`,`L ${r+(Math.abs(o-r)-2*d)*u+d*u} ${s+p*c+d*c}`,`C  ${o} ${s+p*c+d*c} ${o} ${s+p*c+d*c} ${o} ${h-p*c}`,`L ${o} ${h}`].join(" ")},S={top:{containerX:({width:t})=>t/2,containerY:()=>0,edgeX:({node:t,nodeWidth:e})=>t.x+e/2,edgeY:({node:t})=>t.y,edgeMidX:({node:t,nodeWidth:e})=>t.x+e/2,edgeMidY:({node:t})=>t.y,edgeParentX:({parent:t,nodeWidth:e})=>t.x+e/2,edgeParentY:({parent:t,nodeHeight:e})=>t.y+e,nodeFlexSize:({nodeWidth:t,nodeHeight:e,siblingSpacing:n,childrenSpacing:i})=>[t+n,e+i],calculateEdge:M,swap:t=>({x:t.left,y:t.top}),viewBoxDimensions:({rootNode:t,childrenSpacing:e,siblingSpacing:n})=>{if(!t)return{x:0,y:0,width:0,height:0};const{left:i,top:r,right:s,bottom:o}=t.extents,h=Math.abs(i)+Math.abs(s),a=Math.abs(r)+Math.abs(o);return{x:-(Math.abs(i)+n/2),y:-((t.ySize-e)/2),width:h,height:a}}},bottom:{containerX:({width:t})=>t/2,containerY:({height:t,nodeHeight:e})=>t-e-10,edgeX:({node:t,nodeWidth:e})=>t.x+e/2,edgeY:({node:t,nodeHeight:e})=>t.y+e,edgeMidX:({node:t,nodeWidth:e})=>t.x+e/2,edgeMidY:({node:t,nodeHeight:e})=>t.y+e,edgeParentX:({parent:t,nodeWidth:e})=>t.x+e/2,edgeParentY:({parent:t})=>t.y,nodeFlexSize:({nodeWidth:t,nodeHeight:e,siblingSpacing:n,childrenSpacing:i})=>[t+n,e+i],calculateEdge:M,swap:t=>({...t,y:-t.y}),viewBoxDimensions:({rootNode:t,childrenSpacing:e,siblingSpacing:n})=>{if(!t)return{x:0,y:0,width:0,height:0};const{left:i,top:r,right:s,bottom:o}=t.extents,h=Math.abs(i)+Math.abs(s),a=Math.abs(r)+Math.abs(o);return{x:-(Math.abs(i)-(t.xSize-n)/2),y:-(a-t.ySize+e/2),width:h,height:a}}},left:{containerX:()=>10,containerY:({height:t})=>t/2,edgeX:({node:t})=>t.x,edgeY:({node:t,nodeHeight:e})=>t.y+e/2,edgeMidX:({node:t})=>t.x,edgeMidY:({node:t,nodeHeight:e})=>t.y+e/2,edgeParentX:({parent:t,nodeWidth:e})=>t.x+e,edgeParentY:({parent:t,nodeHeight:e})=>t.y+e/2,nodeFlexSize:({nodeWidth:t,nodeHeight:e,siblingSpacing:n,childrenSpacing:i})=>[e+n,t+i],calculateEdge:v,swap:t=>({...t,x:t.y,y:t.x}),viewBoxDimensions:({rootNode:t,childrenSpacing:e,siblingSpacing:n})=>{if(!t)return{x:0,y:0,width:0,height:0};const{left:i,top:r,right:s,bottom:o}=t.extents,h=Math.abs(r)+Math.abs(o),a=Math.abs(i)+Math.abs(s);return{x:-(Math.abs(r)+e/2),y:-(Math.abs(i)-n),width:h,height:a}}},right:{containerX:({width:t,nodeWidth:e})=>t-e-10,containerY:({height:t})=>t/2,edgeX:({node:t,nodeWidth:e})=>t.x+e,edgeY:({node:t,nodeHeight:e})=>t.y+e/2,edgeMidX:({node:t,nodeWidth:e})=>t.x+e,edgeMidY:({node:t,nodeHeight:e})=>t.y+e/2,edgeParentX:({parent:t})=>t.x,edgeParentY:({parent:t,nodeHeight:e})=>t.y+e/2,nodeFlexSize:({nodeWidth:t,nodeHeight:e,siblingSpacing:n,childrenSpacing:i})=>[e+n,t+i],calculateEdge:v,swap:t=>({...t,x:-t.y,y:t.x}),viewBoxDimensions:({rootNode:t,siblingSpacing:e,childrenSpacing:n})=>{if(!t)return{x:0,y:0,width:0,height:0};const{left:i,top:r,right:s,bottom:o}=t.extents,h=Math.abs(r)+Math.abs(o),a=Math.abs(i)+Math.abs(s);return{x:-(h-t.ySize+n/2),y:-(Math.abs(i)-e),width:h,height:a}}}},C=(t,e={})=>{for(const n in e)null==t||t.setAttribute(n,e[n])},E=(t,e,n,i)=>{var r;const s=null==e?void 0:e.data.options,o=(null==s?void 0:s.borderWidth)||i.borderWidth;let h=(null==s?void 0:s.borderColor)||i.borderColor,a=(null==s?void 0:s.nodeBGColor)||i.nodeBGColor;n&&(h=(null==s?void 0:s.borderColorHover)||i.borderColorHover,a=(null==s?void 0:s.nodeBGColorHover)||i.nodeBGColorHover);const l=document.querySelector(`[data-self='${e.data.id}'] foreignObject div`);if(l&&(l.style.borderWidth=`${o}px`,l.style.borderColor=h,l.style.backgroundColor=a),e.parent){const s=document.getElementById(`${e.data.id}-${null==(r=e.parent)?void 0:r.data.id}`);C(s,n?{"stroke-width":i.edgeWidth+1,stroke:i.edgeColorHover}:{"stroke-width":i.edgeWidth,stroke:i.edgeColor}),e.parent&&E(t,e.parent,n,i)}},A=(t={})=>{const e=[];for(const n in t){const i=`${O(n)}: ${t[n]};`;e.push(i)}return e.join(" ")},T=(t="",e,n="")=>{const i=document.getElementById(t);e?null==i||i.setAttribute("style",e):null==i||i.removeAttribute("style"),(null==i?void 0:i.innerHTML.replaceAll("'",'"'))!==n.replaceAll("'",'"')&&i&&(i.innerHTML=n)},O=t=>t.replace(/[A-Z]+(?![a-z])|[A-Z]/g,((t,e)=>(e?"-":"")+t.toLowerCase())),k={},R=[];function N(t,e){if(Array.isArray(t))for(const n of t)N(n,e);else if("object"!=typeof t)X(Object.getOwnPropertyNames(e)),k[t]=Object.assign(k[t]||{},e);else for(const n in t)N(n,t[n])}function z(t){return k[t]||{}}function X(t){R.push(...t)}function I(t,e){let n;const i=t.length,r=[];for(n=0;n<i;n++)r.push(e(t[n]));return r}function j(t){return t%360*Math.PI/180}function P(t){return t.charAt(0).toUpperCase()+t.slice(1)}function $(t,e,n,i){return null!=e&&null!=n||(i=i||t.bbox(),null==e?e=i.width/i.height*n:null==n&&(n=i.height/i.width*e)),{width:e,height:n}}function L(t,e){const n=t.origin;let i=null!=t.ox?t.ox:null!=t.originX?t.originX:"center",r=null!=t.oy?t.oy:null!=t.originY?t.originY:"center";null!=n&&([i,r]=Array.isArray(n)?n:"object"==typeof n?[n.x,n.y]:[n,n]);const s="string"==typeof i,o="string"==typeof r;if(s||o){const{height:t,width:n,x:h,y:a}=e.bbox();s&&(i=i.includes("left")?h:i.includes("right")?h+n:h+n/2),o&&(r=r.includes("top")?a:r.includes("bottom")?a+t:a+t/2)}return[i,r]}const D=new Set(["desc","metadata","title"]),Y=t=>D.has(t.nodeName),F=(t,e,n={})=>{const i={...e};for(const r in i)i[r].valueOf()===n[r]&&delete i[r];Object.keys(i).length?t.node.setAttribute("data-svgjs",JSON.stringify(i)):(t.node.removeAttribute("data-svgjs"),t.node.removeAttribute("svgjs:data"))},B="http://www.w3.org/2000/svg",V="http://www.w3.org/2000/xmlns/",H="http://www.w3.org/1999/xlink",G={window:"undefined"==typeof window?null:window,document:"undefined"==typeof document?null:document};class W{}const q={},Z="___SYMBOL___ROOT___";function U(t,e=B){return G.document.createElementNS(e,t)}function Q(t,e=!1){if(t instanceof W)return t;if("object"==typeof t)return tt(t);if(null==t)return new q[Z];if("string"==typeof t&&"<"!==t.charAt(0))return tt(G.document.querySelector(t));const n=e?G.document.createElement("div"):U("svg");return n.innerHTML=t,t=tt(n.firstChild),n.removeChild(n.firstChild),t}function J(t,e){return e&&(e instanceof G.window.Node||e.ownerDocument&&e instanceof e.ownerDocument.defaultView.Node)?e:U(t)}function K(t){if(!t)return null;if(t.instance instanceof W)return t.instance;if("#document-fragment"===t.nodeName)return new q.Fragment(t);let e=P(t.nodeName||"Dom");return"LinearGradient"===e||"RadialGradient"===e?e="Gradient":q[e]||(e="Dom"),new q[e](t)}let tt=K;function et(t,e=t.name,n=!1){return q[e]=t,n&&(q[Z]=t),X(Object.getOwnPropertyNames(t.prototype)),t}let nt=1e3;function it(t){return"Svgjs"+P(t)+nt++}function rt(t){for(let e=t.children.length-1;e>=0;e--)rt(t.children[e]);return t.id?(t.id=it(t.nodeName),t):t}function st(t,e){let n,i;for(i=(t=Array.isArray(t)?t:[t]).length-1;i>=0;i--)for(n in e)t[i].prototype[n]=e[n]}function ot(t){return function(...e){const n=e[e.length-1];return!n||n.constructor!==Object||n instanceof Array?t.apply(this,e):t.apply(this,e.slice(0,-1)).attr(n)}}N("Dom",{siblings:function(){return this.parent().children()},position:function(){return this.parent().index(this)},next:function(){return this.siblings()[this.position()+1]},prev:function(){return this.siblings()[this.position()-1]},forward:function(){const t=this.position();return this.parent().add(this.remove(),t+1),this},backward:function(){const t=this.position();return this.parent().add(this.remove(),t?t-1:0),this},front:function(){return this.parent().add(this.remove()),this},back:function(){return this.parent().add(this.remove(),0),this},before:function(t){(t=Q(t)).remove();const e=this.position();return this.parent().add(t,e),this},after:function(t){(t=Q(t)).remove();const e=this.position();return this.parent().add(t,e+1),this},insertBefore:function(t){return(t=Q(t)).before(this),this},insertAfter:function(t){return(t=Q(t)).after(this),this}});const ht=/^([+-]?(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?)([a-z%]*)$/i,at=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,lt=/rgb\((\d+),(\d+),(\d+)\)/,ut=/(#[a-z_][a-z0-9\-_]*)/i,ct=/\)\s*,?\s*/,dt=/\s/g,pt=/^#[a-f0-9]{3}$|^#[a-f0-9]{6}$/i,ft=/^rgb\(/,mt=/^(\s+)?$/,gt=/^[+-]?(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,yt=/\.(jpg|jpeg|png|gif|svg)(\?[^=]+.*)?/i,xt=/[\s,]+/,wt=/[MLHVCSQTAZ]/i;function bt(t){const e=Math.round(t),n=Math.max(0,Math.min(255,e)).toString(16);return 1===n.length?"0"+n:n}function _t(t,e){for(let n=e.length;n--;)if(null==t[e[n]])return!1;return!0}function vt(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+6*(e-t)*n:n<.5?e:n<2/3?t+(e-t)*(2/3-n)*6:t}N("Dom",{classes:function(){const t=this.attr("class");return null==t?[]:t.trim().split(xt)},hasClass:function(t){return-1!==this.classes().indexOf(t)},addClass:function(t){if(!this.hasClass(t)){const e=this.classes();e.push(t),this.attr("class",e.join(" "))}return this},removeClass:function(t){return this.hasClass(t)&&this.attr("class",this.classes().filter((function(e){return e!==t})).join(" ")),this},toggleClass:function(t){return this.hasClass(t)?this.removeClass(t):this.addClass(t)}}),N("Dom",{css:function(t,e){const n={};if(0===arguments.length)return this.node.style.cssText.split(/\s*;\s*/).filter((function(t){return!!t.length})).forEach((function(t){const e=t.split(/\s*:\s*/);n[e[0]]=e[1]})),n;if(arguments.length<2){if(Array.isArray(t)){for(const e of t){const t=e;n[e]=this.node.style.getPropertyValue(t)}return n}if("string"==typeof t)return this.node.style.getPropertyValue(t);if("object"==typeof t)for(const e in t)this.node.style.setProperty(e,null==t[e]||mt.test(t[e])?"":t[e])}return 2===arguments.length&&this.node.style.setProperty(t,null==e||mt.test(e)?"":e),this},show:function(){return this.css("display","")},hide:function(){return this.css("display","none")},visible:function(){return"none"!==this.css("display")}}),N("Dom",{data:function(t,e,n){if(null==t)return this.data(I(function(t,e){let n;const i=t.length,r=[];for(n=0;n<i;n++)e(t[n])&&r.push(t[n]);return r}(this.node.attributes,(t=>0===t.nodeName.indexOf("data-"))),(t=>t.nodeName.slice(5))));if(t instanceof Array){const e={};for(const n of t)e[n]=this.data(n);return e}if("object"==typeof t)for(e in t)this.data(e,t[e]);else if(arguments.length<2)try{return JSON.parse(this.attr("data-"+t))}catch(i){return this.attr("data-"+t)}else this.attr("data-"+t,null===e?null:!0===n||"string"==typeof e||"number"==typeof e?e:JSON.stringify(e));return this}}),N("Dom",{remember:function(t,e){if("object"==typeof arguments[0])for(const n in t)this.remember(n,t[n]);else{if(1===arguments.length)return this.memory()[t];this.memory()[t]=e}return this},forget:function(){if(0===arguments.length)this._memory={};else for(let t=arguments.length-1;t>=0;t--)delete this.memory()[arguments[t]];return this},memory:function(){return this._memory=this._memory||{}}});class Mt{constructor(...t){this.init(...t)}static isColor(t){return t&&(t instanceof Mt||this.isRgb(t)||this.test(t))}static isRgb(t){return t&&"number"==typeof t.r&&"number"==typeof t.g&&"number"==typeof t.b}static random(t="vibrant",e){const{random:n,round:i,sin:r,PI:s}=Math;if("vibrant"===t){const t=24*n()+57,e=38*n()+45,i=360*n();return new Mt(t,e,i,"lch")}if("sine"===t){const t=i(80*r(2*s*(e=null==e?n():e)/.5+.01)+150),o=i(50*r(2*s*e/.5****)+200),h=i(100*r(2*s*e/.5****)+150);return new Mt(t,o,h)}if("pastel"===t){const t=8*n()+86,e=17*n()+9,i=360*n();return new Mt(t,e,i,"lch")}if("dark"===t){const t=10+10*n(),e=50*n()+86,i=360*n();return new Mt(t,e,i,"lch")}if("rgb"===t){const t=255*n(),e=255*n(),i=255*n();return new Mt(t,e,i)}if("lab"===t){const t=100*n(),e=256*n()-128,i=256*n()-128;return new Mt(t,e,i,"lab")}if("grey"===t){const t=255*n();return new Mt(t,t,t)}throw new Error("Unsupported random color mode")}static test(t){return"string"==typeof t&&(pt.test(t)||ft.test(t))}cmyk(){const{_a:t,_b:e,_c:n}=this.rgb(),[i,r,s]=[t,e,n].map((t=>t/255)),o=Math.min(1-i,1-r,1-s);if(1===o)return new Mt(0,0,0,1,"cmyk");return new Mt((1-i-o)/(1-o),(1-r-o)/(1-o),(1-s-o)/(1-o),o,"cmyk")}hsl(){const{_a:t,_b:e,_c:n}=this.rgb(),[i,r,s]=[t,e,n].map((t=>t/255)),o=Math.max(i,r,s),h=Math.min(i,r,s),a=(o+h)/2,l=o===h,u=o-h;return new Mt(360*(l?0:o===i?((r-s)/u+(r<s?6:0))/6:o===r?((s-i)/u+2)/6:o===s?((i-r)/u+4)/6:0),100*(l?0:a>.5?u/(2-o-h):u/(o+h)),100*a,"hsl")}init(t=0,e=0,n=0,i=0,r="rgb"){if(t=t||0,this.space)for(const c in this.space)delete this[this.space[c]];if("number"==typeof t)r="string"==typeof i?i:r,i="string"==typeof i?0:i,Object.assign(this,{_a:t,_b:e,_c:n,_d:i,space:r});else if(t instanceof Array)this.space=e||("string"==typeof t[3]?t[3]:t[4])||"rgb",Object.assign(this,{_a:t[0],_b:t[1],_c:t[2],_d:t[3]||0});else if(t instanceof Object){const n=function(t,e){const n=_t(t,"rgb")?{_a:t.r,_b:t.g,_c:t.b,_d:0,space:"rgb"}:_t(t,"xyz")?{_a:t.x,_b:t.y,_c:t.z,_d:0,space:"xyz"}:_t(t,"hsl")?{_a:t.h,_b:t.s,_c:t.l,_d:0,space:"hsl"}:_t(t,"lab")?{_a:t.l,_b:t.a,_c:t.b,_d:0,space:"lab"}:_t(t,"lch")?{_a:t.l,_b:t.c,_c:t.h,_d:0,space:"lch"}:_t(t,"cmyk")?{_a:t.c,_b:t.m,_c:t.y,_d:t.k,space:"cmyk"}:{_a:0,_b:0,_c:0,space:"rgb"};return n.space=e||n.space,n}(t,e);Object.assign(this,n)}else if("string"==typeof t)if(ft.test(t)){const e=t.replace(dt,""),[n,i,r]=lt.exec(e).slice(1,4).map((t=>parseInt(t)));Object.assign(this,{_a:n,_b:i,_c:r,_d:0,space:"rgb"})}else{if(!pt.test(t))throw Error("Unsupported string format, can't construct Color");{const e=t=>parseInt(t,16),[,n,i,r]=at.exec((s=t,4===s.length?["#",s.substring(1,2),s.substring(1,2),s.substring(2,3),s.substring(2,3),s.substring(3,4),s.substring(3,4)].join(""):s)).map(e);Object.assign(this,{_a:n,_b:i,_c:r,_d:0,space:"rgb"})}}var s;const{_a:o,_b:h,_c:a,_d:l}=this,u="rgb"===this.space?{r:o,g:h,b:a}:"xyz"===this.space?{x:o,y:h,z:a}:"hsl"===this.space?{h:o,s:h,l:a}:"lab"===this.space?{l:o,a:h,b:a}:"lch"===this.space?{l:o,c:h,h:a}:"cmyk"===this.space?{c:o,m:h,y:a,k:l}:{};Object.assign(this,u)}lab(){const{x:t,y:e,z:n}=this.xyz();return new Mt(116*e-16,500*(t-e),200*(e-n),"lab")}lch(){const{l:t,a:e,b:n}=this.lab(),i=Math.sqrt(e**2+n**2);let r=180*Math.atan2(n,e)/Math.PI;r<0&&(r*=-1,r=360-r);return new Mt(t,i,r,"lch")}rgb(){if("rgb"===this.space)return this;if("lab"===(t=this.space)||"xyz"===t||"lch"===t){let{x:t,y:e,z:n}=this;if("lab"===this.space||"lch"===this.space){let{l:i,a:r,b:s}=this;if("lch"===this.space){const{c:t,h:e}=this,n=Math.PI/180;r=t*Math.cos(n*e),s=t*Math.sin(n*e)}const o=(i+16)/116,h=r/500+o,a=o-s/200,l=16/116,u=.008856,c=7.787;t=.95047*(h**3>u?h**3:(h-l)/c),e=1*(o**3>u?o**3:(o-l)/c),n=1.08883*(a**3>u?a**3:(a-l)/c)}const i=3.2406*t+-1.5372*e+-.4986*n,r=-.9689*t+1.8758*e+.0415*n,s=.0557*t+-.204*e+1.057*n,o=Math.pow,h=.0031308,a=i>h?1.055*o(i,1/2.4)-.055:12.92*i,l=r>h?1.055*o(r,1/2.4)-.055:12.92*r,u=s>h?1.055*o(s,1/2.4)-.055:12.92*s;return new Mt(255*a,255*l,255*u)}if("hsl"===this.space){let{h:t,s:e,l:n}=this;if(t/=360,e/=100,n/=100,0===e){n*=255;return new Mt(n,n,n)}const i=n<.5?n*(1+e):n+e-n*e,r=2*n-i,s=255*vt(r,i,t+1/3),o=255*vt(r,i,t),h=255*vt(r,i,t-1/3);return new Mt(s,o,h)}if("cmyk"===this.space){const{c:t,m:e,y:n,k:i}=this,r=255*(1-Math.min(1,t*(1-i)+i)),s=255*(1-Math.min(1,e*(1-i)+i)),o=255*(1-Math.min(1,n*(1-i)+i));return new Mt(r,s,o)}return this;var t}toArray(){const{_a:t,_b:e,_c:n,_d:i,space:r}=this;return[t,e,n,i,r]}toHex(){const[t,e,n]=this._clamped().map(bt);return`#${t}${e}${n}`}toRgb(){const[t,e,n]=this._clamped();return`rgb(${t},${e},${n})`}toString(){return this.toHex()}xyz(){const{_a:t,_b:e,_c:n}=this.rgb(),[i,r,s]=[t,e,n].map((t=>t/255)),o=i>.04045?Math.pow((i+.055)/1.055,2.4):i/12.92,h=r>.04045?Math.pow((r+.055)/1.055,2.4):r/12.92,a=s>.04045?Math.pow((s+.055)/1.055,2.4):s/12.92,l=(.4124*o+.3576*h+.1805*a)/.95047,u=(.2126*o+.7152*h+.0722*a)/1,c=(.0193*o+.1192*h+.9505*a)/1.08883,d=l>.008856?Math.pow(l,1/3):7.787*l+16/116,p=u>.008856?Math.pow(u,1/3):7.787*u+16/116,f=c>.008856?Math.pow(c,1/3):7.787*c+16/116;return new Mt(d,p,f,"xyz")}_clamped(){const{_a:t,_b:e,_c:n}=this.rgb(),{max:i,min:r,round:s}=Math;return[t,e,n].map((t=>i(0,r(s(t),255))))}}class St{constructor(...t){this.init(...t)}clone(){return new St(this)}init(t,e){const n=0,i=0,r=Array.isArray(t)?{x:t[0],y:t[1]}:"object"==typeof t?{x:t.x,y:t.y}:{x:t,y:e};return this.x=null==r.x?n:r.x,this.y=null==r.y?i:r.y,this}toArray(){return[this.x,this.y]}transform(t){return this.clone().transformO(t)}transformO(t){Et.isMatrixLike(t)||(t=new Et(t));const{x:e,y:n}=this;return this.x=t.a*e+t.c*n+t.e,this.y=t.b*e+t.d*n+t.f,this}}function Ct(t,e,n){return Math.abs(e-t)<1e-6}class Et{constructor(...t){this.init(...t)}static formatTransforms(t){const e="both"===t.flip||!0===t.flip,n=t.flip&&(e||"x"===t.flip)?-1:1,i=t.flip&&(e||"y"===t.flip)?-1:1,r=t.skew&&t.skew.length?t.skew[0]:isFinite(t.skew)?t.skew:isFinite(t.skewX)?t.skewX:0,s=t.skew&&t.skew.length?t.skew[1]:isFinite(t.skew)?t.skew:isFinite(t.skewY)?t.skewY:0,o=t.scale&&t.scale.length?t.scale[0]*n:isFinite(t.scale)?t.scale*n:isFinite(t.scaleX)?t.scaleX*n:n,h=t.scale&&t.scale.length?t.scale[1]*i:isFinite(t.scale)?t.scale*i:isFinite(t.scaleY)?t.scaleY*i:i,a=t.shear||0,l=t.rotate||t.theta||0,u=new St(t.origin||t.around||t.ox||t.originX,t.oy||t.originY),c=u.x,d=u.y,p=new St(t.position||t.px||t.positionX||NaN,t.py||t.positionY||NaN),f=p.x,m=p.y,g=new St(t.translate||t.tx||t.translateX,t.ty||t.translateY),y=g.x,x=g.y,w=new St(t.relative||t.rx||t.relativeX,t.ry||t.relativeY);return{scaleX:o,scaleY:h,skewX:r,skewY:s,shear:a,theta:l,rx:w.x,ry:w.y,tx:y,ty:x,ox:c,oy:d,px:f,py:m}}static fromArray(t){return{a:t[0],b:t[1],c:t[2],d:t[3],e:t[4],f:t[5]}}static isMatrixLike(t){return null!=t.a||null!=t.b||null!=t.c||null!=t.d||null!=t.e||null!=t.f}static matrixMultiply(t,e,n){const i=t.a*e.a+t.c*e.b,r=t.b*e.a+t.d*e.b,s=t.a*e.c+t.c*e.d,o=t.b*e.c+t.d*e.d,h=t.e+t.a*e.e+t.c*e.f,a=t.f+t.b*e.e+t.d*e.f;return n.a=i,n.b=r,n.c=s,n.d=o,n.e=h,n.f=a,n}around(t,e,n){return this.clone().aroundO(t,e,n)}aroundO(t,e,n){const i=t||0,r=e||0;return this.translateO(-i,-r).lmultiplyO(n).translateO(i,r)}clone(){return new Et(this)}decompose(t=0,e=0){const n=this.a,i=this.b,r=this.c,s=this.d,o=this.e,h=this.f,a=n*s-i*r,l=a>0?1:-1,u=l*Math.sqrt(n*n+i*i),c=Math.atan2(l*i,l*n),d=180/Math.PI*c,p=Math.cos(c),f=Math.sin(c),m=(n*r+i*s)/a,g=r*u/(m*n-i)||s*u/(m*i+n);return{scaleX:u,scaleY:g,shear:m,rotate:d,translateX:o-t+t*p*u+e*(m*p*u-f*g),translateY:h-e+t*f*u+e*(m*f*u+p*g),originX:t,originY:e,a:this.a,b:this.b,c:this.c,d:this.d,e:this.e,f:this.f}}equals(t){if(t===this)return!0;const e=new Et(t);return Ct(this.a,e.a)&&Ct(this.b,e.b)&&Ct(this.c,e.c)&&Ct(this.d,e.d)&&Ct(this.e,e.e)&&Ct(this.f,e.f)}flip(t,e){return this.clone().flipO(t,e)}flipO(t,e){return"x"===t?this.scaleO(-1,1,e,0):"y"===t?this.scaleO(1,-1,0,e):this.scaleO(-1,-1,t,e||t)}init(t){const e=Et.fromArray([1,0,0,1,0,0]);return t=t instanceof Qt?t.matrixify():"string"==typeof t?Et.fromArray(t.split(xt).map(parseFloat)):Array.isArray(t)?Et.fromArray(t):"object"==typeof t&&Et.isMatrixLike(t)?t:"object"==typeof t?(new Et).transform(t):6===arguments.length?Et.fromArray([].slice.call(arguments)):e,this.a=null!=t.a?t.a:e.a,this.b=null!=t.b?t.b:e.b,this.c=null!=t.c?t.c:e.c,this.d=null!=t.d?t.d:e.d,this.e=null!=t.e?t.e:e.e,this.f=null!=t.f?t.f:e.f,this}inverse(){return this.clone().inverseO()}inverseO(){const t=this.a,e=this.b,n=this.c,i=this.d,r=this.e,s=this.f,o=t*i-e*n;if(!o)throw new Error("Cannot invert "+this);const h=i/o,a=-e/o,l=-n/o,u=t/o,c=-(h*r+l*s),d=-(a*r+u*s);return this.a=h,this.b=a,this.c=l,this.d=u,this.e=c,this.f=d,this}lmultiply(t){return this.clone().lmultiplyO(t)}lmultiplyO(t){const e=t instanceof Et?t:new Et(t);return Et.matrixMultiply(e,this,this)}multiply(t){return this.clone().multiplyO(t)}multiplyO(t){const e=t instanceof Et?t:new Et(t);return Et.matrixMultiply(this,e,this)}rotate(t,e,n){return this.clone().rotateO(t,e,n)}rotateO(t,e=0,n=0){t=j(t);const i=Math.cos(t),r=Math.sin(t),{a:s,b:o,c:h,d:a,e:l,f:u}=this;return this.a=s*i-o*r,this.b=o*i+s*r,this.c=h*i-a*r,this.d=a*i+h*r,this.e=l*i-u*r+n*r-e*i+e,this.f=u*i+l*r-e*r-n*i+n,this}scale(){return this.clone().scaleO(...arguments)}scaleO(t,e=t,n=0,i=0){3===arguments.length&&(i=n,n=e,e=t);const{a:r,b:s,c:o,d:h,e:a,f:l}=this;return this.a=r*t,this.b=s*e,this.c=o*t,this.d=h*e,this.e=a*t-n*t+n,this.f=l*e-i*e+i,this}shear(t,e,n){return this.clone().shearO(t,e,n)}shearO(t,e=0,n=0){const{a:i,b:r,c:s,d:o,e:h,f:a}=this;return this.a=i+r*t,this.c=s+o*t,this.e=h+a*t-n*t,this}skew(){return this.clone().skewO(...arguments)}skewO(t,e=t,n=0,i=0){3===arguments.length&&(i=n,n=e,e=t),t=j(t),e=j(e);const r=Math.tan(t),s=Math.tan(e),{a:o,b:h,c:a,d:l,e:u,f:c}=this;return this.a=o+h*r,this.b=h+o*s,this.c=a+l*r,this.d=l+a*s,this.e=u+c*r-i*r,this.f=c+u*s-n*s,this}skewX(t,e,n){return this.skew(t,0,e,n)}skewY(t,e,n){return this.skew(0,t,e,n)}toArray(){return[this.a,this.b,this.c,this.d,this.e,this.f]}toString(){return"matrix("+this.a+","+this.b+","+this.c+","+this.d+","+this.e+","+this.f+")"}transform(t){if(Et.isMatrixLike(t)){return new Et(t).multiplyO(this)}const e=Et.formatTransforms(t),{x:n,y:i}=new St(e.ox,e.oy).transform(this),r=(new Et).translateO(e.rx,e.ry).lmultiplyO(this).translateO(-n,-i).scaleO(e.scaleX,e.scaleY).skewO(e.skewX,e.skewY).shearO(e.shear).rotateO(e.theta).translateO(n,i);if(isFinite(e.px)||isFinite(e.py)){const t=new St(n,i).transform(r),s=isFinite(e.px)?e.px-t.x:0,o=isFinite(e.py)?e.py-t.y:0;r.translateO(s,o)}return r.translateO(e.tx,e.ty),r}translate(t,e){return this.clone().translateO(t,e)}translateO(t,e){return this.e+=t||0,this.f+=e||0,this}valueOf(){return{a:this.a,b:this.b,c:this.c,d:this.d,e:this.e,f:this.f}}}function At(){if(!At.nodes){const t=Q().size(2,0);t.node.style.cssText=["opacity: 0","position: absolute","left: -100%","top: -100%","overflow: hidden"].join(";"),t.attr("focusable","false"),t.attr("aria-hidden","true");const e=t.path().node;At.nodes={svg:t,path:e}}if(!At.nodes.svg.node.parentNode){const t=G.document.body||G.document.documentElement;At.nodes.svg.addTo(t)}return At.nodes}function Tt(t){return!(t.width||t.height||t.x||t.y)}et(Et,"Matrix");class Ot{constructor(...t){this.init(...t)}addOffset(){return this.x+=G.window.pageXOffset,this.y+=G.window.pageYOffset,new Ot(this)}init(t){return t="string"==typeof t?t.split(xt).map(parseFloat):Array.isArray(t)?t:"object"==typeof t?[null!=t.left?t.left:t.x,null!=t.top?t.top:t.y,t.width,t.height]:4===arguments.length?[].slice.call(arguments):[0,0,0,0],this.x=t[0]||0,this.y=t[1]||0,this.width=this.w=t[2]||0,this.height=this.h=t[3]||0,this.x2=this.x+this.w,this.y2=this.y+this.h,this.cx=this.x+this.w/2,this.cy=this.y+this.h/2,this}isNulled(){return Tt(this)}merge(t){const e=Math.min(this.x,t.x),n=Math.min(this.y,t.y),i=Math.max(this.x+this.width,t.x+t.width)-e,r=Math.max(this.y+this.height,t.y+t.height)-n;return new Ot(e,n,i,r)}toArray(){return[this.x,this.y,this.width,this.height]}toString(){return this.x+" "+this.y+" "+this.width+" "+this.height}transform(t){t instanceof Et||(t=new Et(t));let e=1/0,n=-1/0,i=1/0,r=-1/0;return[new St(this.x,this.y),new St(this.x2,this.y),new St(this.x,this.y2),new St(this.x2,this.y2)].forEach((function(s){s=s.transform(t),e=Math.min(e,s.x),n=Math.max(n,s.x),i=Math.min(i,s.y),r=Math.max(r,s.y)})),new Ot(e,i,n-e,r-i)}}function kt(t,e,n){let i;try{if(i=e(t.node),Tt(i)&&((r=t.node)!==G.document&&!(G.document.documentElement.contains||function(t){for(;t.parentNode;)t=t.parentNode;return t===G.document}).call(G.document.documentElement,r)))throw new Error("Element not in the dom")}catch(s){i=n(t)}var r;return i}N({viewbox:{viewbox(t,e,n,i){return null==t?new Ot(this.attr("viewBox")):this.attr("viewBox",new Ot(t,e,n,i))},zoom(t,e){let{width:n,height:i}=this.attr(["width","height"]);if((n||i)&&"string"!=typeof n&&"string"!=typeof i||(n=this.node.clientWidth,i=this.node.clientHeight),!n||!i)throw new Error("Impossible to get absolute width and height. Please provide an absolute width and height attribute on the zooming element");const r=this.viewbox(),s=n/r.width,o=i/r.height,h=Math.min(s,o);if(null==t)return h;let a=h/t;a===1/0&&(a=Number.MAX_SAFE_INTEGER/100),e=e||new St(n/2/s+r.x,i/2/o+r.y);const l=new Ot(r).transform(new Et({scale:a,origin:e}));return this.viewbox(l)}}}),et(Ot,"Box");class Rt extends Array{constructor(t=[],...e){if(super(t,...e),"number"==typeof t)return this;this.length=0,this.push(...t)}}st([Rt],{each(t,...e){return"function"==typeof t?this.map(((e,n,i)=>t.call(e,e,n,i))):this.map((n=>n[t](...e)))},toArray(){return Array.prototype.concat.apply([],this)}});const Nt=["toArray","constructor","each"];function zt(t,e){return new Rt(I((e||G.document).querySelectorAll(t),(function(t){return K(t)})))}Rt.extend=function(t){t=t.reduce(((t,e)=>(Nt.includes(e)||"_"===e[0]||(e in Array.prototype&&(t["$"+e]=Array.prototype[e]),t[e]=function(...t){return this.each(e,...t)}),t)),{}),st([Rt],t)};let Xt=0;const It={};function jt(t){let e=t.getEventHolder();return e===G.window&&(e=It),e.events||(e.events={}),e.events}function Pt(t){return t.getEventTarget()}function $t(t,e,n,i,r){const s=n.bind(i||t),o=Q(t),h=jt(o),a=Pt(o);e=Array.isArray(e)?e:e.split(xt),n._svgjsListenerId||(n._svgjsListenerId=++Xt),e.forEach((function(t){const e=t.split(".")[0],i=t.split(".")[1]||"*";h[e]=h[e]||{},h[e][i]=h[e][i]||{},h[e][i][n._svgjsListenerId]=s,a.addEventListener(e,s,r||!1)}))}function Lt(t,e,n,i){const r=Q(t),s=jt(r),o=Pt(r);("function"!=typeof n||(n=n._svgjsListenerId))&&(e=Array.isArray(e)?e:(e||"").split(xt)).forEach((function(t){const e=t&&t.split(".")[0],h=t&&t.split(".")[1];let a,l;if(n)s[e]&&s[e][h||"*"]&&(o.removeEventListener(e,s[e][h||"*"][n],i||!1),delete s[e][h||"*"][n]);else if(e&&h){if(s[e]&&s[e][h]){for(l in s[e][h])Lt(o,[e,h].join("."),l);delete s[e][h]}}else if(h)for(t in s)for(a in s[t])h===a&&Lt(o,[t,h].join("."));else if(e){if(s[e]){for(a in s[e])Lt(o,[e,a].join("."));delete s[e]}}else{for(t in s)Lt(o,t);!function(t){let e=t.getEventHolder();e===G.window&&(e=It),e.events&&(e.events={})}(r)}}))}class Dt extends W{addEventListener(){}dispatch(t,e,n){return function(t,e,n,i){const r=Pt(t);return e instanceof G.window.Event||(e=new G.window.CustomEvent(e,{detail:n,cancelable:!0,...i})),r.dispatchEvent(e),e}(this,t,e,n)}dispatchEvent(t){const e=this.getEventHolder().events;if(!e)return!0;const n=e[t.type];for(const i in n)for(const e in n[i])n[i][e](t);return!t.defaultPrevented}fire(t,e,n){return this.dispatch(t,e,n),this}getEventHolder(){return this}getEventTarget(){return this}off(t,e,n){return Lt(this,t,e,n),this}on(t,e,n,i){return $t(this,t,e,n,i),this}removeEventListener(){}}function Yt(){}et(Dt,"EventTarget");const Ft=400,Bt=">",Vt=0,Ht={"fill-opacity":1,"stroke-opacity":1,"stroke-width":0,"stroke-linejoin":"miter","stroke-linecap":"butt",fill:"#000000",stroke:"#000000",opacity:1,x:0,y:0,cx:0,cy:0,width:0,height:0,r:0,rx:0,ry:0,offset:0,"stop-opacity":1,"stop-color":"#000000","text-anchor":"start"};class Gt extends Array{constructor(...t){super(...t),this.init(...t)}clone(){return new this.constructor(this)}init(t){return"number"==typeof t||(this.length=0,this.push(...this.parse(t))),this}parse(t=[]){return t instanceof Array?t:t.trim().split(xt).map(parseFloat)}toArray(){return Array.prototype.concat.apply([],this)}toSet(){return new Set(this)}toString(){return this.join(" ")}valueOf(){const t=[];return t.push(...this),t}}class Wt{constructor(...t){this.init(...t)}convert(t){return new Wt(this.value,t)}divide(t){return t=new Wt(t),new Wt(this/t,this.unit||t.unit)}init(t,e){return e=Array.isArray(t)?t[1]:e,t=Array.isArray(t)?t[0]:t,this.value=0,this.unit=e||"","number"==typeof t?this.value=isNaN(t)?0:isFinite(t)?t:t<0?-34e37:34e37:"string"==typeof t?(e=t.match(ht))&&(this.value=parseFloat(e[1]),"%"===e[5]?this.value/=100:"s"===e[5]&&(this.value*=1e3),this.unit=e[5]):t instanceof Wt&&(this.value=t.valueOf(),this.unit=t.unit),this}minus(t){return t=new Wt(t),new Wt(this-t,this.unit||t.unit)}plus(t){return t=new Wt(t),new Wt(this+t,this.unit||t.unit)}times(t){return t=new Wt(t),new Wt(this*t,this.unit||t.unit)}toArray(){return[this.value,this.unit]}toJSON(){return this.toString()}toString(){return("%"===this.unit?~~(1e8*this.value)/1e6:"s"===this.unit?this.value/1e3:this.value)+this.unit}valueOf(){return this.value}}const qt=new Set(["fill","stroke","color","bgcolor","stop-color","flood-color","lighting-color"]),Zt=[];class Ut extends Dt{constructor(t,e){super(),this.node=t,this.type=t.nodeName,e&&t!==e&&this.attr(e)}add(t,e){return(t=Q(t)).removeNamespace&&this.node instanceof G.window.SVGElement&&t.removeNamespace(),null==e?this.node.appendChild(t.node):t.node!==this.node.childNodes[e]&&this.node.insertBefore(t.node,this.node.childNodes[e]),this}addTo(t,e){return Q(t).put(this,e)}children(){return new Rt(I(this.node.children,(function(t){return K(t)})))}clear(){for(;this.node.hasChildNodes();)this.node.removeChild(this.node.lastChild);return this}clone(t=!0,e=!0){this.writeDataToDom();let n=this.node.cloneNode(t);return e&&(n=rt(n)),new this.constructor(n)}each(t,e){const n=this.children();let i,r;for(i=0,r=n.length;i<r;i++)t.apply(n[i],[i,n]),e&&n[i].each(t,e);return this}element(t,e){return this.put(new Ut(U(t),e))}first(){return K(this.node.firstChild)}get(t){return K(this.node.childNodes[t])}getEventHolder(){return this.node}getEventTarget(){return this.node}has(t){return this.index(t)>=0}html(t,e){return this.xml(t,e,"http://www.w3.org/1999/xhtml")}id(t){return void 0!==t||this.node.id||(this.node.id=it(this.type)),this.attr("id",t)}index(t){return[].slice.call(this.node.childNodes).indexOf(t.node)}last(){return K(this.node.lastChild)}matches(t){const e=this.node,n=e.matches||e.matchesSelector||e.msMatchesSelector||e.mozMatchesSelector||e.webkitMatchesSelector||e.oMatchesSelector||null;return n&&n.call(e,t)}parent(t){let e=this;if(!e.node.parentNode)return null;if(e=K(e.node.parentNode),!t)return e;do{if("string"==typeof t?e.matches(t):e instanceof t)return e}while(e=K(e.node.parentNode));return e}put(t,e){return t=Q(t),this.add(t,e),t}putIn(t,e){return Q(t).add(this,e)}remove(){return this.parent()&&this.parent().removeElement(this),this}removeElement(t){return this.node.removeChild(t.node),this}replace(t){return t=Q(t),this.node.parentNode&&this.node.parentNode.replaceChild(t.node,this.node),t}round(t=2,e=null){const n=10**t,i=this.attr(e);for(const r in i)"number"==typeof i[r]&&(i[r]=Math.round(i[r]*n)/n);return this.attr(i),this}svg(t,e){return this.xml(t,e,B)}toString(){return this.id()}words(t){return this.node.textContent=t,this}wrap(t){const e=this.parent();if(!e)return this.addTo(t);const n=e.index(this);return e.put(t,n).put(this)}writeDataToDom(){return this.each((function(){this.writeDataToDom()})),this}xml(t,e,n){if("boolean"==typeof t&&(n=e,e=t,t=null),null==t||"function"==typeof t){e=null==e||e,this.writeDataToDom();let n=this;if(null!=t){if(n=K(n.node.cloneNode(!0)),e){const e=t(n);if(n=e||n,!1===e)return""}n.each((function(){const e=t(this),n=e||this;!1===e?this.remove():e&&this!==n&&this.replace(n)}),!0)}return e?n.node.outerHTML:n.node.innerHTML}e=null!=e&&e;const i=U("wrapper",n),r=G.document.createDocumentFragment();i.innerHTML=t;for(let o=i.children.length;o--;)r.appendChild(i.firstElementChild);const s=this.parent();return e?this.replace(r)&&s:this.add(r)}}st(Ut,{attr:function(t,e,n){if(null==t){t={},e=this.node.attributes;for(const n of e)t[n.nodeName]=gt.test(n.nodeValue)?parseFloat(n.nodeValue):n.nodeValue;return t}if(t instanceof Array)return t.reduce(((t,e)=>(t[e]=this.attr(e),t)),{});if("object"==typeof t&&t.constructor===Object)for(e in t)this.attr(e,t[e]);else if(null===e)this.node.removeAttribute(t);else{if(null==e)return null==(e=this.node.getAttribute(t))?Ht[t]:gt.test(e)?parseFloat(e):e;"number"==typeof(e=Zt.reduce(((e,n)=>n(t,e,this)),e))?e=new Wt(e):qt.has(t)&&Mt.isColor(e)?e=new Mt(e):e.constructor===Array&&(e=new Gt(e)),"leading"===t?this.leading&&this.leading(e):"string"==typeof n?this.node.setAttributeNS(n,t,e.toString()):this.node.setAttribute(t,e.toString()),!this.rebuild||"font-size"!==t&&"x"!==t||this.rebuild()}return this},find:function(t){return zt(t,this.node)},findOne:function(t){return K(this.node.querySelector(t))}}),et(Ut,"Dom");class Qt extends Ut{constructor(t,e){super(t,e),this.dom={},this.node.instance=this,(t.hasAttribute("data-svgjs")||t.hasAttribute("svgjs:data"))&&this.setData(JSON.parse(t.getAttribute("data-svgjs"))??JSON.parse(t.getAttribute("svgjs:data"))??{})}center(t,e){return this.cx(t).cy(e)}cx(t){return null==t?this.x()+this.width()/2:this.x(t-this.width()/2)}cy(t){return null==t?this.y()+this.height()/2:this.y(t-this.height()/2)}defs(){const t=this.root();return t&&t.defs()}dmove(t,e){return this.dx(t).dy(e)}dx(t=0){return this.x(new Wt(t).plus(this.x()))}dy(t=0){return this.y(new Wt(t).plus(this.y()))}getEventHolder(){return this}height(t){return this.attr("height",t)}move(t,e){return this.x(t).y(e)}parents(t=this.root()){const e="string"==typeof t;e||(t=Q(t));const n=new Rt;let i=this;for(;(i=i.parent())&&i.node!==G.document&&"#document-fragment"!==i.nodeName&&(n.push(i),e||i.node!==t.node)&&(!e||!i.matches(t));)if(i.node===this.root().node)return null;return n}reference(t){if(!(t=this.attr(t)))return null;const e=(t+"").match(ut);return e?Q(e[1]):null}root(){const t=this.parent(q[Z]);return t&&t.root()}setData(t){return this.dom=t,this}size(t,e){const n=$(this,t,e);return this.width(new Wt(n.width)).height(new Wt(n.height))}width(t){return this.attr("width",t)}writeDataToDom(){return F(this,this.dom),super.writeDataToDom()}x(t){return this.attr("x",t)}y(t){return this.attr("y",t)}}st(Qt,{bbox:function(){const t=kt(this,(t=>t.getBBox()),(t=>{try{const e=t.clone().addTo(At().svg).show(),n=e.node.getBBox();return e.remove(),n}catch(e){throw new Error(`Getting bbox of element "${t.node.nodeName}" is not possible: ${e.toString()}`)}}));return new Ot(t)},rbox:function(t){const e=kt(this,(t=>t.getBoundingClientRect()),(t=>{throw new Error(`Getting rbox of element "${t.node.nodeName}" is not possible`)})),n=new Ot(e);return t?n.transform(t.screenCTM().inverseO()):n.addOffset()},inside:function(t,e){const n=this.bbox();return t>n.x&&e>n.y&&t<n.x+n.width&&e<n.y+n.height},point:function(t,e){return new St(t,e).transformO(this.screenCTM().inverseO())},ctm:function(){return new Et(this.node.getCTM())},screenCTM:function(){try{if("function"==typeof this.isRoot&&!this.isRoot()){const t=this.rect(1,1),e=t.node.getScreenCTM();return t.remove(),new Et(e)}return new Et(this.node.getScreenCTM())}catch(t){return console.warn(`Cannot get CTM from SVG node ${this.node.nodeName}. Is the element rendered?`),new Et}}}),et(Qt,"Element");const Jt={stroke:["color","width","opacity","linecap","linejoin","miterlimit","dasharray","dashoffset"],fill:["color","opacity","rule"],prefix:function(t,e){return"color"===e?t:t+"-"+e}};["fill","stroke"].forEach((function(t){const e={};let n;e[t]=function(e){if(void 0===e)return this.attr(t);if("string"==typeof e||e instanceof Mt||Mt.isRgb(e)||e instanceof Qt)this.attr(t,e);else for(n=Jt[t].length-1;n>=0;n--)null!=e[Jt[t][n]]&&this.attr(Jt.prefix(t,Jt[t][n]),e[Jt[t][n]]);return this},N(["Element","Runner"],e)})),N(["Element","Runner"],{matrix:function(t,e,n,i,r,s){return null==t?new Et(this):this.attr("transform",new Et(t,e,n,i,r,s))},rotate:function(t,e,n){return this.transform({rotate:t,ox:e,oy:n},!0)},skew:function(t,e,n,i){return 1===arguments.length||3===arguments.length?this.transform({skew:t,ox:e,oy:n},!0):this.transform({skew:[t,e],ox:n,oy:i},!0)},shear:function(t,e,n){return this.transform({shear:t,ox:e,oy:n},!0)},scale:function(t,e,n,i){return 1===arguments.length||3===arguments.length?this.transform({scale:t,ox:e,oy:n},!0):this.transform({scale:[t,e],ox:n,oy:i},!0)},translate:function(t,e){return this.transform({translate:[t,e]},!0)},relative:function(t,e){return this.transform({relative:[t,e]},!0)},flip:function(t="both",e="center"){return-1==="xybothtrue".indexOf(t)&&(e=t,t="both"),this.transform({flip:t,origin:e},!0)},opacity:function(t){return this.attr("opacity",t)}}),N("radius",{radius:function(t,e=t){return"radialGradient"===(this._element||this).type?this.attr("r",new Wt(t)):this.rx(t).ry(e)}}),N("Path",{length:function(){return this.node.getTotalLength()},pointAt:function(t){return new St(this.node.getPointAtLength(t))}}),N(["Element","Runner"],{font:function(t,e){if("object"==typeof t){for(e in t)this.font(e,t[e]);return this}return"leading"===t?this.leading(e):"anchor"===t?this.attr("text-anchor",e):"size"===t||"family"===t||"weight"===t||"stretch"===t||"variant"===t||"style"===t?this.attr("font-"+t,e):this.attr(t,e)}});N("Element",["click","dblclick","mousedown","mouseup","mouseover","mouseout","mousemove","mouseenter","mouseleave","touchstart","touchmove","touchleave","touchend","touchcancel","contextmenu","wheel","pointerdown","pointermove","pointerup","pointerleave","pointercancel"].reduce((function(t,e){return t[e]=function(t){return null===t?this.off(e):this.on(e,t),this},t}),{})),N("Element",{untransform:function(){return this.attr("transform",null)},matrixify:function(){return(this.attr("transform")||"").split(ct).slice(0,-1).map((function(t){const e=t.trim().split("(");return[e[0],e[1].split(xt).map((function(t){return parseFloat(t)}))]})).reverse().reduce((function(t,e){return"matrix"===e[0]?t.lmultiply(Et.fromArray(e[1])):t[e[0]].apply(t,e[1])}),new Et)},toParent:function(t,e){if(this===t)return this;if(Y(this.node))return this.addTo(t,e);const n=this.screenCTM(),i=t.screenCTM().inverse();return this.addTo(t,e).untransform().transform(i.multiply(n)),this},toRoot:function(t){return this.toParent(this.root(),t)},transform:function(t,e){if(null==t||"string"==typeof t){const e=new Et(this).decompose();return null==t?e:e[t]}Et.isMatrixLike(t)||(t={...t,origin:L(t,this)});const n=new Et(!0===e?this:e||!1).transform(t);return this.attr("transform",n)}});class Kt extends Qt{flatten(){return this.each((function(){if(this instanceof Kt)return this.flatten().ungroup()})),this}ungroup(t=this.parent(),e=t.index(this)){return e=-1===e?t.children().length:e,this.each((function(n,i){return i[i.length-n-1].toParent(t,e)})),this.remove()}}et(Kt,"Container");class te extends Kt{constructor(t,e=t){super(J("defs",t),e)}flatten(){return this}ungroup(){return this}}et(te,"Defs");class ee extends Qt{}function ne(t){return this.attr("rx",t)}function ie(t){return this.attr("ry",t)}function re(t){return null==t?this.cx()-this.rx():this.cx(t+this.rx())}function se(t){return null==t?this.cy()-this.ry():this.cy(t+this.ry())}function oe(t){return this.attr("cx",t)}function he(t){return this.attr("cy",t)}function ae(t){return null==t?2*this.rx():this.rx(new Wt(t).divide(2))}function le(t){return null==t?2*this.ry():this.ry(new Wt(t).divide(2))}et(ee,"Shape");const ue=Object.freeze(Object.defineProperty({__proto__:null,cx:oe,cy:he,height:le,rx:ne,ry:ie,width:ae,x:re,y:se},Symbol.toStringTag,{value:"Module"}));class ce extends ee{constructor(t,e=t){super(J("ellipse",t),e)}size(t,e){const n=$(this,t,e);return this.rx(new Wt(n.width).divide(2)).ry(new Wt(n.height).divide(2))}}st(ce,ue),N("Container",{ellipse:ot((function(t=0,e=t){return this.put(new ce).size(t,e).move(0,0)}))}),et(ce,"Ellipse");class de extends Ut{constructor(t=G.document.createDocumentFragment()){super(t)}xml(t,e,n){if("boolean"==typeof t&&(n=e,e=t,t=null),null==t||"function"==typeof t){const t=new Ut(U("wrapper",n));return t.add(this.node.cloneNode(!0)),t.xml(!1,n)}return super.xml(t,!1,n)}}function pe(t,e){return"radialGradient"===(this._element||this).type?this.attr({fx:new Wt(t),fy:new Wt(e)}):this.attr({x1:new Wt(t),y1:new Wt(e)})}function fe(t,e){return"radialGradient"===(this._element||this).type?this.attr({cx:new Wt(t),cy:new Wt(e)}):this.attr({x2:new Wt(t),y2:new Wt(e)})}et(de,"Fragment");const me=Object.freeze(Object.defineProperty({__proto__:null,from:pe,to:fe},Symbol.toStringTag,{value:"Module"}));class ge extends Kt{constructor(t,e){super(J(t+"Gradient","string"==typeof t?null:t),e)}attr(t,e,n){return"transform"===t&&(t="gradientTransform"),super.attr(t,e,n)}bbox(){return new Ot}targets(){return zt("svg [fill*="+this.id()+"]")}toString(){return this.url()}update(t){return this.clear(),"function"==typeof t&&t.call(this,this),this}url(){return"url(#"+this.id()+")"}}st(ge,me),N({Container:{gradient(...t){return this.defs().gradient(...t)}},Defs:{gradient:ot((function(t,e){return this.put(new ge(t)).update(e)}))}}),et(ge,"Gradient");class ye extends Kt{constructor(t,e=t){super(J("pattern",t),e)}attr(t,e,n){return"transform"===t&&(t="patternTransform"),super.attr(t,e,n)}bbox(){return new Ot}targets(){return zt("svg [fill*="+this.id()+"]")}toString(){return this.url()}update(t){return this.clear(),"function"==typeof t&&t.call(this,this),this}url(){return"url(#"+this.id()+")"}}N({Container:{pattern(...t){return this.defs().pattern(...t)}},Defs:{pattern:ot((function(t,e,n){return this.put(new ye).update(n).attr({x:0,y:0,width:t,height:e,patternUnits:"userSpaceOnUse"})}))}}),et(ye,"Pattern");let xe=class extends ee{constructor(t,e=t){super(J("image",t),e)}load(t,e){if(!t)return this;const n=new G.window.Image;return $t(n,"load",(function(t){const i=this.parent(ye);0===this.width()&&0===this.height()&&this.size(n.width,n.height),i instanceof ye&&0===i.width()&&0===i.height()&&i.size(this.width(),this.height()),"function"==typeof e&&e.call(this,t)}),this),$t(n,"load error",(function(){Lt(n)})),this.attr("href",n.src=t,H)}};var we;we=function(t,e,n){return"fill"!==t&&"stroke"!==t||yt.test(e)&&(e=n.root().defs().image(e)),e instanceof xe&&(e=n.root().defs().pattern(0,0,(t=>{t.add(e)}))),e},Zt.push(we),N({Container:{image:ot((function(t,e){return this.put(new xe).size(0,0).load(t,e)}))}}),et(xe,"Image");class be extends Gt{bbox(){let t=-1/0,e=-1/0,n=1/0,i=1/0;return this.forEach((function(r){t=Math.max(r[0],t),e=Math.max(r[1],e),n=Math.min(r[0],n),i=Math.min(r[1],i)})),new Ot(n,i,t-n,e-i)}move(t,e){const n=this.bbox();if(t-=n.x,e-=n.y,!isNaN(t)&&!isNaN(e))for(let i=this.length-1;i>=0;i--)this[i]=[this[i][0]+t,this[i][1]+e];return this}parse(t=[0,0]){const e=[];(t=t instanceof Array?Array.prototype.concat.apply([],t):t.trim().split(xt).map(parseFloat)).length%2!=0&&t.pop();for(let n=0,i=t.length;n<i;n+=2)e.push([t[n],t[n+1]]);return e}size(t,e){let n;const i=this.bbox();for(n=this.length-1;n>=0;n--)i.width&&(this[n][0]=(this[n][0]-i.x)*t/i.width+i.x),i.height&&(this[n][1]=(this[n][1]-i.y)*e/i.height+i.y);return this}toLine(){return{x1:this[0][0],y1:this[0][1],x2:this[1][0],y2:this[1][1]}}toString(){const t=[];for(let e=0,n=this.length;e<n;e++)t.push(this[e].join(","));return t.join(" ")}transform(t){return this.clone().transformO(t)}transformO(t){Et.isMatrixLike(t)||(t=new Et(t));for(let e=this.length;e--;){const[n,i]=this[e];this[e][0]=t.a*n+t.c*i+t.e,this[e][1]=t.b*n+t.d*i+t.f}return this}}const _e=be;const ve=Object.freeze(Object.defineProperty({__proto__:null,MorphArray:_e,height:function(t){const e=this.bbox();return null==t?e.height:this.size(e.width,t)},width:function(t){const e=this.bbox();return null==t?e.width:this.size(t,e.height)},x:function(t){return null==t?this.bbox().x:this.move(t,this.bbox().y)},y:function(t){return null==t?this.bbox().y:this.move(this.bbox().x,t)}},Symbol.toStringTag,{value:"Module"}));class Me extends ee{constructor(t,e=t){super(J("line",t),e)}array(){return new be([[this.attr("x1"),this.attr("y1")],[this.attr("x2"),this.attr("y2")]])}move(t,e){return this.attr(this.array().move(t,e).toLine())}plot(t,e,n,i){return null==t?this.array():(t=void 0!==e?{x1:t,y1:e,x2:n,y2:i}:new be(t).toLine(),this.attr(t))}size(t,e){const n=$(this,t,e);return this.attr(this.array().size(n.width,n.height).toLine())}}st(Me,ve),N({Container:{line:ot((function(...t){return Me.prototype.plot.apply(this.put(new Me),null!=t[0]?t:[0,0,0,0])}))}}),et(Me,"Line");class Se extends Kt{constructor(t,e=t){super(J("marker",t),e)}height(t){return this.attr("markerHeight",t)}orient(t){return this.attr("orient",t)}ref(t,e){return this.attr("refX",t).attr("refY",e)}toString(){return"url(#"+this.id()+")"}update(t){return this.clear(),"function"==typeof t&&t.call(this,this),this}width(t){return this.attr("markerWidth",t)}}function Ce(t,e){return function(n){return null==n?this[t]:(this[t]=n,e&&e.call(this),this)}}N({Container:{marker(...t){return this.defs().marker(...t)}},Defs:{marker:ot((function(t,e,n){return this.put(new Se).size(t,e).ref(t/2,e/2).viewbox(0,0,t,e).attr("orient","auto").update(n)}))},marker:{marker(t,e,n,i){let r=["marker"];return"all"!==t&&r.push(t),r=r.join("-"),t=arguments[1]instanceof Se?arguments[1]:this.defs().marker(e,n,i),this.attr(r,t)}}}),et(Se,"Marker");const Ee={"-":function(t){return t},"<>":function(t){return-Math.cos(t*Math.PI)/2+.5},">":function(t){return Math.sin(t*Math.PI/2)},"<":function(t){return 1-Math.cos(t*Math.PI/2)},bezier:function(t,e,n,i){return function(r){return r<0?t>0?e/t*r:n>0?i/n*r:0:r>1?n<1?(1-i)/(1-n)*r+(i-n)/(1-n):t<1?(1-e)/(1-t)*r+(e-t)/(1-t):1:3*r*(1-r)**2*e+3*r**2*(1-r)*i+r**3}},steps:function(t,e="end"){e=e.split("-").reverse()[0];let n=t;return"none"===e?--n:"both"===e&&++n,(i,r=!1)=>{let s=Math.floor(i*t);const o=i*s%1==0;return"start"!==e&&"both"!==e||++s,r&&o&&--s,i>=0&&s<0&&(s=0),i<=1&&s>n&&(s=n),s/n}}};class Ae{done(){return!1}}class Te extends Ae{constructor(t=Bt){super(),this.ease=Ee[t]||t}step(t,e,n){return"number"!=typeof t?n<1?t:e:t+(e-t)*this.ease(n)}}class Oe extends Ae{constructor(t){super(),this.stepper=t}done(t){return t.done}step(t,e,n,i){return this.stepper(t,e,n,i)}}function ke(){const t=(this._duration||500)/1e3,e=this._overshoot||0,n=Math.PI,i=Math.log(e/100+1e-10),r=-i/Math.sqrt(n*n+i*i),s=3.9/(r*t);this.d=2*r*s,this.k=s*s}st(class extends Oe{constructor(t=500,e=0){super(),this.duration(t).overshoot(e)}step(t,e,n,i){if("string"==typeof t)return t;if(i.done=n===1/0,n===1/0)return e;if(0===n)return t;n>100&&(n=16),n/=1e3;const r=i.velocity||0,s=-this.d*r-this.k*(t-e),o=t+r*n+s*n*n/2;return i.velocity=r+s*n,i.done=Math.abs(e-o)+Math.abs(r)<.002,i.done?e:o}},{duration:Ce("_duration",ke),overshoot:Ce("_overshoot",ke)});st(class extends Oe{constructor(t=.1,e=.01,n=0,i=1e3){super(),this.p(t).i(e).d(n).windup(i)}step(t,e,n,i){if("string"==typeof t)return t;if(i.done=n===1/0,n===1/0)return e;if(0===n)return t;const r=e-t;let s=(i.integral||0)+r*n;const o=(r-(i.error||0))/n,h=this._windup;return!1!==h&&(s=Math.max(-h,Math.min(s,h))),i.error=r,i.integral=s,i.done=Math.abs(r)<.001,i.done?e:t+(this.P*r+this.I*s+this.D*o)}},{windup:Ce("_windup"),p:Ce("P"),i:Ce("I"),d:Ce("D")});const Re={M:2,L:2,H:1,V:1,C:6,S:4,Q:4,T:2,A:7,Z:0},Ne={M:function(t,e,n){return e.x=n.x=t[0],e.y=n.y=t[1],["M",e.x,e.y]},L:function(t,e){return e.x=t[0],e.y=t[1],["L",t[0],t[1]]},H:function(t,e){return e.x=t[0],["H",t[0]]},V:function(t,e){return e.y=t[0],["V",t[0]]},C:function(t,e){return e.x=t[4],e.y=t[5],["C",t[0],t[1],t[2],t[3],t[4],t[5]]},S:function(t,e){return e.x=t[2],e.y=t[3],["S",t[0],t[1],t[2],t[3]]},Q:function(t,e){return e.x=t[2],e.y=t[3],["Q",t[0],t[1],t[2],t[3]]},T:function(t,e){return e.x=t[0],e.y=t[1],["T",t[0],t[1]]},Z:function(t,e,n){return e.x=n.x,e.y=n.y,["Z"]},A:function(t,e){return e.x=t[5],e.y=t[6],["A",t[0],t[1],t[2],t[3],t[4],t[5],t[6]]}},ze="mlhvqtcsaz".split("");for(let jn=0,Pn=ze.length;jn<Pn;++jn)Ne[ze[jn]]=function(t){return function(e,n,i){if("H"===t)e[0]=e[0]+n.x;else if("V"===t)e[0]=e[0]+n.y;else if("A"===t)e[5]=e[5]+n.x,e[6]=e[6]+n.y;else for(let t=0,r=e.length;t<r;++t)e[t]=e[t]+(t%2?n.y:n.x);return Ne[t](e,n,i)}}(ze[jn].toUpperCase());function Xe(t){return t.segment.length&&t.segment.length-1===Re[t.segment[0].toUpperCase()]}function Ie(t,e){t.inNumber&&je(t,!1);const n=wt.test(e);if(n)t.segment=[e];else{const e=t.lastCommand,n=e.toLowerCase(),i=e===n;t.segment=["m"===n?i?"l":"L":e]}return t.inSegment=!0,t.lastCommand=t.segment[0],n}function je(t,e){if(!t.inNumber)throw new Error("Parser Error");t.number&&t.segment.push(parseFloat(t.number)),t.inNumber=e,t.number="",t.pointSeen=!1,t.hasExponent=!1,Xe(t)&&Pe(t)}function Pe(t){t.inSegment=!1,t.absolute&&(t.segment=function(t){const e=t.segment[0];return Ne[e](t.segment.slice(1),t.p,t.p0)}(t)),t.segments.push(t.segment)}function $e(t){if(!t.segment.length)return!1;const e="A"===t.segment[0].toUpperCase(),n=t.segment.length;return e&&(4===n||5===n)}function Le(t){return"E"===t.lastToken.toUpperCase()}const De=new Set([" ",",","\t","\n","\r","\f"]);class Ye extends Gt{bbox(){return At().path.setAttribute("d",this.toString()),new Ot(At.nodes.path.getBBox())}move(t,e){const n=this.bbox();if(t-=n.x,e-=n.y,!isNaN(t)&&!isNaN(e))for(let i,r=this.length-1;r>=0;r--)i=this[r][0],"M"===i||"L"===i||"T"===i?(this[r][1]+=t,this[r][2]+=e):"H"===i?this[r][1]+=t:"V"===i?this[r][1]+=e:"C"===i||"S"===i||"Q"===i?(this[r][1]+=t,this[r][2]+=e,this[r][3]+=t,this[r][4]+=e,"C"===i&&(this[r][5]+=t,this[r][6]+=e)):"A"===i&&(this[r][6]+=t,this[r][7]+=e);return this}parse(t="M0 0"){return Array.isArray(t)&&(t=Array.prototype.concat.apply([],t).toString()),function(t,e=!0){let n=0,i="";const r={segment:[],inNumber:!1,number:"",lastToken:"",inSegment:!1,segments:[],pointSeen:!1,hasExponent:!1,absolute:e,p0:new St,p:new St};for(;r.lastToken=i,i=t.charAt(n++);)if(r.inSegment||!Ie(r,i))if("."!==i)if(isNaN(parseInt(i)))if(De.has(i))r.inNumber&&je(r,!1);else if("-"!==i&&"+"!==i)if("E"!==i.toUpperCase()){if(wt.test(i)){if(r.inNumber)je(r,!1);else{if(!Xe(r))throw new Error("parser Error");Pe(r)}--n}}else r.number+=i,r.hasExponent=!0;else{if(r.inNumber&&!Le(r)){je(r,!1),--n;continue}r.number+=i,r.inNumber=!0}else{if("0"===r.number||$e(r)){r.inNumber=!0,r.number=i,je(r,!0);continue}r.inNumber=!0,r.number+=i}else{if(r.pointSeen||r.hasExponent){je(r,!1),--n;continue}r.inNumber=!0,r.pointSeen=!0,r.number+=i}return r.inNumber&&je(r,!1),r.inSegment&&Xe(r)&&Pe(r),r.segments}(t)}size(t,e){const n=this.bbox();let i,r;for(n.width=0===n.width?1:n.width,n.height=0===n.height?1:n.height,i=this.length-1;i>=0;i--)r=this[i][0],"M"===r||"L"===r||"T"===r?(this[i][1]=(this[i][1]-n.x)*t/n.width+n.x,this[i][2]=(this[i][2]-n.y)*e/n.height+n.y):"H"===r?this[i][1]=(this[i][1]-n.x)*t/n.width+n.x:"V"===r?this[i][1]=(this[i][1]-n.y)*e/n.height+n.y:"C"===r||"S"===r||"Q"===r?(this[i][1]=(this[i][1]-n.x)*t/n.width+n.x,this[i][2]=(this[i][2]-n.y)*e/n.height+n.y,this[i][3]=(this[i][3]-n.x)*t/n.width+n.x,this[i][4]=(this[i][4]-n.y)*e/n.height+n.y,"C"===r&&(this[i][5]=(this[i][5]-n.x)*t/n.width+n.x,this[i][6]=(this[i][6]-n.y)*e/n.height+n.y)):"A"===r&&(this[i][1]=this[i][1]*t/n.width,this[i][2]=this[i][2]*e/n.height,this[i][6]=(this[i][6]-n.x)*t/n.width+n.x,this[i][7]=(this[i][7]-n.y)*e/n.height+n.y);return this}toString(){return function(t){let e="";for(let n=0,i=t.length;n<i;n++)e+=t[n][0],null!=t[n][1]&&(e+=t[n][1],null!=t[n][2]&&(e+=" ",e+=t[n][2],null!=t[n][3]&&(e+=" ",e+=t[n][3],e+=" ",e+=t[n][4],null!=t[n][5]&&(e+=" ",e+=t[n][5],e+=" ",e+=t[n][6],null!=t[n][7]&&(e+=" ",e+=t[n][7])))));return e+" "}(this)}}const Fe=t=>{const e=typeof t;return"number"===e?Wt:"string"===e?Mt.isColor(t)?Mt:xt.test(t)?wt.test(t)?Ye:Gt:ht.test(t)?Wt:Ve:qe.indexOf(t.constructor)>-1?t.constructor:Array.isArray(t)?Gt:"object"===e?We:Ve};class Be{constructor(t){this._stepper=t||new Te("-"),this._from=null,this._to=null,this._type=null,this._context=null,this._morphObj=null}at(t){return this._morphObj.morph(this._from,this._to,t,this._stepper,this._context)}done(){return this._context.map(this._stepper.done).reduce((function(t,e){return t&&e}),!0)}from(t){return null==t?this._from:(this._from=this._set(t),this)}stepper(t){return null==t?this._stepper:(this._stepper=t,this)}to(t){return null==t?this._to:(this._to=this._set(t),this)}type(t){return null==t?this._type:(this._type=t,this)}_set(t){this._type||this.type(Fe(t));let e=new this._type(t);return this._type===Mt&&(e=this._to?e[this._to[4]]():this._from?e[this._from[4]]():e),this._type===We&&(e=this._to?e.align(this._to):this._from?e.align(this._from):e),e=e.toConsumable(),this._morphObj=this._morphObj||new this._type,this._context=this._context||Array.apply(null,Array(e.length)).map(Object).map((function(t){return t.done=!0,t})),e}}class Ve{constructor(...t){this.init(...t)}init(t){return t=Array.isArray(t)?t[0]:t,this.value=t,this}toArray(){return[this.value]}valueOf(){return this.value}}class He{constructor(...t){this.init(...t)}init(t){return Array.isArray(t)&&(t={scaleX:t[0],scaleY:t[1],shear:t[2],rotate:t[3],translateX:t[4],translateY:t[5],originX:t[6],originY:t[7]}),Object.assign(this,He.defaults,t),this}toArray(){const t=this;return[t.scaleX,t.scaleY,t.shear,t.rotate,t.translateX,t.translateY,t.originX,t.originY]}}He.defaults={scaleX:1,scaleY:1,shear:0,rotate:0,translateX:0,translateY:0,originX:0,originY:0};const Ge=(t,e)=>t[0]<e[0]?-1:t[0]>e[0]?1:0;class We{constructor(...t){this.init(...t)}align(t){const e=this.values;for(let n=0,i=e.length;n<i;++n){if(e[n+1]===t[n+1]){if(e[n+1]===Mt&&t[n+7]!==e[n+7]){const e=t[n+7],i=new Mt(this.values.splice(n+3,5))[e]().toArray();this.values.splice(n+3,0,...i)}n+=e[n+2]+2;continue}if(!t[n+1])return this;const i=(new t[n+1]).toArray(),r=e[n+2]+3;e.splice(n,r,t[n],t[n+1],t[n+2],...i),n+=e[n+2]+2}return this}init(t){if(this.values=[],Array.isArray(t))return void(this.values=t.slice());t=t||{};const e=[];for(const n in t){const i=Fe(t[n]),r=new i(t[n]).toArray();e.push([n,i,r.length,...r])}return e.sort(Ge),this.values=e.reduce(((t,e)=>t.concat(e)),[]),this}toArray(){return this.values}valueOf(){const t={},e=this.values;for(;e.length;){const n=e.shift(),i=e.shift(),r=e.shift(),s=e.splice(0,r);t[n]=new i(s)}return t}}const qe=[Ve,He,We];class Ze extends ee{constructor(t,e=t){super(J("path",t),e)}array(){return this._array||(this._array=new Ye(this.attr("d")))}clear(){return delete this._array,this}height(t){return null==t?this.bbox().height:this.size(this.bbox().width,t)}move(t,e){return this.attr("d",this.array().move(t,e))}plot(t){return null==t?this.array():this.clear().attr("d","string"==typeof t?t:this._array=new Ye(t))}size(t,e){const n=$(this,t,e);return this.attr("d",this.array().size(n.width,n.height))}width(t){return null==t?this.bbox().width:this.size(t,this.bbox().height)}x(t){return null==t?this.bbox().x:this.move(t,this.bbox().y)}y(t){return null==t?this.bbox().y:this.move(this.bbox().x,t)}}Ze.prototype.MorphArray=Ye,N({Container:{path:ot((function(t){return this.put(new Ze).plot(t||new Ye)}))}}),et(Ze,"Path");const Ue=Object.freeze(Object.defineProperty({__proto__:null,array:function(){return this._array||(this._array=new be(this.attr("points")))},clear:function(){return delete this._array,this},move:function(t,e){return this.attr("points",this.array().move(t,e))},plot:function(t){return null==t?this.array():this.clear().attr("points","string"==typeof t?t:this._array=new be(t))},size:function(t,e){const n=$(this,t,e);return this.attr("points",this.array().size(n.width,n.height))}},Symbol.toStringTag,{value:"Module"}));class Qe extends ee{constructor(t,e=t){super(J("polygon",t),e)}}N({Container:{polygon:ot((function(t){return this.put(new Qe).plot(t||new be)}))}}),st(Qe,ve),st(Qe,Ue),et(Qe,"Polygon");class Je extends ee{constructor(t,e=t){super(J("polyline",t),e)}}N({Container:{polyline:ot((function(t){return this.put(new Je).plot(t||new be)}))}}),st(Je,ve),st(Je,Ue),et(Je,"Polyline");class Ke extends ee{constructor(t,e=t){super(J("rect",t),e)}}st(Ke,{rx:ne,ry:ie}),N({Container:{rect:ot((function(t,e){return this.put(new Ke).size(t,e)}))}}),et(Ke,"Rect");class tn{constructor(){this._first=null,this._last=null}first(){return this._first&&this._first.value}last(){return this._last&&this._last.value}push(t){const e=void 0!==t.next?t:{value:t,next:null,prev:null};return this._last?(e.prev=this._last,this._last.next=e,this._last=e):(this._last=e,this._first=e),e}remove(t){t.prev&&(t.prev.next=t.next),t.next&&(t.next.prev=t.prev),t===this._last&&(this._last=t.prev),t===this._first&&(this._first=t.next),t.prev=null,t.next=null}shift(){const t=this._first;return t?(this._first=t.next,this._first&&(this._first.prev=null),this._last=this._first?this._last:null,t.value):null}}const en={nextDraw:null,frames:new tn,timeouts:new tn,immediates:new tn,timer:()=>G.window.performance||G.window.Date,transforms:[],frame(t){const e=en.frames.push({run:t});return null===en.nextDraw&&(en.nextDraw=G.window.requestAnimationFrame(en._draw)),e},timeout(t,e){e=e||0;const n=en.timer().now()+e,i=en.timeouts.push({run:t,time:n});return null===en.nextDraw&&(en.nextDraw=G.window.requestAnimationFrame(en._draw)),i},immediate(t){const e=en.immediates.push(t);return null===en.nextDraw&&(en.nextDraw=G.window.requestAnimationFrame(en._draw)),e},cancelFrame(t){null!=t&&en.frames.remove(t)},clearTimeout(t){null!=t&&en.timeouts.remove(t)},cancelImmediate(t){null!=t&&en.immediates.remove(t)},_draw(t){let e=null;const n=en.timeouts.last();for(;(e=en.timeouts.shift())&&(t>=e.time?e.run():en.timeouts.push(e),e!==n););let i=null;const r=en.frames.last();for(;i!==r&&(i=en.frames.shift());)i.run(t);let s=null;for(;s=en.immediates.shift();)s();en.nextDraw=en.timeouts.first()||en.frames.first()?G.window.requestAnimationFrame(en._draw):null}},nn=function(t){const e=t.start,n=t.runner.duration();return{start:e,duration:n,end:e+n,runner:t.runner}},rn=function(){const t=G.window;return(t.performance||t.Date).now()};class sn extends Dt{constructor(t=rn){super(),this._timeSource=t,this.terminate()}active(){return!!this._nextFrame}finish(){return this.time(this.getEndTimeOfTimeline()+1),this.pause()}getEndTime(){const t=this.getLastRunnerInfo(),e=t?t.runner.duration():0;return(t?t.start:this._time)+e}getEndTimeOfTimeline(){const t=this._runners.map((t=>t.start+t.runner.duration()));return Math.max(0,...t)}getLastRunnerInfo(){return this.getRunnerInfoById(this._lastRunnerId)}getRunnerInfoById(t){return this._runners[this._runnerIds.indexOf(t)]||null}pause(){return this._paused=!0,this._continue()}persist(t){return null==t?this._persist:(this._persist=t,this)}play(){return this._paused=!1,this.updateTime()._continue()}reverse(t){const e=this.speed();if(null==t)return this.speed(-e);const n=Math.abs(e);return this.speed(t?-n:n)}schedule(t,e,n){if(null==t)return this._runners.map(nn);let i=0;const r=this.getEndTime();if(e=e||0,null==n||"last"===n||"after"===n)i=r;else if("absolute"===n||"start"===n)i=e,e=0;else if("now"===n)i=this._time;else if("relative"===n){const n=this.getRunnerInfoById(t.id);n&&(i=n.start+e,e=0)}else{if("with-last"!==n)throw new Error('Invalid value for the "when" parameter');{const t=this.getLastRunnerInfo();i=t?t.start:this._time}}t.unschedule(),t.timeline(this);const s=t.persist(),o={persist:null===s?this._persist:s,start:i+e,runner:t};return this._lastRunnerId=t.id,this._runners.push(o),this._runners.sort(((t,e)=>t.start-e.start)),this._runnerIds=this._runners.map((t=>t.runner.id)),this.updateTime()._continue(),this}seek(t){return this.time(this._time+t)}source(t){return null==t?this._timeSource:(this._timeSource=t,this)}speed(t){return null==t?this._speed:(this._speed=t,this)}stop(){return this.time(0),this.pause()}time(t){return null==t?this._time:(this._time=t,this._continue(!0))}unschedule(t){const e=this._runnerIds.indexOf(t.id);return e<0||(this._runners.splice(e,1),this._runnerIds.splice(e,1),t.timeline(null)),this}updateTime(){return this.active()||(this._lastSourceTime=this._timeSource()),this}_continue(t=!1){return en.cancelFrame(this._nextFrame),this._nextFrame=null,t?this._stepImmediate():(this._paused||(this._nextFrame=en.frame(this._step)),this)}_stepFn(t=!1){const e=this._timeSource();let n=e-this._lastSourceTime;t&&(n=0);const i=this._speed*n+(this._time-this._lastStepTime);this._lastSourceTime=e,t||(this._time+=i,this._time=this._time<0?0:this._time),this._lastStepTime=this._time,this.fire("time",this._time);for(let s=this._runners.length;s--;){const t=this._runners[s],e=t.runner;this._time-t.start<=0&&e.reset()}let r=!1;for(let s=0,o=this._runners.length;s<o;s++){const t=this._runners[s],e=t.runner;let n=i;const h=this._time-t.start;if(h<=0){r=!0;continue}if(h<n&&(n=h),!e.active())continue;if(e.step(n).done){if(!0!==t.persist){e.duration()-e.time()+this._time+t.persist<this._time&&(e.unschedule(),--s,--o)}}else r=!0}return r&&!(this._speed<0&&0===this._time)||this._runnerIds.length&&this._speed<0&&this._time>0?this._continue():(this.pause(),this.fire("finished")),this}terminate(){this._startTime=0,this._speed=1,this._persist=0,this._nextFrame=null,this._paused=!0,this._runners=[],this._runnerIds=[],this._lastRunnerId=-1,this._time=0,this._lastSourceTime=0,this._lastStepTime=0,this._step=this._stepFn.bind(this,!1),this._stepImmediate=this._stepFn.bind(this,!0)}}N({Element:{timeline:function(t){return null==t?(this._timeline=this._timeline||new sn,this._timeline):(this._timeline=t,this)}}});class on extends Dt{constructor(t){super(),this.id=on.id++,t="function"==typeof(t=null==t?Ft:t)?new Oe(t):t,this._element=null,this._timeline=null,this.done=!1,this._queue=[],this._duration="number"==typeof t&&t,this._isDeclarative=t instanceof Oe,this._stepper=this._isDeclarative?t:new Te,this._history={},this.enabled=!0,this._time=0,this._lastTime=0,this._reseted=!0,this.transforms=new Et,this.transformId=1,this._haveReversed=!1,this._reverse=!1,this._loopsDone=0,this._swing=!1,this._wait=0,this._times=1,this._frameId=null,this._persist=!!this._isDeclarative||null}static sanitise(t,e,n){let i=1,r=!1,s=0;return e=e??Vt,n=n||"last","object"!=typeof(t=t??Ft)||t instanceof Ae||(e=t.delay??e,n=t.when??n,r=t.swing||r,i=t.times??i,s=t.wait??s,t=t.duration??Ft),{duration:t,delay:e,swing:r,times:i,wait:s,when:n}}active(t){return null==t?this.enabled:(this.enabled=t,this)}addTransform(t){return this.transforms.lmultiplyO(t),this}after(t){return this.on("finished",t)}animate(t,e,n){const i=on.sanitise(t,e,n),r=new on(i.duration);return this._timeline&&r.timeline(this._timeline),this._element&&r.element(this._element),r.loop(i).schedule(i.delay,i.when)}clearTransform(){return this.transforms=new Et,this}clearTransformsFromQueue(){this.done&&this._timeline&&this._timeline._runnerIds.includes(this.id)||(this._queue=this._queue.filter((t=>!t.isTransform)))}delay(t){return this.animate(0,t)}duration(){return this._times*(this._wait+this._duration)-this._wait}during(t){return this.queue(null,t)}ease(t){return this._stepper=new Te(t),this}element(t){return null==t?this._element:(this._element=t,t._prepareRunner(),this)}finish(){return this.step(1/0)}loop(t,e,n){return"object"==typeof t&&(e=t.swing,n=t.wait,t=t.times),this._times=t||1/0,this._swing=e||!1,this._wait=n||0,!0===this._times&&(this._times=1/0),this}loops(t){const e=this._duration+this._wait;if(null==t){const t=Math.floor(this._time/e),n=(this._time-t*e)/this._duration;return Math.min(t+n,this._times)}const n=t%1,i=e*Math.floor(t)+this._duration*n;return this.time(i)}persist(t){return null==t?this._persist:(this._persist=t,this)}position(t){const e=this._time,n=this._duration,i=this._wait,r=this._times,s=this._swing,o=this._reverse;let h;if(null==t){const t=function(t){const e=s*Math.floor(t%(2*(i+n))/(i+n)),r=e&&!o||!e&&o,h=Math.pow(-1,r)*(t%(i+n))/n+r;return Math.max(Math.min(h,1),0)},a=r*(i+n)-i;return h=e<=0?Math.round(t(1e-5)):e<a?t(e):Math.round(t(a-1e-5)),h}const a=Math.floor(this.loops()),l=s&&a%2==0;return h=a+(l&&!o||o&&l?t:1-t),this.loops(h)}progress(t){return null==t?Math.min(1,this._time/this.duration()):this.time(t*this.duration())}queue(t,e,n,i){this._queue.push({initialiser:t||Yt,runner:e||Yt,retarget:n,isTransform:i,initialised:!1,finished:!1});return this.timeline()&&this.timeline()._continue(),this}reset(){return this._reseted||(this.time(0),this._reseted=!0),this}reverse(t){return this._reverse=null==t?!this._reverse:t,this}schedule(t,e,n){if(t instanceof sn||(n=e,e=t,t=this.timeline()),!t)throw Error("Runner cannot be scheduled without timeline");return t.schedule(this,e,n),this}step(t){if(!this.enabled)return this;t=null==t?16:t,this._time+=t;const e=this.position(),n=this._lastPosition!==e&&this._time>=0;this._lastPosition=e;const i=this.duration(),r=this._lastTime<=0&&this._time>0,s=this._lastTime<i&&this._time>=i;this._lastTime=this._time,r&&this.fire("start",this);const o=this._isDeclarative;this.done=!o&&!s&&this._time>=i,this._reseted=!1;let h=!1;return(n||o)&&(this._initialise(n),this.transforms=new Et,h=this._run(o?t:e),this.fire("step",this)),this.done=this.done||h&&o,s&&this.fire("finished",this),this}time(t){if(null==t)return this._time;const e=t-this._time;return this.step(e),this}timeline(t){return void 0===t?this._timeline:(this._timeline=t,this)}unschedule(){const t=this.timeline();return t&&t.unschedule(this),this}_initialise(t){if(t||this._isDeclarative)for(let e=0,n=this._queue.length;e<n;++e){const n=this._queue[e],i=this._isDeclarative||!n.initialised&&t;t=!n.finished,i&&t&&(n.initialiser.call(this),n.initialised=!0)}}_rememberMorpher(t,e){if(this._history[t]={morpher:e,caller:this._queue[this._queue.length-1]},this._isDeclarative){const t=this.timeline();t&&t.play()}}_run(t){let e=!0;for(let n=0,i=this._queue.length;n<i;++n){const i=this._queue[n],r=i.runner.call(this,t);i.finished=i.finished||!0===r,e=e&&i.finished}return e}_tryRetarget(t,e,n){if(this._history[t]){if(!this._history[t].caller.initialised){const e=this._queue.indexOf(this._history[t].caller);return this._queue.splice(e,1),!1}this._history[t].caller.retarget?this._history[t].caller.retarget.call(this,e,n):this._history[t].morpher.to(e),this._history[t].caller.finished=!1;const i=this.timeline();return i&&i.play(),!0}return!1}}on.id=0;class hn{constructor(t=new Et,e=-1,n=!0){this.transforms=t,this.id=e,this.done=n}clearTransformsFromQueue(){}}st([on,hn],{mergeWith(t){return new hn(t.transforms.lmultiply(this.transforms),t.id)}});const an=(t,e)=>t.lmultiplyO(e),ln=t=>t.transforms;function un(){const t=this._transformationRunners.runners.map(ln).reduce(an,new Et);this.transform(t),this._transformationRunners.merge(),1===this._transformationRunners.length()&&(this._frameId=null)}class cn{constructor(){this.runners=[],this.ids=[]}add(t){if(this.runners.includes(t))return;const e=t.id+1;return this.runners.push(t),this.ids.push(e),this}clearBefore(t){const e=this.ids.indexOf(t+1)||1;return this.ids.splice(0,e,0),this.runners.splice(0,e,new hn).forEach((t=>t.clearTransformsFromQueue())),this}edit(t,e){const n=this.ids.indexOf(t+1);return this.ids.splice(n,1,t+1),this.runners.splice(n,1,e),this}getByID(t){return this.runners[this.ids.indexOf(t+1)]}length(){return this.ids.length}merge(){let t=null;for(let e=0;e<this.runners.length;++e){const n=this.runners[e];if(t&&n.done&&t.done&&(!n._timeline||!n._timeline._runnerIds.includes(n.id))&&(!t._timeline||!t._timeline._runnerIds.includes(t.id))){this.remove(n.id);const i=n.mergeWith(t);this.edit(t.id,i),t=i,--e}else t=n}return this}remove(t){const e=this.ids.indexOf(t+1);return this.ids.splice(e,1),this.runners.splice(e,1),this}}N({Element:{animate(t,e,n){const i=on.sanitise(t,e,n),r=this.timeline();return new on(i.duration).loop(i).element(this).timeline(r.play()).schedule(i.delay,i.when)},delay(t,e){return this.animate(0,t,e)},_clearTransformRunnersBefore(t){this._transformationRunners.clearBefore(t.id)},_currentTransform(t){return this._transformationRunners.runners.filter((e=>e.id<=t.id)).map(ln).reduce(an,new Et)},_addRunner(t){this._transformationRunners.add(t),en.cancelImmediate(this._frameId),this._frameId=en.immediate(un.bind(this))},_prepareRunner(){null==this._frameId&&(this._transformationRunners=(new cn).add(new hn(new Et(this))))}}});st(on,{attr(t,e){return this.styleAttr("attr",t,e)},css(t,e){return this.styleAttr("css",t,e)},styleAttr(t,e,n){if("string"==typeof e)return this.styleAttr(t,{[e]:n});let i=e;if(this._tryRetarget(t,i))return this;let r=new Be(this._stepper).to(i),s=Object.keys(i);return this.queue((function(){r=r.from(this.element()[t](s))}),(function(e){return this.element()[t](r.at(e).valueOf()),r.done()}),(function(e){const n=Object.keys(e),o=(h=s,n.filter((t=>!h.includes(t))));var h;if(o.length){const e=this.element()[t](o),n=new We(r.from()).valueOf();Object.assign(n,e),r.from(n)}const a=new We(r.to()).valueOf();Object.assign(a,e),r.to(a),s=n,i=e})),this._rememberMorpher(t,r),this},zoom(t,e){if(this._tryRetarget("zoom",t,e))return this;let n=new Be(this._stepper).to(new Wt(t));return this.queue((function(){n=n.from(this.element().zoom())}),(function(t){return this.element().zoom(n.at(t),e),n.done()}),(function(t,i){e=i,n.to(t)})),this._rememberMorpher("zoom",n),this},transform(t,e,n){if(e=t.relative||e,this._isDeclarative&&!e&&this._tryRetarget("transform",t))return this;const i=Et.isMatrixLike(t);n=null!=t.affine?t.affine:null!=n?n:!i;const r=new Be(this._stepper).type(n?He:Et);let s,o,h,a,l;return this.queue((function(){o=o||this.element(),s=s||L(t,o),l=new Et(e?void 0:o),o._addRunner(this),e||o._clearTransformRunnersBefore(this)}),(function(u){e||this.clearTransform();const{x:c,y:d}=new St(s).transform(o._currentTransform(this));let p=new Et({...t,origin:[c,d]}),f=this._isDeclarative&&h?h:l;if(n){p=p.decompose(c,d),f=f.decompose(c,d);const t=p.rotate,e=f.rotate,n=[t-360,t,t+360],i=n.map((t=>Math.abs(t-e))),r=Math.min(...i),s=i.indexOf(r);p.rotate=n[s]}e&&(i||(p.rotate=t.rotate||0),this._isDeclarative&&a&&(f.rotate=a)),r.from(f),r.to(p);const m=r.at(u);return a=m.rotate,h=new Et(m),this.addTransform(h),o._addRunner(this),r.done()}),(function(e){(e.origin||"center").toString()!==(t.origin||"center").toString()&&(s=L(e,o)),t={...e,origin:s}}),!0),this._isDeclarative&&this._rememberMorpher("transform",r),this},x(t){return this._queueNumber("x",t)},y(t){return this._queueNumber("y",t)},ax(t){return this._queueNumber("ax",t)},ay(t){return this._queueNumber("ay",t)},dx(t=0){return this._queueNumberDelta("x",t)},dy(t=0){return this._queueNumberDelta("y",t)},dmove(t,e){return this.dx(t).dy(e)},_queueNumberDelta(t,e){if(e=new Wt(e),this._tryRetarget(t,e))return this;const n=new Be(this._stepper).to(e);let i=null;return this.queue((function(){i=this.element()[t](),n.from(i),n.to(i+e)}),(function(e){return this.element()[t](n.at(e)),n.done()}),(function(t){n.to(i+new Wt(t))})),this._rememberMorpher(t,n),this},_queueObject(t,e){if(this._tryRetarget(t,e))return this;const n=new Be(this._stepper).to(e);return this.queue((function(){n.from(this.element()[t]())}),(function(e){return this.element()[t](n.at(e)),n.done()})),this._rememberMorpher(t,n),this},_queueNumber(t,e){return this._queueObject(t,new Wt(e))},cx(t){return this._queueNumber("cx",t)},cy(t){return this._queueNumber("cy",t)},move(t,e){return this.x(t).y(e)},amove(t,e){return this.ax(t).ay(e)},center(t,e){return this.cx(t).cy(e)},size(t,e){let n;return t&&e||(n=this._element.bbox()),t||(t=n.width/n.height*e),e||(e=n.height/n.width*t),this.width(t).height(e)},width(t){return this._queueNumber("width",t)},height(t){return this._queueNumber("height",t)},plot(t,e,n,i){if(4===arguments.length)return this.plot([t,e,n,i]);if(this._tryRetarget("plot",t))return this;const r=new Be(this._stepper).type(this._element.MorphArray).to(t);return this.queue((function(){r.from(this._element.array())}),(function(t){return this._element.plot(r.at(t)),r.done()})),this._rememberMorpher("plot",r),this},leading(t){return this._queueNumber("leading",t)},viewbox(t,e,n,i){return this._queueObject("viewbox",new Ot(t,e,n,i))},update(t){return"object"!=typeof t?this.update({offset:arguments[0],color:arguments[1],opacity:arguments[2]}):(null!=t.opacity&&this.attr("stop-opacity",t.opacity),null!=t.color&&this.attr("stop-color",t.color),null!=t.offset&&this.attr("offset",t.offset),this)}}),st(on,{rx:ne,ry:ie,from:pe,to:fe}),et(on,"Runner");class dn extends Kt{constructor(t,e=t){super(J("svg",t),e),this.namespace()}defs(){return this.isRoot()?K(this.node.querySelector("defs"))||this.put(new te):this.root().defs()}isRoot(){return!this.node.parentNode||!(this.node.parentNode instanceof G.window.SVGElement)&&"#document-fragment"!==this.node.parentNode.nodeName}namespace(){return this.isRoot()?this.attr({xmlns:B,version:"1.1"}).attr("xmlns:xlink",H,V):this.root().namespace()}removeNamespace(){return this.attr({xmlns:null,version:null}).attr("xmlns:xlink",null,V).attr("xmlns:svgjs",null,V)}root(){return this.isRoot()?this:super.root()}}N({Container:{nested:ot((function(){return this.put(new dn)}))}}),et(dn,"Svg",!0);let pn=class extends Kt{constructor(t,e=t){super(J("symbol",t),e)}};N({Container:{symbol:ot((function(){return this.put(new pn)}))}}),et(pn,"Symbol");const fn=Object.freeze(Object.defineProperty({__proto__:null,amove:function(t,e){return this.ax(t).ay(e)},ax:function(t){return this.attr("x",t)},ay:function(t){return this.attr("y",t)},build:function(t){return this._build=!!t,this},center:function(t,e,n=this.bbox()){return this.cx(t,n).cy(e,n)},cx:function(t,e=this.bbox()){return null==t?e.cx:this.attr("x",this.attr("x")+t-e.cx)},cy:function(t,e=this.bbox()){return null==t?e.cy:this.attr("y",this.attr("y")+t-e.cy)},length:function(){return this.node.getComputedTextLength()},move:function(t,e,n=this.bbox()){return this.x(t,n).y(e,n)},plain:function(t){return!1===this._build&&this.clear(),this.node.appendChild(G.document.createTextNode(t)),this},x:function(t,e=this.bbox()){return null==t?e.x:this.attr("x",this.attr("x")+t-e.x)},y:function(t,e=this.bbox()){return null==t?e.y:this.attr("y",this.attr("y")+t-e.y)}},Symbol.toStringTag,{value:"Module"}));class mn extends ee{constructor(t,e=t){super(J("text",t),e),this.dom.leading=this.dom.leading??new Wt(1.3),this._rebuild=!0,this._build=!1}leading(t){return null==t?this.dom.leading:(this.dom.leading=new Wt(t),this.rebuild())}rebuild(t){if("boolean"==typeof t&&(this._rebuild=t),this._rebuild){const t=this;let e=0;const n=this.dom.leading;this.each((function(i){if(Y(this.node))return;const r=G.window.getComputedStyle(this.node).getPropertyValue("font-size"),s=n*new Wt(r);this.dom.newLined&&(this.attr("x",t.attr("x")),"\n"===this.text()?e+=s:(this.attr("dy",i?s+e:0),e=0))})),this.fire("rebuild")}return this}setData(t){return this.dom=t,this.dom.leading=new Wt(t.leading||1.3),this}writeDataToDom(){return F(this,this.dom,{leading:1.3}),this}text(t){if(void 0===t){const e=this.node.childNodes;let n=0;t="";for(let i=0,r=e.length;i<r;++i)"textPath"===e[i].nodeName||Y(e[i])?0===i&&(n=i+1):(i!==n&&3!==e[i].nodeType&&!0===K(e[i]).dom.newLined&&(t+="\n"),t+=e[i].textContent);return t}if(this.clear().build(!0),"function"==typeof t)t.call(this,this);else for(let e=0,n=(t=(t+"").split("\n")).length;e<n;e++)this.newLine(t[e]);return this.build(!1).rebuild()}}st(mn,fn),N({Container:{text:ot((function(t=""){return this.put(new mn).text(t)})),plain:ot((function(t=""){return this.put(new mn).plain(t)}))}}),et(mn,"Text");class gn extends ee{constructor(t,e=t){super(J("tspan",t),e),this._build=!1}dx(t){return this.attr("dx",t)}dy(t){return this.attr("dy",t)}newLine(){this.dom.newLined=!0;const t=this.parent();if(!(t instanceof mn))return this;const e=t.index(this),n=G.window.getComputedStyle(this.node).getPropertyValue("font-size"),i=t.dom.leading*new Wt(n);return this.dy(e?i:0).attr("x",t.x())}text(t){return null==t?this.node.textContent+(this.dom.newLined?"\n":""):("function"==typeof t?(this.clear().build(!0),t.call(this,this),this.build(!1)):this.plain(t),this)}}st(gn,fn),N({Tspan:{tspan:ot((function(t=""){const e=new gn;return this._build||this.clear(),this.put(e).text(t)}))},Text:{newLine:function(t=""){return this.tspan(t).newLine()}}}),et(gn,"Tspan");class yn extends ee{constructor(t,e=t){super(J("circle",t),e)}radius(t){return this.attr("r",t)}rx(t){return this.attr("r",t)}ry(t){return this.rx(t)}size(t){return this.radius(new Wt(t).divide(2))}}st(yn,{x:re,y:se,cx:oe,cy:he,width:ae,height:le}),N({Container:{circle:ot((function(t=0){return this.put(new yn).size(t).move(0,0)}))}}),et(yn,"Circle");class xn extends Kt{constructor(t,e=t){super(J("clipPath",t),e)}remove(){return this.targets().forEach((function(t){t.unclip()})),super.remove()}targets(){return zt("svg [clip-path*="+this.id()+"]")}}N({Container:{clip:ot((function(){return this.defs().put(new xn)}))},Element:{clipper(){return this.reference("clip-path")},clipWith(t){const e=t instanceof xn?t:this.parent().clip().add(t);return this.attr("clip-path","url(#"+e.id()+")")},unclip(){return this.attr("clip-path",null)}}}),et(xn,"ClipPath");class wn extends Qt{constructor(t,e=t){super(J("foreignObject",t),e)}}N({Container:{foreignObject:ot((function(t,e){return this.put(new wn).size(t,e)}))}}),et(wn,"ForeignObject");const bn=Object.freeze(Object.defineProperty({__proto__:null,dmove:function(t,e){return this.children().forEach((n=>{let i;try{i=n.node instanceof G.window.SVGSVGElement?new Ot(n.attr(["x","y","width","height"])):n.bbox()}catch(h){return}const r=new Et(n),s=r.translate(t,e).transform(r.inverse()),o=new St(i.x,i.y).transform(s);n.move(o.x,o.y)})),this},dx:function(t){return this.dmove(t,0)},dy:function(t){return this.dmove(0,t)},height:function(t,e=this.bbox()){return null==t?e.height:this.size(e.width,t,e)},move:function(t=0,e=0,n=this.bbox()){const i=t-n.x,r=e-n.y;return this.dmove(i,r)},size:function(t,e,n=this.bbox()){const i=$(this,t,e,n),r=i.width/n.width,s=i.height/n.height;return this.children().forEach((t=>{const e=new St(n).transform(new Et(t).inverse());t.scale(r,s,e.x,e.y)})),this},width:function(t,e=this.bbox()){return null==t?e.width:this.size(t,e.height,e)},x:function(t,e=this.bbox()){return null==t?e.x:this.move(t,e.y,e)},y:function(t,e=this.bbox()){return null==t?e.y:this.move(e.x,t,e)}},Symbol.toStringTag,{value:"Module"}));class _n extends Kt{constructor(t,e=t){super(J("g",t),e)}}st(_n,bn),N({Container:{group:ot((function(){return this.put(new _n)}))}}),et(_n,"G");class vn extends Kt{constructor(t,e=t){super(J("a",t),e)}target(t){return this.attr("target",t)}to(t){return this.attr("href",t,H)}}st(vn,bn),N({Container:{link:ot((function(t){return this.put(new vn).to(t)}))},Element:{unlink(){const t=this.linker();if(!t)return this;const e=t.parent();if(!e)return this.remove();const n=e.index(t);return e.add(this,n),t.remove(),this},linkTo(t){let e=this.linker();return e||(e=new vn,this.wrap(e)),"function"==typeof t?t.call(e,e):e.to(t),this},linker(){const t=this.parent();return t&&"a"===t.node.nodeName.toLowerCase()?t:null}}}),et(vn,"A");class Mn extends Kt{constructor(t,e=t){super(J("mask",t),e)}remove(){return this.targets().forEach((function(t){t.unmask()})),super.remove()}targets(){return zt("svg [mask*="+this.id()+"]")}}N({Container:{mask:ot((function(){return this.defs().put(new Mn)}))},Element:{masker(){return this.reference("mask")},maskWith(t){const e=t instanceof Mn?t:this.parent().mask().add(t);return this.attr("mask","url(#"+e.id()+")")},unmask(){return this.attr("mask",null)}}}),et(Mn,"Mask");class Sn extends Qt{constructor(t,e=t){super(J("stop",t),e)}update(t){return("number"==typeof t||t instanceof Wt)&&(t={offset:arguments[0],color:arguments[1],opacity:arguments[2]}),null!=t.opacity&&this.attr("stop-opacity",t.opacity),null!=t.color&&this.attr("stop-color",t.color),null!=t.offset&&this.attr("offset",new Wt(t.offset)),this}}N({Gradient:{stop:function(t,e,n){return this.put(new Sn).update(t,e,n)}}}),et(Sn,"Stop");class Cn extends Qt{constructor(t,e=t){super(J("style",t),e)}addText(t=""){return this.node.textContent+=t,this}font(t,e,n={}){return this.rule("@font-face",{fontFamily:t,src:e,...n})}rule(t,e){return this.addText(function(t,e){if(!t)return"";if(!e)return t;let n=t+"{";for(const i in e)n+=i.replace(/([A-Z])/g,(function(t,e){return"-"+e.toLowerCase()}))+":"+e[i]+";";return n+="}",n}(t,e))}}N("Dom",{style(t,e){return this.put(new Cn).rule(t,e)},fontface(t,e,n){return this.put(new Cn).font(t,e,n)}}),et(Cn,"Style");class En extends mn{constructor(t,e=t){super(J("textPath",t),e)}array(){const t=this.track();return t?t.array():null}plot(t){const e=this.track();let n=null;return e&&(n=e.plot(t)),null==t?n:this}track(){return this.reference("href")}}N({Container:{textPath:ot((function(t,e){return t instanceof mn||(t=this.text(t)),t.path(e)}))},Text:{path:ot((function(t,e=!0){const n=new En;let i;if(t instanceof Ze||(t=this.defs().path(t)),n.attr("href","#"+t,H),e)for(;i=this.node.firstChild;)n.node.appendChild(i);return this.put(n)})),textPath(){return this.findOne("textPath")}},Path:{text:ot((function(t){return t instanceof mn||(t=(new mn).addTo(this.parent()).text(t)),t.path(this)})),targets(){return zt("svg textPath").filter((t=>(t.attr("href")||"").includes(this.id())))}}}),En.prototype.MorphArray=Ye,et(En,"TextPath");class An extends ee{constructor(t,e=t){super(J("use",t),e)}use(t,e){return this.attr("href",(e||"")+"#"+t,H)}}N({Container:{use:ot((function(t,e){return this.put(new An).use(t,e)}))}}),et(An,"Use");const Tn=Q;st([dn,pn,xe,ye,Se],z("viewbox")),st([Me,Je,Qe,Ze],z("marker")),st(mn,z("Text")),st(Ze,z("Path")),st(te,z("Defs")),st([mn,gn],z("Tspan")),st([Ke,ce,ge,on],z("radius")),st(Dt,z("EventTarget")),st(Ut,z("Dom")),st(Qt,z("Element")),st(ee,z("Shape")),st([Kt,de],z("Container")),st(ge,z("Gradient")),st(on,z("Runner")),Rt.extend([...new Set(R)]),function(t=[]){qe.push(...[].concat(t))}([Wt,Mt,Ot,Et,Gt,be,Ye,St]),st(qe,{to(t){return(new Be).type(this.constructor).from(this.toArray()).to(t)},fromArray(t){return this.init(t),this},toConsumable(){return this.toArray()},morph(t,e,n,i,r){return this.fromArray(t.map((function(t,s){return i.step(t,e[s],n,r[s],r)})))}});const On={width:400,height:400,contentKey:"name",nodeWidth:50,nodeHeight:30,nodeTemplate:t=>`<div style='display: flex;justify-content: center;align-items: center; text-align: center; height: 100%;'>${t}</div>`,nodeBGColor:"#FFFFFF",nodeBGColorHover:"#FFFFFF",nodeStyle:"",nodeClassName:"apextree-node",borderWidth:1,borderStyle:"solid",borderRadius:"5px",borderColor:"#BCBCBC",borderColorHover:"#5C6BC0",enableExpandCollapse:!1,siblingSpacing:50,childrenSpacing:50,direction:"top",highlightOnHover:!0,containerClassName:"root",enableTooltip:!1,tooltipId:"apextree-tooltip-container",tooltipMaxWidth:100,tooltipBorderColor:"#BCBCBC",tooltipBGColor:"#FFFFFF",fontSize:"14px",fontFamily:"",fontWeight:400,fontColor:"#000000",canvasStyle:"",enableToolbar:!1,edgeWidth:1,edgeColor:"#BCBCBC",edgeColorHover:"#5C6BC0"};var kn=function(t){return t.touches||[{clientX:t.clientX,clientY:t.clientY}]};st(dn,{panZoom:function(t){var e,n,i,r,s,o,h,a,l,u,c,d,p=this;if(this.off(".panZoom"),!1===t)return this;var f,m,g=null!=(n=(t=null!=(e=t)?e:{}).zoomFactor)?n:2,y=null!=(i=t.zoomMin)?i:Number.MIN_VALUE,x=null!=(r=t.zoomMax)?r:Number.MAX_VALUE,w=null==(s=t.wheelZoom)||s,b=null==(o=t.pinchZoom)||o,_=null==(h=t.panning)||h,v=null!=(a=t.panButton)?a:0,M=null!=(l=t.oneFingerPan)&&l,S=null!=(u=t.margins)&&u,C=null!=(c=t.wheelZoomDeltaModeLinePixels)?c:17,E=null!=(d=t.wheelZoomDeltaModeScreenPixels)?d:53,A=!1,T=this.viewbox(),O=function(t){if(!S)return t;var e=S.top,n=S.left,i=S.bottom,r=S.right,s=p.attr(["width","height"]),o=s.width,h=s.height,a=p.node.preserveAspectRatio.baseVal,l=0,u=0,c=0,d=0;if(a.align!==a.SVG_PRESERVEASPECTRATIO_NONE){var f=o/h,m=T.width/T.height;if(m!==f){var g=a.meetOrSlice!==a.SVG_MEETORSLICE_SLICE,y=f>m?"width":"height",x="width"===y,w=g&&x||!g&&!x,b=w?f/m:m/f,_=t[y]-t[y]*b;w?a.align===a.SVG_PRESERVEASPECTRATIO_XMIDYMIN||a.align===a.SVG_PRESERVEASPECTRATIO_XMIDYMID||a.align===a.SVG_PRESERVEASPECTRATIO_XMIDYMAX?(l=_/2,u=-_/2):a.align===a.SVG_PRESERVEASPECTRATIO_XMINYMIN||a.align===a.SVG_PRESERVEASPECTRATIO_XMINYMID||a.align===a.SVG_PRESERVEASPECTRATIO_XMINYMAX?u=-_:a.align!==a.SVG_PRESERVEASPECTRATIO_XMAXYMIN&&a.align!==a.SVG_PRESERVEASPECTRATIO_XMAXYMID&&a.align!==a.SVG_PRESERVEASPECTRATIO_XMAXYMAX||(l=_):a.align===a.SVG_PRESERVEASPECTRATIO_XMINYMID||a.align===a.SVG_PRESERVEASPECTRATIO_XMIDYMID||a.align===a.SVG_PRESERVEASPECTRATIO_XMAXYMID?(c=_/2,d=-_/2):a.align===a.SVG_PRESERVEASPECTRATIO_XMINYMIN||a.align===a.SVG_PRESERVEASPECTRATIO_XMIDYMIN||a.align===a.SVG_PRESERVEASPECTRATIO_XMAXYMIN?d=-_:a.align!==a.SVG_PRESERVEASPECTRATIO_XMINYMAX&&a.align!==a.SVG_PRESERVEASPECTRATIO_XMIDYMAX&&a.align!==a.SVG_PRESERVEASPECTRATIO_XMAXYMAX||(c=_)}}var v=T.width+T.x-n-l,M=T.x+r-t.width-u,C=T.height+T.y-e-c,E=T.y+i-t.height-d;return t.x=Math.min(v,Math.max(M,t.x)),t.y=Math.min(C,Math.max(E,t.y)),t},k=function t(e){(m=kn(e)).length<2?_&&M&&z.call(this,e):(_&&M&&X.call(this,e),e.preventDefault(),this.dispatch("pinchZoomStart",{event:e}).defaultPrevented||(this.off("touchstart.panZoom",t),A=!0,$t(document,"touchmove.panZoom",N,this,{passive:!1}),$t(document,"touchend.panZoom",R,this,{passive:!1})))},R=function t(e){e.preventDefault();var n=kn(e);n.length>1||(A=!1,this.dispatch("pinchZoomEnd",{event:e}),Lt(document,"touchmove.panZoom",N),Lt(document,"touchend.panZoom",t),this.on("touchstart.panZoom",k),n.length&&_&&M&&z.call(this,e))},N=function(t){t.preventDefault();var e=kn(t),n=this.zoom(),i=Math.sqrt(Math.pow(m[0].clientX-m[1].clientX,2)+Math.pow(m[0].clientY-m[1].clientY,2))/Math.sqrt(Math.pow(e[0].clientX-e[1].clientX,2)+Math.pow(e[0].clientY-e[1].clientY,2));(n<y&&i>1||n>x&&i<1)&&(i=1);var r={x:e[0].clientX+.5*(e[1].clientX-e[0].clientX),y:e[0].clientY+.5*(e[1].clientY-e[0].clientY)},s=m[0].clientX+.5*(m[1].clientX-m[0].clientX),o=m[0].clientY+.5*(m[1].clientY-m[0].clientY),h=this.point(r.x,r.y),a=this.point(2*r.x-s,2*r.y-o),l=new Ot(this.viewbox()).transform((new Et).translate(-a.x,-a.y).scale(i,0,0).translate(h.x,h.y));O(l),this.viewbox(l),m=e,this.dispatch("zoom",{box:l,focus:a})},z=function t(e){e.type.indexOf("mouse")>-1&&e.button!==v&&e.which!==v+1||(e.preventDefault(),this.off("mousedown.panZoom",t),m=kn(e),A||(this.dispatch("panStart",{event:e}),f={x:m[0].clientX,y:m[0].clientY},$t(document,"touchmove.panZoom mousemove.panZoom",I,this,{passive:!1}),$t(document,"touchend.panZoom mouseup.panZoom",X,this,{passive:!1})))},X=function t(e){e.preventDefault(),Lt(document,"touchmove.panZoom mousemove.panZoom",I),Lt(document,"touchend.panZoom mouseup.panZoom",t),this.on("mousedown.panZoom",z),this.dispatch("panEnd",{event:e})},I=function(t){t.preventDefault();var e=kn(t),n={x:e[0].clientX,y:e[0].clientY},i=this.point(n.x,n.y),r=this.point(f.x,f.y),s=[r.x-i.x,r.y-i.y];if(s[0]||s[1]){var o=new Ot(this.viewbox()).transform((new Et).translate(s[0],s[1]));f=n,O(o),this.dispatch("panning",{box:o,event:t}).defaultPrevented||this.viewbox(o)}};return w&&this.on("wheel.panZoom",(function(t){var e;switch(t.preventDefault(),t.deltaMode){case 1:e=t.deltaY*C;break;case 2:e=t.deltaY*E;break;default:e=t.deltaY}var n=Math.pow(1+g,-1*e/100)*this.zoom(),i=this.point(t.clientX,t.clientY);if(n>x&&(n=x),n<y&&(n=y),this.dispatch("zoom",{level:n,focus:i}).defaultPrevented)return this;if(this.zoom(n,i),S){var r=O(this.viewbox());this.viewbox(r)}}),this,{passive:!1}),b&&this.on("touchstart.panZoom",k,this,{passive:!1}),_&&this.on("mousedown.panZoom",z,this,{passive:!1}),this}});class Rn{constructor(t,e,n,i){this.width=e,this.height=n,this.canvas=Tn().addTo(t).size(e,n).viewbox(`0 0 ${e} ${n}`).panZoom({zoomFactor:.2,zoomMin:.1}).attr({style:i})}add(t){this.canvas.add(t)}resetViewBox(){this.canvas.viewbox(`0 0 ${this.width} ${this.height}`)}updateViewBox(t,e,n,i){this.canvas.viewbox(`${t} ${e} ${n} ${i}`)}zoom(t){const e=this.canvas.zoom()+t;e>=.1&&this.canvas.zoom(e)}clear(){this.canvas.clear().viewbox(`0 0 ${this.width} ${this.height}`)}static drawRect({x1:t,y1:e,width:n=0,height:i=0,radius:r=0,color:s="#fefefe",opacity:o=1}={}){const h=new Ke;return h.attr({x:t??void 0,y:e??void 0,width:n,height:i,rx:r,ry:r,opacity:o}),h.fill(s),h}static drawCircle(t={}){const e=new yn;return e.attr(t),e}static drawText(t="",{x:e,y:n,dx:i,dy:r}){const s=new mn;return s.font({fill:"#f06"}),s.tspan(t),void 0!==e&&void 0!==n&&s.move(e,n),void 0!==i&&void 0!==r&&s.attr({dx:i,dy:r}),s}static drawTemplate(t,{nodeWidth:e,nodeHeight:n}={}){const i=new wn({width:e,height:n}),r=document.createElement("div");return r.innerHTML=t,r.setAttribute("xmlns","http://www.w3.org/1999/xhtml"),i.add(r),i}static drawGroup(t=0,e=0,n,i){const r=new _n;return r.attr({transform:`translate(${t}, ${e})`,"data-self":n,"data-parent":i}),r}static drawPath(t,{id:e="",borderColor:n=On.borderColor}={}){const i=new Ze({d:t});return i.id(e),i.fill("none").stroke({color:n,width:1}),i}}class Nn extends Rn{constructor(t,e){super(t,e.width,e.height,e.canvasStyle),this.element=t,this.options=e}construct(t){const{nodeWidth:e,nodeHeight:n,siblingSpacing:i,childrenSpacing:r}=this.options,s=l({nodeSize:()=>S[this.options.direction].nodeFlexSize({nodeWidth:e,nodeHeight:n,siblingSpacing:i,childrenSpacing:r}),spacing:0}),o=s.hierarchy(t);this.rootNode=s(o)}renderNode(t,e){var n,i;const r=this.options,{nodeWidth:s,nodeHeight:o,nodeTemplate:h,highlightOnHover:a,borderRadius:l,enableTooltip:u,tooltipTemplate:c,enableExpandCollapse:d}=r,{tooltipId:p,tooltipMaxWidth:f,tooltipBGColor:m,tooltipBorderColor:g,fontSize:y,fontWeight:x,fontFamily:w,fontColor:b,borderWidth:_,borderStyle:v,borderColor:M,nodeBGColor:C,nodeStyle:O,nodeClassName:k}={...r,...t.data.options},{x:R,y:N}=S[r.direction].swap(t),z=this,X=Rn.drawGroup(R,N,t.data.id,null==(n=t.parent)?void 0:n.data.id),I=h(t.data[r.contentKey]),j=Rn.drawTemplate(I,{nodeWidth:s,nodeHeight:o}),P=A({fontSize:y,fontWeight:x,fontFamily:w,color:b}),$=A({borderColor:M,borderStyle:v,borderWidth:`${_}px`,borderRadius:l,backgroundColor:C,height:"100%",boxSizing:"border-box"});j.first().attr("style",$.concat(O)),j.attr("class",k),X.attr("style",P),X.add(j);const L=(null==(i=this.rootNode)?void 0:i.nodes)||[];if(a&&(X.on("mouseover",(function(){const t=this.node.dataset.self,e=L.find((e=>e.data.id===t));e&&E(L,e,!0,r)})),X.on("mouseout",(function(){const t=this.node.dataset.self,e=L.find((e=>e.data.id===t));e&&E(L,e,!1,r)}))),u){const e=c?c(t.data[this.options.contentKey]):I;X.on("mousemove",(function(t){const n=((t,e,n,i,r,s)=>{const o=["position: absolute;",`left: ${t+20}px;`,`top: ${e+20}px;`,`border: 1px solid ${i};`,"border-radius: 5px;",`max-width: ${n}px;`,`background-color: ${r};`];return s&&o.push("padding: 10px;"),o})(t.pageX,t.pageY,f,g,m,!c);T(p,n.join(" "),e)})),X.on("mouseout",(function(t){"svg"===t.relatedTarget.tagName&&T(p)}))}if(e.add(X),(t.children||t.hiddenChildren)&&d){const n=7,i=Rn.drawGroup(R+s/2-n,N+o-n,t.data.id),r=Rn.drawCircle({cx:n,cy:n,r:n,style:"fill: #FFF; cursor: pointer;"});i.data("expanded",!1),i.add(r),t.hiddenChildren?i.add('<svg height="14" viewBox="0 0 24 24" width="14" xmlns="http://www.w3.org/2000/svg">\n  <path d="m12 0a12 12 0 1 0 12 12 12.013 12.013 0 0 0 -12-12zm0 22a10 10 0 1 1 10-10 10.011 10.011 0 0 1 -10 10zm5-10a1 1 0 0 1 -1 1h-3v3a1 1 0 0 1 -2 0v-3h-3a1 1 0 0 1 0-2h3v-3a1 1 0 0 1 2 0v3h3a1 1 0 0 1 1 1z"/>\n</svg>'):i.add('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="14" height="14">\n  <path d="m12,0C5.383,0,0,5.383,0,12s5.383,12,12,12,12-5.383,12-12S18.617,0,12,0Zm0,22c-5.514,0-10-4.486-10-10S6.486,2,12,2s10,4.486,10,10-4.486,10-10,10Zm5-10c0,.552-.448,1-1,1h-8c-.552,0-1-.448-1-1s.448-1,1-1h8c.552,0,1,.448,1,1Z"/>\n</svg>\n'),i.on("click",(function(){t.hiddenChildren?z.expand(this.node.dataset.self):z.collapse(this.node.dataset.self)})),e.add(i)}}renderEdge(t,e){var n;const{nodeWidth:i,nodeHeight:r}=this.options,s=((t,e,n,i)=>{if(!t||!t.parent)return null;const{edgeX:r,edgeY:s,edgeParentX:o,edgeParentY:h,edgeMidX:a,edgeMidY:l,calculateEdge:u,swap:c}=S[i],d=c(t),p=c(t.parent);return u({x:r({node:d,nodeWidth:e,nodeHeight:n}),y:s({node:d,nodeWidth:e,nodeHeight:n})},{x:o({parent:p,nodeWidth:e,nodeHeight:n}),y:h({parent:p,nodeWidth:e,nodeHeight:n})},{x:a({node:d,nodeWidth:e,nodeHeight:n}),y:l({node:d,nodeWidth:e,nodeHeight:n})},{sy:0})})(t,i,r,this.options.direction);if(!s)return;const o=Rn.drawPath(s,{id:`${t.data.id}-${null==(n=t.parent)?void 0:n.data.id}`});t.edge=o,e.add(o)}collapse(t){var e;const n=((null==(e=this.rootNode)?void 0:e.descendants())||[]).find((e=>e.data.id===t));(null==n?void 0:n.children)&&(n.hiddenChildren=n.children,n.hiddenChildren.forEach((t=>this.collapse(t))),n.children=void 0,this.render({keepOldPosition:!0}))}expand(t){var e;const n=((null==(e=this.rootNode)?void 0:e.descendants())||[]).find((e=>e.data.id===t));(null==n?void 0:n.hiddenChildren)&&(n.children=n.hiddenChildren,n.children.forEach((t=>this.expand(t))),n.hiddenChildren=void 0,this.render({keepOldPosition:!0}))}changeLayout(t="top"){this.options={...this.options,direction:t},this.render({keepOldPosition:!1})}fitScreen(){const{childrenSpacing:t,siblingSpacing:e}=this.options,{viewBoxDimensions:n}=S[this.options.direction],{x:i,y:r,width:s,height:o}=n({rootNode:this.rootNode,childrenSpacing:t,siblingSpacing:e});this.updateViewBox(i,r,s,o)}render({keepOldPosition:t=!1}={}){var e;const n=this.canvas.viewbox();this.clear();const{containerClassName:i,enableTooltip:r,tooltipId:s,fontSize:o,fontWeight:h,fontFamily:a,fontColor:l}=this.options,u=A({fontSize:o,fontWeight:h,fontFamily:a,color:l}),c=Rn.drawGroup(0,0,i);c.attr("style",u),c.id(i);const d=(null==(e=this.rootNode)?void 0:e.nodes)||[];if(d.forEach((t=>{this.renderEdge(t,c)})),d.forEach((t=>{this.renderNode(t,c)})),this.add(c),this.fitScreen(),t&&this.updateViewBox(n.x,n.y,n.width,n.height),r){const t=((t="apextree-tooltip-container")=>{const e=document.getElementById(t)||document.createElement("div");return e.id=t,e})(s);(document.body||document.getElementsByTagName("body")[0]).append(t)}}}class zn{constructor(t){this.graph=t}getSvgString(){return this.graph.canvas.svg().replace(/(<img [\w\W]+?)(>)/g,"$1 />").replace(/(<br)(>)/g,"$1 />").replace(/(<hr)(>)/g,"$1 />")}svgUrl(){const t=this.getSvgString(),e=new Blob([t],{type:"image/svg+xml;charset=utf-8"});return URL.createObjectURL(e)}triggerDownload(t,e){const n=document.createElement("a");n.href=t,n.download=e,document.body.appendChild(n),n.click(),document.body.removeChild(n)}exportToSVG(){this.triggerDownload(this.svgUrl(),`apex-tree-${(new Date).getTime()}.svg`)}}const Xn={"zoom-in":"data:image/svg+xml,%3csvg%20width='20px'%20height='20px'%20viewBox='0%200%2032%2032'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20xmlns:sketch='http://www.bohemiancoding.com/sketch/ns'%3e%3cg%20stroke='none'%20stroke-width='1'%20fill='none'%20fill-rule='evenodd'%3e%3cg%20transform='translate(-308.000000,%20-1139.000000)'%20fill='%23000000'%3e%3cpath%20d='M321.46,1163.45%20C315.17,1163.45%20310.07,1158.44%20310.07,1152.25%20C310.07,1146.06%20315.17,1141.04%20321.46,1141.04%20C327.75,1141.04%20332.85,1146.06%20332.85,1152.25%20C332.85,1158.44%20327.75,1163.45%20321.46,1163.45%20L321.46,1163.45%20Z%20M339.688,1169.25%20L331.429,1161.12%20C333.592,1158.77%20334.92,1155.67%20334.92,1152.25%20C334.92,1144.93%20328.894,1139%20321.46,1139%20C314.026,1139%20308,1144.93%20308,1152.25%20C308,1159.56%20314.026,1165.49%20321.46,1165.49%20C324.672,1165.49%20327.618,1164.38%20329.932,1162.53%20L338.225,1170.69%20C338.629,1171.09%20339.284,1171.09%20339.688,1170.69%20C340.093,1170.3%20340.093,1169.65%20339.688,1169.25%20L339.688,1169.25%20Z%20M326.519,1151.41%20L322.522,1151.41%20L322.522,1147.41%20C322.522,1146.85%20322.075,1146.41%20321.523,1146.41%20C320.972,1146.41%20320.524,1146.85%20320.524,1147.41%20L320.524,1151.41%20L316.529,1151.41%20C315.978,1151.41%20315.53,1151.59%20315.53,1152.14%20C315.53,1152.7%20315.978,1153.41%20316.529,1153.41%20L320.524,1153.41%20L320.524,1157.41%20C320.524,1157.97%20320.972,1158.41%20321.523,1158.41%20C322.075,1158.41%20322.522,1157.97%20322.522,1157.41%20L322.522,1153.41%20L326.519,1153.41%20C327.07,1153.41%20327.518,1152.96%20327.518,1152.41%20C327.518,1151.86%20327.07,1151.41%20326.519,1151.41%20L326.519,1151.41%20Z'%20/%3e%3c/g%3e%3c/g%3e%3c/svg%3e","zoom-out":"data:image/svg+xml,%3csvg%20width='20px'%20height='20px'%20viewBox='0%200%2032%2032'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%3e%3cg%20stroke='none'%20stroke-width='1'%20fill='none'%20fill-rule='evenodd'%20%3e%3cg%20transform='translate(-360.000000,%20-1139.000000)'%20fill='%23000000'%3e%3cpath%20d='M373.46,1163.45%20C367.17,1163.45%20362.071,1158.44%20362.071,1152.25%20C362.071,1146.06%20367.17,1141.04%20373.46,1141.04%20C379.75,1141.04%20384.85,1146.06%20384.85,1152.25%20C384.85,1158.44%20379.75,1163.45%20373.46,1163.45%20L373.46,1163.45%20Z%20M391.688,1169.25%20L383.429,1161.12%20C385.592,1158.77%20386.92,1155.67%20386.92,1152.25%20C386.92,1144.93%20380.894,1139%20373.46,1139%20C366.026,1139%20360,1144.93%20360,1152.25%20C360,1159.56%20366.026,1165.49%20373.46,1165.49%20C376.672,1165.49%20379.618,1164.38%20381.932,1162.53%20L390.225,1170.69%20C390.629,1171.09%20391.284,1171.09%20391.688,1170.69%20C392.093,1170.3%20392.093,1169.65%20391.688,1169.25%20L391.688,1169.25%20Z%20M378.689,1151.41%20L368.643,1151.41%20C368.102,1151.41%20367.663,1151.84%20367.663,1152.37%20C367.663,1152.9%20368.102,1153.33%20368.643,1153.33%20L378.689,1153.33%20C379.23,1153.33%20379.669,1152.9%20379.669,1152.37%20C379.669,1151.84%20379.23,1151.41%20378.689,1151.41%20L378.689,1151.41%20Z'%20/%3e%3c/g%3e%3c/g%3e%3c/svg%3e","fit-screen":"data:image/svg+xml,%3csvg%20width='20px'%20height='20px'%20viewBox='0%200%2032%2032'%20id='icon'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpolygon%20points='8%202%202%202%202%208%204%208%204%204%208%204%208%202'/%3e%3cpolygon%20points='24%202%2030%202%2030%208%2028%208%2028%204%2024%204%2024%202'/%3e%3cpolygon%20points='8%2030%202%2030%202%2024%204%2024%204%2028%208%2028%208%2030'/%3e%3cpolygon%20points='24%2030%2030%2030%2030%2024%2028%2024%2028%2028%2024%2028%2024%2030'/%3e%3cpath%20d='M24,24H8a2.0023,2.0023,0,0,1-2-2V10A2.0023,2.0023,0,0,1,8,8H24a2.0023,2.0023,0,0,1,2,2V22A2.0023,2.0023,0,0,1,24,24ZM8,10V22H24V10Z'/%3e%3crect%20fill='none'%20width='32'%20height='32'/%3e%3c/svg%3e",export:"data:image/svg+xml,%3csvg%20fill='%23000000'%20width='20px'%20height='20px'%20viewBox='0%200%2024%2024'%20id='export-2'%20xmlns='http://www.w3.org/2000/svg'%20class='icon%20line'%3e%3cpolyline%20id='primary'%20points='15%203%2021%203%2021%209'%20style='fill:%20none;%20stroke:%20rgb(0,%200,%200);%20stroke-linecap:%20round;%20stroke-linejoin:%20round;%20stroke-width:%201.5;'%3e%3c/polyline%3e%3cpath%20id='primary-2'%20data-name='primary'%20d='M21,13v7a1,1,0,0,1-1,1H4a1,1,0,0,1-1-1V4A1,1,0,0,1,4,3h7'%20style='fill:%20none;%20stroke:%20rgb(0,%200,%200);%20stroke-linecap:%20round;%20stroke-linejoin:%20round;%20stroke-width:%201.5;'%3e%3c/path%3e%3cline%20id='primary-3'%20data-name='primary'%20x1='11'%20y1='13'%20x2='21'%20y2='3'%20style='fill:%20none;%20stroke:%20rgb(0,%200,%200);%20stroke-linecap:%20round;%20stroke-linejoin:%20round;%20stroke-width:%201.5;'%3e%3c/line%3e%3c/svg%3e"};class In{constructor(t,e){this.element=t,this.graph=e,this.export=new zn(e)}render(){var t;const e=document.createElement("div");e.id="toolbar";const n=A({display:"flex",gap:"5px",position:"absolute",right:"20px",top:"20px"});e.setAttribute("style",n);const i=this.createToolbarItem("zoom-in",Xn["zoom-in"]),r=this.createToolbarItem("zoom-out",Xn["zoom-out"]),s=this.createToolbarItem("fit-screen",Xn["fit-screen"]),o=this.createToolbarItem("export",Xn.export);i.addEventListener("click",(()=>{this.graph.zoom(.1)})),r.addEventListener("click",(()=>{this.graph.zoom(-.1)})),s.addEventListener("click",(()=>{this.graph.fitScreen()})),o.addEventListener("click",(()=>{this.export.exportToSVG()})),e.append(i,r,s,o),null==(t=this.element)||t.append(e)}createToolbarItem(t,e){const n=document.createElement("div"),i=new Image;i.src=e,n.id=t,n.append(i);const r=A({width:"30px",height:"30px",display:"flex",alignItems:"center",justifyContent:"center",border:"1px solid #BCBCBC",backgroundColor:"#FFFFFF",cursor:"pointer"});return n.setAttribute("style",r),n}}return class{constructor(t,e){this.element=t,this.options={...On,...e};const n=document.createElement("div");n.id="apexTreeWrapper",n.style.position="relative",this.graph=new Nn(n,this.options),this.element.append(n)}render(t){if(!this.element)throw new Error("Element not found");if(this.graph.construct(t),this.graph.render(),this.options.enableToolbar){new In(document.getElementById("apexTreeWrapper"),this.graph).render()}return this.graph}}}));
