export declare function medianValue(positions: any): any;
export declare function sortNodes(G: any, order: any, sweepDirection?: number, includeLoops?: boolean): void;
export declare function neighbourPositions(G: any, order: any, i: any, j: any, u: any, includeLoops?: boolean): any[];
/**
 * Sort arr according to order. -1 in order means stay in same position.
 */
export declare function sortByPositions(arr: any, order: any): void;
