import"../../admin.bundle-DI9_jvUJ.js";/* empty css                               */import"../../form-input-spin.init-BLcZ9-IP.js";import{r as h,p as I,a as A,b as v,c as q,d as R}from"../../filepond-plugin-image-preview-DrhefRSI.js";import"../../main-Cyta4iCA.js";h(I,A,v,q);R(document.querySelector("#companyLogoInput"),{labelIdle:`
    <i class="ri-upload-2-line fs-22"></i>
    <h6 class="mb-0 mt-2">Upload Your Company Logo</h6>
  `,imagePreviewHeight:120});document.addEventListener("DOMContentLoaded",function(){const l=document.getElementById("tableBody"),g=document.getElementById("addProductItem");document.getElementById("shippingCharge").value=15 .toFixed(2),g.addEventListener("click",f),s(),l.addEventListener("click",function(t){if(t.target.classList.contains("remove-item")||t.target.parentElement&&t.target.parentElement.classList.contains("remove-item")){const e=u(t.target,"TR");e&&e.cells[0].textContent!==""&&(e.remove(),d(),s()),t.preventDefault()}if(t.target.classList.contains("input-spin-minus")||t.target.parentElement&&t.target.parentElement.classList.contains("input-spin-minus")){const e=u(t.target,"TR"),n=e.querySelector(".input-spin");let o=parseInt(n.value);o>1&&(n.value=--o,r(e),s())}if(t.target.classList.contains("input-spin-plus")||t.target.parentElement&&t.target.parentElement.classList.contains("input-spin-plus")){const e=u(t.target,"TR"),n=e.querySelector(".input-spin");let o=parseInt(n.value);n.value=++o,r(e),s()}}),l.addEventListener("input",function(t){if(t.target.tagName==="INPUT"&&t.target.type==="number"){const e=u(t.target,"TR");e&&e.cells.length>1&&(r(e),s())}});function f(){const t=l.querySelector("tr");if(!t)return;const e=t.cloneNode(!0);e.querySelectorAll('input[type="text"], input[type="number"]').forEach(i=>{i.classList.contains("input-spin")?i.value="1":i.value=""});const o=l.querySelector("tr:nth-child(2)");l.insertBefore(e,o),d()}function d(){const t=l.querySelectorAll("tr");let e=1;t.forEach(n=>{n.firstElementChild&&!n.firstElementChild.hasAttribute("colspan")&&(n.firstElementChild.textContent=e++)})}function r(t){const e=t.querySelector(".input-spin"),n=t.querySelector("td:nth-child(4) input"),o=t.querySelector("td:nth-child(5) input"),i=t.querySelector("td:nth-child(6) input");if(!e||!n||!o||!i)return;const p=parseInt(e.value)||0,a=parseFloat(n.value)||0,c=parseFloat(o.value)||0,y=a*(c/100),E=(a-y)*p;i.value=E.toFixed(2)}function s(){let t=0;Array.from(l.querySelectorAll("tr")).filter(a=>a.firstElementChild&&!a.firstElementChild.hasAttribute("colspan")&&a.cells.length>5).forEach(a=>{const c=a.querySelector("td:nth-child(6) input");c&&(t+=parseFloat(c.value)||0)}),document.getElementById("subTotal").value=t.toFixed(2);const n=t*.06;document.getElementById("vatAmount").value=n.toFixed(2);const o=t*.1;document.getElementById("discount").value=o.toFixed(2);const i=parseFloat(document.getElementById("shippingCharge").value)||0,p=t+n-o+i;document.getElementById("totalAmount").value=p.toFixed(2)}function u(t,e){for(;t&&t.tagName!==e.toUpperCase();)t=t.parentElement;return t}const m=l.querySelector("tr");m&&r(m)});VirtualSelect.init({ele:"#invoiceStatus",options:[{label:"All",value:"All"},{label:"Paid",value:"Paid"},{label:"Unpaid",value:"Unpaid"},{label:"Pending",value:"Pending"},{label:"Overdue",value:"Overdue"}]});
