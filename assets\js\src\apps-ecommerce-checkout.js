import{c as S,i as q}from"../../admin.bundle-DI9_jvUJ.js";import"../../main-Cyta4iCA.js";let o=[{id:1,firstName:"Jordan",lastName:"Roughley",phoneNumber:"2015184185",alternatePhoneNumber:"",address:"13833 Jayda Squares Apt. 849",city:"Sharonville",country:"Washington, USA",zipCode:"33199 8539",addressType:"Home"},{id:2,firstName:"Prof. <PERSON>",lastName:"Funk",phoneNumber:"6179419815",alternatePhoneNumber:"",address:"6602 Schroeder Ville Apt. 066",city:"Bennytown",country:"USA",zipCode:"62144 1437",addressType:"Work"}];document.querySelectorAll(".btn-close").forEach(t=>{t.addEventListener("click",function(){const e=this.closest(".mb-3");e&&e.remove()})});document.addEventListener("DOMContentLoaded",function(){n(),document.querySelectorAll(".btn-close").forEach(d=>{d.addEventListener("click",function(){const s=this.closest(".mb-3");s&&(s.remove(),n())})});const e=document.getElementById("discountCode");e&&e.addEventListener("blur",function(){this.value.trim()!==""?n(!0):n(!1)});function n(d=!1){const s=document.querySelectorAll(".card-body > .mb-3:not(:last-of-type)");let r=0;s.forEach(c=>{const a=c.querySelector(".total-price");if(a){const u=parseFloat(a.textContent),m=c.querySelector(".text-muted span:first-child").textContent,l=parseInt(m.match(/\d+/)[0]);r+=u*(l/l)}});const h=document.querySelector("tr:nth-child(1) span");h&&(h.textContent=r.toFixed(2));const I=r*.06,E=document.querySelector("tr:nth-child(2) span");E&&(E.textContent=I.toFixed(2));let y=0;d&&(y=r*.1);const g=document.querySelector("tr:nth-child(3) span");g&&(g.textContent=y.toFixed(2));const N=r+I-y+35,C=document.querySelector("tr:last-child span");if(C&&(C.textContent=N.toFixed(2)),s.length===0){const c=document.querySelector(".card-body");if(c){const a=document.querySelector(".discount-code"),u=document.querySelector("table"),m=document.querySelector(".btn-primary"),l=document.querySelector("#termsAgree");a&&(a.style.display="none"),u&&(u.style.display="none"),m&&(m.style.display="none"),l&&(l.style.display="none");const f=document.createElement("div");f.className="text-center py-5",f.innerHTML=`
                    <i class="fs-40 text-muted" data-lucide="shopping-cart"></i>
                    <h5 class="mt-3">Your cart is empty</h5>
                    <p class="text-muted">Browse our products and discover amazing deals!</p>
                    <a href="apps-ecommerce-products.html" class="btn btn-primary mt-3">Continue Shopping</a>
                `,c.appendChild(f),S({icons:q})}}}});let i=null;const B=document.getElementById("addressForm"),A=document.getElementById("resetBtn"),v=document.getElementById("addressContainer"),x=document.getElementById("addressCardTemplate");document.addEventListener("DOMContentLoaded",()=>{p(),L()});function L(){B.addEventListener("submit",w),A.addEventListener("click",b)}function p(){v.innerHTML="",o.forEach((t,e)=>{const n=T(t);e===0&&(n.querySelector(".select-address").classList.add("btn-primary"),n.querySelector(".select-address").classList.remove("btn-light")),v.appendChild(n)})}function T(t){const e=x.content.cloneNode(!0);e.querySelector(".address-type").textContent=t.addressType,e.querySelector(".address-name").textContent=`${t.firstName} ${t.lastName} - ${t.phoneNumber}`,e.querySelector(".address-details").textContent=`${t.address}, ${t.city}, ${t.country} - ${t.zipCode}`;const n=e.querySelector(".card");return n.dataset.addressId=t.id,e.querySelector(".edit-address").addEventListener("click",()=>z(t.id)),e.querySelector(".delete-address").addEventListener("click",()=>D(t.id)),e.querySelector(".select-address").addEventListener("click",()=>P(t.id)),e}function w(t){t.preventDefault();const e={firstName:document.getElementById("firstNameInput").value,lastName:document.getElementById("lastNameInput").value,phoneNumber:document.getElementById("phoneNumberInput").value,alternatePhoneNumber:document.getElementById("alternatePhoneNumberInput").value,address:document.getElementById("addressInput").value,city:document.getElementById("cityDistrictTownInput").value,country:document.getElementById("countryNameInput").value,zipCode:document.getElementById("zipCodeInput").value,addressType:document.querySelector('input[name="addressType"]:checked').value};i?k(i,e):F(e),b(),window.bootstrap.Collapse.getInstance(document.getElementById("addressFormCollapse")).hide()}function F(t){const n={id:o.length>0?Math.max(...o.map(d=>d.id))+1:1,...t};o.push(n),p()}function k(t,e){const n=o.findIndex(d=>d.id===t);n!==-1&&(o[n]={...o[n],...e},i=null,document.getElementById("formTitle").textContent="Add a New Address",p())}function z(t){const e=o.find(n=>n.id===t);e&&(i=t,document.getElementById("formTitle").textContent="Edit Address",document.getElementById("firstNameInput").value=e.firstName,document.getElementById("lastNameInput").value=e.lastName,document.getElementById("phoneNumberInput").value=e.phoneNumber,document.getElementById("alternatePhoneNumberInput").value=e.alternatePhoneNumber||"",document.getElementById("addressInput").value=e.address,document.getElementById("cityDistrictTownInput").value=e.city,document.getElementById("countryNameInput").value=e.country,document.getElementById("zipCodeInput").value=e.zipCode,document.getElementById(e.addressType).checked=!0,new window.bootstrap.Collapse(document.getElementById("addressFormCollapse")).show())}function D(t){o=o.filter(e=>e.id!==t),p()}function P(t){o.find(n=>n.id===t)&&document.querySelectorAll(".select-address").forEach(d=>{const s=d.closest(".card");parseInt(s.dataset.addressId)===t?(d.classList.remove("btn-light"),d.classList.add("btn-primary")):(d.classList.remove("btn-primary"),d.classList.add("btn-light"))})}function b(){B.reset(),i=null,document.getElementById("formTitle").textContent="Add a New Address"}
