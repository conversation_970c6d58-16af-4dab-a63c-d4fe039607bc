var Dr=(L,S)=>()=>(S||L((S={exports:{}}).exports,S),S.exports);import"../../admin.bundle-DI9_jvUJ.js";import"../../aos.init-Di4NEl7u.js";var Ir=Dr((ss,di)=>{/*!
  * Bootstrap v5.3.6 (https://getbootstrap.com/)
  * Copyright 2011-2025 The Bootstrap Authors (https://github.com/twbs/bootstrap/graphs/contributors)
  * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
  */(function(L,S){typeof ss=="object"&&typeof di<"u"?di.exports=S():typeof define=="function"&&define.amd?define(S):(L=typeof globalThis<"u"?globalThis:L||self).bootstrap=S()})(void 0,function(){const L=new Map,S={set(i,e,t){L.has(i)||L.set(i,new Map);const n=L.get(i);n.has(e)||n.size===0?n.set(e,t):console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(n.keys())[0]}.`)},get:(i,e)=>L.has(i)&&L.get(i).get(e)||null,remove(i,e){if(!L.has(i))return;const t=L.get(i);t.delete(e),t.size===0&&L.delete(i)}},F="transitionend",J=i=>(i&&window.CSS&&window.CSS.escape&&(i=i.replace(/#([^\s"#']+)/g,(e,t)=>`#${CSS.escape(t)}`)),i),it=i=>{i.dispatchEvent(new Event(F))},H=i=>!(!i||typeof i!="object")&&(i.jquery!==void 0&&(i=i[0]),i.nodeType!==void 0),he=i=>H(i)?i.jquery?i[0]:i:typeof i=="string"&&i.length>0?document.querySelector(J(i)):null,xe=i=>{if(!H(i)||i.getClientRects().length===0)return!1;const e=getComputedStyle(i).getPropertyValue("visibility")==="visible",t=i.closest("details:not([open])");if(!t)return e;if(t!==i){const n=i.closest("summary");if(n&&n.parentNode!==t||n===null)return!1}return e},ue=i=>!i||i.nodeType!==Node.ELEMENT_NODE||!!i.classList.contains("disabled")||(i.disabled!==void 0?i.disabled:i.hasAttribute("disabled")&&i.getAttribute("disabled")!=="false"),fi=i=>{if(!document.documentElement.attachShadow)return null;if(typeof i.getRootNode=="function"){const e=i.getRootNode();return e instanceof ShadowRoot?e:null}return i instanceof ShadowRoot?i:i.parentNode?fi(i.parentNode):null},nt=()=>{},Re=i=>{i.offsetHeight},pi=()=>window.jQuery&&!document.body.hasAttribute("data-bs-no-jquery")?window.jQuery:null,Ot=[],V=()=>document.documentElement.dir==="rtl",X=i=>{var e;e=()=>{const t=pi();if(t){const n=i.NAME,s=t.fn[n];t.fn[n]=i.jQueryInterface,t.fn[n].Constructor=i,t.fn[n].noConflict=()=>(t.fn[n]=s,i.jQueryInterface)}},document.readyState==="loading"?(Ot.length||document.addEventListener("DOMContentLoaded",()=>{for(const t of Ot)t()}),Ot.push(e)):e()},B=(i,e=[],t=i)=>typeof i=="function"?i.call(...e):t,mi=(i,e,t=!0)=>{if(!t)return void B(i);const n=(r=>{if(!r)return 0;let{transitionDuration:a,transitionDelay:c}=window.getComputedStyle(r);const u=Number.parseFloat(a),d=Number.parseFloat(c);return u||d?(a=a.split(",")[0],c=c.split(",")[0],1e3*(Number.parseFloat(a)+Number.parseFloat(c))):0})(e)+5;let s=!1;const o=({target:r})=>{r===e&&(s=!0,e.removeEventListener(F,o),B(i))};e.addEventListener(F,o),setTimeout(()=>{s||it(e)},n)},xt=(i,e,t,n)=>{const s=i.length;let o=i.indexOf(e);return o===-1?!t&&n?i[s-1]:i[0]:(o+=t?1:-1,n&&(o=(o+s)%s),i[Math.max(0,Math.min(o,s-1))])},os=/[^.]*(?=\..*)\.|.*/,rs=/\..*/,as=/::\d+$/,kt={};let gi=1;const _i={mouseenter:"mouseover",mouseleave:"mouseout"},ls=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function bi(i,e){return e&&`${e}::${gi++}`||i.uidEvent||gi++}function vi(i){const e=bi(i);return i.uidEvent=e,kt[e]=kt[e]||{},kt[e]}function yi(i,e,t=null){return Object.values(i).find(n=>n.callable===e&&n.delegationSelector===t)}function wi(i,e,t){const n=typeof e=="string",s=n?t:e||t;let o=Ai(i);return ls.has(o)||(o=i),[n,s,o]}function Ei(i,e,t,n,s){if(typeof e!="string"||!i)return;let[o,r,a]=wi(e,t,n);e in _i&&(r=(g=>function(m){if(!m.relatedTarget||m.relatedTarget!==m.delegateTarget&&!m.delegateTarget.contains(m.relatedTarget))return g.call(this,m)})(r));const c=vi(i),u=c[a]||(c[a]={}),d=yi(u,r,o?t:null);if(d)return void(d.oneOff=d.oneOff&&s);const h=bi(r,e.replace(os,"")),b=o?function(p,g,m){return function _(C){const k=p.querySelectorAll(g);for(let{target:y}=C;y&&y!==this;y=y.parentNode)for(const E of k)if(E===y)return St(C,{delegateTarget:y}),_.oneOff&&l.off(p,C.type,g,m),m.apply(y,[C])}}(i,t,r):function(p,g){return function m(_){return St(_,{delegateTarget:p}),m.oneOff&&l.off(p,_.type,g),g.apply(p,[_])}}(i,r);b.delegationSelector=o?t:null,b.callable=r,b.oneOff=s,b.uidEvent=h,u[h]=b,i.addEventListener(a,b,o)}function Lt(i,e,t,n,s){const o=yi(e[t],n,s);o&&(i.removeEventListener(t,o,!!s),delete e[t][o.uidEvent])}function cs(i,e,t,n){const s=e[t]||{};for(const[o,r]of Object.entries(s))o.includes(n)&&Lt(i,e,t,r.callable,r.delegationSelector)}function Ai(i){return i=i.replace(rs,""),_i[i]||i}const l={on(i,e,t,n){Ei(i,e,t,n,!1)},one(i,e,t,n){Ei(i,e,t,n,!0)},off(i,e,t,n){if(typeof e!="string"||!i)return;const[s,o,r]=wi(e,t,n),a=r!==e,c=vi(i),u=c[r]||{},d=e.startsWith(".");if(o===void 0){if(d)for(const h of Object.keys(c))cs(i,c,h,e.slice(1));for(const[h,b]of Object.entries(u)){const p=h.replace(as,"");a&&!e.includes(p)||Lt(i,c,r,b.callable,b.delegationSelector)}}else{if(!Object.keys(u).length)return;Lt(i,c,r,o,s?t:null)}},trigger(i,e,t){if(typeof e!="string"||!i)return null;const n=pi();let s=null,o=!0,r=!0,a=!1;e!==Ai(e)&&n&&(s=n.Event(e,t),n(i).trigger(s),o=!s.isPropagationStopped(),r=!s.isImmediatePropagationStopped(),a=s.isDefaultPrevented());const c=St(new Event(e,{bubbles:o,cancelable:!0}),t);return a&&c.preventDefault(),r&&i.dispatchEvent(c),c.defaultPrevented&&s&&s.preventDefault(),c}};function St(i,e={}){for(const[t,n]of Object.entries(e))try{i[t]=n}catch{Object.defineProperty(i,t,{configurable:!0,get:()=>n})}return i}function Ti(i){if(i==="true")return!0;if(i==="false")return!1;if(i===Number(i).toString())return Number(i);if(i===""||i==="null")return null;if(typeof i!="string")return i;try{return JSON.parse(decodeURIComponent(i))}catch{return i}}function Dt(i){return i.replace(/[A-Z]/g,e=>`-${e.toLowerCase()}`)}const re={setDataAttribute(i,e,t){i.setAttribute(`data-bs-${Dt(e)}`,t)},removeDataAttribute(i,e){i.removeAttribute(`data-bs-${Dt(e)}`)},getDataAttributes(i){if(!i)return{};const e={},t=Object.keys(i.dataset).filter(n=>n.startsWith("bs")&&!n.startsWith("bsConfig"));for(const n of t){let s=n.replace(/^bs/,"");s=s.charAt(0).toLowerCase()+s.slice(1),e[s]=Ti(i.dataset[n])}return e},getDataAttribute:(i,e)=>Ti(i.getAttribute(`data-bs-${Dt(e)}`))};class Ve{static get Default(){return{}}static get DefaultType(){return{}}static get NAME(){throw new Error('You have to implement the static method "NAME", for each component!')}_getConfig(e){return e=this._mergeConfigObj(e),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}_configAfterMerge(e){return e}_mergeConfigObj(e,t){const n=H(t)?re.getDataAttribute(t,"config"):{};return{...this.constructor.Default,...typeof n=="object"?n:{},...H(t)?re.getDataAttributes(t):{},...typeof e=="object"?e:{}}}_typeCheckConfig(e,t=this.constructor.DefaultType){for(const[s,o]of Object.entries(t)){const r=e[s],a=H(r)?"element":(n=r)==null?`${n}`:Object.prototype.toString.call(n).match(/\s([a-z]+)/i)[1].toLowerCase();if(!new RegExp(o).test(a))throw new TypeError(`${this.constructor.NAME.toUpperCase()}: Option "${s}" provided type "${a}" but expected type "${o}".`)}var n}}class G extends Ve{constructor(e,t){super(),(e=he(e))&&(this._element=e,this._config=this._getConfig(t),S.set(this._element,this.constructor.DATA_KEY,this))}dispose(){S.remove(this._element,this.constructor.DATA_KEY),l.off(this._element,this.constructor.EVENT_KEY);for(const e of Object.getOwnPropertyNames(this))this[e]=null}_queueCallback(e,t,n=!0){mi(e,t,n)}_getConfig(e){return e=this._mergeConfigObj(e,this._element),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}static getInstance(e){return S.get(he(e),this.DATA_KEY)}static getOrCreateInstance(e,t={}){return this.getInstance(e)||new this(e,typeof t=="object"?t:null)}static get VERSION(){return"5.3.6"}static get DATA_KEY(){return`bs.${this.NAME}`}static get EVENT_KEY(){return`.${this.DATA_KEY}`}static eventName(e){return`${e}${this.EVENT_KEY}`}}const It=i=>{let e=i.getAttribute("data-bs-target");if(!e||e==="#"){let t=i.getAttribute("href");if(!t||!t.includes("#")&&!t.startsWith("."))return null;t.includes("#")&&!t.startsWith("#")&&(t=`#${t.split("#")[1]}`),e=t&&t!=="#"?t.trim():null}return e?e.split(",").map(t=>J(t)).join(","):null},f={find:(i,e=document.documentElement)=>[].concat(...Element.prototype.querySelectorAll.call(e,i)),findOne:(i,e=document.documentElement)=>Element.prototype.querySelector.call(e,i),children:(i,e)=>[].concat(...i.children).filter(t=>t.matches(e)),parents(i,e){const t=[];let n=i.parentNode.closest(e);for(;n;)t.push(n),n=n.parentNode.closest(e);return t},prev(i,e){let t=i.previousElementSibling;for(;t;){if(t.matches(e))return[t];t=t.previousElementSibling}return[]},next(i,e){let t=i.nextElementSibling;for(;t;){if(t.matches(e))return[t];t=t.nextElementSibling}return[]},focusableChildren(i){const e=["a","button","input","textarea","select","details","[tabindex]",'[contenteditable="true"]'].map(t=>`${t}:not([tabindex^="-"])`).join(",");return this.find(e,i).filter(t=>!ue(t)&&xe(t))},getSelectorFromElement(i){const e=It(i);return e&&f.findOne(e)?e:null},getElementFromSelector(i){const e=It(i);return e?f.findOne(e):null},getMultipleElementsFromSelector(i){const e=It(i);return e?f.find(e):[]}},st=(i,e="hide")=>{const t=`click.dismiss${i.EVENT_KEY}`,n=i.NAME;l.on(document,t,`[data-bs-dismiss="${n}"]`,function(s){if(["A","AREA"].includes(this.tagName)&&s.preventDefault(),ue(this))return;const o=f.getElementFromSelector(this)||this.closest(`.${n}`);i.getOrCreateInstance(o)[e]()})},Ci=".bs.alert",hs=`close${Ci}`,us=`closed${Ci}`;class Xe extends G{static get NAME(){return"alert"}close(){if(l.trigger(this._element,hs).defaultPrevented)return;this._element.classList.remove("show");const e=this._element.classList.contains("fade");this._queueCallback(()=>this._destroyElement(),this._element,e)}_destroyElement(){this._element.remove(),l.trigger(this._element,us),this.dispose()}static jQueryInterface(e){return this.each(function(){const t=Xe.getOrCreateInstance(this);if(typeof e=="string"){if(t[e]===void 0||e.startsWith("_")||e==="constructor")throw new TypeError(`No method named "${e}"`);t[e](this)}})}}st(Xe,"close"),X(Xe);const Oi='[data-bs-toggle="button"]';class Ye extends G{static get NAME(){return"button"}toggle(){this._element.setAttribute("aria-pressed",this._element.classList.toggle("active"))}static jQueryInterface(e){return this.each(function(){const t=Ye.getOrCreateInstance(this);e==="toggle"&&t[e]()})}}l.on(document,"click.bs.button.data-api",Oi,i=>{i.preventDefault();const e=i.target.closest(Oi);Ye.getOrCreateInstance(e).toggle()}),X(Ye);const ke=".bs.swipe",ds=`touchstart${ke}`,fs=`touchmove${ke}`,ps=`touchend${ke}`,ms=`pointerdown${ke}`,gs=`pointerup${ke}`,_s={endCallback:null,leftCallback:null,rightCallback:null},bs={endCallback:"(function|null)",leftCallback:"(function|null)",rightCallback:"(function|null)"};class ot extends Ve{constructor(e,t){super(),this._element=e,e&&ot.isSupported()&&(this._config=this._getConfig(t),this._deltaX=0,this._supportPointerEvents=!!window.PointerEvent,this._initEvents())}static get Default(){return _s}static get DefaultType(){return bs}static get NAME(){return"swipe"}dispose(){l.off(this._element,ke)}_start(e){this._supportPointerEvents?this._eventIsPointerPenTouch(e)&&(this._deltaX=e.clientX):this._deltaX=e.touches[0].clientX}_end(e){this._eventIsPointerPenTouch(e)&&(this._deltaX=e.clientX-this._deltaX),this._handleSwipe(),B(this._config.endCallback)}_move(e){this._deltaX=e.touches&&e.touches.length>1?0:e.touches[0].clientX-this._deltaX}_handleSwipe(){const e=Math.abs(this._deltaX);if(e<=40)return;const t=e/this._deltaX;this._deltaX=0,t&&B(t>0?this._config.rightCallback:this._config.leftCallback)}_initEvents(){this._supportPointerEvents?(l.on(this._element,ms,e=>this._start(e)),l.on(this._element,gs,e=>this._end(e)),this._element.classList.add("pointer-event")):(l.on(this._element,ds,e=>this._start(e)),l.on(this._element,fs,e=>this._move(e)),l.on(this._element,ps,e=>this._end(e)))}_eventIsPointerPenTouch(e){return this._supportPointerEvents&&(e.pointerType==="pen"||e.pointerType==="touch")}static isSupported(){return"ontouchstart"in document.documentElement||navigator.maxTouchPoints>0}}const de=".bs.carousel",xi=".data-api",vs="ArrowLeft",ys="ArrowRight",Ue="next",Le="prev",Se="left",rt="right",ws=`slide${de}`,$t=`slid${de}`,Es=`keydown${de}`,As=`mouseenter${de}`,Ts=`mouseleave${de}`,Cs=`dragstart${de}`,Os=`load${de}${xi}`,xs=`click${de}${xi}`,ki="carousel",at="active",Li=".active",Si=".carousel-item",ks=Li+Si,Ls={[vs]:rt,[ys]:Se},Ss={interval:5e3,keyboard:!0,pause:"hover",ride:!1,touch:!0,wrap:!0},Ds={interval:"(number|boolean)",keyboard:"boolean",pause:"(string|boolean)",ride:"(boolean|string)",touch:"boolean",wrap:"boolean"};class De extends G{constructor(e,t){super(e,t),this._interval=null,this._activeElement=null,this._isSliding=!1,this.touchTimeout=null,this._swipeHelper=null,this._indicatorsElement=f.findOne(".carousel-indicators",this._element),this._addEventListeners(),this._config.ride===ki&&this.cycle()}static get Default(){return Ss}static get DefaultType(){return Ds}static get NAME(){return"carousel"}next(){this._slide(Ue)}nextWhenVisible(){!document.hidden&&xe(this._element)&&this.next()}prev(){this._slide(Le)}pause(){this._isSliding&&it(this._element),this._clearInterval()}cycle(){this._clearInterval(),this._updateInterval(),this._interval=setInterval(()=>this.nextWhenVisible(),this._config.interval)}_maybeEnableCycle(){this._config.ride&&(this._isSliding?l.one(this._element,$t,()=>this.cycle()):this.cycle())}to(e){const t=this._getItems();if(e>t.length-1||e<0)return;if(this._isSliding)return void l.one(this._element,$t,()=>this.to(e));const n=this._getItemIndex(this._getActive());if(n===e)return;const s=e>n?Ue:Le;this._slide(s,t[e])}dispose(){this._swipeHelper&&this._swipeHelper.dispose(),super.dispose()}_configAfterMerge(e){return e.defaultInterval=e.interval,e}_addEventListeners(){this._config.keyboard&&l.on(this._element,Es,e=>this._keydown(e)),this._config.pause==="hover"&&(l.on(this._element,As,()=>this.pause()),l.on(this._element,Ts,()=>this._maybeEnableCycle())),this._config.touch&&ot.isSupported()&&this._addTouchEventListeners()}_addTouchEventListeners(){for(const t of f.find(".carousel-item img",this._element))l.on(t,Cs,n=>n.preventDefault());const e={leftCallback:()=>this._slide(this._directionToOrder(Se)),rightCallback:()=>this._slide(this._directionToOrder(rt)),endCallback:()=>{this._config.pause==="hover"&&(this.pause(),this.touchTimeout&&clearTimeout(this.touchTimeout),this.touchTimeout=setTimeout(()=>this._maybeEnableCycle(),500+this._config.interval))}};this._swipeHelper=new ot(this._element,e)}_keydown(e){if(/input|textarea/i.test(e.target.tagName))return;const t=Ls[e.key];t&&(e.preventDefault(),this._slide(this._directionToOrder(t)))}_getItemIndex(e){return this._getItems().indexOf(e)}_setActiveIndicatorElement(e){if(!this._indicatorsElement)return;const t=f.findOne(Li,this._indicatorsElement);t.classList.remove(at),t.removeAttribute("aria-current");const n=f.findOne(`[data-bs-slide-to="${e}"]`,this._indicatorsElement);n&&(n.classList.add(at),n.setAttribute("aria-current","true"))}_updateInterval(){const e=this._activeElement||this._getActive();if(!e)return;const t=Number.parseInt(e.getAttribute("data-bs-interval"),10);this._config.interval=t||this._config.defaultInterval}_slide(e,t=null){if(this._isSliding)return;const n=this._getActive(),s=e===Ue,o=t||xt(this._getItems(),n,s,this._config.wrap);if(o===n)return;const r=this._getItemIndex(o),a=h=>l.trigger(this._element,h,{relatedTarget:o,direction:this._orderToDirection(e),from:this._getItemIndex(n),to:r});if(a(ws).defaultPrevented||!n||!o)return;const c=!!this._interval;this.pause(),this._isSliding=!0,this._setActiveIndicatorElement(r),this._activeElement=o;const u=s?"carousel-item-start":"carousel-item-end",d=s?"carousel-item-next":"carousel-item-prev";o.classList.add(d),Re(o),n.classList.add(u),o.classList.add(u),this._queueCallback(()=>{o.classList.remove(u,d),o.classList.add(at),n.classList.remove(at,d,u),this._isSliding=!1,a($t)},n,this._isAnimated()),c&&this.cycle()}_isAnimated(){return this._element.classList.contains("slide")}_getActive(){return f.findOne(ks,this._element)}_getItems(){return f.find(Si,this._element)}_clearInterval(){this._interval&&(clearInterval(this._interval),this._interval=null)}_directionToOrder(e){return V()?e===Se?Le:Ue:e===Se?Ue:Le}_orderToDirection(e){return V()?e===Le?Se:rt:e===Le?rt:Se}static jQueryInterface(e){return this.each(function(){const t=De.getOrCreateInstance(this,e);if(typeof e!="number"){if(typeof e=="string"){if(t[e]===void 0||e.startsWith("_")||e==="constructor")throw new TypeError(`No method named "${e}"`);t[e]()}}else t.to(e)})}}l.on(document,xs,"[data-bs-slide], [data-bs-slide-to]",function(i){const e=f.getElementFromSelector(this);if(!e||!e.classList.contains(ki))return;i.preventDefault();const t=De.getOrCreateInstance(e),n=this.getAttribute("data-bs-slide-to");return n?(t.to(n),void t._maybeEnableCycle()):re.getDataAttribute(this,"slide")==="next"?(t.next(),void t._maybeEnableCycle()):(t.prev(),void t._maybeEnableCycle())}),l.on(window,Os,()=>{const i=f.find('[data-bs-ride="carousel"]');for(const e of i)De.getOrCreateInstance(e)}),X(De);const Ke=".bs.collapse",Is=`show${Ke}`,$s=`shown${Ke}`,Ns=`hide${Ke}`,Ps=`hidden${Ke}`,Ms=`click${Ke}.data-api`,Nt="show",Ie="collapse",lt="collapsing",js=`:scope .${Ie} .${Ie}`,Pt='[data-bs-toggle="collapse"]',Fs={parent:null,toggle:!0},Hs={parent:"(null|element)",toggle:"boolean"};class $e extends G{constructor(e,t){super(e,t),this._isTransitioning=!1,this._triggerArray=[];const n=f.find(Pt);for(const s of n){const o=f.getSelectorFromElement(s),r=f.find(o).filter(a=>a===this._element);o!==null&&r.length&&this._triggerArray.push(s)}this._initializeChildren(),this._config.parent||this._addAriaAndCollapsedClass(this._triggerArray,this._isShown()),this._config.toggle&&this.toggle()}static get Default(){return Fs}static get DefaultType(){return Hs}static get NAME(){return"collapse"}toggle(){this._isShown()?this.hide():this.show()}show(){if(this._isTransitioning||this._isShown())return;let e=[];if(this._config.parent&&(e=this._getFirstLevelChildren(".collapse.show, .collapse.collapsing").filter(s=>s!==this._element).map(s=>$e.getOrCreateInstance(s,{toggle:!1}))),e.length&&e[0]._isTransitioning||l.trigger(this._element,Is).defaultPrevented)return;for(const s of e)s.hide();const t=this._getDimension();this._element.classList.remove(Ie),this._element.classList.add(lt),this._element.style[t]=0,this._addAriaAndCollapsedClass(this._triggerArray,!0),this._isTransitioning=!0;const n=`scroll${t[0].toUpperCase()+t.slice(1)}`;this._queueCallback(()=>{this._isTransitioning=!1,this._element.classList.remove(lt),this._element.classList.add(Ie,Nt),this._element.style[t]="",l.trigger(this._element,$s)},this._element,!0),this._element.style[t]=`${this._element[n]}px`}hide(){if(this._isTransitioning||!this._isShown()||l.trigger(this._element,Ns).defaultPrevented)return;const e=this._getDimension();this._element.style[e]=`${this._element.getBoundingClientRect()[e]}px`,Re(this._element),this._element.classList.add(lt),this._element.classList.remove(Ie,Nt);for(const t of this._triggerArray){const n=f.getElementFromSelector(t);n&&!this._isShown(n)&&this._addAriaAndCollapsedClass([t],!1)}this._isTransitioning=!0,this._element.style[e]="",this._queueCallback(()=>{this._isTransitioning=!1,this._element.classList.remove(lt),this._element.classList.add(Ie),l.trigger(this._element,Ps)},this._element,!0)}_isShown(e=this._element){return e.classList.contains(Nt)}_configAfterMerge(e){return e.toggle=!!e.toggle,e.parent=he(e.parent),e}_getDimension(){return this._element.classList.contains("collapse-horizontal")?"width":"height"}_initializeChildren(){if(!this._config.parent)return;const e=this._getFirstLevelChildren(Pt);for(const t of e){const n=f.getElementFromSelector(t);n&&this._addAriaAndCollapsedClass([t],this._isShown(n))}}_getFirstLevelChildren(e){const t=f.find(js,this._config.parent);return f.find(e,this._config.parent).filter(n=>!t.includes(n))}_addAriaAndCollapsedClass(e,t){if(e.length)for(const n of e)n.classList.toggle("collapsed",!t),n.setAttribute("aria-expanded",t)}static jQueryInterface(e){const t={};return typeof e=="string"&&/show|hide/.test(e)&&(t.toggle=!1),this.each(function(){const n=$e.getOrCreateInstance(this,t);if(typeof e=="string"){if(n[e]===void 0)throw new TypeError(`No method named "${e}"`);n[e]()}})}}l.on(document,Ms,Pt,function(i){(i.target.tagName==="A"||i.delegateTarget&&i.delegateTarget.tagName==="A")&&i.preventDefault();for(const e of f.getMultipleElementsFromSelector(this))$e.getOrCreateInstance(e,{toggle:!1}).toggle()}),X($e);var N="top",W="bottom",z="right",P="left",ct="auto",Ne=[N,W,z,P],ge="start",Pe="end",Di="clippingParents",Mt="viewport",Me="popper",Ii="reference",jt=Ne.reduce(function(i,e){return i.concat([e+"-"+ge,e+"-"+Pe])},[]),Ft=[].concat(Ne,[ct]).reduce(function(i,e){return i.concat([e,e+"-"+ge,e+"-"+Pe])},[]),$i="beforeRead",Ni="read",Pi="afterRead",Mi="beforeMain",ji="main",Fi="afterMain",Hi="beforeWrite",Bi="write",Wi="afterWrite",zi=[$i,Ni,Pi,Mi,ji,Fi,Hi,Bi,Wi];function ie(i){return i?(i.nodeName||"").toLowerCase():null}function q(i){if(i==null)return window;if(i.toString()!=="[object Window]"){var e=i.ownerDocument;return e&&e.defaultView||window}return i}function _e(i){return i instanceof q(i).Element||i instanceof Element}function Y(i){return i instanceof q(i).HTMLElement||i instanceof HTMLElement}function Ht(i){return typeof ShadowRoot<"u"&&(i instanceof q(i).ShadowRoot||i instanceof ShadowRoot)}const Bt={name:"applyStyles",enabled:!0,phase:"write",fn:function(i){var e=i.state;Object.keys(e.elements).forEach(function(t){var n=e.styles[t]||{},s=e.attributes[t]||{},o=e.elements[t];Y(o)&&ie(o)&&(Object.assign(o.style,n),Object.keys(s).forEach(function(r){var a=s[r];a===!1?o.removeAttribute(r):o.setAttribute(r,a===!0?"":a)}))})},effect:function(i){var e=i.state,t={popper:{position:e.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(e.elements.popper.style,t.popper),e.styles=t,e.elements.arrow&&Object.assign(e.elements.arrow.style,t.arrow),function(){Object.keys(e.elements).forEach(function(n){var s=e.elements[n],o=e.attributes[n]||{},r=Object.keys(e.styles.hasOwnProperty(n)?e.styles[n]:t[n]).reduce(function(a,c){return a[c]="",a},{});Y(s)&&ie(s)&&(Object.assign(s.style,r),Object.keys(o).forEach(function(a){s.removeAttribute(a)}))})}},requires:["computeStyles"]};function ne(i){return i.split("-")[0]}var be=Math.max,ht=Math.min,je=Math.round;function Wt(){var i=navigator.userAgentData;return i!=null&&i.brands&&Array.isArray(i.brands)?i.brands.map(function(e){return e.brand+"/"+e.version}).join(" "):navigator.userAgent}function qi(){return!/^((?!chrome|android).)*safari/i.test(Wt())}function Fe(i,e,t){e===void 0&&(e=!1),t===void 0&&(t=!1);var n=i.getBoundingClientRect(),s=1,o=1;e&&Y(i)&&(s=i.offsetWidth>0&&je(n.width)/i.offsetWidth||1,o=i.offsetHeight>0&&je(n.height)/i.offsetHeight||1);var r=(_e(i)?q(i):window).visualViewport,a=!qi()&&t,c=(n.left+(a&&r?r.offsetLeft:0))/s,u=(n.top+(a&&r?r.offsetTop:0))/o,d=n.width/s,h=n.height/o;return{width:d,height:h,top:u,right:c+d,bottom:u+h,left:c,x:c,y:u}}function zt(i){var e=Fe(i),t=i.offsetWidth,n=i.offsetHeight;return Math.abs(e.width-t)<=1&&(t=e.width),Math.abs(e.height-n)<=1&&(n=e.height),{x:i.offsetLeft,y:i.offsetTop,width:t,height:n}}function Ri(i,e){var t=e.getRootNode&&e.getRootNode();if(i.contains(e))return!0;if(t&&Ht(t)){var n=e;do{if(n&&i.isSameNode(n))return!0;n=n.parentNode||n.host}while(n)}return!1}function ae(i){return q(i).getComputedStyle(i)}function Bs(i){return["table","td","th"].indexOf(ie(i))>=0}function fe(i){return((_e(i)?i.ownerDocument:i.document)||window.document).documentElement}function ut(i){return ie(i)==="html"?i:i.assignedSlot||i.parentNode||(Ht(i)?i.host:null)||fe(i)}function Vi(i){return Y(i)&&ae(i).position!=="fixed"?i.offsetParent:null}function Qe(i){for(var e=q(i),t=Vi(i);t&&Bs(t)&&ae(t).position==="static";)t=Vi(t);return t&&(ie(t)==="html"||ie(t)==="body"&&ae(t).position==="static")?e:t||function(n){var s=/firefox/i.test(Wt());if(/Trident/i.test(Wt())&&Y(n)&&ae(n).position==="fixed")return null;var o=ut(n);for(Ht(o)&&(o=o.host);Y(o)&&["html","body"].indexOf(ie(o))<0;){var r=ae(o);if(r.transform!=="none"||r.perspective!=="none"||r.contain==="paint"||["transform","perspective"].indexOf(r.willChange)!==-1||s&&r.willChange==="filter"||s&&r.filter&&r.filter!=="none")return o;o=o.parentNode}return null}(i)||e}function qt(i){return["top","bottom"].indexOf(i)>=0?"x":"y"}function Ze(i,e,t){return be(i,ht(e,t))}function Xi(i){return Object.assign({},{top:0,right:0,bottom:0,left:0},i)}function Yi(i,e){return e.reduce(function(t,n){return t[n]=i,t},{})}const Ui={name:"arrow",enabled:!0,phase:"main",fn:function(i){var e,t=i.state,n=i.name,s=i.options,o=t.elements.arrow,r=t.modifiersData.popperOffsets,a=ne(t.placement),c=qt(a),u=[P,z].indexOf(a)>=0?"height":"width";if(o&&r){var d=function(O,T){return Xi(typeof(O=typeof O=="function"?O(Object.assign({},T.rects,{placement:T.placement})):O)!="number"?O:Yi(O,Ne))}(s.padding,t),h=zt(o),b=c==="y"?N:P,p=c==="y"?W:z,g=t.rects.reference[u]+t.rects.reference[c]-r[c]-t.rects.popper[u],m=r[c]-t.rects.reference[c],_=Qe(o),C=_?c==="y"?_.clientHeight||0:_.clientWidth||0:0,k=g/2-m/2,y=d[b],E=C-h[u]-d[p],v=C/2-h[u]/2+k,w=Ze(y,v,E),A=c;t.modifiersData[n]=((e={})[A]=w,e.centerOffset=w-v,e)}},effect:function(i){var e=i.state,t=i.options.element,n=t===void 0?"[data-popper-arrow]":t;n!=null&&(typeof n!="string"||(n=e.elements.popper.querySelector(n)))&&Ri(e.elements.popper,n)&&(e.elements.arrow=n)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function He(i){return i.split("-")[1]}var Ws={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Ki(i){var e,t=i.popper,n=i.popperRect,s=i.placement,o=i.variation,r=i.offsets,a=i.position,c=i.gpuAcceleration,u=i.adaptive,d=i.roundOffsets,h=i.isFixed,b=r.x,p=b===void 0?0:b,g=r.y,m=g===void 0?0:g,_=typeof d=="function"?d({x:p,y:m}):{x:p,y:m};p=_.x,m=_.y;var C=r.hasOwnProperty("x"),k=r.hasOwnProperty("y"),y=P,E=N,v=window;if(u){var w=Qe(t),A="clientHeight",O="clientWidth";w===q(t)&&ae(w=fe(t)).position!=="static"&&a==="absolute"&&(A="scrollHeight",O="scrollWidth"),(s===N||(s===P||s===z)&&o===Pe)&&(E=W,m-=(h&&w===v&&v.visualViewport?v.visualViewport.height:w[A])-n.height,m*=c?1:-1),s!==P&&(s!==N&&s!==W||o!==Pe)||(y=z,p-=(h&&w===v&&v.visualViewport?v.visualViewport.width:w[O])-n.width,p*=c?1:-1)}var T,I=Object.assign({position:a},u&&Ws),R=d===!0?function(te,M){var K=te.x,Q=te.y,D=M.devicePixelRatio||1;return{x:je(K*D)/D||0,y:je(Q*D)/D||0}}({x:p,y:m},q(t)):{x:p,y:m};return p=R.x,m=R.y,c?Object.assign({},I,((T={})[E]=k?"0":"",T[y]=C?"0":"",T.transform=(v.devicePixelRatio||1)<=1?"translate("+p+"px, "+m+"px)":"translate3d("+p+"px, "+m+"px, 0)",T)):Object.assign({},I,((e={})[E]=k?m+"px":"",e[y]=C?p+"px":"",e.transform="",e))}const Rt={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(i){var e=i.state,t=i.options,n=t.gpuAcceleration,s=n===void 0||n,o=t.adaptive,r=o===void 0||o,a=t.roundOffsets,c=a===void 0||a,u={placement:ne(e.placement),variation:He(e.placement),popper:e.elements.popper,popperRect:e.rects.popper,gpuAcceleration:s,isFixed:e.options.strategy==="fixed"};e.modifiersData.popperOffsets!=null&&(e.styles.popper=Object.assign({},e.styles.popper,Ki(Object.assign({},u,{offsets:e.modifiersData.popperOffsets,position:e.options.strategy,adaptive:r,roundOffsets:c})))),e.modifiersData.arrow!=null&&(e.styles.arrow=Object.assign({},e.styles.arrow,Ki(Object.assign({},u,{offsets:e.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:c})))),e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-placement":e.placement})},data:{}};var dt={passive:!0};const Vt={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(i){var e=i.state,t=i.instance,n=i.options,s=n.scroll,o=s===void 0||s,r=n.resize,a=r===void 0||r,c=q(e.elements.popper),u=[].concat(e.scrollParents.reference,e.scrollParents.popper);return o&&u.forEach(function(d){d.addEventListener("scroll",t.update,dt)}),a&&c.addEventListener("resize",t.update,dt),function(){o&&u.forEach(function(d){d.removeEventListener("scroll",t.update,dt)}),a&&c.removeEventListener("resize",t.update,dt)}},data:{}};var zs={left:"right",right:"left",bottom:"top",top:"bottom"};function ft(i){return i.replace(/left|right|bottom|top/g,function(e){return zs[e]})}var qs={start:"end",end:"start"};function Qi(i){return i.replace(/start|end/g,function(e){return qs[e]})}function Xt(i){var e=q(i);return{scrollLeft:e.pageXOffset,scrollTop:e.pageYOffset}}function Yt(i){return Fe(fe(i)).left+Xt(i).scrollLeft}function Ut(i){var e=ae(i),t=e.overflow,n=e.overflowX,s=e.overflowY;return/auto|scroll|overlay|hidden/.test(t+s+n)}function Zi(i){return["html","body","#document"].indexOf(ie(i))>=0?i.ownerDocument.body:Y(i)&&Ut(i)?i:Zi(ut(i))}function Je(i,e){var t;e===void 0&&(e=[]);var n=Zi(i),s=n===((t=i.ownerDocument)==null?void 0:t.body),o=q(n),r=s?[o].concat(o.visualViewport||[],Ut(n)?n:[]):n,a=e.concat(r);return s?a:a.concat(Je(ut(r)))}function Kt(i){return Object.assign({},i,{left:i.x,top:i.y,right:i.x+i.width,bottom:i.y+i.height})}function Ji(i,e,t){return e===Mt?Kt(function(n,s){var o=q(n),r=fe(n),a=o.visualViewport,c=r.clientWidth,u=r.clientHeight,d=0,h=0;if(a){c=a.width,u=a.height;var b=qi();(b||!b&&s==="fixed")&&(d=a.offsetLeft,h=a.offsetTop)}return{width:c,height:u,x:d+Yt(n),y:h}}(i,t)):_e(e)?function(n,s){var o=Fe(n,!1,s==="fixed");return o.top=o.top+n.clientTop,o.left=o.left+n.clientLeft,o.bottom=o.top+n.clientHeight,o.right=o.left+n.clientWidth,o.width=n.clientWidth,o.height=n.clientHeight,o.x=o.left,o.y=o.top,o}(e,t):Kt(function(n){var s,o=fe(n),r=Xt(n),a=(s=n.ownerDocument)==null?void 0:s.body,c=be(o.scrollWidth,o.clientWidth,a?a.scrollWidth:0,a?a.clientWidth:0),u=be(o.scrollHeight,o.clientHeight,a?a.scrollHeight:0,a?a.clientHeight:0),d=-r.scrollLeft+Yt(n),h=-r.scrollTop;return ae(a||o).direction==="rtl"&&(d+=be(o.clientWidth,a?a.clientWidth:0)-c),{width:c,height:u,x:d,y:h}}(fe(i)))}function Gi(i){var e,t=i.reference,n=i.element,s=i.placement,o=s?ne(s):null,r=s?He(s):null,a=t.x+t.width/2-n.width/2,c=t.y+t.height/2-n.height/2;switch(o){case N:e={x:a,y:t.y-n.height};break;case W:e={x:a,y:t.y+t.height};break;case z:e={x:t.x+t.width,y:c};break;case P:e={x:t.x-n.width,y:c};break;default:e={x:t.x,y:t.y}}var u=o?qt(o):null;if(u!=null){var d=u==="y"?"height":"width";switch(r){case ge:e[u]=e[u]-(t[d]/2-n[d]/2);break;case Pe:e[u]=e[u]+(t[d]/2-n[d]/2)}}return e}function Be(i,e){e===void 0&&(e={});var t=e,n=t.placement,s=n===void 0?i.placement:n,o=t.strategy,r=o===void 0?i.strategy:o,a=t.boundary,c=a===void 0?Di:a,u=t.rootBoundary,d=u===void 0?Mt:u,h=t.elementContext,b=h===void 0?Me:h,p=t.altBoundary,g=p!==void 0&&p,m=t.padding,_=m===void 0?0:m,C=Xi(typeof _!="number"?_:Yi(_,Ne)),k=b===Me?Ii:Me,y=i.rects.popper,E=i.elements[g?k:b],v=function(M,K,Q,D){var se=K==="clippingParents"?function(x){var j=Je(ut(x)),Z=["absolute","fixed"].indexOf(ae(x).position)>=0&&Y(x)?Qe(x):x;return _e(Z)?j.filter(function(me){return _e(me)&&Ri(me,Z)&&ie(me)!=="body"}):[]}(M):[].concat(K),oe=[].concat(se,[Q]),qe=oe[0],$=oe.reduce(function(x,j){var Z=Ji(M,j,D);return x.top=be(Z.top,x.top),x.right=ht(Z.right,x.right),x.bottom=ht(Z.bottom,x.bottom),x.left=be(Z.left,x.left),x},Ji(M,qe,D));return $.width=$.right-$.left,$.height=$.bottom-$.top,$.x=$.left,$.y=$.top,$}(_e(E)?E:E.contextElement||fe(i.elements.popper),c,d,r),w=Fe(i.elements.reference),A=Gi({reference:w,element:y,placement:s}),O=Kt(Object.assign({},y,A)),T=b===Me?O:w,I={top:v.top-T.top+C.top,bottom:T.bottom-v.bottom+C.bottom,left:v.left-T.left+C.left,right:T.right-v.right+C.right},R=i.modifiersData.offset;if(b===Me&&R){var te=R[s];Object.keys(I).forEach(function(M){var K=[z,W].indexOf(M)>=0?1:-1,Q=[N,W].indexOf(M)>=0?"y":"x";I[M]+=te[Q]*K})}return I}function Rs(i,e){e===void 0&&(e={});var t=e,n=t.placement,s=t.boundary,o=t.rootBoundary,r=t.padding,a=t.flipVariations,c=t.allowedAutoPlacements,u=c===void 0?Ft:c,d=He(n),h=d?a?jt:jt.filter(function(g){return He(g)===d}):Ne,b=h.filter(function(g){return u.indexOf(g)>=0});b.length===0&&(b=h);var p=b.reduce(function(g,m){return g[m]=Be(i,{placement:m,boundary:s,rootBoundary:o,padding:r})[ne(m)],g},{});return Object.keys(p).sort(function(g,m){return p[g]-p[m]})}const en={name:"flip",enabled:!0,phase:"main",fn:function(i){var e=i.state,t=i.options,n=i.name;if(!e.modifiersData[n]._skip){for(var s=t.mainAxis,o=s===void 0||s,r=t.altAxis,a=r===void 0||r,c=t.fallbackPlacements,u=t.padding,d=t.boundary,h=t.rootBoundary,b=t.altBoundary,p=t.flipVariations,g=p===void 0||p,m=t.allowedAutoPlacements,_=e.options.placement,C=ne(_),k=c||(C!==_&&g?function(x){if(ne(x)===ct)return[];var j=ft(x);return[Qi(x),j,Qi(j)]}(_):[ft(_)]),y=[_].concat(k).reduce(function(x,j){return x.concat(ne(j)===ct?Rs(e,{placement:j,boundary:d,rootBoundary:h,padding:u,flipVariations:g,allowedAutoPlacements:m}):j)},[]),E=e.rects.reference,v=e.rects.popper,w=new Map,A=!0,O=y[0],T=0;T<y.length;T++){var I=y[T],R=ne(I),te=He(I)===ge,M=[N,W].indexOf(R)>=0,K=M?"width":"height",Q=Be(e,{placement:I,boundary:d,rootBoundary:h,altBoundary:b,padding:u}),D=M?te?z:P:te?W:N;E[K]>v[K]&&(D=ft(D));var se=ft(D),oe=[];if(o&&oe.push(Q[R]<=0),a&&oe.push(Q[D]<=0,Q[se]<=0),oe.every(function(x){return x})){O=I,A=!1;break}w.set(I,oe)}if(A)for(var qe=function(x){var j=y.find(function(Z){var me=w.get(Z);if(me)return me.slice(0,x).every(function(Et){return Et})});if(j)return O=j,"break"},$=g?3:1;$>0&&qe($)!=="break";$--);e.placement!==O&&(e.modifiersData[n]._skip=!0,e.placement=O,e.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function tn(i,e,t){return t===void 0&&(t={x:0,y:0}),{top:i.top-e.height-t.y,right:i.right-e.width+t.x,bottom:i.bottom-e.height+t.y,left:i.left-e.width-t.x}}function nn(i){return[N,z,W,P].some(function(e){return i[e]>=0})}const sn={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(i){var e=i.state,t=i.name,n=e.rects.reference,s=e.rects.popper,o=e.modifiersData.preventOverflow,r=Be(e,{elementContext:"reference"}),a=Be(e,{altBoundary:!0}),c=tn(r,n),u=tn(a,s,o),d=nn(c),h=nn(u);e.modifiersData[t]={referenceClippingOffsets:c,popperEscapeOffsets:u,isReferenceHidden:d,hasPopperEscaped:h},e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-reference-hidden":d,"data-popper-escaped":h})}},on={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(i){var e=i.state,t=i.options,n=i.name,s=t.offset,o=s===void 0?[0,0]:s,r=Ft.reduce(function(d,h){return d[h]=function(b,p,g){var m=ne(b),_=[P,N].indexOf(m)>=0?-1:1,C=typeof g=="function"?g(Object.assign({},p,{placement:b})):g,k=C[0],y=C[1];return k=k||0,y=(y||0)*_,[P,z].indexOf(m)>=0?{x:y,y:k}:{x:k,y}}(h,e.rects,o),d},{}),a=r[e.placement],c=a.x,u=a.y;e.modifiersData.popperOffsets!=null&&(e.modifiersData.popperOffsets.x+=c,e.modifiersData.popperOffsets.y+=u),e.modifiersData[n]=r}},Qt={name:"popperOffsets",enabled:!0,phase:"read",fn:function(i){var e=i.state,t=i.name;e.modifiersData[t]=Gi({reference:e.rects.reference,element:e.rects.popper,placement:e.placement})},data:{}},rn={name:"preventOverflow",enabled:!0,phase:"main",fn:function(i){var e=i.state,t=i.options,n=i.name,s=t.mainAxis,o=s===void 0||s,r=t.altAxis,a=r!==void 0&&r,c=t.boundary,u=t.rootBoundary,d=t.altBoundary,h=t.padding,b=t.tether,p=b===void 0||b,g=t.tetherOffset,m=g===void 0?0:g,_=Be(e,{boundary:c,rootBoundary:u,padding:h,altBoundary:d}),C=ne(e.placement),k=He(e.placement),y=!k,E=qt(C),v=E==="x"?"y":"x",w=e.modifiersData.popperOffsets,A=e.rects.reference,O=e.rects.popper,T=typeof m=="function"?m(Object.assign({},e.rects,{placement:e.placement})):m,I=typeof T=="number"?{mainAxis:T,altAxis:T}:Object.assign({mainAxis:0,altAxis:0},T),R=e.modifiersData.offset?e.modifiersData.offset[e.placement]:null,te={x:0,y:0};if(w){if(o){var M,K=E==="y"?N:P,Q=E==="y"?W:z,D=E==="y"?"height":"width",se=w[E],oe=se+_[K],qe=se-_[Q],$=p?-O[D]/2:0,x=k===ge?A[D]:O[D],j=k===ge?-O[D]:-A[D],Z=e.elements.arrow,me=p&&Z?zt(Z):{width:0,height:0},Et=e.modifiersData["arrow#persistent"]?e.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},Xn=Et[K],Yn=Et[Q],At=Ze(0,A[D],me[D]),Ar=y?A[D]/2-$-At-Xn-I.mainAxis:x-At-Xn-I.mainAxis,Tr=y?-A[D]/2+$+At+Yn+I.mainAxis:j+At+Yn+I.mainAxis,ci=e.elements.arrow&&Qe(e.elements.arrow),Cr=ci?E==="y"?ci.clientTop||0:ci.clientLeft||0:0,Un=(M=R==null?void 0:R[E])!=null?M:0,Or=se+Tr-Un,Kn=Ze(p?ht(oe,se+Ar-Un-Cr):oe,se,p?be(qe,Or):qe);w[E]=Kn,te[E]=Kn-se}if(a){var Qn,xr=E==="x"?N:P,kr=E==="x"?W:z,Oe=w[v],Tt=v==="y"?"height":"width",Zn=Oe+_[xr],Jn=Oe-_[kr],hi=[N,P].indexOf(C)!==-1,Gn=(Qn=R==null?void 0:R[v])!=null?Qn:0,es=hi?Zn:Oe-A[Tt]-O[Tt]-Gn+I.altAxis,ts=hi?Oe+A[Tt]+O[Tt]-Gn-I.altAxis:Jn,is=p&&hi?function(Lr,Sr,ui){var ns=Ze(Lr,Sr,ui);return ns>ui?ui:ns}(es,Oe,ts):Ze(p?es:Zn,Oe,p?ts:Jn);w[v]=is,te[v]=is-Oe}e.modifiersData[n]=te}},requiresIfExists:["offset"]};function Vs(i,e,t){t===void 0&&(t=!1);var n,s,o=Y(e),r=Y(e)&&function(h){var b=h.getBoundingClientRect(),p=je(b.width)/h.offsetWidth||1,g=je(b.height)/h.offsetHeight||1;return p!==1||g!==1}(e),a=fe(e),c=Fe(i,r,t),u={scrollLeft:0,scrollTop:0},d={x:0,y:0};return(o||!o&&!t)&&((ie(e)!=="body"||Ut(a))&&(u=(n=e)!==q(n)&&Y(n)?{scrollLeft:(s=n).scrollLeft,scrollTop:s.scrollTop}:Xt(n)),Y(e)?((d=Fe(e,!0)).x+=e.clientLeft,d.y+=e.clientTop):a&&(d.x=Yt(a))),{x:c.left+u.scrollLeft-d.x,y:c.top+u.scrollTop-d.y,width:c.width,height:c.height}}function Xs(i){var e=new Map,t=new Set,n=[];function s(o){t.add(o.name),[].concat(o.requires||[],o.requiresIfExists||[]).forEach(function(r){if(!t.has(r)){var a=e.get(r);a&&s(a)}}),n.push(o)}return i.forEach(function(o){e.set(o.name,o)}),i.forEach(function(o){t.has(o.name)||s(o)}),n}var an={placement:"bottom",modifiers:[],strategy:"absolute"};function ln(){for(var i=arguments.length,e=new Array(i),t=0;t<i;t++)e[t]=arguments[t];return!e.some(function(n){return!(n&&typeof n.getBoundingClientRect=="function")})}function pt(i){i===void 0&&(i={});var e=i,t=e.defaultModifiers,n=t===void 0?[]:t,s=e.defaultOptions,o=s===void 0?an:s;return function(r,a,c){c===void 0&&(c=o);var u,d,h={placement:"bottom",orderedModifiers:[],options:Object.assign({},an,o),modifiersData:{},elements:{reference:r,popper:a},attributes:{},styles:{}},b=[],p=!1,g={state:h,setOptions:function(_){var C=typeof _=="function"?_(h.options):_;m(),h.options=Object.assign({},o,h.options,C),h.scrollParents={reference:_e(r)?Je(r):r.contextElement?Je(r.contextElement):[],popper:Je(a)};var k,y,E=function(v){var w=Xs(v);return zi.reduce(function(A,O){return A.concat(w.filter(function(T){return T.phase===O}))},[])}((k=[].concat(n,h.options.modifiers),y=k.reduce(function(v,w){var A=v[w.name];return v[w.name]=A?Object.assign({},A,w,{options:Object.assign({},A.options,w.options),data:Object.assign({},A.data,w.data)}):w,v},{}),Object.keys(y).map(function(v){return y[v]})));return h.orderedModifiers=E.filter(function(v){return v.enabled}),h.orderedModifiers.forEach(function(v){var w=v.name,A=v.options,O=A===void 0?{}:A,T=v.effect;if(typeof T=="function"){var I=T({state:h,name:w,instance:g,options:O});b.push(I||function(){})}}),g.update()},forceUpdate:function(){if(!p){var _=h.elements,C=_.reference,k=_.popper;if(ln(C,k)){h.rects={reference:Vs(C,Qe(k),h.options.strategy==="fixed"),popper:zt(k)},h.reset=!1,h.placement=h.options.placement,h.orderedModifiers.forEach(function(T){return h.modifiersData[T.name]=Object.assign({},T.data)});for(var y=0;y<h.orderedModifiers.length;y++)if(h.reset!==!0){var E=h.orderedModifiers[y],v=E.fn,w=E.options,A=w===void 0?{}:w,O=E.name;typeof v=="function"&&(h=v({state:h,options:A,name:O,instance:g})||h)}else h.reset=!1,y=-1}}},update:(u=function(){return new Promise(function(_){g.forceUpdate(),_(h)})},function(){return d||(d=new Promise(function(_){Promise.resolve().then(function(){d=void 0,_(u())})})),d}),destroy:function(){m(),p=!0}};if(!ln(r,a))return g;function m(){b.forEach(function(_){return _()}),b=[]}return g.setOptions(c).then(function(_){!p&&c.onFirstUpdate&&c.onFirstUpdate(_)}),g}}var Ys=pt(),Us=pt({defaultModifiers:[Vt,Qt,Rt,Bt]}),Zt=pt({defaultModifiers:[Vt,Qt,Rt,Bt,on,en,rn,Ui,sn]});const cn=Object.freeze(Object.defineProperty({__proto__:null,afterMain:Fi,afterRead:Pi,afterWrite:Wi,applyStyles:Bt,arrow:Ui,auto:ct,basePlacements:Ne,beforeMain:Mi,beforeRead:$i,beforeWrite:Hi,bottom:W,clippingParents:Di,computeStyles:Rt,createPopper:Zt,createPopperBase:Ys,createPopperLite:Us,detectOverflow:Be,end:Pe,eventListeners:Vt,flip:en,hide:sn,left:P,main:ji,modifierPhases:zi,offset:on,placements:Ft,popper:Me,popperGenerator:pt,popperOffsets:Qt,preventOverflow:rn,read:Ni,reference:Ii,right:z,start:ge,top:N,variationPlacements:jt,viewport:Mt,write:Bi},Symbol.toStringTag,{value:"Module"})),hn="dropdown",ve=".bs.dropdown",Jt=".data-api",Ks="ArrowUp",un="ArrowDown",Qs=`hide${ve}`,Zs=`hidden${ve}`,Js=`show${ve}`,Gs=`shown${ve}`,dn=`click${ve}${Jt}`,fn=`keydown${ve}${Jt}`,eo=`keyup${ve}${Jt}`,We="show",ye='[data-bs-toggle="dropdown"]:not(.disabled):not(:disabled)',to=`${ye}.${We}`,mt=".dropdown-menu",io=V()?"top-end":"top-start",no=V()?"top-start":"top-end",so=V()?"bottom-end":"bottom-start",oo=V()?"bottom-start":"bottom-end",ro=V()?"left-start":"right-start",ao=V()?"right-start":"left-start",lo={autoClose:!0,boundary:"clippingParents",display:"dynamic",offset:[0,2],popperConfig:null,reference:"toggle"},co={autoClose:"(boolean|string)",boundary:"(string|element)",display:"string",offset:"(array|string|function)",popperConfig:"(null|object|function)",reference:"(string|element|object)"};class ee extends G{constructor(e,t){super(e,t),this._popper=null,this._parent=this._element.parentNode,this._menu=f.next(this._element,mt)[0]||f.prev(this._element,mt)[0]||f.findOne(mt,this._parent),this._inNavbar=this._detectNavbar()}static get Default(){return lo}static get DefaultType(){return co}static get NAME(){return hn}toggle(){return this._isShown()?this.hide():this.show()}show(){if(ue(this._element)||this._isShown())return;const e={relatedTarget:this._element};if(!l.trigger(this._element,Js,e).defaultPrevented){if(this._createPopper(),"ontouchstart"in document.documentElement&&!this._parent.closest(".navbar-nav"))for(const t of[].concat(...document.body.children))l.on(t,"mouseover",nt);this._element.focus(),this._element.setAttribute("aria-expanded",!0),this._menu.classList.add(We),this._element.classList.add(We),l.trigger(this._element,Gs,e)}}hide(){if(ue(this._element)||!this._isShown())return;const e={relatedTarget:this._element};this._completeHide(e)}dispose(){this._popper&&this._popper.destroy(),super.dispose()}update(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.update()}_completeHide(e){if(!l.trigger(this._element,Qs,e).defaultPrevented){if("ontouchstart"in document.documentElement)for(const t of[].concat(...document.body.children))l.off(t,"mouseover",nt);this._popper&&this._popper.destroy(),this._menu.classList.remove(We),this._element.classList.remove(We),this._element.setAttribute("aria-expanded","false"),re.removeDataAttribute(this._menu,"popper"),l.trigger(this._element,Zs,e),this._element.focus()}}_getConfig(e){if(typeof(e=super._getConfig(e)).reference=="object"&&!H(e.reference)&&typeof e.reference.getBoundingClientRect!="function")throw new TypeError(`${hn.toUpperCase()}: Option "reference" provided type "object" without a required "getBoundingClientRect" method.`);return e}_createPopper(){if(cn===void 0)throw new TypeError("Bootstrap's dropdowns require Popper (https://popper.js.org/docs/v2/)");let e=this._element;this._config.reference==="parent"?e=this._parent:H(this._config.reference)?e=he(this._config.reference):typeof this._config.reference=="object"&&(e=this._config.reference);const t=this._getPopperConfig();this._popper=Zt(e,this._menu,t)}_isShown(){return this._menu.classList.contains(We)}_getPlacement(){const e=this._parent;if(e.classList.contains("dropend"))return ro;if(e.classList.contains("dropstart"))return ao;if(e.classList.contains("dropup-center"))return"top";if(e.classList.contains("dropdown-center"))return"bottom";const t=getComputedStyle(this._menu).getPropertyValue("--bs-position").trim()==="end";return e.classList.contains("dropup")?t?no:io:t?oo:so}_detectNavbar(){return this._element.closest(".navbar")!==null}_getOffset(){const{offset:e}=this._config;return typeof e=="string"?e.split(",").map(t=>Number.parseInt(t,10)):typeof e=="function"?t=>e(t,this._element):e}_getPopperConfig(){const e={placement:this._getPlacement(),modifiers:[{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"offset",options:{offset:this._getOffset()}}]};return(this._inNavbar||this._config.display==="static")&&(re.setDataAttribute(this._menu,"popper","static"),e.modifiers=[{name:"applyStyles",enabled:!1}]),{...e,...B(this._config.popperConfig,[void 0,e])}}_selectMenuItem({key:e,target:t}){const n=f.find(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",this._menu).filter(s=>xe(s));n.length&&xt(n,t,e===un,!n.includes(t)).focus()}static jQueryInterface(e){return this.each(function(){const t=ee.getOrCreateInstance(this,e);if(typeof e=="string"){if(t[e]===void 0)throw new TypeError(`No method named "${e}"`);t[e]()}})}static clearMenus(e){if(e.button===2||e.type==="keyup"&&e.key!=="Tab")return;const t=f.find(to);for(const n of t){const s=ee.getInstance(n);if(!s||s._config.autoClose===!1)continue;const o=e.composedPath(),r=o.includes(s._menu);if(o.includes(s._element)||s._config.autoClose==="inside"&&!r||s._config.autoClose==="outside"&&r||s._menu.contains(e.target)&&(e.type==="keyup"&&e.key==="Tab"||/input|select|option|textarea|form/i.test(e.target.tagName)))continue;const a={relatedTarget:s._element};e.type==="click"&&(a.clickEvent=e),s._completeHide(a)}}static dataApiKeydownHandler(e){const t=/input|textarea/i.test(e.target.tagName),n=e.key==="Escape",s=[Ks,un].includes(e.key);if(!s&&!n||t&&!n)return;e.preventDefault();const o=this.matches(ye)?this:f.prev(this,ye)[0]||f.next(this,ye)[0]||f.findOne(ye,e.delegateTarget.parentNode),r=ee.getOrCreateInstance(o);if(s)return e.stopPropagation(),r.show(),void r._selectMenuItem(e);r._isShown()&&(e.stopPropagation(),r.hide(),o.focus())}}l.on(document,fn,ye,ee.dataApiKeydownHandler),l.on(document,fn,mt,ee.dataApiKeydownHandler),l.on(document,dn,ee.clearMenus),l.on(document,eo,ee.clearMenus),l.on(document,dn,ye,function(i){i.preventDefault(),ee.getOrCreateInstance(this).toggle()}),X(ee);const pn="backdrop",mn="show",gn=`mousedown.bs.${pn}`,ho={className:"modal-backdrop",clickCallback:null,isAnimated:!1,isVisible:!0,rootElement:"body"},uo={className:"string",clickCallback:"(function|null)",isAnimated:"boolean",isVisible:"boolean",rootElement:"(element|string)"};class _n extends Ve{constructor(e){super(),this._config=this._getConfig(e),this._isAppended=!1,this._element=null}static get Default(){return ho}static get DefaultType(){return uo}static get NAME(){return pn}show(e){if(!this._config.isVisible)return void B(e);this._append();const t=this._getElement();this._config.isAnimated&&Re(t),t.classList.add(mn),this._emulateAnimation(()=>{B(e)})}hide(e){this._config.isVisible?(this._getElement().classList.remove(mn),this._emulateAnimation(()=>{this.dispose(),B(e)})):B(e)}dispose(){this._isAppended&&(l.off(this._element,gn),this._element.remove(),this._isAppended=!1)}_getElement(){if(!this._element){const e=document.createElement("div");e.className=this._config.className,this._config.isAnimated&&e.classList.add("fade"),this._element=e}return this._element}_configAfterMerge(e){return e.rootElement=he(e.rootElement),e}_append(){if(this._isAppended)return;const e=this._getElement();this._config.rootElement.append(e),l.on(e,gn,()=>{B(this._config.clickCallback)}),this._isAppended=!0}_emulateAnimation(e){mi(e,this._getElement(),this._config.isAnimated)}}const gt=".bs.focustrap",fo=`focusin${gt}`,po=`keydown.tab${gt}`,bn="backward",mo={autofocus:!0,trapElement:null},go={autofocus:"boolean",trapElement:"element"};class vn extends Ve{constructor(e){super(),this._config=this._getConfig(e),this._isActive=!1,this._lastTabNavDirection=null}static get Default(){return mo}static get DefaultType(){return go}static get NAME(){return"focustrap"}activate(){this._isActive||(this._config.autofocus&&this._config.trapElement.focus(),l.off(document,gt),l.on(document,fo,e=>this._handleFocusin(e)),l.on(document,po,e=>this._handleKeydown(e)),this._isActive=!0)}deactivate(){this._isActive&&(this._isActive=!1,l.off(document,gt))}_handleFocusin(e){const{trapElement:t}=this._config;if(e.target===document||e.target===t||t.contains(e.target))return;const n=f.focusableChildren(t);n.length===0?t.focus():this._lastTabNavDirection===bn?n[n.length-1].focus():n[0].focus()}_handleKeydown(e){e.key==="Tab"&&(this._lastTabNavDirection=e.shiftKey?bn:"forward")}}const yn=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",wn=".sticky-top",_t="padding-right",En="margin-right";class Gt{constructor(){this._element=document.body}getWidth(){const e=document.documentElement.clientWidth;return Math.abs(window.innerWidth-e)}hide(){const e=this.getWidth();this._disableOverFlow(),this._setElementAttributes(this._element,_t,t=>t+e),this._setElementAttributes(yn,_t,t=>t+e),this._setElementAttributes(wn,En,t=>t-e)}reset(){this._resetElementAttributes(this._element,"overflow"),this._resetElementAttributes(this._element,_t),this._resetElementAttributes(yn,_t),this._resetElementAttributes(wn,En)}isOverflowing(){return this.getWidth()>0}_disableOverFlow(){this._saveInitialAttribute(this._element,"overflow"),this._element.style.overflow="hidden"}_setElementAttributes(e,t,n){const s=this.getWidth();this._applyManipulationCallback(e,o=>{if(o!==this._element&&window.innerWidth>o.clientWidth+s)return;this._saveInitialAttribute(o,t);const r=window.getComputedStyle(o).getPropertyValue(t);o.style.setProperty(t,`${n(Number.parseFloat(r))}px`)})}_saveInitialAttribute(e,t){const n=e.style.getPropertyValue(t);n&&re.setDataAttribute(e,t,n)}_resetElementAttributes(e,t){this._applyManipulationCallback(e,n=>{const s=re.getDataAttribute(n,t);s!==null?(re.removeDataAttribute(n,t),n.style.setProperty(t,s)):n.style.removeProperty(t)})}_applyManipulationCallback(e,t){if(H(e))t(e);else for(const n of f.find(e,this._element))t(n)}}const U=".bs.modal",_o=`hide${U}`,bo=`hidePrevented${U}`,An=`hidden${U}`,Tn=`show${U}`,vo=`shown${U}`,yo=`resize${U}`,wo=`click.dismiss${U}`,Eo=`mousedown.dismiss${U}`,Ao=`keydown.dismiss${U}`,To=`click${U}.data-api`,Cn="modal-open",On="show",ei="modal-static",Co={backdrop:!0,focus:!0,keyboard:!0},Oo={backdrop:"(boolean|string)",focus:"boolean",keyboard:"boolean"};class we extends G{constructor(e,t){super(e,t),this._dialog=f.findOne(".modal-dialog",this._element),this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._isShown=!1,this._isTransitioning=!1,this._scrollBar=new Gt,this._addEventListeners()}static get Default(){return Co}static get DefaultType(){return Oo}static get NAME(){return"modal"}toggle(e){return this._isShown?this.hide():this.show(e)}show(e){this._isShown||this._isTransitioning||l.trigger(this._element,Tn,{relatedTarget:e}).defaultPrevented||(this._isShown=!0,this._isTransitioning=!0,this._scrollBar.hide(),document.body.classList.add(Cn),this._adjustDialog(),this._backdrop.show(()=>this._showElement(e)))}hide(){this._isShown&&!this._isTransitioning&&(l.trigger(this._element,_o).defaultPrevented||(this._isShown=!1,this._isTransitioning=!0,this._focustrap.deactivate(),this._element.classList.remove(On),this._queueCallback(()=>this._hideModal(),this._element,this._isAnimated())))}dispose(){l.off(window,U),l.off(this._dialog,U),this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}handleUpdate(){this._adjustDialog()}_initializeBackDrop(){return new _n({isVisible:!!this._config.backdrop,isAnimated:this._isAnimated()})}_initializeFocusTrap(){return new vn({trapElement:this._element})}_showElement(e){document.body.contains(this._element)||document.body.append(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0;const t=f.findOne(".modal-body",this._dialog);t&&(t.scrollTop=0),Re(this._element),this._element.classList.add(On),this._queueCallback(()=>{this._config.focus&&this._focustrap.activate(),this._isTransitioning=!1,l.trigger(this._element,vo,{relatedTarget:e})},this._dialog,this._isAnimated())}_addEventListeners(){l.on(this._element,Ao,e=>{e.key==="Escape"&&(this._config.keyboard?this.hide():this._triggerBackdropTransition())}),l.on(window,yo,()=>{this._isShown&&!this._isTransitioning&&this._adjustDialog()}),l.on(this._element,Eo,e=>{l.one(this._element,wo,t=>{this._element===e.target&&this._element===t.target&&(this._config.backdrop!=="static"?this._config.backdrop&&this.hide():this._triggerBackdropTransition())})})}_hideModal(){this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._backdrop.hide(()=>{document.body.classList.remove(Cn),this._resetAdjustments(),this._scrollBar.reset(),l.trigger(this._element,An)})}_isAnimated(){return this._element.classList.contains("fade")}_triggerBackdropTransition(){if(l.trigger(this._element,bo).defaultPrevented)return;const e=this._element.scrollHeight>document.documentElement.clientHeight,t=this._element.style.overflowY;t==="hidden"||this._element.classList.contains(ei)||(e||(this._element.style.overflowY="hidden"),this._element.classList.add(ei),this._queueCallback(()=>{this._element.classList.remove(ei),this._queueCallback(()=>{this._element.style.overflowY=t},this._dialog)},this._dialog),this._element.focus())}_adjustDialog(){const e=this._element.scrollHeight>document.documentElement.clientHeight,t=this._scrollBar.getWidth(),n=t>0;if(n&&!e){const s=V()?"paddingLeft":"paddingRight";this._element.style[s]=`${t}px`}if(!n&&e){const s=V()?"paddingRight":"paddingLeft";this._element.style[s]=`${t}px`}}_resetAdjustments(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}static jQueryInterface(e,t){return this.each(function(){const n=we.getOrCreateInstance(this,e);if(typeof e=="string"){if(n[e]===void 0)throw new TypeError(`No method named "${e}"`);n[e](t)}})}}l.on(document,To,'[data-bs-toggle="modal"]',function(i){const e=f.getElementFromSelector(this);["A","AREA"].includes(this.tagName)&&i.preventDefault(),l.one(e,Tn,n=>{n.defaultPrevented||l.one(e,An,()=>{xe(this)&&this.focus()})});const t=f.findOne(".modal.show");t&&we.getInstance(t).hide(),we.getOrCreateInstance(e).toggle(this)}),st(we),X(we);const le=".bs.offcanvas",xn=".data-api",xo=`load${le}${xn}`,kn="show",Ln="showing",Sn="hiding",Dn=".offcanvas.show",ko=`show${le}`,Lo=`shown${le}`,So=`hide${le}`,In=`hidePrevented${le}`,$n=`hidden${le}`,Do=`resize${le}`,Io=`click${le}${xn}`,$o=`keydown.dismiss${le}`,No={backdrop:!0,keyboard:!0,scroll:!1},Po={backdrop:"(boolean|string)",keyboard:"boolean",scroll:"boolean"};class ce extends G{constructor(e,t){super(e,t),this._isShown=!1,this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._addEventListeners()}static get Default(){return No}static get DefaultType(){return Po}static get NAME(){return"offcanvas"}toggle(e){return this._isShown?this.hide():this.show(e)}show(e){this._isShown||l.trigger(this._element,ko,{relatedTarget:e}).defaultPrevented||(this._isShown=!0,this._backdrop.show(),this._config.scroll||new Gt().hide(),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.classList.add(Ln),this._queueCallback(()=>{this._config.scroll&&!this._config.backdrop||this._focustrap.activate(),this._element.classList.add(kn),this._element.classList.remove(Ln),l.trigger(this._element,Lo,{relatedTarget:e})},this._element,!0))}hide(){this._isShown&&(l.trigger(this._element,So).defaultPrevented||(this._focustrap.deactivate(),this._element.blur(),this._isShown=!1,this._element.classList.add(Sn),this._backdrop.hide(),this._queueCallback(()=>{this._element.classList.remove(kn,Sn),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._config.scroll||new Gt().reset(),l.trigger(this._element,$n)},this._element,!0)))}dispose(){this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}_initializeBackDrop(){const e=!!this._config.backdrop;return new _n({className:"offcanvas-backdrop",isVisible:e,isAnimated:!0,rootElement:this._element.parentNode,clickCallback:e?()=>{this._config.backdrop!=="static"?this.hide():l.trigger(this._element,In)}:null})}_initializeFocusTrap(){return new vn({trapElement:this._element})}_addEventListeners(){l.on(this._element,$o,e=>{e.key==="Escape"&&(this._config.keyboard?this.hide():l.trigger(this._element,In))})}static jQueryInterface(e){return this.each(function(){const t=ce.getOrCreateInstance(this,e);if(typeof e=="string"){if(t[e]===void 0||e.startsWith("_")||e==="constructor")throw new TypeError(`No method named "${e}"`);t[e](this)}})}}l.on(document,Io,'[data-bs-toggle="offcanvas"]',function(i){const e=f.getElementFromSelector(this);if(["A","AREA"].includes(this.tagName)&&i.preventDefault(),ue(this))return;l.one(e,$n,()=>{xe(this)&&this.focus()});const t=f.findOne(Dn);t&&t!==e&&ce.getInstance(t).hide(),ce.getOrCreateInstance(e).toggle(this)}),l.on(window,xo,()=>{for(const i of f.find(Dn))ce.getOrCreateInstance(i).show()}),l.on(window,Do,()=>{for(const i of f.find("[aria-modal][class*=show][class*=offcanvas-]"))getComputedStyle(i).position!=="fixed"&&ce.getOrCreateInstance(i).hide()}),st(ce),X(ce);const Nn={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],dd:[],div:[],dl:[],dt:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},Mo=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]),jo=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i,Fo=(i,e)=>{const t=i.nodeName.toLowerCase();return e.includes(t)?!Mo.has(t)||!!jo.test(i.nodeValue):e.filter(n=>n instanceof RegExp).some(n=>n.test(t))},Ho={allowList:Nn,content:{},extraClass:"",html:!1,sanitize:!0,sanitizeFn:null,template:"<div></div>"},Bo={allowList:"object",content:"object",extraClass:"(string|function)",html:"boolean",sanitize:"boolean",sanitizeFn:"(null|function)",template:"string"},Wo={entry:"(string|element|function|null)",selector:"(string|element)"};class zo extends Ve{constructor(e){super(),this._config=this._getConfig(e)}static get Default(){return Ho}static get DefaultType(){return Bo}static get NAME(){return"TemplateFactory"}getContent(){return Object.values(this._config.content).map(e=>this._resolvePossibleFunction(e)).filter(Boolean)}hasContent(){return this.getContent().length>0}changeContent(e){return this._checkContent(e),this._config.content={...this._config.content,...e},this}toHtml(){const e=document.createElement("div");e.innerHTML=this._maybeSanitize(this._config.template);for(const[s,o]of Object.entries(this._config.content))this._setContent(e,o,s);const t=e.children[0],n=this._resolvePossibleFunction(this._config.extraClass);return n&&t.classList.add(...n.split(" ")),t}_typeCheckConfig(e){super._typeCheckConfig(e),this._checkContent(e.content)}_checkContent(e){for(const[t,n]of Object.entries(e))super._typeCheckConfig({selector:t,entry:n},Wo)}_setContent(e,t,n){const s=f.findOne(n,e);s&&((t=this._resolvePossibleFunction(t))?H(t)?this._putElementInTemplate(he(t),s):this._config.html?s.innerHTML=this._maybeSanitize(t):s.textContent=t:s.remove())}_maybeSanitize(e){return this._config.sanitize?function(t,n,s){if(!t.length)return t;if(s&&typeof s=="function")return s(t);const o=new window.DOMParser().parseFromString(t,"text/html"),r=[].concat(...o.body.querySelectorAll("*"));for(const a of r){const c=a.nodeName.toLowerCase();if(!Object.keys(n).includes(c)){a.remove();continue}const u=[].concat(...a.attributes),d=[].concat(n["*"]||[],n[c]||[]);for(const h of u)Fo(h,d)||a.removeAttribute(h.nodeName)}return o.body.innerHTML}(e,this._config.allowList,this._config.sanitizeFn):e}_resolvePossibleFunction(e){return B(e,[void 0,this])}_putElementInTemplate(e,t){if(this._config.html)return t.innerHTML="",void t.append(e);t.textContent=e.textContent}}const qo=new Set(["sanitize","allowList","sanitizeFn"]),ti="fade",bt="show",Ro=".tooltip-inner",Pn=".modal",Mn="hide.bs.modal",Ge="hover",ii="focus",Vo={AUTO:"auto",TOP:"top",RIGHT:V()?"left":"right",BOTTOM:"bottom",LEFT:V()?"right":"left"},Xo={allowList:Nn,animation:!0,boundary:"clippingParents",container:!1,customClass:"",delay:0,fallbackPlacements:["top","right","bottom","left"],html:!1,offset:[0,6],placement:"top",popperConfig:null,sanitize:!0,sanitizeFn:null,selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',title:"",trigger:"hover focus"},Yo={allowList:"object",animation:"boolean",boundary:"(string|element)",container:"(string|element|boolean)",customClass:"(string|function)",delay:"(number|object)",fallbackPlacements:"array",html:"boolean",offset:"(array|string|function)",placement:"(string|function)",popperConfig:"(null|object|function)",sanitize:"boolean",sanitizeFn:"(null|function)",selector:"(string|boolean)",template:"string",title:"(string|element|function)",trigger:"string"};class Ee extends G{constructor(e,t){if(cn===void 0)throw new TypeError("Bootstrap's tooltips require Popper (https://popper.js.org/docs/v2/)");super(e,t),this._isEnabled=!0,this._timeout=0,this._isHovered=null,this._activeTrigger={},this._popper=null,this._templateFactory=null,this._newContent=null,this.tip=null,this._setListeners(),this._config.selector||this._fixTitle()}static get Default(){return Xo}static get DefaultType(){return Yo}static get NAME(){return"tooltip"}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(){this._isEnabled&&(this._isShown()?this._leave():this._enter())}dispose(){clearTimeout(this._timeout),l.off(this._element.closest(Pn),Mn,this._hideModalHandler),this._element.getAttribute("data-bs-original-title")&&this._element.setAttribute("title",this._element.getAttribute("data-bs-original-title")),this._disposePopper(),super.dispose()}show(){if(this._element.style.display==="none")throw new Error("Please use show on visible elements");if(!this._isWithContent()||!this._isEnabled)return;const e=l.trigger(this._element,this.constructor.eventName("show")),t=(fi(this._element)||this._element.ownerDocument.documentElement).contains(this._element);if(e.defaultPrevented||!t)return;this._disposePopper();const n=this._getTipElement();this._element.setAttribute("aria-describedby",n.getAttribute("id"));const{container:s}=this._config;if(this._element.ownerDocument.documentElement.contains(this.tip)||(s.append(n),l.trigger(this._element,this.constructor.eventName("inserted"))),this._popper=this._createPopper(n),n.classList.add(bt),"ontouchstart"in document.documentElement)for(const o of[].concat(...document.body.children))l.on(o,"mouseover",nt);this._queueCallback(()=>{l.trigger(this._element,this.constructor.eventName("shown")),this._isHovered===!1&&this._leave(),this._isHovered=!1},this.tip,this._isAnimated())}hide(){if(this._isShown()&&!l.trigger(this._element,this.constructor.eventName("hide")).defaultPrevented){if(this._getTipElement().classList.remove(bt),"ontouchstart"in document.documentElement)for(const e of[].concat(...document.body.children))l.off(e,"mouseover",nt);this._activeTrigger.click=!1,this._activeTrigger[ii]=!1,this._activeTrigger[Ge]=!1,this._isHovered=null,this._queueCallback(()=>{this._isWithActiveTrigger()||(this._isHovered||this._disposePopper(),this._element.removeAttribute("aria-describedby"),l.trigger(this._element,this.constructor.eventName("hidden")))},this.tip,this._isAnimated())}}update(){this._popper&&this._popper.update()}_isWithContent(){return!!this._getTitle()}_getTipElement(){return this.tip||(this.tip=this._createTipElement(this._newContent||this._getContentForTemplate())),this.tip}_createTipElement(e){const t=this._getTemplateFactory(e).toHtml();if(!t)return null;t.classList.remove(ti,bt),t.classList.add(`bs-${this.constructor.NAME}-auto`);const n=(s=>{do s+=Math.floor(1e6*Math.random());while(document.getElementById(s));return s})(this.constructor.NAME).toString();return t.setAttribute("id",n),this._isAnimated()&&t.classList.add(ti),t}setContent(e){this._newContent=e,this._isShown()&&(this._disposePopper(),this.show())}_getTemplateFactory(e){return this._templateFactory?this._templateFactory.changeContent(e):this._templateFactory=new zo({...this._config,content:e,extraClass:this._resolvePossibleFunction(this._config.customClass)}),this._templateFactory}_getContentForTemplate(){return{[Ro]:this._getTitle()}}_getTitle(){return this._resolvePossibleFunction(this._config.title)||this._element.getAttribute("data-bs-original-title")}_initializeOnDelegatedTarget(e){return this.constructor.getOrCreateInstance(e.delegateTarget,this._getDelegateConfig())}_isAnimated(){return this._config.animation||this.tip&&this.tip.classList.contains(ti)}_isShown(){return this.tip&&this.tip.classList.contains(bt)}_createPopper(e){const t=B(this._config.placement,[this,e,this._element]),n=Vo[t.toUpperCase()];return Zt(this._element,e,this._getPopperConfig(n))}_getOffset(){const{offset:e}=this._config;return typeof e=="string"?e.split(",").map(t=>Number.parseInt(t,10)):typeof e=="function"?t=>e(t,this._element):e}_resolvePossibleFunction(e){return B(e,[this._element,this._element])}_getPopperConfig(e){const t={placement:e,modifiers:[{name:"flip",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:"offset",options:{offset:this._getOffset()}},{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"arrow",options:{element:`.${this.constructor.NAME}-arrow`}},{name:"preSetPlacement",enabled:!0,phase:"beforeMain",fn:n=>{this._getTipElement().setAttribute("data-popper-placement",n.state.placement)}}]};return{...t,...B(this._config.popperConfig,[void 0,t])}}_setListeners(){const e=this._config.trigger.split(" ");for(const t of e)if(t==="click")l.on(this._element,this.constructor.eventName("click"),this._config.selector,n=>{this._initializeOnDelegatedTarget(n).toggle()});else if(t!=="manual"){const n=t===Ge?this.constructor.eventName("mouseenter"):this.constructor.eventName("focusin"),s=t===Ge?this.constructor.eventName("mouseleave"):this.constructor.eventName("focusout");l.on(this._element,n,this._config.selector,o=>{const r=this._initializeOnDelegatedTarget(o);r._activeTrigger[o.type==="focusin"?ii:Ge]=!0,r._enter()}),l.on(this._element,s,this._config.selector,o=>{const r=this._initializeOnDelegatedTarget(o);r._activeTrigger[o.type==="focusout"?ii:Ge]=r._element.contains(o.relatedTarget),r._leave()})}this._hideModalHandler=()=>{this._element&&this.hide()},l.on(this._element.closest(Pn),Mn,this._hideModalHandler)}_fixTitle(){const e=this._element.getAttribute("title");e&&(this._element.getAttribute("aria-label")||this._element.textContent.trim()||this._element.setAttribute("aria-label",e),this._element.setAttribute("data-bs-original-title",e),this._element.removeAttribute("title"))}_enter(){this._isShown()||this._isHovered?this._isHovered=!0:(this._isHovered=!0,this._setTimeout(()=>{this._isHovered&&this.show()},this._config.delay.show))}_leave(){this._isWithActiveTrigger()||(this._isHovered=!1,this._setTimeout(()=>{this._isHovered||this.hide()},this._config.delay.hide))}_setTimeout(e,t){clearTimeout(this._timeout),this._timeout=setTimeout(e,t)}_isWithActiveTrigger(){return Object.values(this._activeTrigger).includes(!0)}_getConfig(e){const t=re.getDataAttributes(this._element);for(const n of Object.keys(t))qo.has(n)&&delete t[n];return e={...t,...typeof e=="object"&&e?e:{}},e=this._mergeConfigObj(e),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}_configAfterMerge(e){return e.container=e.container===!1?document.body:he(e.container),typeof e.delay=="number"&&(e.delay={show:e.delay,hide:e.delay}),typeof e.title=="number"&&(e.title=e.title.toString()),typeof e.content=="number"&&(e.content=e.content.toString()),e}_getDelegateConfig(){const e={};for(const[t,n]of Object.entries(this._config))this.constructor.Default[t]!==n&&(e[t]=n);return e.selector=!1,e.trigger="manual",e}_disposePopper(){this._popper&&(this._popper.destroy(),this._popper=null),this.tip&&(this.tip.remove(),this.tip=null)}static jQueryInterface(e){return this.each(function(){const t=Ee.getOrCreateInstance(this,e);if(typeof e=="string"){if(t[e]===void 0)throw new TypeError(`No method named "${e}"`);t[e]()}})}}X(Ee);const Uo=".popover-header",Ko=".popover-body",Qo={...Ee.Default,content:"",offset:[0,8],placement:"right",template:'<div class="popover" role="tooltip"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>',trigger:"click"},Zo={...Ee.DefaultType,content:"(null|string|element|function)"};class vt extends Ee{static get Default(){return Qo}static get DefaultType(){return Zo}static get NAME(){return"popover"}_isWithContent(){return this._getTitle()||this._getContent()}_getContentForTemplate(){return{[Uo]:this._getTitle(),[Ko]:this._getContent()}}_getContent(){return this._resolvePossibleFunction(this._config.content)}static jQueryInterface(e){return this.each(function(){const t=vt.getOrCreateInstance(this,e);if(typeof e=="string"){if(t[e]===void 0)throw new TypeError(`No method named "${e}"`);t[e]()}})}}X(vt);const ni=".bs.scrollspy",Jo=`activate${ni}`,jn=`click${ni}`,Go=`load${ni}.data-api`,ze="active",si="[href]",Fn=".nav-link",er=`${Fn}, .nav-item > ${Fn}, .list-group-item`,tr={offset:null,rootMargin:"0px 0px -25%",smoothScroll:!1,target:null,threshold:[.1,.5,1]},ir={offset:"(number|null)",rootMargin:"string",smoothScroll:"boolean",target:"element",threshold:"array"};class et extends G{constructor(e,t){super(e,t),this._targetLinks=new Map,this._observableSections=new Map,this._rootElement=getComputedStyle(this._element).overflowY==="visible"?null:this._element,this._activeTarget=null,this._observer=null,this._previousScrollData={visibleEntryTop:0,parentScrollTop:0},this.refresh()}static get Default(){return tr}static get DefaultType(){return ir}static get NAME(){return"scrollspy"}refresh(){this._initializeTargetsAndObservables(),this._maybeEnableSmoothScroll(),this._observer?this._observer.disconnect():this._observer=this._getNewObserver();for(const e of this._observableSections.values())this._observer.observe(e)}dispose(){this._observer.disconnect(),super.dispose()}_configAfterMerge(e){return e.target=he(e.target)||document.body,e.rootMargin=e.offset?`${e.offset}px 0px -30%`:e.rootMargin,typeof e.threshold=="string"&&(e.threshold=e.threshold.split(",").map(t=>Number.parseFloat(t))),e}_maybeEnableSmoothScroll(){this._config.smoothScroll&&(l.off(this._config.target,jn),l.on(this._config.target,jn,si,e=>{const t=this._observableSections.get(e.target.hash);if(t){e.preventDefault();const n=this._rootElement||window,s=t.offsetTop-this._element.offsetTop;if(n.scrollTo)return void n.scrollTo({top:s,behavior:"smooth"});n.scrollTop=s}}))}_getNewObserver(){const e={root:this._rootElement,threshold:this._config.threshold,rootMargin:this._config.rootMargin};return new IntersectionObserver(t=>this._observerCallback(t),e)}_observerCallback(e){const t=r=>this._targetLinks.get(`#${r.target.id}`),n=r=>{this._previousScrollData.visibleEntryTop=r.target.offsetTop,this._process(t(r))},s=(this._rootElement||document.documentElement).scrollTop,o=s>=this._previousScrollData.parentScrollTop;this._previousScrollData.parentScrollTop=s;for(const r of e){if(!r.isIntersecting){this._activeTarget=null,this._clearActiveClass(t(r));continue}const a=r.target.offsetTop>=this._previousScrollData.visibleEntryTop;if(o&&a){if(n(r),!s)return}else o||a||n(r)}}_initializeTargetsAndObservables(){this._targetLinks=new Map,this._observableSections=new Map;const e=f.find(si,this._config.target);for(const t of e){if(!t.hash||ue(t))continue;const n=f.findOne(decodeURI(t.hash),this._element);xe(n)&&(this._targetLinks.set(decodeURI(t.hash),t),this._observableSections.set(t.hash,n))}}_process(e){this._activeTarget!==e&&(this._clearActiveClass(this._config.target),this._activeTarget=e,e.classList.add(ze),this._activateParents(e),l.trigger(this._element,Jo,{relatedTarget:e}))}_activateParents(e){if(e.classList.contains("dropdown-item"))f.findOne(".dropdown-toggle",e.closest(".dropdown")).classList.add(ze);else for(const t of f.parents(e,".nav, .list-group"))for(const n of f.prev(t,er))n.classList.add(ze)}_clearActiveClass(e){e.classList.remove(ze);const t=f.find(`${si}.${ze}`,e);for(const n of t)n.classList.remove(ze)}static jQueryInterface(e){return this.each(function(){const t=et.getOrCreateInstance(this,e);if(typeof e=="string"){if(t[e]===void 0||e.startsWith("_")||e==="constructor")throw new TypeError(`No method named "${e}"`);t[e]()}})}}l.on(window,Go,()=>{for(const i of f.find('[data-bs-spy="scroll"]'))et.getOrCreateInstance(i)}),X(et);const Ae=".bs.tab",nr=`hide${Ae}`,sr=`hidden${Ae}`,or=`show${Ae}`,rr=`shown${Ae}`,ar=`click${Ae}`,lr=`keydown${Ae}`,cr=`load${Ae}`,hr="ArrowLeft",Hn="ArrowRight",ur="ArrowUp",Bn="ArrowDown",oi="Home",Wn="End",Te="active",zn="fade",ri="show",qn=".dropdown-toggle",ai=`:not(${qn})`,Rn='[data-bs-toggle="tab"], [data-bs-toggle="pill"], [data-bs-toggle="list"]',li=`.nav-link${ai}, .list-group-item${ai}, [role="tab"]${ai}, ${Rn}`,dr=`.${Te}[data-bs-toggle="tab"], .${Te}[data-bs-toggle="pill"], .${Te}[data-bs-toggle="list"]`;class Ce extends G{constructor(e){super(e),this._parent=this._element.closest('.list-group, .nav, [role="tablist"]'),this._parent&&(this._setInitialAttributes(this._parent,this._getChildren()),l.on(this._element,lr,t=>this._keydown(t)))}static get NAME(){return"tab"}show(){const e=this._element;if(this._elemIsActive(e))return;const t=this._getActiveElem(),n=t?l.trigger(t,nr,{relatedTarget:e}):null;l.trigger(e,or,{relatedTarget:t}).defaultPrevented||n&&n.defaultPrevented||(this._deactivate(t,e),this._activate(e,t))}_activate(e,t){e&&(e.classList.add(Te),this._activate(f.getElementFromSelector(e)),this._queueCallback(()=>{e.getAttribute("role")==="tab"?(e.removeAttribute("tabindex"),e.setAttribute("aria-selected",!0),this._toggleDropDown(e,!0),l.trigger(e,rr,{relatedTarget:t})):e.classList.add(ri)},e,e.classList.contains(zn)))}_deactivate(e,t){e&&(e.classList.remove(Te),e.blur(),this._deactivate(f.getElementFromSelector(e)),this._queueCallback(()=>{e.getAttribute("role")==="tab"?(e.setAttribute("aria-selected",!1),e.setAttribute("tabindex","-1"),this._toggleDropDown(e,!1),l.trigger(e,sr,{relatedTarget:t})):e.classList.remove(ri)},e,e.classList.contains(zn)))}_keydown(e){if(![hr,Hn,ur,Bn,oi,Wn].includes(e.key))return;e.stopPropagation(),e.preventDefault();const t=this._getChildren().filter(s=>!ue(s));let n;if([oi,Wn].includes(e.key))n=t[e.key===oi?0:t.length-1];else{const s=[Hn,Bn].includes(e.key);n=xt(t,e.target,s,!0)}n&&(n.focus({preventScroll:!0}),Ce.getOrCreateInstance(n).show())}_getChildren(){return f.find(li,this._parent)}_getActiveElem(){return this._getChildren().find(e=>this._elemIsActive(e))||null}_setInitialAttributes(e,t){this._setAttributeIfNotExists(e,"role","tablist");for(const n of t)this._setInitialAttributesOnChild(n)}_setInitialAttributesOnChild(e){e=this._getInnerElement(e);const t=this._elemIsActive(e),n=this._getOuterElement(e);e.setAttribute("aria-selected",t),n!==e&&this._setAttributeIfNotExists(n,"role","presentation"),t||e.setAttribute("tabindex","-1"),this._setAttributeIfNotExists(e,"role","tab"),this._setInitialAttributesOnTargetPanel(e)}_setInitialAttributesOnTargetPanel(e){const t=f.getElementFromSelector(e);t&&(this._setAttributeIfNotExists(t,"role","tabpanel"),e.id&&this._setAttributeIfNotExists(t,"aria-labelledby",`${e.id}`))}_toggleDropDown(e,t){const n=this._getOuterElement(e);if(!n.classList.contains("dropdown"))return;const s=(o,r)=>{const a=f.findOne(o,n);a&&a.classList.toggle(r,t)};s(qn,Te),s(".dropdown-menu",ri),n.setAttribute("aria-expanded",t)}_setAttributeIfNotExists(e,t,n){e.hasAttribute(t)||e.setAttribute(t,n)}_elemIsActive(e){return e.classList.contains(Te)}_getInnerElement(e){return e.matches(li)?e:f.findOne(li,e)}_getOuterElement(e){return e.closest(".nav-item, .list-group-item")||e}static jQueryInterface(e){return this.each(function(){const t=Ce.getOrCreateInstance(this);if(typeof e=="string"){if(t[e]===void 0||e.startsWith("_")||e==="constructor")throw new TypeError(`No method named "${e}"`);t[e]()}})}}l.on(document,ar,Rn,function(i){["A","AREA"].includes(this.tagName)&&i.preventDefault(),ue(this)||Ce.getOrCreateInstance(this).show()}),l.on(window,cr,()=>{for(const i of f.find(dr))Ce.getOrCreateInstance(i)}),X(Ce);const pe=".bs.toast",fr=`mouseover${pe}`,pr=`mouseout${pe}`,mr=`focusin${pe}`,gr=`focusout${pe}`,_r=`hide${pe}`,br=`hidden${pe}`,vr=`show${pe}`,yr=`shown${pe}`,Vn="hide",yt="show",wt="showing",wr={animation:"boolean",autohide:"boolean",delay:"number"},Er={animation:!0,autohide:!0,delay:5e3};class tt extends G{constructor(e,t){super(e,t),this._timeout=null,this._hasMouseInteraction=!1,this._hasKeyboardInteraction=!1,this._setListeners()}static get Default(){return Er}static get DefaultType(){return wr}static get NAME(){return"toast"}show(){l.trigger(this._element,vr).defaultPrevented||(this._clearTimeout(),this._config.animation&&this._element.classList.add("fade"),this._element.classList.remove(Vn),Re(this._element),this._element.classList.add(yt,wt),this._queueCallback(()=>{this._element.classList.remove(wt),l.trigger(this._element,yr),this._maybeScheduleHide()},this._element,this._config.animation))}hide(){this.isShown()&&(l.trigger(this._element,_r).defaultPrevented||(this._element.classList.add(wt),this._queueCallback(()=>{this._element.classList.add(Vn),this._element.classList.remove(wt,yt),l.trigger(this._element,br)},this._element,this._config.animation)))}dispose(){this._clearTimeout(),this.isShown()&&this._element.classList.remove(yt),super.dispose()}isShown(){return this._element.classList.contains(yt)}_maybeScheduleHide(){this._config.autohide&&(this._hasMouseInteraction||this._hasKeyboardInteraction||(this._timeout=setTimeout(()=>{this.hide()},this._config.delay)))}_onInteraction(e,t){switch(e.type){case"mouseover":case"mouseout":this._hasMouseInteraction=t;break;case"focusin":case"focusout":this._hasKeyboardInteraction=t}if(t)return void this._clearTimeout();const n=e.relatedTarget;this._element===n||this._element.contains(n)||this._maybeScheduleHide()}_setListeners(){l.on(this._element,fr,e=>this._onInteraction(e,!0)),l.on(this._element,pr,e=>this._onInteraction(e,!1)),l.on(this._element,mr,e=>this._onInteraction(e,!0)),l.on(this._element,gr,e=>this._onInteraction(e,!1))}_clearTimeout(){clearTimeout(this._timeout),this._timeout=null}static jQueryInterface(e){return this.each(function(){const t=tt.getOrCreateInstance(this,e);if(typeof e=="string"){if(t[e]===void 0)throw new TypeError(`No method named "${e}"`);t[e](this)}})}}return st(tt),X(tt),{Alert:Xe,Button:Ye,Carousel:De,Collapse:$e,Dropdown:ee,Modal:we,Offcanvas:ce,Popover:vt,ScrollSpy:et,Tab:Ce,Toast:tt,Tooltip:Ee}});document.addEventListener("DOMContentLoaded",function(){const L=document.querySelector("#navbarEmail"),S=100;function F(){window.scrollY>S?L.classList.add("scroll-sticky"):L.classList.remove("scroll-sticky")}let J;window.addEventListener("scroll",function(){window.clearTimeout(J),J=setTimeout(F,50)},!1),F()});const Ct=document.querySelectorAll(".navbar-nav .nav-link");Ct.forEach(L=>{L.addEventListener("click",function(){Ct.forEach(S=>S.classList.remove("active")),this.classList.add("active")})});window.addEventListener("scroll",function(){let L=window.scrollY+100;Ct.forEach(S=>{const F=document.querySelector(S.getAttribute("href"));F&&L>=F.offsetTop&&L<F.offsetTop+F.offsetHeight&&(Ct.forEach(J=>J.classList.remove("active")),S.classList.add("active"))})});document.addEventListener("DOMContentLoaded",function(){var L=document.getElementById("theme-toggle-btn"),S=document.getElementById("moon-icon"),F=document.getElementById("sun-icon"),J=localStorage.getItem("theme")||"light";document.documentElement.setAttribute("data-bs-theme",J),S.style.display=J==="light"?"block":"none",F.style.display=J==="dark"?"block":"none",L.addEventListener("click",function(){var it=document.documentElement.getAttribute("data-bs-theme"),H=it==="dark"?"light":"dark";document.documentElement.setAttribute("data-bs-theme",H),localStorage.setItem("theme",H),S.style.display=H==="light"?"block":"none",F.style.display=H==="dark"?"block":"none"})})});export default Ir();
