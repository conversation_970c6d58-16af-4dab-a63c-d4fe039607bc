import{c as k,i as q}from"../../admin.bundle-DI9_jvUJ.js";/* empty css                               */import{A as P}from"../../air-datepicker-DlUcrly3.js";import"../../main-Cyta4iCA.js";VirtualSelect.init({ele:"#productNameSelect",options:[{label:"Denim Jacket",value:"Denim Jacket"},{label:"Leather Wallet",value:"Leather Wallet"},{label:"Wireless Headphones",value:"Wireless Headphones"},{label:"Sunglasses",value:"Sunglasses"},{label:"Backpack",value:"Backpack"},{label:"Winter Coat",value:"Winter Coat"},{label:"Handbag",value:"Handbag"},{label:"Sweater",value:"Sweater"},{label:"Sports Watch",value:"Sports Watch"}],allowNewOption:!0});VirtualSelect.init({ele:"#paymentStatusSelect",options:[{label:"Paid",value:"Paid"},{label:"Unpaid",value:"Unpaid"},{label:"COD",value:"COD"}],allowNewOption:!0});VirtualSelect.init({ele:"#orderStatusSelect",options:[{label:"New",value:"New"},{label:"Pending",value:"Pending"},{label:"Shipping",value:"Shipping"},{label:"Delivered",value:"Delivered"}],allowNewOption:!0});class w{constructor(e,t=10){this.tableId=e,this.table=document.getElementById(e),this.tbody=this.table.querySelector("tbody"),this.rowsPerPage=t,this.currentPage=1,this.data=[],this.filteredData=[],this.totalPages=0,this.paginationContainer=null,this.resultsInfoContainer=null,this.checkAllCheckbox=document.getElementById("checkDataAll"),this.deleteSelectedButton=document.querySelector("#deleteOrder"),this.initEvents()}initEvents(){this.checkAllCheckbox&&this.checkAllCheckbox.addEventListener("change",()=>{const e=this.checkAllCheckbox.checked;this.tbody.querySelectorAll(".form-check-input").forEach(a=>{a.checked=e}),this.toggleDeleteSelectedButton()}),this.deleteSelectedButton&&this.deleteSelectedButton.addEventListener("click",()=>{this.deleteSelectedOrders()}),this.tbody.addEventListener("change",e=>{e.target&&e.target.classList.contains("form-check-input")&&this.toggleDeleteSelectedButton()})}toggleDeleteSelectedButton(){const e=this.tbody.querySelectorAll(".form-check-input"),t=Array.from(e).some(a=>a.checked);this.deleteSelectedButton&&(t?this.deleteSelectedButton.classList.remove("d-none"):this.deleteSelectedButton.classList.add("d-none"))}deleteSelectedOrders(){const e=this.tbody.querySelectorAll(".form-check-input:checked"),t=[];if(e.forEach(a=>{const n=a.closest("tr").querySelector(".link-custom-primary");n&&t.push(n.textContent)}),t.length>0){const a=new window.window.bootstrap.Modal(document.getElementById("deleteModal")),l=document.querySelector("#deleteModal .modal-body p");l&&(l.textContent=t.length===1?"Are you sure you want to delete this order?":`Are you sure you want to delete these ${t.length} orders?`);const n=document.getElementById("confirmDeleteBtn");if(n){n.dataset.orderIds=JSON.stringify(t);const d=n.cloneNode(!0);n.parentNode.replaceChild(d,n),d.addEventListener("click",()=>{const u=JSON.parse(d.dataset.orderIds);this.deleteMultipleOrders(u),a.hide()})}a.show()}}deleteMultipleOrders(e){this.data=this.data.filter(t=>!e.includes(t.orderId)),this.filteredData=[...this.data],this.checkAllCheckbox&&(this.checkAllCheckbox.checked=!1),this.deleteSelectedButton&&this.deleteSelectedButton.classList.add("d-none"),this.totalPages=Math.ceil(this.filteredData.length/this.rowsPerPage),this.currentPage>this.totalPages&&this.totalPages>0&&(this.currentPage=this.totalPages),this.renderTable(),this.renderPagination(),this.updateResultsInfo(),typeof b=="function"&&b(this.data)}setData(e){this.data=e,this.filteredData=[...e],this.totalPages=Math.ceil(this.filteredData.length/this.rowsPerPage),this.renderTable(),this.renderPagination(),this.updateResultsInfo()}setPaginationContainer(e){this.paginationContainer=document.getElementById(e),this.renderPagination()}setResultsInfoContainer(e){this.resultsInfoContainer=document.getElementById(e),this.updateResultsInfo()}renderTable(){if(this.tbody.innerHTML="",this.filteredData.length===0){const a=document.createElement("tr");a.className="NotFoundData",a.innerHTML=`
           <td colspan="11" class="text-center py-4">
                    <div class="d-flex flex-column align-items-center">
                 <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" class="mx-auto size-12" viewBox="0 0 48 48">
                                                <linearGradient id="SVGID_1__h35ynqzIJzH4_gr1" x1="34.598" x2="15.982" y1="15.982" y2="34.598" gradientUnits="userSpaceOnUse">
                                                    <stop offset="0" stop-color="#60e8fe"></stop>
                                                    <stop offset=".033" stop-color="#6ae9fe"></stop>
                                                    <stop offset=".197" stop-color="#97f0fe"></stop>
                                                    <stop offset=".362" stop-color="#bdf5ff"></stop>
                                                    <stop offset=".525" stop-color="#dafaff"></stop>
                                                    <stop offset=".687" stop-color="#eefdff"></stop>
                                                    <stop offset=".846" stop-color="#fbfeff"></stop>
                                                    <stop offset="1" stop-color="#fff"></stop>
                                                </linearGradient>
                                                <path fill="url(#SVGID_1__h35ynqzIJzH4_gr1)" d="M40.036,33.826L31.68,25.6c0.847-1.739,1.335-3.684,1.335-5.748c0-7.27-5.894-13.164-13.164-13.164	S6.688,12.582,6.688,19.852c0,7.27,5.894,13.164,13.164,13.164c2.056,0,3.995-0.485,5.728-1.326l3.914,4.015l4.331,4.331	c1.715,1.715,4.496,1.715,6.211,0C41.751,38.321,41.751,35.541,40.036,33.826z"></path>
                                                <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M31.95,25.739l8.086,8.086c1.715,1.715,1.715,4.496,0,6.211l0,0c-1.715,1.715-4.496,1.715-6.211,0	l-4.331-4.331"></path>
                                                <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M7.525,24.511c-1.771-4.694-0.767-10.196,3.011-13.975c3.847-3.847,9.48-4.817,14.228-2.912"></path>
                                                <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M30.856,12.603c3.376,5.114,2.814,12.063-1.688,16.565c-4.858,4.858-12.565,5.129-17.741,0.814"></path>
                                            </svg>
                                            <p class="mt-2 text-center text-gray-500 dark:text-dark-500">No matching records found</p>
                        <p class="text-muted mb-0">We couldn't find any categories matching your search.</p>
                    </div>
                </td>
        `,this.tbody.appendChild(a);return}const e=(this.currentPage-1)*this.rowsPerPage,t=Math.min(e+this.rowsPerPage,this.filteredData.length);for(let a=e;a<t;a++){const l=this.filteredData[a],n=this.createTableRow(l,a+1);this.tbody.appendChild(n)}}createTableRow(e,t){const a=document.createElement("tr"),l=f=>{switch(f.toLowerCase()){case"delivered":return"bg-success-subtle text-success border border-success-subtle";case"pending":return"bg-warning-subtle text-warning border border-warning-subtle";case"new":return"bg-primary-subtle text-primary border border-primary-subtle";case"shipping":return"bg-secondary-subtle text-secondary border border-secondary-subtle";default:return"bg-body-tertiary text-body-tertiary border"}},n=f=>{switch(f.toLowerCase()){case"paid":return"bg-success-subtle text-success border border-success-subtle";case"unpaid":return"bg-danger-subtle text-danger border border-danger-subtle";default:return"bg-body-tertiary text-body-tertiary border"}};return a.innerHTML=`
        <td>
          <div class="form-check check-primary">
            <input class="form-check-input" type="checkbox" aria-label="Check Data Checkbox" id="checkData${t}">
            <label class="form-check-label d-none" for="checkData${t}">
              Data ${t}
            </label>
          </div>
        </td>
        <td><a href="#!" class="link link-custom-primary">${e.orderId}</a></td>
        <td>${e.orderDate}</td>
        <td>${e.deliveredDate}</td>
        <td>${e.customer}</td>
        <td>${e.product}</td>
        <td><span class="badge ${n(e.payment)}">${e.payment}</span></td>
        <td>${e.total}</td>
        <td>${e.qty}</td>
        <td><span class="badge ${l(e.status)}">${e.status}</span></td>
        <td>
          <a href="#!" class="link link-custom-primary" type="button" id="actionDropdown${t}" data-bs-toggle="dropdown" aria-expanded="false" aria-label="dropdown-button">
            <i class="ri-more-2-fill"></i>
          </a>
          <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="actionDropdown${t}">
            <li>
              <a href="#overviewOrderModal" data-bs-toggle="modal" class="dropdown-item d-flex gap-3 align-items-center">
                <i class="ri-eye-line"></i>
                <span>Overview</span>
              </a>
            </li>
            <li>
              <a href="#addOrderModal" data-bs-toggle="modal" class="dropdown-item d-flex gap-3 align-items-center edit-order-btn" data-order-id="${e.orderId}">
                <i class="ri-pencil-line"></i>
                Edit
              </a>
            </li>
            <li>
              <a href="#!" class="dropdown-item d-flex gap-3 align-items-center delete-order-btn" data-order-id="${e.orderId}">
                <i class="ri-delete-bin-line"></i>
                <span>Delete</span>
              </a>
            </li>
          </ul>
        </td>
      `,a.querySelector(".edit-order-btn").addEventListener("click",()=>{this.populateEditForm(e)}),a.querySelector(".delete-order-btn").addEventListener("click",f=>{f.preventDefault(),document.getElementById("confirmDeleteBtn").dataset.orderId=e.orderId,new window.window.bootstrap.Modal(document.getElementById("deleteModal")).show()}),a.querySelector(".form-check-input").addEventListener("change",()=>{this.toggleDeleteSelectedButton()}),a}populateEditForm(e){document.querySelector("#addOrderModal .modal-header h6").textContent="Edit Order";const t=document.querySelector("#addOrderModal .btn-primary");t.textContent="Update Order",t.dataset.orderId=e.orderId,document.getElementById("orderIDInput").value=e.orderId,document.getElementById("orderIDInput").disabled=!0;const a=document.querySelectorAll('#addOrderModal input[placeholder="dd MMM, yyyy"]');a[0].value=e.orderDate,a[1].value=e.deliveredDate,document.querySelector('#addOrderModal input[placeholder="Customer Name"]').value=e.customer,document.querySelector("#addOrderModal .input-spin").value=e.qty,document.querySelector('#addOrderModal input[placeholder="Total Amount"]').value=e.total}setDropdownValue(e,t){const a=document.querySelector(`${e} .select-dropdown`);if(a){a.textContent=t;const l=document.querySelector(`${e} input[type="hidden"]`);l&&(l.value=t);const n=document.querySelector(e);n&&n.tagName==="SELECT"&&Array.from(n.options).forEach(d=>{(d.textContent===t||d.value===t)&&(d.selected=!0)})}}renderPagination(){if(!this.paginationContainer)return;this.paginationContainer.innerHTML="";const e=document.createElement("ul");e.className="pagination justify-content-center justify-content-md-end mb-0";const t=document.createElement("li");t.className=`page-item ${this.currentPage===1?"disabled":""}`,t.innerHTML='<a class="page-link" href="#!"><i data-lucide="chevron-left" class="size-4"></i> Previous</a>',t.addEventListener("click",u=>{u.preventDefault(),this.currentPage>1&&this.goToPage(this.currentPage-1)}),e.appendChild(t);const a=5;let l=Math.max(1,this.currentPage-Math.floor(a/2)),n=Math.min(this.totalPages,l+a-1);n-l+1<a&&(l=Math.max(1,n-a+1));for(let u=l;u<=n;u++){const m=document.createElement("li");m.className=`page-item ${u===this.currentPage?"active":""}`,m.innerHTML=`<a class="page-link" href="#!">${u}</a>`,m.addEventListener("click",f=>{f.preventDefault(),this.goToPage(u)}),e.appendChild(m)}const d=document.createElement("li");d.className=`page-item ${this.currentPage===this.totalPages?"disabled":""}`,d.innerHTML='<a class="page-link" href="#!">Next <i data-lucide="chevron-right" class="size-4"></i></a>',d.addEventListener("click",u=>{u.preventDefault(),this.currentPage<this.totalPages&&this.goToPage(this.currentPage+1)}),e.appendChild(d),this.paginationContainer.appendChild(e),k({icons:q})}updateResultsInfo(){if(!this.resultsInfoContainer)return;const e=(this.currentPage-1)*this.rowsPerPage+1,t=Math.min(e+this.rowsPerPage-1,this.filteredData.length);this.resultsInfoContainer.innerHTML=`Showing <b class="me-1">${this.filteredData.length>0?e:0}-${t}</b>of<b class="ms-1">${this.filteredData.length}</b> Results`}addOrder(e){e.orderId||(e.orderId=`#PEO-${Math.floor(1e4+Math.random()*9e4)}`),this.data.unshift(e),this.filteredData=[...this.data],this.totalPages=Math.ceil(this.filteredData.length/this.rowsPerPage),this.currentPage=1,this.renderTable(),this.renderPagination(),this.updateResultsInfo(),typeof b=="function"&&b(this.data)}editOrder(e,t){const a=this.data.findIndex(l=>l.orderId===e);if(a!==-1){this.data[a]={...this.data[a],...t,orderId:e};const l=this.filteredData.findIndex(n=>n.orderId===e);l!==-1?this.filteredData[l]={...this.data[a]}:this.filteredData=[...this.data],this.renderTable(),this.renderPagination(),this.updateResultsInfo(),typeof b=="function"&&b(this.data)}}deleteOrder(e){this.data=this.data.filter(t=>t.orderId!==e),this.filteredData=[...this.data],this.totalPages=Math.ceil(this.filteredData.length/this.rowsPerPage),this.currentPage>this.totalPages&&this.totalPages>0&&(this.currentPage=this.totalPages),this.renderTable(),this.renderPagination(),this.updateResultsInfo(),typeof b=="function"&&b(this.data)}goToPage(e){e<1||e>this.totalPages||(this.currentPage=e,this.renderTable(),this.renderPagination(),this.updateResultsInfo())}search(e){if(!e)this.filteredData=[...this.data];else{const t=e.toLowerCase();this.filteredData=this.data.filter(a=>a.orderId.toLowerCase().includes(t)||a.customer.toLowerCase().includes(t)||a.product.toLowerCase().includes(t)||a.status.toLowerCase().includes(t))}this.totalPages=Math.ceil(this.filteredData.length/this.rowsPerPage),this.currentPage=1,this.renderTable(),this.renderPagination(),this.updateResultsInfo()}sort(e,t=!0){this.filteredData.sort((a,l)=>{let n=a[e],d=l[e];return!isNaN(n)&&!isNaN(d)?(n=Number(n),d=Number(d)):(n=String(n).toLowerCase(),d=String(d).toLowerCase()),n<d?t?-1:1:n>d?t?1:-1:0}),this.renderTable()}}const M=[{orderId:"PEO-14521",orderDate:"15 Mar, 2022",deliveredDate:"21 Mar, 2022",customer:"Ella Patel",product:"Denim Jacket",payment:"Paid",total:45.99,qty:1,status:"Delivered"},{orderId:"PEO-14522",orderDate:"02 Apr, 2022",deliveredDate:"09 Apr, 2022",customer:"Lucas Nguyen",product:"Leather Wallet",payment:"COD",total:35.5,qty:1,status:"Pending"},{orderId:"PEO-14523",orderDate:"18 Jun, 2022",deliveredDate:"26 Jun, 2022",customer:"Isabella Thomas",product:"Summer Dress",payment:"Unpaid",total:28.75,qty:2,status:"New"},{orderId:"PEO-14524",orderDate:"30 Jul, 2022",deliveredDate:"07 Aug, 2022",customer:"Mason Wilson",product:"Wireless Headphones",payment:"Paid",total:79.99,qty:1,status:"Delivered"},{orderId:"PEO-14525",orderDate:"12 Sep, 2022",deliveredDate:"19 Sep, 2022",customer:"Olivia Brown",product:"Sunglasses",payment:"COD",total:19.95,qty:1,status:"Shipping"},{orderId:"PEO-14526",orderDate:"24 Oct, 2022",deliveredDate:"31 Oct, 2022",customer:"William Garcia",product:"Sports Watch",payment:"Paid",total:55,qty:1,status:"Delivered"},{orderId:"PEO-14527",orderDate:"05 Nov, 2022",deliveredDate:"12 Nov, 2022",customer:"Ava Martinez",product:"Backpack",payment:"COD",total:42.75,qty:1,status:"Shipping"},{orderId:"PEO-14528",orderDate:"14 Dec, 2022",deliveredDate:"22 Dec, 2022",customer:"Liam Clark",product:"Winter Coat",payment:"Unpaid",total:89.99,qty:1,status:"New"},{orderId:"PEO-14529",orderDate:"01 Jan, 2023",deliveredDate:"09 Jan, 2023",customer:"Charlotte Lewis",product:"Scarf",payment:"Paid",total:12.5,qty:2,status:"Pending"},{orderId:"PEO-14530",orderDate:"10 Feb, 2023",deliveredDate:"18 Feb, 2023",customer:"James Taylor",product:"Smartphone Case",payment:"COD",total:15.99,qty:1,status:"Shipping"},{orderId:"PEO-14531",orderDate:"20 Mar, 2023",deliveredDate:"27 Mar, 2023",customer:"Emma Hernandez",product:"Fitness Tracker",payment:"Paid",total:69,qty:1,status:"Delivered"},{orderId:"PEO-14532",orderDate:"05 Apr, 2023",deliveredDate:"12 Apr, 2023",customer:"Noah Young",product:"Sneakers",payment:"COD",total:49.95,qty:1,status:"Shipping"},{orderId:"PEO-14533",orderDate:"18 May, 2023",deliveredDate:"25 May, 2023",customer:"Sophie Johnson",product:"Handbag",payment:"Paid",total:65.5,qty:1,status:"Delivered"},{orderId:"PEO-14534",orderDate:"22 Jun, 2023",deliveredDate:"29 Jun, 2023",customer:"Ethan Davis",product:"Running Shoes",payment:"Unpaid",total:89.95,qty:1,status:"New"},{orderId:"PEO-14535",orderDate:"10 Jul, 2023",deliveredDate:"17 Jul, 2023",customer:"Amelia Rodriguez",product:"Bluetooth Speaker",payment:"COD",total:45.99,qty:1,status:"Pending"},{orderId:"PEO-14536",orderDate:"05 Aug, 2023",deliveredDate:"12 Aug, 2023",customer:"Alexander White",product:"Laptop Bag",payment:"Paid",total:38.75,qty:1,status:"Delivered"},{orderId:"PEO-14537",orderDate:"19 Sep, 2023",deliveredDate:"26 Sep, 2023",customer:"Harper Scott",product:"Wireless Mouse",payment:"COD",total:22.5,qty:1,status:"Shipping"}];function b(o){const e={new:o.filter(t=>t.status.toLowerCase()==="new").length,pending:o.filter(t=>t.status.toLowerCase()==="pending").length,delivered:o.filter(t=>t.status.toLowerCase()==="delivered").length,total:o.length};document.querySelector(".card.border-primary-subtle h4").textContent=e.new,document.querySelector(".card.border-warning-subtle h4").textContent=e.pending,document.querySelector(".card.border-success-subtle h4").textContent=e.delivered,document.querySelector(".card.border-secondary-subtle h4").textContent=e.total}document.addEventListener("DOMContentLoaded",function(){b(M)});function x(){return`PEO-${Math.floor(Math.random()*1e5)}`}function D(){const o=parseInt(document.querySelector(".input-spin").value)||0,e=parseFloat(document.querySelector('input[placeholder="Amount"]').value)||0,t=(o*e).toFixed(2),a=document.querySelector('input[placeholder="Total Amount"]');a.value=t,a.readOnly=!0}document.querySelector('input[placeholder="Amount"]').addEventListener("input",D);document.querySelector(".input-spin-plus").addEventListener("click",()=>{const o=document.querySelector(".input-spin");let e=parseInt(o.value)||1;o.value=e+1,D()});document.querySelector(".input-spin-minus").addEventListener("click",()=>{const o=document.querySelector(".input-spin");let e=parseInt(o.value)||1;e>1&&(o.value=e-1),D()});document.addEventListener("DOMContentLoaded",function(){const o=document.querySelector('input[placeholder="Total Amount"]');o.readOnly=!0,o.classList.add("bg-light"),D()});document.addEventListener("DOMContentLoaded",function(){const o=new w("orderTable");o.setData(M),o.setPaginationContainer("paginationContainer"),o.setResultsInfoContainer("resultsInfo");const e=document.getElementById("searchInput");e&&e.addEventListener("input",function(){o.search(this.value)}),new P("#orderDateInput",{dateFormat:"dd MMM, yyyy",autoClose:!0,position:"bottom left",locale:{days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],daysShort:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],daysMin:["Su","Mo","Tu","We","Th","Fr","Sa"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],monthsShort:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],today:"Today",clear:"Clear",firstDay:0}}),new P("#deliveredDateInput",{dateFormat:"dd MMM, yyyy",autoClose:!0,position:"bottom left",locale:{days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],daysShort:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],daysMin:["Su","Mo","Tu","We","Th","Fr","Sa"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],monthsShort:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],today:"Today",clear:"Clear",firstDay:0}});const t=document.querySelectorAll(".sortable");t.forEach(r=>{r.addEventListener("click",function(){const i=this.dataset.column,c=(this.dataset.direction||"asc")==="asc"?"desc":"asc";t.forEach(h=>{h.dataset.direction="",h.querySelector("i").className="ri-arrow-up-down-line ms-1"}),this.dataset.direction=c,this.querySelector("i").className=c==="asc"?"ri-arrow-up-line ms-1":"ri-arrow-down-line ms-1",o.sort(i,c==="asc")})});const a=document.querySelectorAll("#ordersTab .nav-link");a.length>0&&a.forEach(r=>{r.addEventListener("click",function(){a.forEach(s=>s.classList.remove("active")),this.classList.add("active");const i=this.textContent.trim();i==="All Orders"?o.filteredData=[...o.data]:o.filteredData=o.data.filter(s=>s.status.toLowerCase()===i.toLowerCase()),o.totalPages=Math.ceil(o.filteredData.length/o.rowsPerPage),o.currentPage=1,o.renderTable(),o.renderPagination(),o.updateResultsInfo()})});const l=document.querySelector("#addOrderModal .btn-primary");l&&l.addEventListener("click",function(){if(u(),!n())return;const i={orderId:document.getElementById("orderIDInput").value,orderDate:document.getElementById("orderDateInput").value||"",deliveredDate:document.getElementById("deliveredDateInput").value||"",customer:document.querySelector('#addOrderModal input[placeholder="Customer Name"]').value||"",qty:document.querySelector("#addOrderModal .input-spin").value||"0",total:document.querySelector('#addOrderModal input[placeholder="Total Amount"]').value||"0",product:document.querySelector("#productNameSelect").value||"Default Product",payment:document.querySelector("#paymentStatusSelect").value||"Unpaid",status:document.querySelector("#orderStatusSelect").value||"New"};this.dataset.orderId?(o.editOrder(this.dataset.orderId,i),delete this.dataset.orderId):o.addOrder(i),document.querySelector("#addOrderModal .modal-header h6").textContent="Add Order",this.textContent="Add Order",window.bootstrap.Modal.getInstance(document.getElementById("addOrderModal")).hide()}),document.getElementById("confirmDeleteBtn").addEventListener("click",function(){this.dataset.orderId&&(o.deleteOrder(this.dataset.orderId),delete this.dataset.orderId,window.bootstrap.Modal.getInstance(document.getElementById("deleteModal")).hide())}),w.prototype.populateEditForm=function(r){document.querySelector("#addOrderModal .modal-header h6").textContent="Edit Order";const i=document.querySelector("#addOrderModal .btn-primary");i.textContent="Update Order",i.dataset.orderId=r.orderId,document.getElementById("orderIDInput").value=r.orderId,document.getElementById("orderIDInput").disabled=!0,document.getElementById("orderDateInput").value=r.orderDate,document.getElementById("deliveredDateInput").value=r.deliveredDate,document.querySelector('#addOrderModal input[placeholder="Customer Name"]').value=r.customer,document.querySelector("#addOrderModal .input-spin").value=r.qty,document.querySelector('#addOrderModal input[placeholder="Total Amount"]').value=r.total;const s=document.querySelector('input[placeholder="Amount"]');if(s&&r.qty>0){const c=(parseFloat(r.total)/parseInt(r.qty)).toFixed(2);s.value=c}typeof this.setDropdownValue=="function"&&(this.setDropdownValue("#productNameSelect",r.product),this.setDropdownValue("#paymentStatusSelect",r.payment),this.setDropdownValue("#orderStatusSelect",r.status))};function n(){let r=!0;u();const i=document.getElementById("orderIDInput");i.value.trim()?/^[a-zA-Z0-9-]+$/.test(i.value)||(d(i,"Order ID should contain only letters, numbers, and hyphens"),r=!1):(d(i,"Order ID is required"),r=!1);const s=document.getElementById("orderDateInput");s.value.trim()?m(s.value)||(d(s,"Please enter a valid date in dd MMM, yyyy format"),r=!1):(d(s,"Order date is required"),r=!1);const c=document.querySelector('#addOrderModal input[placeholder="Customer Name"]');c.value.trim()||(d(c,"Customer name is required"),r=!1);const h=document.querySelector("#addOrderModal .input-spin");(!h.value.trim()||isNaN(h.value)||parseInt(h.value)<=0)&&(d(h,"Please enter a valid quantity (greater than 0)"),r=!1);const y=document.querySelector('#addOrderModal input[placeholder="Total Amount"]');(!y.value.trim()||isNaN(y.value)||parseFloat(y.value)<=0)&&(d(y,"Please enter a valid amount (greater than 0)"),r=!1);const v=document.querySelector("#productNameSelect");v.value||(d(v,"Please select a product"),r=!1);const p=document.getElementById("deliveredDateInput");return p.value.trim()&&(m(p.value)?s.value.trim()&&m(s.value)&&f(s.value,p.value)>0&&(d(p,"Delivered date cannot be before order date"),r=!1):(d(p,"Please enter a valid date in dd MMM, yyyy format"),r=!1)),r}function d(r,i){const s=document.createElement("div");s.className="invalid-feedback d-block",s.textContent=i,r.classList.add("is-invalid"),r.parentNode.insertBefore(s,r.nextSibling)}function u(){document.querySelectorAll(".invalid-feedback").forEach(r=>r.remove()),document.querySelectorAll(".is-invalid").forEach(r=>r.classList.remove("is-invalid"))}function m(r){if(!r||!r.trim())return!1;const i=/^(\d{1,2})\s+(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec),\s+(\d{4})$/i,s=r.match(i);if(!s)return!1;const c=parseInt(s[1],10),h=s[2].toLowerCase(),y=parseInt(s[3],10),p={jan:0,feb:1,mar:2,apr:3,may:4,jun:5,jul:6,aug:7,sep:8,oct:9,nov:10,dec:11}[h.toLowerCase()],g=new Date(y,p,c);return g.getFullYear()===y&&g.getMonth()===p&&g.getDate()===c}function f(r,i){const s=/^(\d{1,2})\s+(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec),\s+(\d{4})$/i,c=r.match(s);if(!c)return null;const h=parseInt(c[1],10),y=new Date(Date.parse(`${c[2]} 1, 2000`)).getMonth(),v=parseInt(c[3],10),p=/^(\d{1,2})\s+(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec),\s+(\d{4})$/i,g=i.match(p);if(!g)return null;const O=parseInt(g[1],10),E=new Date(Date.parse(`${g[2]} 1, 2000`)).getMonth(),C=parseInt(g[3],10),I=new Date(v,y,h),S=new Date(C,E,O);return I<S?-1:I>S?1:0}document.querySelector('[data-bs-target="#addOrderModal"]').addEventListener("click",()=>{document.querySelector("#addOrderModal form").reset();const r=document.getElementById("orderIDInput");r.disabled=!0,r.value=x(),document.querySelector("#addOrderModal .modal-header h6").textContent="Add Order";const i=document.querySelector("#addOrderModal .btn-primary");i.textContent="Add Order",delete i.dataset.orderId})});
