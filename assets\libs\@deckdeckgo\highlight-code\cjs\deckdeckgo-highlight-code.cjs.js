'use strict';

const index = require('./index-f6fc0a5c.js');

/*
 Stencil Client Patch Browser v2.17.4 | MIT Licensed | https://stenciljs.com
 */
const patchBrowser = () => {
    const importMeta = (typeof document === 'undefined' ? new (require('u' + 'rl').URL)('file:' + __filename).href : (document.currentScript && document.currentScript.src || new URL('deckdeckgo-highlight-code.cjs.js', document.baseURI).href));
    const opts = {};
    if (importMeta !== '') {
        opts.resourcesUrl = new URL('.', importMeta).href;
    }
    return index.promiseResolve(opts);
};

patchBrowser().then(options => {
  return index.bootstrapLazy([["deckgo-highlight-code_2.cjs",[[1,"deckgo-highlight-code",{"language":[513],"highlightLines":[513,"highlight-lines"],"lineNumbers":[516,"line-numbers"],"terminal":[513],"editable":[4],"editableLabel":[1,"editable-label"],"theme":[513],"revealProgress":[1025,"reveal-progress"],"themeStyle":[32],"loaded":[32],"highlightRows":[32],"load":[64],"reveal":[64],"hide":[64],"revealAll":[64],"hideAll":[64],"nextHighlight":[64],"prevHighlight":[64]},[[5,"prismLanguageLoaded","onLanguageLoaded"],[5,"prismLanguageError","onLanguageError"]]],[1,"deckgo-highlight-code-edit",{"label":[1]}]]]], options);
});
