/*!
FullCalendar Premium Common v6.1.17
Docs & License: https://fullcalendar.io/docs/premium
(c) 2024 Adam <PERSON>
*/
FullCalendar.PremiumCommon=function(e,n,t,l){"use strict";const r=["GPL-My-Project-Is-Open-Source","CC-Attribution-NonCommercial-NoDerivatives"],i={position:"absolute",zIndex:99999,bottom:"1px",left:"1px",background:"#eee",borderColor:"#ddd",borderStyle:"solid",borderWidth:"1px 1px 0 0",padding:"2px 4px",fontSize:"12px",borderTopRightRadius:"3px"};const o={schedulerLicenseKey:String};var a=n.createPlugin({name:"@fullcalendar/premium-common",premiumReleaseDate:"2025-04-02",optionRefiners:o,viewContainerAppends:[function(e){let n=e.options.schedulerLicenseKey,o="undefined"!=typeof window?window.location.href:"";if(!/\w+:\/\/fullcalendar\.io\/|\/examples\/[\w-]+\.html$/.test(o)){let o=function(e,n){if(-1!==r.indexOf(e))return"valid";const l=(e||"").match(/^(\d+)-fcs-(\d+)$/);if(l&&10===l[1].length){const e=new Date(1e3*parseInt(l[2],10)),r=t.config.mockSchedulerReleaseDate||n;if(t.isValidDate(r)){return t.addDays(r,-372)<e?"valid":"outdated"}}return"invalid"}(n,e.pluginHooks.premiumReleaseDate);if("valid"!==o)return l.createElement("div",{className:"fc-license-message",style:i},"outdated"===o?l.createElement(l.Fragment,null,"Your license key is too old to work with this version. ",l.createElement("a",{href:"https://fullcalendar.io/docs/schedulerLicenseKey#outdated"},"More Info")):l.createElement(l.Fragment,null,"Your license key is invalid. ",l.createElement("a",{href:"https://fullcalendar.io/docs/schedulerLicenseKey#invalid"},"More Info")))}return null}]});return n.globalPlugins.push(a),e.default=a,Object.defineProperty(e,"__esModule",{value:!0}),e}({},FullCalendar,FullCalendar.Internal,FullCalendar.Preact);