import"../../admin.bundle-DI9_jvUJ.js";import"../../main-Cyta4iCA.js";document.addEventListener("DOMContentLoaded",function(){document.getElementById("wishlistTable").addEventListener("click",function(t){if(t.target.closest(".input-spin-plus")){const e=t.target.closest(".input-spin-group").querySelector(".input-spin");e.value=parseInt(e.value)+1,n(t.target.closest("tr"))}if(t.target.closest(".input-spin-minus")){const e=t.target.closest(".input-spin-group").querySelector(".input-spin");parseInt(e.value)>1&&(e.value=parseInt(e.value)-1,n(t.target.closest("tr")))}t.target.closest(".close-btn")&&(t.preventDefault(),confirm("Are you sure you want to remove this item from your wishlist?")&&t.target.closest("tr").remove())});function n(t){const e=parseFloat(t.querySelector("td:nth-child(2)").textContent),s=parseInt(t.querySelector(".input-spin").value),i=e*s;t.querySelector("td:nth-child(4)").textContent=i.toFixed(2)}});
