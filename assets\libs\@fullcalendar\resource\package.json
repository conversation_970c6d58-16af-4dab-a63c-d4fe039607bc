{"name": "@fullcalendar/resource", "version": "6.1.17", "title": "FullCalendar Resource Plugin", "description": "Base support for resources, required by resource views", "keywords": ["calendar", "event", "full-sized", "fullcalendar", "scheduler", "resource", "scheduler", "resource"], "dependencies": {"@fullcalendar/premium-common": "~6.1.17"}, "peerDependencies": {"@fullcalendar/core": "~6.1.17"}, "type": "module", "sideEffects": true, "homepage": "https://fullcalendar.io/docs/premium", "bugs": "https://fullcalendar.io/reporting-bugs", "repository": {"type": "git", "url": "https://github.com/fullcalendar/fullcalendar-workspace.git", "directory": "premium/packages/resource"}, "license": "SEE LICENSE IN LICENSE.md", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://arshaw.com/"}, "copyright": "2024 <PERSON>", "types": "./index.d.ts", "main": "./index.cjs", "module": "./index.js", "unpkg": "./index.global.min.js", "jsdelivr": "./index.global.min.js", "exports": {"./package.json": "./package.json", "./index.cjs": "./index.cjs", "./index.js": "./index.js", ".": {"types": "./index.d.ts", "require": "./index.cjs", "import": "./index.js"}, "./internal.cjs": "./internal.cjs", "./internal.js": "./internal.js", "./internal": {"types": "./internal.d.ts", "require": "./internal.cjs", "import": "./internal.js"}}}