<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
    <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@400;600&display=swap" rel="stylesheet" />
    <script src="https://cdn.jsdelivr.net/npm/@svgdotjs/svg.js"></script>
    <script src="../apexsankey.min.js"></script>
  </head>
  <body>
    <div id="svg-sankey" style="margin: 0 auto"></div>
    <script>
      const data = {
        nodes: [
          {
            id: '<PERSON>',
            title: '<PERSON>',
          },
          {
            id: '<PERSON>',
            title: '<PERSON>',
          },
          {
            id: '<PERSON>',
            title: '<PERSON>',
          },
          {
            id: '<PERSON>',
            title: '<PERSON>',
          },
          {
            id: '<PERSON>',
            title: '<PERSON>',
          },
          {
            id: '<PERSON><PERSON>',
            title: '<PERSON><PERSON>',
          },
        ],
        edges: [
          {
            source: '<PERSON>',
            target: '<PERSON>',
            value: 72,
          },
          {
            source: '<PERSON>',
            target: '<PERSON>',
            value: 155,
          },
          {
            source: '<PERSON>',
            target: '<PERSON>',
            value: 109,
          },
          {
            source: '<PERSON>',
            target: '<PERSON>',
            value: 100,
          },
          {
            source: '<PERSON>',
            target: '<PERSON>',
            value: 100,
          },
          {
            source: '<PERSON>',
            target: '<PERSON><PERSON>',
            value: 127,
          },
          {
            source: '<PERSON>',
            target: '<PERSON>',
            value: 67,
          },
          {
            source: '<PERSON><PERSON>',
            target: '<PERSON>',
            value: 140,
          },
          {
            source: '<PERSON><PERSON>',
            target: '<PERSON>',
            value: 93,
          },
        ],
      };
      const graphOptions = {
        width: '90%',
        fontFamily: 'Quicksand, sans-serif',
        fontWeight: '600',
        nodeWidth: 5,
        nodeBorderWidth: 2,
        nodeBorderColor: '#111',
        edgeGradientFill: false,
      };
      const s = new ApexSankey(document.getElementById('svg-sankey'), graphOptions);
      s.render(data);
    </script>
  </body>
</html>
