/*!
FullCalendar ScrollGrid Plugin v6.1.17
Docs & License: https://fullcalendar.io/docs/premium
(c) 2024 Adam Shaw
*/
FullCalendar.ScrollGrid=function(e,t,l,s,i){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}var o=r(l);function n(e){let t=e.scrollLeft;if("rtl"===window.getComputedStyle(e).direction)switch(a()){case"negative":t*=-1;case"reverse":t=e.scrollWidth-t-e.clientWidth}return t}function h(e,t){if("rtl"===window.getComputedStyle(e).direction)switch(a()){case"reverse":t=e.scrollWidth-t;break;case"negative":t=-(e.scrollWidth-t)}e.scrollLeft=t}let c;function a(){return c||(c=function(){let e=document.createElement("div");e.style.position="absolute",e.style.top="-1000px",e.style.width="100px",e.style.height="100px",e.style.overflow="scroll",e.style.direction="rtl";let t,l=document.createElement("div");l.style.width="200px",l.style.height="200px",e.appendChild(l),document.body.appendChild(e),e.scrollLeft>0?t="positive":(e.scrollLeft=1,t=e.scrollLeft>0?"reverse":"negative");return s.removeElement(e),t}())}class d{constructor(e,t){this.scrollEl=e,this.isRtl=t,this.updateSize=()=>{let{scrollEl:e}=this,t=s.findElements(e,".fc-sticky");!function(e,t,l){e.forEach((e,i)=>{let r,{textAlign:o,elWidth:n,parentBound:h}=t[i],c=h.right-h.left;r="center"===o&&c>l?(l-n)/2:"",s.applyStyle(e,{left:r,right:r,top:0})})}(t,this.queryElGeoms(t),e.clientWidth)}}queryElGeoms(e){let{scrollEl:t,isRtl:l}=this,i=function(e){let t=e.getBoundingClientRect(),l=s.computeEdges(e);return{left:t.left+l.borderLeft+l.scrollbarLeft-n(e),top:t.top+l.borderTop-e.scrollTop}}(t),r=[];for(let t of e){let e=s.translateRect(s.computeInnerRect(t.parentNode,!0,!0),-i.left,-i.top),o=t.getBoundingClientRect(),n=window.getComputedStyle(t),h=window.getComputedStyle(t.parentNode).textAlign,c=null;"start"===h?h=l?"right":"left":"end"===h&&(h=l?"left":"right"),"sticky"!==n.position&&(c=s.translateRect(o,-i.left-(parseFloat(n.left)||0),-i.top-(parseFloat(n.top)||0))),r.push({parentBound:e,naturalBound:c,elWidth:o.width,elHeight:o.height,textAlign:h})}return r}}class u extends s.BaseComponent{constructor(){super(...arguments),this.elRef=i.createRef(),this.state={xScrollbarWidth:0,yScrollbarWidth:0},this.handleScroller=e=>{this.scroller=e,s.setRef(this.props.scrollerRef,e)},this.handleSizing=()=>{let{props:e}=this;"scroll-hidden"===e.overflowY&&this.setState({yScrollbarWidth:this.scroller.getYScrollbarWidth()}),"scroll-hidden"===e.overflowX&&this.setState({xScrollbarWidth:this.scroller.getXScrollbarWidth()})}}render(){let{props:e,state:t,context:l}=this,r=l.isRtl&&s.getIsRtlScrollbarOnLeft(),o=0,n=0,h=0,{overflowX:c,overflowY:a}=e;return e.forPrint&&(c="visible",a="visible"),"scroll-hidden"===c&&(h=t.xScrollbarWidth),"scroll-hidden"===a&&null!=t.yScrollbarWidth&&(r?o=t.yScrollbarWidth:n=t.yScrollbarWidth),i.createElement("div",{ref:this.elRef,className:"fc-scroller-harness"+(e.liquid?" fc-scroller-harness-liquid":"")},i.createElement(s.Scroller,{ref:this.handleScroller,elRef:this.props.scrollerElRef,overflowX:"scroll-hidden"===c?"scroll":c,overflowY:"scroll-hidden"===a?"scroll":a,overcomeLeft:o,overcomeRight:n,overcomeBottom:h,maxHeight:"number"==typeof e.maxHeight?e.maxHeight+("scroll-hidden"===c?t.xScrollbarWidth:0):"",liquid:e.liquid,liquidIsAbsolute:!0},e.children))}componentDidMount(){this.handleSizing(),this.context.addResizeHandler(this.handleSizing)}getSnapshotBeforeUpdate(e){return this.props.forPrint&&!e.forPrint?{simulateScrollLeft:this.scroller.el.scrollLeft}:{}}componentDidUpdate(e,t,l){const{props:i,scroller:{el:r}}=this;if(s.isPropsEqual(e,i)||this.handleSizing(),void 0!==l.simulateScrollLeft)r.style.left=-l.simulateScrollLeft+"px";else if(!i.forPrint&&e.forPrint){const e=-parseInt(r.style.left);r.style.left="",r.scrollLeft=e}}componentWillUnmount(){this.context.removeResizeHandler(this.handleSizing)}needsXScrolling(){return this.scroller.needsXScrolling()}needsYScrolling(){return this.scroller.needsYScrolling()}}const f="wheel mousewheel DomMouseScroll MozMousePixelScroll".split(" ");class p{constructor(e){this.el=e,this.emitter=new s.Emitter,this.isScrolling=!1,this.isTouching=!1,this.isRecentlyWheeled=!1,this.isRecentlyScrolled=!1,this.wheelWaiter=new s.DelayedRunner(this._handleWheelWaited.bind(this)),this.scrollWaiter=new s.DelayedRunner(this._handleScrollWaited.bind(this)),this.handleScroll=()=>{this.startScroll(),this.emitter.trigger("scroll",this.isRecentlyWheeled,this.isTouching),this.isRecentlyScrolled=!0,this.scrollWaiter.request(500)},this.handleWheel=()=>{this.isRecentlyWheeled=!0,this.wheelWaiter.request(500)},this.handleTouchStart=()=>{this.isTouching=!0},this.handleTouchEnd=()=>{this.isTouching=!1,this.isRecentlyScrolled||this.endScroll()},e.addEventListener("scroll",this.handleScroll),e.addEventListener("touchstart",this.handleTouchStart,{passive:!0}),e.addEventListener("touchend",this.handleTouchEnd);for(let t of f)e.addEventListener(t,this.handleWheel)}destroy(){let{el:e}=this;e.removeEventListener("scroll",this.handleScroll),e.removeEventListener("touchstart",this.handleTouchStart,{passive:!0}),e.removeEventListener("touchend",this.handleTouchEnd);for(let t of f)e.removeEventListener(t,this.handleWheel)}startScroll(){this.isScrolling||(this.isScrolling=!0,this.emitter.trigger("scrollStart",this.isRecentlyWheeled,this.isTouching))}endScroll(){this.isScrolling&&(this.emitter.trigger("scrollEnd"),this.isScrolling=!1,this.isRecentlyScrolled=!0,this.isRecentlyWheeled=!1,this.scrollWaiter.clear(),this.wheelWaiter.clear())}_handleScrollWaited(){this.isRecentlyScrolled=!1,this.isTouching||this.endScroll()}_handleWheelWaited(){this.isRecentlyWheeled=!1}}class g{constructor(e,t){this.isVertical=e,this.scrollEls=t,this.isPaused=!1,this.scrollListeners=t.map(e=>this.bindScroller(e))}destroy(){for(let e of this.scrollListeners)e.destroy()}bindScroller(e){let{scrollEls:t,isVertical:l}=this,s=new p(e);return s.emitter.on("scroll",(s,i)=>{if(!this.isPaused&&((!this.masterEl||this.masterEl!==e&&(s||i))&&this.assignMaster(e),this.masterEl===e))for(let s of t)s!==e&&(l?s.scrollTop=e.scrollTop:s.scrollLeft=e.scrollLeft)}),s.emitter.on("scrollEnd",()=>{this.masterEl===e&&(this.masterEl=null)}),s}assignMaster(e){this.masterEl=e;for(let t of this.scrollListeners)t.el!==e&&t.endScroll()}forceScrollLeft(e){this.isPaused=!0;for(let t of this.scrollListeners)h(t.el,e);this.isPaused=!1}forceScrollTop(e){this.isPaused=!0;for(let t of this.scrollListeners)t.el.scrollTop=e;this.isPaused=!1}}s.config.SCROLLGRID_RESIZE_INTERVAL=500;class S extends s.BaseComponent{constructor(){super(...arguments),this.compileColGroupStats=s.memoizeArraylike(w,W),this.renderMicroColGroups=s.memoizeArraylike(s.renderMicroColGroup),this.clippedScrollerRefs=new s.RefMap,this.scrollerElRefs=new s.RefMap(this._handleScrollerEl.bind(this)),this.chunkElRefs=new s.RefMap(this._handleChunkEl.bind(this)),this.scrollSyncersBySection={},this.scrollSyncersByColumn={},this.rowUnstableMap=new Map,this.rowInnerMaxHeightMap=new Map,this.anyRowHeightsChanged=!1,this.recentSizingCnt=0,this.state={shrinkWidths:[],forceYScrollbars:!1,forceXScrollbars:!1,scrollerClientWidths:{},scrollerClientHeights:{},sectionRowMaxHeights:[]},this.handleSizing=(e,t)=>{if(!this.allowSizing())return;t||(this.anyRowHeightsChanged=!0);let l={};(e||!t&&!this.rowUnstableMap.size)&&(l.sectionRowMaxHeights=this.computeSectionRowMaxHeights()),this.setState(Object.assign(Object.assign({shrinkWidths:this.computeShrinkWidths()},this.computeScrollerDims()),l),()=>{this.rowUnstableMap.size||this.updateStickyScrolling()})},this.handleRowHeightChange=(e,t)=>{let{rowUnstableMap:l,rowInnerMaxHeightMap:s}=this;if(t){l.delete(e);let t=y(e);s.has(e)&&s.get(e)===t||(s.set(e,t),this.anyRowHeightsChanged=!0),!l.size&&this.anyRowHeightsChanged&&(this.anyRowHeightsChanged=!1,this.setState({sectionRowMaxHeights:this.computeSectionRowMaxHeights()}))}else l.set(e,!0)}}render(){let{props:e,state:t,context:l}=this,{shrinkWidths:r}=t,o=this.compileColGroupStats(e.colGroups.map(e=>[e])),n=this.renderMicroColGroups(o.map((e,t)=>[e.cols,r[t]])),h=s.getScrollGridClassNames(e.liquid,l);this.getDims();let c,a=e.sections,d=a.length,u=0,f=[],p=[],g=[];for(;u<d&&"header"===(c=a[u]).type;)f.push(this.renderSection(c,u,o,n,t.sectionRowMaxHeights,!0)),u+=1;for(;u<d&&"body"===(c=a[u]).type;)p.push(this.renderSection(c,u,o,n,t.sectionRowMaxHeights,!1)),u+=1;for(;u<d&&"footer"===(c=a[u]).type;)g.push(this.renderSection(c,u,o,n,t.sectionRowMaxHeights,!0)),u+=1;const S=!s.getCanVGrowWithinCell(),m={role:"rowgroup"};return i.createElement("table",{ref:e.elRef,role:"grid",className:h.join(" ")},function(e,t){let l=e.map((e,l)=>{let r=e.width;return"shrink"===r&&(r=e.totalColWidth+s.sanitizeShrinkWidth(t[l])+1),i.createElement("col",{style:{width:r}})});return i.createElement("colgroup",{},...l)}(o,r),Boolean(!S&&f.length)&&i.createElement("thead",m,...f),Boolean(!S&&p.length)&&i.createElement("tbody",m,...p),Boolean(!S&&g.length)&&i.createElement("tfoot",m,...g),S&&i.createElement("tbody",m,...f,...p,...g))}renderSection(e,t,l,r,o,n){return"outerContent"in e?i.createElement(i.Fragment,{key:e.key},e.outerContent):i.createElement("tr",{key:e.key,role:"presentation",className:s.getSectionClassNames(e,this.props.liquid).join(" ")},e.chunks.map((s,i)=>this.renderChunk(e,t,l[i],r[i],s,i,(o[t]||[])[i]||[],n)))}renderChunk(e,t,l,r,o,n,h,c){if("outerContent"in o)return i.createElement(i.Fragment,{key:o.key},o.outerContent);let{state:a}=this,{scrollerClientWidths:d,scrollerClientHeights:f}=a,[p,g]=this.getDims(),S=t*g+n,m=n===(!this.context.isRtl||s.getIsRtlScrollbarOnLeft()?g-1:0),y=t===p-1,R=y&&a.forceXScrollbars,w=m&&a.forceYScrollbars,C=l&&l.allowXScrolling,E=s.getAllowYScrolling(this.props,e),W=s.getSectionHasLiquidHeight(this.props,e),b=e.expandRows&&W,k=l&&l.totalColMinWidth||"",x=s.renderChunkContent(e,o,{tableColGroupNode:r,tableMinWidth:k,clientWidth:void 0!==d[S]?d[S]:null,clientHeight:void 0!==f[S]?f[S]:null,expandRows:b,syncRowHeights:Boolean(e.syncRowHeights),rowSyncHeights:h,reportRowHeightChange:this.handleRowHeightChange},c),M=R?y?"scroll":"scroll-hidden":C?y?"auto":"scroll-hidden":"hidden",v=w?m?"scroll":"scroll-hidden":E?m?"auto":"scroll-hidden":"hidden";return x=i.createElement(u,{ref:this.clippedScrollerRefs.createRef(S),scrollerElRef:this.scrollerElRefs.createRef(S),overflowX:M,overflowY:v,forPrint:this.props.forPrint,liquid:W,maxHeight:e.maxHeight},x),i.createElement(c?"th":"td",{key:o.key,ref:this.chunkElRefs.createRef(S),role:"presentation"},x)}componentDidMount(){this.getStickyScrolling=s.memoizeArraylike(x),this.getScrollSyncersBySection=s.memoizeHashlike(b.bind(this,!0),null,k),this.getScrollSyncersByColumn=s.memoizeHashlike(b.bind(this,!1),null,k),this.updateScrollSyncers(),this.handleSizing(!1),this.context.addResizeHandler(this.handleSizing)}componentDidUpdate(e,t){this.updateScrollSyncers(),this.handleSizing(!1,t.sectionRowMaxHeights!==this.state.sectionRowMaxHeights)}componentWillUnmount(){this.context.removeResizeHandler(this.handleSizing),this.destroyScrollSyncers()}allowSizing(){let e=new Date;return!this.lastSizingDate||e.valueOf()>this.lastSizingDate.valueOf()+s.config.SCROLLGRID_RESIZE_INTERVAL?(this.lastSizingDate=e,this.recentSizingCnt=0,!0):(this.recentSizingCnt+=1)<=10}computeShrinkWidths(){let e=this.compileColGroupStats(this.props.colGroups.map(e=>[e])),[t,l]=this.getDims(),i=t*l,r=[];return e.forEach((e,t)=>{if(e.hasShrinkCol){let e=this.chunkElRefs.collect(t,i,l);r[t]=s.computeShrinkWidth(e)}}),r}computeSectionRowMaxHeights(){let e=new Map,[t,l]=this.getDims(),i=[];for(let r=0;r<t;r+=1){let t=this.props.sections[r],o=[];if(t&&t.syncRowHeights){let i=[];for(let t=0;t<l;t+=1){let o=r*l+t,n=[],h=this.chunkElRefs.currentMap[o];n=h?s.findElements(h,".fc-scrollgrid-sync-table tr").map(t=>{let l=y(t);return e.set(t,l),l}):[],i.push(n)}let n=i[0].length,h=!0;for(let e=1;e<l;e+=1){if(!(t.chunks[e]&&void 0!==t.chunks[e].outerContent)&&i[e].length!==n){h=!1;break}}if(h){for(let e=0;e<l;e+=1)o.push([]);for(let e=0;e<n;e+=1){let t=[];for(let s=0;s<l;s+=1){let l=i[s][e];null!=l&&t.push(l)}let s=Math.max(...t);for(let e=0;e<l;e+=1)o[e].push(s)}}else{let e=[];for(let t=0;t<l;t+=1)e.push(m(i[t])+i[t].length);let t=Math.max(...e);for(let e=0;e<l;e+=1){let l=i[e].length,s=t-l,r=Math.floor(s/l),n=s-r*(l-1),h=[],c=0;for(c<l&&(h.push(n),c+=1);c<l;)h.push(r),c+=1;o.push(h)}}}i.push(o)}return this.rowInnerMaxHeightMap=e,i}computeScrollerDims(){let e=s.getScrollbarWidths(),[t,l]=this.getDims(),i=!this.context.isRtl||s.getIsRtlScrollbarOnLeft()?l-1:0,r=t-1,o=this.clippedScrollerRefs.currentMap,n=this.scrollerElRefs.currentMap,h=!1,c=!1,a={},d={};for(let e=0;e<t;e+=1){let t=o[e*l+i];if(t&&t.needsYScrolling()){h=!0;break}}for(let e=0;e<l;e+=1){let t=o[r*l+e];if(t&&t.needsXScrolling()){c=!0;break}}for(let s=0;s<t;s+=1)for(let t=0;t<l;t+=1){let o=s*l+t,u=n[o];if(u){let l=u.parentNode;a[o]=Math.floor(l.getBoundingClientRect().width-(t===i&&h?e.y:0)),d[o]=Math.floor(l.getBoundingClientRect().height-(s===r&&c?e.x:0))}}return{forceYScrollbars:h,forceXScrollbars:c,scrollerClientWidths:a,scrollerClientHeights:d}}updateStickyScrolling(){let{isRtl:e}=this.context,t=this.scrollerElRefs.getAll().map(t=>[t,e]);this.getStickyScrolling(t).forEach(e=>e.updateSize())}updateScrollSyncers(){let[e,t]=this.getDims(),l=e*t,i={},r={},o=this.scrollerElRefs.currentMap;for(let l=0;l<e;l+=1){let e=l*t,r=e+t;i[l]=s.collectFromHash(o,e,r,1)}for(let e=0;e<t;e+=1)r[e]=this.scrollerElRefs.collect(e,l,t);this.scrollSyncersBySection=this.getScrollSyncersBySection(i),this.scrollSyncersByColumn=this.getScrollSyncersByColumn(r)}destroyScrollSyncers(){s.mapHash(this.scrollSyncersBySection,k),s.mapHash(this.scrollSyncersByColumn,k)}getChunkConfigByIndex(e){let t=this.getDims()[1],l=Math.floor(e/t),s=e%t,i=this.props.sections[l];return i&&i.chunks[s]}forceScrollLeft(e,t){let l=this.scrollSyncersByColumn[e];l&&l.forceScrollLeft(t)}forceScrollTop(e,t){let l=this.scrollSyncersBySection[e];l&&l.forceScrollTop(t)}_handleChunkEl(e,t){let l=this.getChunkConfigByIndex(parseInt(t,10));l&&s.setRef(l.elRef,e)}_handleScrollerEl(e,t){let l=this.getChunkConfigByIndex(parseInt(t,10));l&&s.setRef(l.scrollerElRef,e)}getDims(){let e=this.props.sections.length;return[e,e?this.props.sections[0].chunks.length:0]}}function m(e){let t=0;for(let l of e)t+=l;return t}function y(e){let t=s.findElements(e,".fc-scrollgrid-sync-inner").map(R);return t.length?Math.max(...t):0}function R(e){return e.offsetHeight}function w(e){let t=C(e.cols,"width"),l=C(e.cols,"minWidth"),i=s.hasShrinkWidth(e.cols);return{hasShrinkCol:i,totalColWidth:t,totalColMinWidth:l,allowXScrolling:"shrink"!==e.width&&Boolean(t||l||i),cols:e.cols,width:e.width}}function C(e,t){let l=0;for(let s of e){let e=s[t];"number"==typeof e&&(l+=e*(s.span||1))}return l}S.addStateEquality({shrinkWidths:s.isArraysEqual,scrollerClientWidths:s.isPropsEqual,scrollerClientHeights:s.isPropsEqual});const E={cols:s.isColPropsEqual};function W(e,t){return s.compareObjs(e,t,E)}function b(e,...t){return new g(e,t)}function k(e){e.destroy()}function x(e,t){return new d(e,t)}var M=t.createPlugin({name:"@fullcalendar/scrollgrid",premiumReleaseDate:"2025-04-02",deps:[o.default],scrollGridImpl:S}),v={__proto__:null,ScrollGrid:S};return t.globalPlugins.push(M),e.Internal=v,e.default=M,Object.defineProperty(e,"__esModule",{value:!0}),e}({},FullCalendar,FullCalendar.PremiumCommon,FullCalendar.Internal,FullCalendar.Preact);