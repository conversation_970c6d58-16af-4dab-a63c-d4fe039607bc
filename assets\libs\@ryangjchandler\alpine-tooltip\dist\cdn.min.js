(()=>{var V="top",H="bottom",k="right",W="left",Ve="auto",ye=[V,H,k,W],ce="start",lt="end",Zt="clippingParents",ct="viewport",Ie="popper",er="reference",Tt=ye.reduce(function(t,e){return t.concat([e+"-"+ce,e+"-"+lt])},[]),dt=[].concat(ye,[Ve]).reduce(function(t,e){return t.concat([e,e+"-"+ce,e+"-"+lt])},[]),tn="beforeRead",rn="read",nn="afterRead",on="beforeMain",an="main",sn="afterMain",pn="beforeWrite",fn="write",un="afterWrite",ze=[tn,rn,nn,on,an,sn,pn,fn,un];function F(t){return t?(t.nodeName||"").toLowerCase():null}function B(t){if(t==null)return window;if(t.toString()!=="[object Window]"){var e=t.ownerDocument;return e&&e.defaultView||window}return t}function de(t){var e=B(t).Element;return t instanceof e||t instanceof Element}function _(t){var e=B(t).HTMLElement;return t instanceof e||t instanceof HTMLElement}function mt(t){if(typeof ShadowRoot=="undefined")return!1;var e=B(t).ShadowRoot;return t instanceof e||t instanceof ShadowRoot}function ln(t){var e=t.state;Object.keys(e.elements).forEach(function(r){var n=e.styles[r]||{},i=e.attributes[r]||{},a=e.elements[r];!_(a)||!F(a)||(Object.assign(a.style,n),Object.keys(i).forEach(function(p){var f=i[p];f===!1?a.removeAttribute(p):a.setAttribute(p,f===!0?"":f)}))})}function cn(t){var e=t.state,r={popper:{position:e.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(e.elements.popper.style,r.popper),e.styles=r,e.elements.arrow&&Object.assign(e.elements.arrow.style,r.arrow),function(){Object.keys(e.elements).forEach(function(n){var i=e.elements[n],a=e.attributes[n]||{},p=Object.keys(e.styles.hasOwnProperty(n)?e.styles[n]:r[n]),f=p.reduce(function(u,c){return u[c]="",u},{});!_(i)||!F(i)||(Object.assign(i.style,f),Object.keys(a).forEach(function(u){i.removeAttribute(u)}))})}}var vt={name:"applyStyles",enabled:!0,phase:"write",fn:ln,effect:cn,requires:["computeStyles"]};function I(t){return t.split("-")[0]}function Q(t){var e=t.getBoundingClientRect();return{width:e.width,height:e.height,top:e.top,right:e.right,bottom:e.bottom,left:e.left,x:e.left,y:e.top}}function Pe(t){var e=Q(t),r=t.offsetWidth,n=t.offsetHeight;return Math.abs(e.width-r)<=1&&(r=e.width),Math.abs(e.height-n)<=1&&(n=e.height),{x:t.offsetLeft,y:t.offsetTop,width:r,height:n}}function Ge(t,e){var r=e.getRootNode&&e.getRootNode();if(t.contains(e))return!0;if(r&&mt(r)){var n=e;do{if(n&&t.isSameNode(n))return!0;n=n.parentNode||n.host}while(n)}return!1}function $(t){return B(t).getComputedStyle(t)}function Ct(t){return["table","td","th"].indexOf(F(t))>=0}function U(t){return((de(t)?t.ownerDocument:t.document)||window.document).documentElement}function me(t){return F(t)==="html"?t:t.assignedSlot||t.parentNode||(mt(t)?t.host:null)||U(t)}function tr(t){return!_(t)||$(t).position==="fixed"?null:t.offsetParent}function dn(t){var e=navigator.userAgent.toLowerCase().indexOf("firefox")!==-1,r=navigator.userAgent.indexOf("Trident")!==-1;if(r&&_(t)){var n=$(t);if(n.position==="fixed")return null}for(var i=me(t);_(i)&&["html","body"].indexOf(F(i))<0;){var a=$(i);if(a.transform!=="none"||a.perspective!=="none"||a.contain==="paint"||["transform","perspective"].indexOf(a.willChange)!==-1||e&&a.willChange==="filter"||e&&a.filter&&a.filter!=="none")return i;i=i.parentNode}return null}function re(t){for(var e=B(t),r=tr(t);r&&Ct(r)&&$(r).position==="static";)r=tr(r);return r&&(F(r)==="html"||F(r)==="body"&&$(r).position==="static")?e:r||dn(t)||e}function Te(t){return["top","bottom"].indexOf(t)>=0?"x":"y"}var Z=Math.max,be=Math.min,Ke=Math.round;function Ce(t,e,r){return Z(t,be(e,r))}function Je(){return{top:0,right:0,bottom:0,left:0}}function Qe(t){return Object.assign({},Je(),t)}function Ze(t,e){return e.reduce(function(r,n){return r[n]=t,r},{})}var mn=function(e,r){return e=typeof e=="function"?e(Object.assign({},r.rects,{placement:r.placement})):e,Qe(typeof e!="number"?e:Ze(e,ye))};function vn(t){var e,r=t.state,n=t.name,i=t.options,a=r.elements.arrow,p=r.modifiersData.popperOffsets,f=I(r.placement),u=Te(f),c=[W,k].indexOf(f)>=0,l=c?"height":"width";if(!(!a||!p)){var y=mn(i.padding,r),x=Pe(a),b=u==="y"?V:W,g=u==="y"?H:k,h=r.rects.reference[l]+r.rects.reference[u]-p[u]-r.rects.popper[l],w=p[u]-r.rects.reference[u],O=re(a),E=O?u==="y"?O.clientHeight||0:O.clientWidth||0:0,N=h/2-w/2,o=y[b],T=E-x[l]-y[g],d=E/2-x[l]/2+N,D=Ce(o,d,T),j=u;r.modifiersData[n]=(e={},e[j]=D,e.centerOffset=D-d,e)}}function gn(t){var e=t.state,r=t.options,n=r.element,i=n===void 0?"[data-popper-arrow]":n;if(i!=null&&!(typeof i=="string"&&(i=e.elements.popper.querySelector(i),!i))){if(_(i)||console.error(['Popper: "arrow" element must be an HTMLElement (not an SVGElement).',"To use an SVG arrow, wrap it in an HTMLElement that will be used as","the arrow."].join(" ")),!Ge(e.elements.popper,i)){console.error(['Popper: "arrow" modifier\'s `element` must be a child of the popper',"element."].join(" "));return}e.elements.arrow=i}}var rr={name:"arrow",enabled:!0,phase:"main",fn:vn,effect:gn,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};var hn={top:"auto",right:"auto",bottom:"auto",left:"auto"};function yn(t){var e=t.x,r=t.y,n=window,i=n.devicePixelRatio||1;return{x:Ke(Ke(e*i)/i)||0,y:Ke(Ke(r*i)/i)||0}}function nr(t){var e,r=t.popper,n=t.popperRect,i=t.placement,a=t.offsets,p=t.position,f=t.gpuAcceleration,u=t.adaptive,c=t.roundOffsets,l=c===!0?yn(a):typeof c=="function"?c(a):a,y=l.x,x=y===void 0?0:y,b=l.y,g=b===void 0?0:b,h=a.hasOwnProperty("x"),w=a.hasOwnProperty("y"),O=W,E=V,N=window;if(u){var o=re(r),T="clientHeight",d="clientWidth";o===B(r)&&(o=U(r),$(o).position!=="static"&&(T="scrollHeight",d="scrollWidth")),o=o,i===V&&(E=H,g-=o[T]-n.height,g*=f?1:-1),i===W&&(O=k,x-=o[d]-n.width,x*=f?1:-1)}var D=Object.assign({position:p},u&&hn);if(f){var j;return Object.assign({},D,(j={},j[E]=w?"0":"",j[O]=h?"0":"",j.transform=(N.devicePixelRatio||1)<2?"translate("+x+"px, "+g+"px)":"translate3d("+x+"px, "+g+"px, 0)",j))}return Object.assign({},D,(e={},e[E]=w?g+"px":"",e[O]=h?x+"px":"",e.transform="",e))}function bn(t){var e=t.state,r=t.options,n=r.gpuAcceleration,i=n===void 0?!0:n,a=r.adaptive,p=a===void 0?!0:a,f=r.roundOffsets,u=f===void 0?!0:f,c=$(e.elements.popper).transitionProperty||"";p&&["transform","top","right","bottom","left"].some(function(y){return c.indexOf(y)>=0})&&console.warn(["Popper: Detected CSS transitions on at least one of the following",'CSS properties: "transform", "top", "right", "bottom", "left".',`

`,'Disable the "computeStyles" modifier\'s `adaptive` option to allow',"for smooth transitions, or remove these properties from the CSS","transition declaration on the popper element if only transitioning","opacity or background-color for example.",`

`,"We recommend using the popper element as a wrapper around an inner","element that can have any CSS property transitioned for animations."].join(" "));var l={placement:I(e.placement),popper:e.elements.popper,popperRect:e.rects.popper,gpuAcceleration:i};e.modifiersData.popperOffsets!=null&&(e.styles.popper=Object.assign({},e.styles.popper,nr(Object.assign({},l,{offsets:e.modifiersData.popperOffsets,position:e.options.strategy,adaptive:p,roundOffsets:u})))),e.modifiersData.arrow!=null&&(e.styles.arrow=Object.assign({},e.styles.arrow,nr(Object.assign({},l,{offsets:e.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:u})))),e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-placement":e.placement})}var or={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:bn,data:{}};var gt={passive:!0};function wn(t){var e=t.state,r=t.instance,n=t.options,i=n.scroll,a=i===void 0?!0:i,p=n.resize,f=p===void 0?!0:p,u=B(e.elements.popper),c=[].concat(e.scrollParents.reference,e.scrollParents.popper);return a&&c.forEach(function(l){l.addEventListener("scroll",r.update,gt)}),f&&u.addEventListener("resize",r.update,gt),function(){a&&c.forEach(function(l){l.removeEventListener("scroll",r.update,gt)}),f&&u.removeEventListener("resize",r.update,gt)}}var ir={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:wn,data:{}};var On={left:"right",right:"left",bottom:"top",top:"bottom"};function ke(t){return t.replace(/left|right|bottom|top/g,function(e){return On[e]})}var xn={start:"end",end:"start"};function ht(t){return t.replace(/start|end/g,function(e){return xn[e]})}function Se(t){var e=B(t),r=e.pageXOffset,n=e.pageYOffset;return{scrollLeft:r,scrollTop:n}}function De(t){return Q(U(t)).left+Se(t).scrollLeft}function St(t){var e=B(t),r=U(t),n=e.visualViewport,i=r.clientWidth,a=r.clientHeight,p=0,f=0;return n&&(i=n.width,a=n.height,/^((?!chrome|android).)*safari/i.test(navigator.userAgent)||(p=n.offsetLeft,f=n.offsetTop)),{width:i,height:a,x:p+De(t),y:f}}function Dt(t){var e,r=U(t),n=Se(t),i=(e=t.ownerDocument)==null?void 0:e.body,a=Z(r.scrollWidth,r.clientWidth,i?i.scrollWidth:0,i?i.clientWidth:0),p=Z(r.scrollHeight,r.clientHeight,i?i.scrollHeight:0,i?i.clientHeight:0),f=-n.scrollLeft+De(t),u=-n.scrollTop;return $(i||r).direction==="rtl"&&(f+=Z(r.clientWidth,i?i.clientWidth:0)-a),{width:a,height:p,x:f,y:u}}function Ae(t){var e=$(t),r=e.overflow,n=e.overflowX,i=e.overflowY;return/auto|scroll|overlay|hidden/.test(r+i+n)}function yt(t){return["html","body","#document"].indexOf(F(t))>=0?t.ownerDocument.body:_(t)&&Ae(t)?t:yt(me(t))}function we(t,e){var r;e===void 0&&(e=[]);var n=yt(t),i=n===((r=t.ownerDocument)==null?void 0:r.body),a=B(n),p=i?[a].concat(a.visualViewport||[],Ae(n)?n:[]):n,f=e.concat(p);return i?f:f.concat(we(me(p)))}function We(t){return Object.assign({},t,{left:t.x,top:t.y,right:t.x+t.width,bottom:t.y+t.height})}function En(t){var e=Q(t);return e.top=e.top+t.clientTop,e.left=e.left+t.clientLeft,e.bottom=e.top+t.clientHeight,e.right=e.left+t.clientWidth,e.width=t.clientWidth,e.height=t.clientHeight,e.x=e.left,e.y=e.top,e}function ar(t,e){return e===ct?We(St(t)):_(e)?En(e):We(Dt(U(t)))}function jn(t){var e=we(me(t)),r=["absolute","fixed"].indexOf($(t).position)>=0,n=r&&_(t)?re(t):t;return de(n)?e.filter(function(i){return de(i)&&Ge(i,n)&&F(i)!=="body"}):[]}function At(t,e,r){var n=e==="clippingParents"?jn(t):[].concat(e),i=[].concat(n,[r]),a=i[0],p=i.reduce(function(f,u){var c=ar(t,u);return f.top=Z(c.top,f.top),f.right=be(c.right,f.right),f.bottom=be(c.bottom,f.bottom),f.left=Z(c.left,f.left),f},ar(t,a));return p.width=p.right-p.left,p.height=p.bottom-p.top,p.x=p.left,p.y=p.top,p}function ae(t){return t.split("-")[1]}function et(t){var e=t.reference,r=t.element,n=t.placement,i=n?I(n):null,a=n?ae(n):null,p=e.x+e.width/2-r.width/2,f=e.y+e.height/2-r.height/2,u;switch(i){case V:u={x:p,y:e.y-r.height};break;case H:u={x:p,y:e.y+e.height};break;case k:u={x:e.x+e.width,y:f};break;case W:u={x:e.x-r.width,y:f};break;default:u={x:e.x,y:e.y}}var c=i?Te(i):null;if(c!=null){var l=c==="y"?"height":"width";switch(a){case ce:u[c]=u[c]-(e[l]/2-r[l]/2);break;case lt:u[c]=u[c]+(e[l]/2-r[l]/2);break;default:}}return u}function ne(t,e){e===void 0&&(e={});var r=e,n=r.placement,i=n===void 0?t.placement:n,a=r.boundary,p=a===void 0?Zt:a,f=r.rootBoundary,u=f===void 0?ct:f,c=r.elementContext,l=c===void 0?Ie:c,y=r.altBoundary,x=y===void 0?!1:y,b=r.padding,g=b===void 0?0:b,h=Qe(typeof g!="number"?g:Ze(g,ye)),w=l===Ie?er:Ie,O=t.elements.reference,E=t.rects.popper,N=t.elements[x?w:l],o=At(de(N)?N:N.contextElement||U(t.elements.popper),p,u),T=Q(O),d=et({reference:T,element:E,strategy:"absolute",placement:i}),D=We(Object.assign({},E,d)),j=l===Ie?D:T,M={top:o.top-j.top+h.top,bottom:j.bottom-o.bottom+h.bottom,left:o.left-j.left+h.left,right:j.right-o.right+h.right},S=t.modifiersData.offset;if(l===Ie&&S){var R=S[i];Object.keys(M).forEach(function(L){var A=[k,H].indexOf(L)>=0?1:-1,q=[V,H].indexOf(L)>=0?"y":"x";M[L]+=R[q]*A})}return M}function Nt(t,e){e===void 0&&(e={});var r=e,n=r.placement,i=r.boundary,a=r.rootBoundary,p=r.padding,f=r.flipVariations,u=r.allowedAutoPlacements,c=u===void 0?dt:u,l=ae(n),y=l?f?Tt:Tt.filter(function(g){return ae(g)===l}):ye,x=y.filter(function(g){return c.indexOf(g)>=0});x.length===0&&(x=y,console.error(["Popper: The `allowedAutoPlacements` option did not allow any","placements. Ensure the `placement` option matches the variation","of the allowed placements.",'For example, "auto" cannot be used to allow "bottom-start".','Use "auto-start" instead.'].join(" ")));var b=x.reduce(function(g,h){return g[h]=ne(t,{placement:h,boundary:i,rootBoundary:a,padding:p})[I(h)],g},{});return Object.keys(b).sort(function(g,h){return b[g]-b[h]})}function Pn(t){if(I(t)===Ve)return[];var e=ke(t);return[ht(t),e,ht(e)]}function Tn(t){var e=t.state,r=t.options,n=t.name;if(!e.modifiersData[n]._skip){for(var i=r.mainAxis,a=i===void 0?!0:i,p=r.altAxis,f=p===void 0?!0:p,u=r.fallbackPlacements,c=r.padding,l=r.boundary,y=r.rootBoundary,x=r.altBoundary,b=r.flipVariations,g=b===void 0?!0:b,h=r.allowedAutoPlacements,w=e.options.placement,O=I(w),E=O===w,N=u||(E||!g?[ke(w)]:Pn(w)),o=[w].concat(N).reduce(function(ie,z){return ie.concat(I(z)===Ve?Nt(e,{placement:z,boundary:l,rootBoundary:y,padding:c,flipVariations:g,allowedAutoPlacements:h}):z)},[]),T=e.rects.reference,d=e.rects.popper,D=new Map,j=!0,M=o[0],S=0;S<o.length;S++){var R=o[S],L=I(R),A=ae(R)===ce,q=[V,H].indexOf(L)>=0,ee=q?"width":"height",fe=ne(e,{placement:R,boundary:l,rootBoundary:y,altBoundary:x,padding:c}),K=q?A?k:W:A?H:V;T[ee]>d[ee]&&(K=ke(K));var X=ke(K),ue=[];if(a&&ue.push(fe[L]<=0),f&&ue.push(fe[K]<=0,fe[X]<=0),ue.every(function(ie){return ie})){M=R,j=!1;break}D.set(R,ue)}if(j)for(var oe=g?3:1,Oe=function(z){var ge=o.find(function(He){var he=D.get(He);if(he)return he.slice(0,z).every(function(Re){return Re})});if(ge)return M=ge,"break"},te=oe;te>0;te--){var xe=Oe(te);if(xe==="break")break}e.placement!==M&&(e.modifiersData[n]._skip=!0,e.placement=M,e.reset=!0)}}var sr={name:"flip",enabled:!0,phase:"main",fn:Tn,requiresIfExists:["offset"],data:{_skip:!1}};function pr(t,e,r){return r===void 0&&(r={x:0,y:0}),{top:t.top-e.height-r.y,right:t.right-e.width+r.x,bottom:t.bottom-e.height+r.y,left:t.left-e.width-r.x}}function fr(t){return[V,k,H,W].some(function(e){return t[e]>=0})}function Cn(t){var e=t.state,r=t.name,n=e.rects.reference,i=e.rects.popper,a=e.modifiersData.preventOverflow,p=ne(e,{elementContext:"reference"}),f=ne(e,{altBoundary:!0}),u=pr(p,n),c=pr(f,i,a),l=fr(u),y=fr(c);e.modifiersData[r]={referenceClippingOffsets:u,popperEscapeOffsets:c,isReferenceHidden:l,hasPopperEscaped:y},e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-reference-hidden":l,"data-popper-escaped":y})}var ur={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:Cn};function Sn(t,e,r){var n=I(t),i=[W,V].indexOf(n)>=0?-1:1,a=typeof r=="function"?r(Object.assign({},e,{placement:t})):r,p=a[0],f=a[1];return p=p||0,f=(f||0)*i,[W,k].indexOf(n)>=0?{x:f,y:p}:{x:p,y:f}}function Dn(t){var e=t.state,r=t.options,n=t.name,i=r.offset,a=i===void 0?[0,0]:i,p=dt.reduce(function(l,y){return l[y]=Sn(y,e.rects,a),l},{}),f=p[e.placement],u=f.x,c=f.y;e.modifiersData.popperOffsets!=null&&(e.modifiersData.popperOffsets.x+=u,e.modifiersData.popperOffsets.y+=c),e.modifiersData[n]=p}var lr={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:Dn};function An(t){var e=t.state,r=t.name;e.modifiersData[r]=et({reference:e.rects.reference,element:e.rects.popper,strategy:"absolute",placement:e.placement})}var cr={name:"popperOffsets",enabled:!0,phase:"read",fn:An,data:{}};function Mt(t){return t==="x"?"y":"x"}function Nn(t){var e=t.state,r=t.options,n=t.name,i=r.mainAxis,a=i===void 0?!0:i,p=r.altAxis,f=p===void 0?!1:p,u=r.boundary,c=r.rootBoundary,l=r.altBoundary,y=r.padding,x=r.tether,b=x===void 0?!0:x,g=r.tetherOffset,h=g===void 0?0:g,w=ne(e,{boundary:u,rootBoundary:c,padding:y,altBoundary:l}),O=I(e.placement),E=ae(e.placement),N=!E,o=Te(O),T=Mt(o),d=e.modifiersData.popperOffsets,D=e.rects.reference,j=e.rects.popper,M=typeof h=="function"?h(Object.assign({},e.rects,{placement:e.placement})):h,S={x:0,y:0};if(!!d){if(a||f){var R=o==="y"?V:W,L=o==="y"?H:k,A=o==="y"?"height":"width",q=d[o],ee=d[o]+w[R],fe=d[o]-w[L],K=b?-j[A]/2:0,X=E===ce?D[A]:j[A],ue=E===ce?-j[A]:-D[A],oe=e.elements.arrow,Oe=b&&oe?Pe(oe):{width:0,height:0},te=e.modifiersData["arrow#persistent"]?e.modifiersData["arrow#persistent"].padding:Je(),xe=te[R],ie=te[L],z=Ce(0,D[A],Oe[A]),ge=N?D[A]/2-K-z-xe-M:X-z-xe-M,He=N?-D[A]/2+K+z+ie+M:ue+z+ie+M,he=e.elements.arrow&&re(e.elements.arrow),Re=he?o==="y"?he.clientTop||0:he.clientLeft||0:0,le=e.modifiersData.offset?e.modifiersData.offset[e.placement][o]:0,Fe=d[o]+ge-le-Re,$e=d[o]+He-le;if(a){var Ue=Ce(b?be(ee,Fe):ee,q,b?Z(fe,$e):fe);d[o]=Ue,S[o]=Ue-q}if(f){var at=o==="x"?V:W,st=o==="x"?H:k,Ee=d[T],qe=Ee+w[at],Xe=Ee-w[st],Ye=Ce(b?be(qe,Fe):qe,Ee,b?Z(Xe,$e):Xe);d[T]=Ye,S[T]=Ye-Ee}}e.modifiersData[n]=S}}var dr={name:"preventOverflow",enabled:!0,phase:"main",fn:Nn,requiresIfExists:["offset"]};function Rt(t){return{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}}function Lt(t){return t===B(t)||!_(t)?Se(t):Rt(t)}function Bt(t,e,r){r===void 0&&(r=!1);var n=U(e),i=Q(t),a=_(e),p={scrollLeft:0,scrollTop:0},f={x:0,y:0};return(a||!a&&!r)&&((F(e)!=="body"||Ae(n))&&(p=Lt(e)),_(e)?(f=Q(e),f.x+=e.clientLeft,f.y+=e.clientTop):n&&(f.x=De(n))),{x:i.left+p.scrollLeft-f.x,y:i.top+p.scrollTop-f.y,width:i.width,height:i.height}}function Mn(t){var e=new Map,r=new Set,n=[];t.forEach(function(a){e.set(a.name,a)});function i(a){r.add(a.name);var p=[].concat(a.requires||[],a.requiresIfExists||[]);p.forEach(function(f){if(!r.has(f)){var u=e.get(f);u&&i(u)}}),n.push(a)}return t.forEach(function(a){r.has(a.name)||i(a)}),n}function Vt(t){var e=Mn(t);return ze.reduce(function(r,n){return r.concat(e.filter(function(i){return i.phase===n}))},[])}function It(t){var e;return function(){return e||(e=new Promise(function(r){Promise.resolve().then(function(){e=void 0,r(t())})})),e}}function se(t){for(var e=arguments.length,r=new Array(e>1?e-1:0),n=1;n<e;n++)r[n-1]=arguments[n];return[].concat(r).reduce(function(i,a){return i.replace(/%s/,a)},t)}var Ne='Popper: modifier "%s" provided an invalid %s property, expected %s but got %s',Rn='Popper: modifier "%s" requires "%s", but "%s" modifier is not available',Ln=["name","enabled","phase","fn","effect","requires","options"];function kt(t){t.forEach(function(e){Object.keys(e).forEach(function(r){switch(r){case"name":typeof e.name!="string"&&console.error(se(Ne,String(e.name),'"name"','"string"','"'+String(e.name)+'"'));break;case"enabled":typeof e.enabled!="boolean"&&console.error(se(Ne,e.name,'"enabled"','"boolean"','"'+String(e.enabled)+'"'));case"phase":ze.indexOf(e.phase)<0&&console.error(se(Ne,e.name,'"phase"',"either "+ze.join(", "),'"'+String(e.phase)+'"'));break;case"fn":typeof e.fn!="function"&&console.error(se(Ne,e.name,'"fn"','"function"','"'+String(e.fn)+'"'));break;case"effect":typeof e.effect!="function"&&console.error(se(Ne,e.name,'"effect"','"function"','"'+String(e.fn)+'"'));break;case"requires":Array.isArray(e.requires)||console.error(se(Ne,e.name,'"requires"','"array"','"'+String(e.requires)+'"'));break;case"requiresIfExists":Array.isArray(e.requiresIfExists)||console.error(se(Ne,e.name,'"requiresIfExists"','"array"','"'+String(e.requiresIfExists)+'"'));break;case"options":case"data":break;default:console.error('PopperJS: an invalid property has been provided to the "'+e.name+'" modifier, valid properties are '+Ln.map(function(n){return'"'+n+'"'}).join(", ")+'; but "'+r+'" was provided.')}e.requires&&e.requires.forEach(function(n){t.find(function(i){return i.name===n})==null&&console.error(se(Rn,String(e.name),n,n))})})})}function Wt(t,e){var r=new Set;return t.filter(function(n){var i=e(n);if(!r.has(i))return r.add(i),!0})}function _t(t){var e=t.reduce(function(r,n){var i=r[n.name];return r[n.name]=i?Object.assign({},i,n,{options:Object.assign({},i.options,n.options),data:Object.assign({},i.data,n.data)}):n,r},{});return Object.keys(e).map(function(r){return e[r]})}var mr="Popper: Invalid reference or popper argument provided. They must be either a DOM element or virtual element.",Bn="Popper: An infinite loop in the modifiers cycle has been detected! The cycle has been interrupted to prevent a browser crash.",vr={placement:"bottom",modifiers:[],strategy:"absolute"};function gr(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];return!e.some(function(n){return!(n&&typeof n.getBoundingClientRect=="function")})}function hr(t){t===void 0&&(t={});var e=t,r=e.defaultModifiers,n=r===void 0?[]:r,i=e.defaultOptions,a=i===void 0?vr:i;return function(f,u,c){c===void 0&&(c=a);var l={placement:"bottom",orderedModifiers:[],options:Object.assign({},vr,a),modifiersData:{},elements:{reference:f,popper:u},attributes:{},styles:{}},y=[],x=!1,b={state:l,setOptions:function(O){h(),l.options=Object.assign({},a,l.options,O),l.scrollParents={reference:de(f)?we(f):f.contextElement?we(f.contextElement):[],popper:we(u)};var E=Vt(_t([].concat(n,l.options.modifiers)));l.orderedModifiers=E.filter(function(S){return S.enabled});var N=Wt([].concat(E,l.options.modifiers),function(S){var R=S.name;return R});if(kt(N),I(l.options.placement)===Ve){var o=l.orderedModifiers.find(function(S){var R=S.name;return R==="flip"});o||console.error(['Popper: "auto" placements require the "flip" modifier be',"present and enabled to work."].join(" "))}var T=$(u),d=T.marginTop,D=T.marginRight,j=T.marginBottom,M=T.marginLeft;return[d,D,j,M].some(function(S){return parseFloat(S)})&&console.warn(['Popper: CSS "margin" styles cannot be used to apply padding',"between the popper and its reference element or boundary.","To replicate margin, use the `offset` modifier, as well as","the `padding` option in the `preventOverflow` and `flip`","modifiers."].join(" ")),g(),b.update()},forceUpdate:function(){if(!x){var O=l.elements,E=O.reference,N=O.popper;if(!gr(E,N)){console.error(mr);return}l.rects={reference:Bt(E,re(N),l.options.strategy==="fixed"),popper:Pe(N)},l.reset=!1,l.placement=l.options.placement,l.orderedModifiers.forEach(function(R){return l.modifiersData[R.name]=Object.assign({},R.data)});for(var o=0,T=0;T<l.orderedModifiers.length;T++){if(o+=1,o>100){console.error(Bn);break}if(l.reset===!0){l.reset=!1,T=-1;continue}var d=l.orderedModifiers[T],D=d.fn,j=d.options,M=j===void 0?{}:j,S=d.name;typeof D=="function"&&(l=D({state:l,options:M,name:S,instance:b})||l)}}},update:It(function(){return new Promise(function(w){b.forceUpdate(),w(l)})}),destroy:function(){h(),x=!0}};if(!gr(f,u))return console.error(mr),b;b.setOptions(c).then(function(w){!x&&c.onFirstUpdate&&c.onFirstUpdate(w)});function g(){l.orderedModifiers.forEach(function(w){var O=w.name,E=w.options,N=E===void 0?{}:E,o=w.effect;if(typeof o=="function"){var T=o({state:l,name:O,instance:b,options:N}),d=function(){};y.push(T||d)}})}function h(){y.forEach(function(w){return w()}),y=[]}return b}}var Vn=[ir,cr,or,vt,lr,sr,dr,rr,ur],yr=hr({defaultModifiers:Vn});var In="tippy-box",br="tippy-content",kn="tippy-backdrop",wr="tippy-arrow",Or="tippy-svg-arrow",Me={passive:!0,capture:!0};function Wn(t,e){return{}.hasOwnProperty.call(t,e)}function Ht(t,e,r){if(Array.isArray(t)){var n=t[e];return n??(Array.isArray(r)?r[e]:r)}return t}function Ft(t,e){var r={}.toString.call(t);return r.indexOf("[object")===0&&r.indexOf(e+"]")>-1}function xr(t,e){return typeof t=="function"?t.apply(void 0,e):t}function Er(t,e){if(e===0)return t;var r;return function(n){clearTimeout(r),r=setTimeout(function(){t(n)},e)}}function _n(t,e){var r=Object.assign({},t);return e.forEach(function(n){delete r[n]}),r}function Hn(t){return t.split(/\s+/).filter(Boolean)}function tt(t){return[].concat(t)}function jr(t,e){t.indexOf(e)===-1&&t.push(e)}function Fn(t){return t.filter(function(e,r){return t.indexOf(e)===r})}function $n(t){return t.split("-")[0]}function bt(t){return[].slice.call(t)}function Un(t){return Object.keys(t).reduce(function(e,r){return t[r]!==void 0&&(e[r]=t[r]),e},{})}function rt(){return document.createElement("div")}function nt(t){return["Element","Fragment"].some(function(e){return Ft(t,e)})}function qn(t){return Ft(t,"NodeList")}function Pr(t){return Ft(t,"MouseEvent")}function Xn(t){return!!(t&&t._tippy&&t._tippy.reference===t)}function Yn(t){return nt(t)?[t]:qn(t)?bt(t):Array.isArray(t)?t:bt(document.querySelectorAll(t))}function $t(t,e){t.forEach(function(r){r&&(r.style.transitionDuration=e+"ms")})}function Tr(t,e){t.forEach(function(r){r&&r.setAttribute("data-state",e)})}function Cr(t){var e,r=tt(t),n=r[0];return(n==null||(e=n.ownerDocument)==null?void 0:e.body)?n.ownerDocument:document}function zn(t,e){var r=e.clientX,n=e.clientY;return t.every(function(i){var a=i.popperRect,p=i.popperState,f=i.props,u=f.interactiveBorder,c=$n(p.placement),l=p.modifiersData.offset;if(!l)return!0;var y=c==="bottom"?l.top.y:0,x=c==="top"?l.bottom.y:0,b=c==="right"?l.left.x:0,g=c==="left"?l.right.x:0,h=a.top-n+y>u,w=n-a.bottom-x>u,O=a.left-r+b>u,E=r-a.right-g>u;return h||w||O||E})}function Ut(t,e,r){var n=e+"EventListener";["transitionend","webkitTransitionEnd"].forEach(function(i){t[n](i,r)})}var pe={isTouch:!1},Sr=0;function Gn(){pe.isTouch||(pe.isTouch=!0,window.performance&&document.addEventListener("mousemove",Dr))}function Dr(){var t=performance.now();t-Sr<20&&(pe.isTouch=!1,document.removeEventListener("mousemove",Dr)),Sr=t}function Kn(){var t=document.activeElement;if(Xn(t)){var e=t._tippy;t.blur&&!e.state.isVisible&&t.blur()}}function Jn(){document.addEventListener("touchstart",Gn,Me),window.addEventListener("blur",Kn)}var Qn=typeof window!="undefined"&&typeof document!="undefined",Zn=Qn?navigator.userAgent:"",eo=/MSIE |Trident\//.test(Zn);function _e(t){var e=t==="destroy"?"n already-":" ";return[t+"() was called on a"+e+"destroyed instance. This is a no-op but","indicates a potential memory leak."].join(" ")}function Ar(t){var e=/[ \t]{2,}/g,r=/^[ \t]*/gm;return t.replace(e," ").replace(r,"").trim()}function to(t){return Ar(`
  %ctippy.js

  %c`+Ar(t)+`

  %c\u{1F477}\u200D This is a development-only message. It will be removed in production.
  `)}function Nr(t){return[to(t),"color: #00C584; font-size: 1.3em; font-weight: bold;","line-height: 1.5","color: #a6a095;"]}var ot;ro();function ro(){ot=new Set}function ve(t,e){if(t&&!ot.has(e)){var r;ot.add(e),(r=console).warn.apply(r,Nr(e))}}function qt(t,e){if(t&&!ot.has(e)){var r;ot.add(e),(r=console).error.apply(r,Nr(e))}}function no(t){var e=!t,r=Object.prototype.toString.call(t)==="[object Object]"&&!t.addEventListener;qt(e,["tippy() was passed","`"+String(t)+"`","as its targets (first) argument. Valid types are: String, Element,","Element[], or NodeList."].join(" ")),qt(r,["tippy() was passed a plain object which is not supported as an argument","for virtual positioning. Use props.getReferenceClientRect instead."].join(" "))}var Mr={animateFill:!1,followCursor:!1,inlinePositioning:!1,sticky:!1},oo={allowHTML:!1,animation:"fade",arrow:!0,content:"",inertia:!1,maxWidth:350,role:"tooltip",theme:"",zIndex:9999},G=Object.assign({appendTo:function(){return document.body},aria:{content:"auto",expanded:"auto"},delay:0,duration:[300,250],getReferenceClientRect:null,hideOnClick:!0,ignoreAttributes:!1,interactive:!1,interactiveBorder:2,interactiveDebounce:0,moveTransition:"",offset:[0,10],onAfterUpdate:function(){},onBeforeUpdate:function(){},onCreate:function(){},onDestroy:function(){},onHidden:function(){},onHide:function(){},onMount:function(){},onShow:function(){},onShown:function(){},onTrigger:function(){},onUntrigger:function(){},onClickOutside:function(){},placement:"top",plugins:[],popperOptions:{},render:null,showOnCreate:!1,touch:!0,trigger:"mouseenter focus",triggerTarget:null},Mr,{},oo),io=Object.keys(G),ao=function(e){Rr(e,[]);var r=Object.keys(e);r.forEach(function(n){G[n]=e[n]})};function Lr(t){var e=t.plugins||[],r=e.reduce(function(n,i){var a=i.name,p=i.defaultValue;return a&&(n[a]=t[a]!==void 0?t[a]:p),n},{});return Object.assign({},t,{},r)}function so(t,e){var r=e?Object.keys(Lr(Object.assign({},G,{plugins:e}))):io,n=r.reduce(function(i,a){var p=(t.getAttribute("data-tippy-"+a)||"").trim();if(!p)return i;if(a==="content")i[a]=p;else try{i[a]=JSON.parse(p)}catch(f){i[a]=p}return i},{});return n}function Br(t,e){var r=Object.assign({},e,{content:xr(e.content,[t])},e.ignoreAttributes?{}:so(t,e.plugins));return r.aria=Object.assign({},G.aria,{},r.aria),r.aria={expanded:r.aria.expanded==="auto"?e.interactive:r.aria.expanded,content:r.aria.content==="auto"?e.interactive?null:"describedby":r.aria.content},r}function Rr(t,e){t===void 0&&(t={}),e===void 0&&(e=[]);var r=Object.keys(t);r.forEach(function(n){var i=_n(G,Object.keys(Mr)),a=!Wn(i,n);a&&(a=e.filter(function(p){return p.name===n}).length===0),ve(a,["`"+n+"`","is not a valid prop. You may have spelled it incorrectly, or if it's","a plugin, forgot to pass it in an array as props.plugins.",`

`,`All props: https://atomiks.github.io/tippyjs/v6/all-props/
`,"Plugins: https://atomiks.github.io/tippyjs/v6/plugins/"].join(" "))})}var po=function(){return"innerHTML"};function Xt(t,e){t[po()]=e}function Vr(t){var e=rt();return t===!0?e.className=wr:(e.className=Or,nt(t)?e.appendChild(t):Xt(e,t)),e}function Ir(t,e){nt(e.content)?(Xt(t,""),t.appendChild(e.content)):typeof e.content!="function"&&(e.allowHTML?Xt(t,e.content):t.textContent=e.content)}function Yt(t){var e=t.firstElementChild,r=bt(e.children);return{box:e,content:r.find(function(n){return n.classList.contains(br)}),arrow:r.find(function(n){return n.classList.contains(wr)||n.classList.contains(Or)}),backdrop:r.find(function(n){return n.classList.contains(kn)})}}function kr(t){var e=rt(),r=rt();r.className=In,r.setAttribute("data-state","hidden"),r.setAttribute("tabindex","-1");var n=rt();n.className=br,n.setAttribute("data-state","hidden"),Ir(n,t.props),e.appendChild(r),r.appendChild(n),i(t.props,t.props);function i(a,p){var f=Yt(e),u=f.box,c=f.content,l=f.arrow;p.theme?u.setAttribute("data-theme",p.theme):u.removeAttribute("data-theme"),typeof p.animation=="string"?u.setAttribute("data-animation",p.animation):u.removeAttribute("data-animation"),p.inertia?u.setAttribute("data-inertia",""):u.removeAttribute("data-inertia"),u.style.maxWidth=typeof p.maxWidth=="number"?p.maxWidth+"px":p.maxWidth,p.role?u.setAttribute("role",p.role):u.removeAttribute("role"),(a.content!==p.content||a.allowHTML!==p.allowHTML)&&Ir(c,t.props),p.arrow?l?a.arrow!==p.arrow&&(u.removeChild(l),u.appendChild(Vr(p.arrow))):u.appendChild(Vr(p.arrow)):l&&u.removeChild(l)}return{popper:e,onUpdate:i}}kr.$$tippy=!0;var fo=1,wt=[],zt=[];function uo(t,e){var r=Br(t,Object.assign({},G,{},Lr(Un(e)))),n,i,a,p=!1,f=!1,u=!1,c=!1,l,y,x,b=[],g=Er(at,r.interactiveDebounce),h,w=fo++,O=null,E=Fn(r.plugins),N={isEnabled:!0,isVisible:!1,isDestroyed:!1,isMounted:!1,isShown:!1},o={id:w,reference:t,popper:rt(),popperInstance:O,props:r,state:N,plugins:E,clearDelayTimeouts:Xr,setProps:Yr,setContent:zr,show:Gr,hide:Kr,hideWithInteractivity:Jr,enable:Ur,disable:qr,unmount:Qr,destroy:Zr};if(!r.render)return qt(!0,"render() function has not been supplied."),o;var T=r.render(o),d=T.popper,D=T.onUpdate;d.setAttribute("data-tippy-root",""),d.id="tippy-"+o.id,o.popper=d,t._tippy=o,d._tippy=o;var j=E.map(function(s){return s.fn(o)}),M=t.hasAttribute("aria-expanded");return Fe(),oe(),K(),X("onCreate",[o]),r.showOnCreate&&Jt(),d.addEventListener("mouseenter",function(){o.props.interactive&&o.state.isVisible&&o.clearDelayTimeouts()}),d.addEventListener("mouseleave",function(s){o.props.interactive&&o.props.trigger.indexOf("mouseenter")>=0&&(q().addEventListener("mousemove",g),g(s))}),o;function S(){var s=o.props.touch;return Array.isArray(s)?s:[s,0]}function R(){return S()[0]==="hold"}function L(){var s;return!!((s=o.props.render)==null?void 0:s.$$tippy)}function A(){return h||t}function q(){var s=A().parentNode;return s?Cr(s):document}function ee(){return Yt(d)}function fe(s){return o.state.isMounted&&!o.state.isVisible||pe.isTouch||l&&l.type==="focus"?0:Ht(o.props.delay,s?0:1,G.delay)}function K(){d.style.pointerEvents=o.props.interactive&&o.state.isVisible?"":"none",d.style.zIndex=""+o.props.zIndex}function X(s,m,v){if(v===void 0&&(v=!0),j.forEach(function(P){P[s]&&P[s].apply(void 0,m)}),v){var C;(C=o.props)[s].apply(C,m)}}function ue(){var s=o.props.aria;if(!!s.content){var m="aria-"+s.content,v=d.id,C=tt(o.props.triggerTarget||t);C.forEach(function(P){var Y=P.getAttribute(m);if(o.state.isVisible)P.setAttribute(m,Y?Y+" "+v:v);else{var J=Y&&Y.replace(v,"").trim();J?P.setAttribute(m,J):P.removeAttribute(m)}})}}function oe(){if(!(M||!o.props.aria.expanded)){var s=tt(o.props.triggerTarget||t);s.forEach(function(m){o.props.interactive?m.setAttribute("aria-expanded",o.state.isVisible&&m===A()?"true":"false"):m.removeAttribute("aria-expanded")})}}function Oe(){q().removeEventListener("mousemove",g),wt=wt.filter(function(s){return s!==g})}function te(s){if(!(pe.isTouch&&(u||s.type==="mousedown"))&&!(o.props.interactive&&d.contains(s.target))){if(A().contains(s.target)){if(pe.isTouch||o.state.isVisible&&o.props.trigger.indexOf("click")>=0)return}else X("onClickOutside",[o,s]);o.props.hideOnClick===!0&&(o.clearDelayTimeouts(),o.hide(),f=!0,setTimeout(function(){f=!1}),o.state.isMounted||ge())}}function xe(){u=!0}function ie(){u=!1}function z(){var s=q();s.addEventListener("mousedown",te,!0),s.addEventListener("touchend",te,Me),s.addEventListener("touchstart",ie,Me),s.addEventListener("touchmove",xe,Me)}function ge(){var s=q();s.removeEventListener("mousedown",te,!0),s.removeEventListener("touchend",te,Me),s.removeEventListener("touchstart",ie,Me),s.removeEventListener("touchmove",xe,Me)}function He(s,m){Re(s,function(){!o.state.isVisible&&d.parentNode&&d.parentNode.contains(d)&&m()})}function he(s,m){Re(s,m)}function Re(s,m){var v=ee().box;function C(P){P.target===v&&(Ut(v,"remove",C),m())}if(s===0)return m();Ut(v,"remove",y),Ut(v,"add",C),y=C}function le(s,m,v){v===void 0&&(v=!1);var C=tt(o.props.triggerTarget||t);C.forEach(function(P){P.addEventListener(s,m,v),b.push({node:P,eventType:s,handler:m,options:v})})}function Fe(){R()&&(le("touchstart",Ue,{passive:!0}),le("touchend",st,{passive:!0})),Hn(o.props.trigger).forEach(function(s){if(s!=="manual")switch(le(s,Ue),s){case"mouseenter":le("mouseleave",st);break;case"focus":le(eo?"focusout":"blur",Ee);break;case"focusin":le("focusout",Ee);break}})}function $e(){b.forEach(function(s){var m=s.node,v=s.eventType,C=s.handler,P=s.options;m.removeEventListener(v,C,P)}),b=[]}function Ue(s){var m,v=!1;if(!(!o.state.isEnabled||qe(s)||f)){var C=((m=l)==null?void 0:m.type)==="focus";l=s,h=s.currentTarget,oe(),!o.state.isVisible&&Pr(s)&&wt.forEach(function(P){return P(s)}),s.type==="click"&&(o.props.trigger.indexOf("mouseenter")<0||p)&&o.props.hideOnClick!==!1&&o.state.isVisible?v=!0:Jt(s),s.type==="click"&&(p=!v),v&&!C&&pt(s)}}function at(s){var m=s.target,v=A().contains(m)||d.contains(m);if(!(s.type==="mousemove"&&v)){var C=Et().concat(d).map(function(P){var Y,J=P._tippy,Le=(Y=J.popperInstance)==null?void 0:Y.state;return Le?{popperRect:P.getBoundingClientRect(),popperState:Le,props:r}:null}).filter(Boolean);zn(C,s)&&(Oe(),pt(s))}}function st(s){var m=qe(s)||o.props.trigger.indexOf("click")>=0&&p;if(!m){if(o.props.interactive){o.hideWithInteractivity(s);return}pt(s)}}function Ee(s){o.props.trigger.indexOf("focusin")<0&&s.target!==A()||o.props.interactive&&s.relatedTarget&&d.contains(s.relatedTarget)||pt(s)}function qe(s){return pe.isTouch?R()!==s.type.indexOf("touch")>=0:!1}function Xe(){Ye();var s=o.props,m=s.popperOptions,v=s.placement,C=s.offset,P=s.getReferenceClientRect,Y=s.moveTransition,J=L()?Yt(d).arrow:null,Le=P?{getBoundingClientRect:P,contextElement:P.contextElement||A()}:t,Qt={name:"$$tippy",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:function(ft){var Be=ft.state;if(L()){var en=ee(),Pt=en.box;["placement","reference-hidden","escaped"].forEach(function(ut){ut==="placement"?Pt.setAttribute("data-placement",Be.placement):Be.attributes.popper["data-popper-"+ut]?Pt.setAttribute("data-"+ut,""):Pt.removeAttribute("data-"+ut)}),Be.attributes.popper={}}}},je=[{name:"offset",options:{offset:C}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5}},{name:"computeStyles",options:{adaptive:!Y}},Qt];L()&&J&&je.push({name:"arrow",options:{element:J,padding:3}}),je.push.apply(je,(m==null?void 0:m.modifiers)||[]),o.popperInstance=yr(Le,d,Object.assign({},m,{placement:v,onFirstUpdate:x,modifiers:je}))}function Ye(){o.popperInstance&&(o.popperInstance.destroy(),o.popperInstance=null)}function $r(){var s=o.props.appendTo,m,v=A();o.props.interactive&&s===G.appendTo||s==="parent"?m=v.parentNode:m=xr(s,[v]),m.contains(d)||m.appendChild(d),Xe(),ve(o.props.interactive&&s===G.appendTo&&v.nextElementSibling!==d,["Interactive tippy element may not be accessible via keyboard","navigation because it is not directly after the reference element","in the DOM source order.",`

`,"Using a wrapper <div> or <span> tag around the reference element","solves this by creating a new parentNode context.",`

`,"Specifying `appendTo: document.body` silences this warning, but it","assumes you are using a focus management solution to handle","keyboard navigation.",`

`,"See: https://atomiks.github.io/tippyjs/v6/accessibility/#interactivity"].join(" "))}function Et(){return bt(d.querySelectorAll("[data-tippy-root]"))}function Jt(s){o.clearDelayTimeouts(),s&&X("onTrigger",[o,s]),z();var m=fe(!0),v=S(),C=v[0],P=v[1];pe.isTouch&&C==="hold"&&P&&(m=P),m?n=setTimeout(function(){o.show()},m):o.show()}function pt(s){if(o.clearDelayTimeouts(),X("onUntrigger",[o,s]),!o.state.isVisible){ge();return}if(!(o.props.trigger.indexOf("mouseenter")>=0&&o.props.trigger.indexOf("click")>=0&&["mouseleave","mousemove"].indexOf(s.type)>=0&&p)){var m=fe(!1);m?i=setTimeout(function(){o.state.isVisible&&o.hide()},m):a=requestAnimationFrame(function(){o.hide()})}}function Ur(){o.state.isEnabled=!0}function qr(){o.hide(),o.state.isEnabled=!1}function Xr(){clearTimeout(n),clearTimeout(i),cancelAnimationFrame(a)}function Yr(s){if(ve(o.state.isDestroyed,_e("setProps")),!o.state.isDestroyed){X("onBeforeUpdate",[o,s]),$e();var m=o.props,v=Br(t,Object.assign({},o.props,{},s,{ignoreAttributes:!0}));o.props=v,Fe(),m.interactiveDebounce!==v.interactiveDebounce&&(Oe(),g=Er(at,v.interactiveDebounce)),m.triggerTarget&&!v.triggerTarget?tt(m.triggerTarget).forEach(function(C){C.removeAttribute("aria-expanded")}):v.triggerTarget&&t.removeAttribute("aria-expanded"),oe(),K(),D&&D(m,v),o.popperInstance&&(Xe(),Et().forEach(function(C){requestAnimationFrame(C._tippy.popperInstance.forceUpdate)})),X("onAfterUpdate",[o,s])}}function zr(s){o.setProps({content:s})}function Gr(){ve(o.state.isDestroyed,_e("show"));var s=o.state.isVisible,m=o.state.isDestroyed,v=!o.state.isEnabled,C=pe.isTouch&&!o.props.touch,P=Ht(o.props.duration,0,G.duration);if(!(s||m||v||C)&&!A().hasAttribute("disabled")&&(X("onShow",[o],!1),o.props.onShow(o)!==!1)){if(o.state.isVisible=!0,L()&&(d.style.visibility="visible"),K(),z(),o.state.isMounted||(d.style.transition="none"),L()){var Y=ee(),J=Y.box,Le=Y.content;$t([J,Le],0)}x=function(){var je;if(!(!o.state.isVisible||c)){if(c=!0,d.offsetHeight,d.style.transition=o.props.moveTransition,L()&&o.props.animation){var jt=ee(),ft=jt.box,Be=jt.content;$t([ft,Be],P),Tr([ft,Be],"visible")}ue(),oe(),jr(zt,o),(je=o.popperInstance)==null||je.forceUpdate(),o.state.isMounted=!0,X("onMount",[o]),o.props.animation&&L()&&he(P,function(){o.state.isShown=!0,X("onShown",[o])})}},$r()}}function Kr(){ve(o.state.isDestroyed,_e("hide"));var s=!o.state.isVisible,m=o.state.isDestroyed,v=!o.state.isEnabled,C=Ht(o.props.duration,1,G.duration);if(!(s||m||v)&&(X("onHide",[o],!1),o.props.onHide(o)!==!1)){if(o.state.isVisible=!1,o.state.isShown=!1,c=!1,p=!1,L()&&(d.style.visibility="hidden"),Oe(),ge(),K(),L()){var P=ee(),Y=P.box,J=P.content;o.props.animation&&($t([Y,J],C),Tr([Y,J],"hidden"))}ue(),oe(),o.props.animation?L()&&He(C,o.unmount):o.unmount()}}function Jr(s){ve(o.state.isDestroyed,_e("hideWithInteractivity")),q().addEventListener("mousemove",g),jr(wt,g),g(s)}function Qr(){ve(o.state.isDestroyed,_e("unmount")),o.state.isVisible&&o.hide(),!!o.state.isMounted&&(Ye(),Et().forEach(function(s){s._tippy.unmount()}),d.parentNode&&d.parentNode.removeChild(d),zt=zt.filter(function(s){return s!==o}),o.state.isMounted=!1,X("onHidden",[o]))}function Zr(){ve(o.state.isDestroyed,_e("destroy")),!o.state.isDestroyed&&(o.clearDelayTimeouts(),o.unmount(),$e(),delete t._tippy,o.state.isDestroyed=!0,X("onDestroy",[o]))}}function it(t,e){e===void 0&&(e={});var r=G.plugins.concat(e.plugins||[]);no(t),Rr(e,r),Jn();var n=Object.assign({},e,{plugins:r}),i=Yn(t),a=nt(n.content),p=i.length>1;ve(a&&p,["tippy() was passed an Element as the `content` prop, but more than","one tippy instance was created by this invocation. This means the","content element will only be appended to the last tippy instance.",`

`,"Instead, pass the .innerHTML of the element, or use a function that","returns a cloned version of the element instead.",`

`,`1) content: element.innerHTML
`,"2) content: () => element.cloneNode(true)"].join(" "));var f=i.reduce(function(u,c){var l=c&&uo(c,n);return l&&u.push(l),u},[]);return nt(t)?f[0]:f}it.defaultProps=G;it.setDefaultProps=ao;it.currentInput=pe;var ip=Object.assign({},vt,{effect:function(e){var r=e.state,n={popper:{position:r.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};Object.assign(r.elements.popper.style,n.popper),r.styles=n,r.elements.arrow&&Object.assign(r.elements.arrow.style,n.arrow)}});var Gt={clientX:0,clientY:0},Ot=[];function Wr(t){var e=t.clientX,r=t.clientY;Gt={clientX:e,clientY:r}}function lo(t){t.addEventListener("mousemove",Wr)}function co(t){t.removeEventListener("mousemove",Wr)}var _r={name:"followCursor",defaultValue:!1,fn:function(e){var r=e.reference,n=Cr(e.props.triggerTarget||r),i=!1,a=!1,p=!0,f=e.props;function u(){return e.props.followCursor==="initial"&&e.state.isVisible}function c(){n.addEventListener("mousemove",x)}function l(){n.removeEventListener("mousemove",x)}function y(){i=!0,e.setProps({getReferenceClientRect:null}),i=!1}function x(h){var w=h.target?r.contains(h.target):!0,O=e.props.followCursor,E=h.clientX,N=h.clientY,o=r.getBoundingClientRect(),T=E-o.left,d=N-o.top;(w||!e.props.interactive)&&e.setProps({getReferenceClientRect:function(){var j=r.getBoundingClientRect(),M=E,S=N;O==="initial"&&(M=j.left+T,S=j.top+d);var R=O==="horizontal"?j.top:S,L=O==="vertical"?j.right:M,A=O==="horizontal"?j.bottom:S,q=O==="vertical"?j.left:M;return{width:L-q,height:A-R,top:R,right:L,bottom:A,left:q}}})}function b(){e.props.followCursor&&(Ot.push({instance:e,doc:n}),lo(n))}function g(){Ot=Ot.filter(function(h){return h.instance!==e}),Ot.filter(function(h){return h.doc===n}).length===0&&co(n)}return{onCreate:b,onDestroy:g,onBeforeUpdate:function(){f=e.props},onAfterUpdate:function(w,O){var E=O.followCursor;i||E!==void 0&&f.followCursor!==E&&(g(),E?(b(),e.state.isMounted&&!a&&!u()&&c()):(l(),y()))},onMount:function(){e.props.followCursor&&!a&&(p&&(x(Gt),p=!1),u()||c())},onTrigger:function(w,O){Pr(O)&&(Gt={clientX:O.clientX,clientY:O.clientY}),a=O.type==="focus"},onHidden:function(){e.props.followCursor&&(y(),l(),p=!0)}}}};it.setDefaultProps({render:kr});var xt=it;var Hr=t=>{let e={plugins:[]},r=i=>t[t.indexOf(i)+1];if(t.includes("animation")&&(e.animation=r("animation")),t.includes("duration")&&(e.duration=parseInt(r("duration"))),t.includes("delay")){let i=r("delay");e.delay=i.includes("-")?i.split("-").map(a=>parseInt(a)):parseInt(i)}if(t.includes("cursor")){e.plugins.push(_r);let i=r("cursor");["x","initial"].includes(i)?e.followCursor=i==="x"?"horizontal":"initial":e.followCursor=!0}t.includes("on")&&(e.trigger=r("on")),t.includes("arrowless")&&(e.arrow=!1),t.includes("html")&&(e.allowHTML=!0),t.includes("interactive")&&(e.interactive=!0),t.includes("border")&&e.interactive&&(e.interactiveBorder=parseInt(r("border"))),t.includes("debounce")&&e.interactive&&(e.interactiveDebounce=parseInt(r("debounce"))),t.includes("max-width")&&(e.maxWidth=parseInt(r("max-width"))),t.includes("theme")&&(e.theme=r("theme")),t.includes("placement")&&(e.placement=r("placement"));let n={};return t.includes("no-flip")&&(n.modifiers||=[],n.modifiers.push({name:"flip",enabled:!1})),e.popperOptions=n,e};function Kt(t){t.magic("tooltip",e=>(r,n={})=>{let i=n.timeout;delete n.timeout;let a=xt(e,{content:r,trigger:"manual",...n});a.show(),setTimeout(()=>{a.hide(),setTimeout(()=>a.destroy(),n.duration||300)},i||2e3)}),t.directive("tooltip",(e,{modifiers:r,expression:n},{evaluateLater:i,effect:a,cleanup:p})=>{let f=r.length>0?Hr(r):{};e.__x_tippy||(e.__x_tippy=xt(e,f)),p(()=>{e.__x_tippy&&(e.__x_tippy.destroy(),delete e.__x_tippy)});let u=()=>e.__x_tippy.enable(),c=()=>e.__x_tippy.disable(),l=y=>{y?(u(),e.__x_tippy.setContent(y)):c()};if(r.includes("raw"))l(n);else{let y=i(n);a(()=>{y(x=>{typeof x=="object"?(e.__x_tippy.setProps(x),u()):l(x)})})}})}Kt.defaultProps=t=>(xt.setDefaultProps(t),Kt);var Fr=Kt;document.addEventListener("alpine:initializing",()=>{Fr(window.Alpine)});})();
