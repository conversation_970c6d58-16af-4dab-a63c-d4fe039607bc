import { c as g, i as f } from "../../admin.bundle-DI9_jvUJ.js";
import { u as b } from "../../apexcharts.esm-BofaT7g3.js";
import "../../main-Cyta4iCA.js";
class P {
  constructor(e, o, a = 8) {
    (this.tableId = e),
      (this.data = o),
      (this.itemsPerPage = a),
      (this.currentPage = 1),
      (this.tableElement = document.getElementById(e)),
      (this.totalPages = Math.ceil(this.data.length / this.itemsPerPage)),
      this.init();
  }
  init() {
    this.renderTable(), this.setupPagination(), this.updatePaginationInfo();
  }
  renderTable() {
    const e = this.tableElement.querySelector("tbody");
    e.innerHTML = "";
    const o = (this.currentPage - 1) * this.itemsPerPage,
      a = Math.min(o + this.itemsPerPage, this.data.length);
    for (let n = o; n < a; n++) {
      const s = this.data[n],
        i = document.createElement("tr");
      let r = document.createElement("td");
      (r.textContent = s.productCode),
        i.appendChild(r),
        (r = document.createElement("td"));
      const d = document.createElement("a");
      (d.href = "apps-ecommerce-product-overview.html"),
        (d.className = "text-body"),
        (d.textContent = s.item),
        r.appendChild(d),
        i.appendChild(r),
        (r = document.createElement("td")),
        (r.textContent = s.qtyLeft),
        i.appendChild(r),
        (r = document.createElement("td"));
      const c = document.createElement("span");
      s.qtyLeft === 0
        ? ((c.className =
            "badge bg-danger-subtle text-danger border border-danger-subtle"),
          (c.textContent = "Out of Stock"))
        : s.qtyLeft < 150
        ? ((c.className =
            "badge bg-warning-subtle text-warning border border-warning-subtle"),
          (c.textContent = "Low Stock"))
        : ((c.className =
            "badge bg-secondary-subtle text-secondary border border-secondary-subtle"),
          (c.textContent = "In Stock")),
        r.appendChild(c),
        i.appendChild(r),
        (r = document.createElement("td")),
        (r.textContent = s.price),
        i.appendChild(r),
        e.appendChild(i);
    }
  }
  setupPagination() {
    const e = document.getElementById("pagination");
    e.innerHTML = "";
    const o = document.createElement("li");
    o.className = `page-item ${this.currentPage === 1 ? "disabled" : ""}`;
    const a = document.createElement("a");
    (a.className = "page-link"),
      (a.href = "#!"),
      (a.innerHTML =
        '<i data-lucide="chevron-left" class="size-4"></i> Previous'),
      a.addEventListener("click", (i) => {
        i.preventDefault(),
          this.currentPage > 1 && this.goToPage(this.currentPage - 1);
      }),
      o.appendChild(a),
      e.appendChild(o);
    for (let i = 1; i <= this.totalPages; i++) {
      const r = document.createElement("li");
      r.className = `page-item ${i === this.currentPage ? "active" : ""}`;
      const d = document.createElement("a");
      (d.className = "page-link"),
        (d.href = "#!"),
        (d.textContent = i),
        d.addEventListener("click", (c) => {
          c.preventDefault(), this.goToPage(i);
        }),
        r.appendChild(d),
        e.appendChild(r);
    }
    const n = document.createElement("li");
    n.className = `page-item ${
      this.currentPage === this.totalPages ? "disabled" : ""
    }`;
    const s = document.createElement("a");
    (s.className = "page-link"),
      (s.href = "#!"),
      (s.innerHTML = 'Next <i data-lucide="chevron-right" class="size-4"></i>'),
      s.addEventListener("click", (i) => {
        i.preventDefault(),
          this.currentPage < this.totalPages &&
            this.goToPage(this.currentPage + 1);
      }),
      n.appendChild(s),
      e.appendChild(n),
      g({ icons: f });
  }
  updatePaginationInfo() {
    const e = document.getElementById("pagination-info"),
      o = (this.currentPage - 1) * this.itemsPerPage + 1,
      a = Math.min(o + this.itemsPerPage - 1, this.data.length);
    e.innerHTML = `Showing <b class="me-1">${o}-${a}</b> of <b class="ms-1">${this.data.length}</b> Results`;
  }
  goToPage(e) {
    (this.currentPage = e),
      this.renderTable(),
      this.setupPagination(),
      this.updatePaginationInfo();
  }
}
const y = [
  {
    productCode: "PEP-1478",
    item: "Blouse Ruffle Tube top",
    qtyLeft: 145,
    price: "$14.99",
  },
  {
    productCode: "PEP-1479",
    item: "Gold-colored locket watch",
    qtyLeft: 569,
    price: "$19.99",
  },
  {
    productCode: "PEP-1480",
    item: "Crop Top Sweater Clothing",
    qtyLeft: 541,
    price: "$22.49",
  },
  {
    productCode: "PEP-1481",
    item: "Sleeve Clothing Leggings",
    qtyLeft: 126,
    price: "$31.78",
  },
  {
    productCode: "PEP-1482",
    item: "Yellow Women Shoes",
    qtyLeft: 0,
    price: "$49.99",
  },
  {
    productCode: "PEP-1483",
    item: "Straw hat Cap Cowboy",
    qtyLeft: 571,
    price: "$49.99",
  },
  {
    productCode: "PEP-1484",
    item: "Sneakers Shoe Nike Basketball",
    qtyLeft: 0,
    price: "$49.99",
  },
  {
    productCode: "PEP-1485",
    item: "Modern Fashion T shirt",
    qtyLeft: 321,
    price: "$29.49",
  },
  {
    productCode: "PEP-1486",
    item: "Denim Jacket",
    qtyLeft: 250,
    price: "$59.99",
  },
  {
    productCode: "PEP-1487",
    item: "Leather Wallet",
    qtyLeft: 89,
    price: "$24.99",
  },
  {
    productCode: "PEP-1488",
    item: "Sunglasses",
    qtyLeft: 120,
    price: "$15.99",
  },
  {
    productCode: "PEP-1489",
    item: "Canvas Backpack",
    qtyLeft: 203,
    price: "$39.99",
  },
];
document.addEventListener("DOMContentLoaded", function () {
  new P("product-table", y, 8);
});
function C(t) {
  const e = getComputedStyle(document.documentElement)
    .getPropertyValue(t)
    .trim();
  return /^\d{1,3},\s*\d{1,3},\s*\d{1,3}$/.test(e) ? `rgb(${e})` : e;
}
var p = [];
const E = (t) => {
  const e = JSON.parse(JSON.stringify(t)),
    o = (a) => {
      for (const n in a)
        typeof a[n] == "string" && a[n].startsWith("--dx-")
          ? (a[n] = C(a[n]))
          : typeof a[n] == "object" && a[n] !== null && o(a[n]);
    };
  return o(e), e;
};
function m(t = "") {
  t && document.documentElement.setAttribute("data-colors", t),
    p.forEach((e) => {
      const o = JSON.parse(JSON.stringify(e[0].data)),
        a = E(structuredClone(o));
      e[0].chart && e[0].chart.destroy();
      var n = new b(document.querySelector("#" + e[0].id), a);
      n.render(), (e[0].chart = n);
    });
}
document.querySelectorAll('input[name="data-colors"]').forEach((t) => {
  t.addEventListener("change", function () {
    u(this.value);
  });
});
document.querySelectorAll('input[name="data-bs-theme"]').forEach((t) => {
  t.addEventListener("change", function () {
    u(this.value);
  });
});
var h;
(h = document.getElementById("darkModeButton")) == null ||
  h.addEventListener("click", function () {
    u(this.value);
  });
function u(t) {
  setTimeout(() => {
    m(t);
  }, 0);
}
var l = {
  series: [44, 55, 41, 17, 15],
  chart: { height: 75, type: "donut" },
  dataLabels: { enabled: !1 },
  legend: { show: !1, position: "bottom" },
  colors: [
    "--dx-primary",
    "--dx-success",
    "--dx-warning",
    "--dx-danger",
    "--dx-secondary",
  ],
  grid: { padding: { top: -6, right: 0, bottom: -10, left: 0 } },
};
p.push([{ id: "expenseChart", data: l }]);
var l = {
  series: [
    { name: "Net Profit", data: [44, 55, 57, 56, 61, 58, 63, 60, 66] },
    { name: "Revenue", data: [76, 85, 101, 98, 87, 105, 91, 114, 94] },
  ],
  chart: { height: 280, type: "bar", toolbar: { show: !1 } },
  plotOptions: {
    bar: { horizontal: !1, columnWidth: "55%", borderRadius: [10] },
  },
  states: { hover: { filter: { type: "none" } } },
  dataLabels: { enabled: !1 },
  stroke: { show: !0, width: 2, lineCap: "round", colors: ["transparent"] },
  colors: ["--dx-danger", "--dx-primary"],
  xaxis: {
    categories: ["Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct"],
  },
  yaxis: { title: { text: "$ (thousands)" } },
  tooltip: {
    y: {
      formatter: function (t) {
        return "$" + t + "k";
      },
    },
  },
};
p.push([{ id: "productSalesChart", data: l }]);
var l = {
  series: [{ name: "Profit", data: [5, 4, 7, 2, 8, 6, 3] }],
  chart: {
    height: 130,
    type: "bar",
    toolbar: { show: !1 },
    sparkline: { enabled: !0 },
  },
  plotOptions: { bar: { horizontal: !1, endingShape: "rounded" } },
  colors: ["--dx-primary"],
  grid: { padding: { top: 0, right: -10, bottom: 0, left: -10 } },
  stroke: { show: !0, width: 2, colors: ["transparent"] },
};
p.push([{ id: "netProfitChart", data: l }]);
var l = {
  series: [
    {
      name: "Sales",
      data: [
        0.4, 0.65, 0.76, 0.88, 1.5, 2.1, 2.9, 3.8, 3.9, 4.2, 4, 4.3, 4.1, 4.2,
        4.5, 3.9, 3.5,
      ],
    },
    {
      name: "Visit",
      data: [
        -0.8, -1.05, -1.06, -1.18, -1.4, -2.2, -2.85, -3.7, -3.96, -4.22, -4.3,
        -4.4, -4.1, -4, -4.1, -3.4, -3.1,
      ],
    },
  ],
  chart: { height: 320, type: "bar", stacked: !0, toolbar: { show: !1 } },
  colors: ["--dx-primary", "--dx-success"],
  plotOptions: { bar: { horizontal: !0, barHeight: "80%" } },
  dataLabels: { enabled: !1 },
  grid: {
    strokeDashArray: 2,
    xaxis: { lines: { show: !1 } },
    yaxis: { lines: { show: !0 } },
    padding: { top: -20, bottom: 0 },
    row: { opacity: 0 },
  },
  yaxis: { min: -5, max: 5 },
  states: { hover: { filter: { type: "none" } } },
  tooltip: {
    shared: !1,
    x: {
      formatter: function (t) {
        return t;
      },
    },
    y: {
      formatter: function (t) {
        return Math.abs(t) + "%";
      },
    },
  },
  xaxis: {
    categories: [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ],
    labels: {
      formatter: function (t) {
        return Math.abs(Math.round(t)) + "%";
      },
    },
  },
};
p.push([{ id: "trafficChart", data: l }]);
m();
new jsVectorMap({
  selector: "#topLocationMap",
  map: "world",
  markers: [
    { name: "Brazil", coords: [-14.235, -51.9253] },
    { name: "Russia", coords: [61.524, 105.3188] },
    { name: "China", coords: [35.8617, 104.1954] },
  ],
  labels: { markers: { render: (t) => t.name } },
  selectedMarkers: [1],
});
