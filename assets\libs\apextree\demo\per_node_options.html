<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@400;600&display=swap" rel="stylesheet" />
    <script src="../apextree.min.js"></script>
  </head>
  <body>
    <div id="svg-tree" style="display: flex; justify-content: center"></div>
    <script>
      const data = {
        id: '1',
        name: 'Species',
        options: {
          fontSize: '25px',
          fontFamily: 'sans-serif',
          fontWeight: 600,
          fontColor: '#a06dcc',
          borderWidth: 2,
          borderColor: '#a06dcc',
          borderColorHover: '#ec327e',
          nodeBGColor: '#eedfff',
          nodeBGColorHover: '#d5c3ff',
        },
        children: [
          {
            id: '2',
            name: 'Plants',
            category: 'Species',
            options: {
              nodeBGColorHover: '#d7c7e3',
              borderColorHover: '#e563c7',
            },
            children: [
              {
                id: '3',
                name: 'Mosses',
                category: 'Plants',
                options: {
                  nodeBGColorHover: '#d7c7e3',
                  borderColorHover: '#e563c7',
                },
              },
              {
                id: '4',
                name: 'Ferns',
                category: 'Plants',
              },
              {
                id: '5',
                name: 'Gymnosperms',
                category: 'Plants',
              },
              {
                id: '6',
                name: 'Dicotyledens',
                category: 'Plants',
              },
              {
                id: '7',
                name: 'Monocotyledens',
                category: 'Plants',
              },
            ],
          },
          {
            id: '8',
            name: 'Fungi',
          },
          {
            id: '9',
            name: 'Lichens',
          },
          {
            id: '10',
            name: 'Animals',
            children: [
              {
                id: '11',
                name: 'Invertebrates',
                category: 'Animals',
                children: [
                  {
                    id: '12',
                    name: 'Insects',
                    category: 'Invertebrates',
                  },
                  {
                    id: '13',
                    name: 'Molluscs',
                    category: 'Invertebrates',
                  },
                  {
                    id: '14',
                    name: 'Crustaceans',
                    category: 'Invertebrates',
                  },
                  {
                    id: '15',
                    name: 'Others',
                    category: 'Invertebrates',
                  },
                ],
              },
              {
                id: '16',
                name: 'Vertebrates',
                category: 'Animals',
                children: [
                  {
                    id: '17',
                    name: 'Fish',
                    category: 'Vertebrates',
                  },
                  {
                    id: '18',
                    name: 'Amphibians',
                    category: 'Vertebrates',
                  },
                  {
                    id: '19',
                    name: 'Reptiles',
                    category: 'Vertebrates',
                  },
                  {
                    id: '20',
                    name: 'Birds',
                    category: 'Vertebrates',
                  },
                  {
                    id: '21',
                    name: 'Mammals',
                    category: 'Vertebrates',
                  },
                ],
              },
            ],
          },
        ],
      };
      const options = {
        contentKey: 'name',
        width: 1200,
        height: 800,
        nodeWidth: 180,
        nodeHeight: 50,
        childrenSpacing: 180,
        siblingSpacing: 30,
        direction: 'left',
        fontSize: '20px',
        fontFamily: 'Quicksand, sans-serif',
        fontWeight: 600,
        fontColor: '#388ac4',
        borderColorHover: '#388ac4',
        nodeBGColorHover: '#d7d7d7',
        enableToolbar: true,
        canvasStyle: 'border: 1px solid black;background: #f6f6f6;',
      };
      const tree = new ApexTree(document.getElementById('svg-tree'), options);
      tree.render(data);
    </script>
  </body>
</html>
