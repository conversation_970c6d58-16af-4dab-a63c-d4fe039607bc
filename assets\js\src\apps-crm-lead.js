import{c,i as g}from"../../admin.bundle-DI9_jvUJ.js";/* empty css                               */import"../../main-Cyta4iCA.js";VirtualSelect.init({ele:"#statusSelect2",options:[{label:"New",value:"1"},{label:"Host",value:"2"},{label:"Pending",value:"3"},{label:"Host",value:"4"}],selectedValue:0});class h{constructor(){this.leads={new:[],hot:[],pending:[],lost:[]},this.currentLeadId=null,this.drake=null,this.nextId=1,this.imagePreview=null,this.loadSampleData(),this.initDragAndDrop(),this.initEventListeners(),this.renderAllContainers()}initDragAndDrop(){const a=Array.from(document.querySelectorAll(".lead-container"));this.drake=dragula(a,{moves:(e,s,t)=>!0}),this.drake.on("drop",(e,s,t)=>{const n=e.getAttribute("data-id"),d=s.closest("[data-status]").getAttribute("data-status"),i=t.closest("[data-status]").getAttribute("data-status");i!==d&&this.updateLeadStatus(parseInt(n),i,d)})}initEventListeners(){document.getElementById("leadForm").addEventListener("submit",t=>{t.preventDefault(),this.saveLeadFromForm()}),document.getElementById("confirmDelete").addEventListener("click",()=>{this.currentLeadId&&this.deleteLead(this.currentLeadId)});const a=document.getElementById("imageInput"),e=document.getElementById("uploadIcon"),s=document.querySelector("label.avatar");a.addEventListener("change",t=>{if(t.target.files&&t.target.files[0]){const n=new FileReader;n.onload=d=>{if(this.imagePreview=d.target.result,s){e.style.display="none";let i=s.querySelector("img.preview-image");i||(i=document.createElement("img"),i.className="preview-image size-24 rounded-circle",s.appendChild(i)),i.src=this.imagePreview}},n.readAsDataURL(t.target.files[0])}}),document.addEventListener("click",t=>{if(t.target.closest(".link-custom-primary")){const n=t.target.closest(".p-3.bg-body.rounded");if(n){const d=parseInt(n.getAttribute("data-id"));this.editLead(d),t.preventDefault()}}else if(t.target.closest(".link-custom-danger")){const n=t.target.closest(".p-3.bg-body.rounded");n&&(this.currentLeadId=parseInt(n.getAttribute("data-id")))}})}updateLeadStatus(a,e,s){const t=this.leads[e].findIndex(n=>n.id===a);if(t!==-1){const n=this.leads[e][t];this.leads[e].splice(t,1),n.status=s,this.leads[s].push(n),this.updateStatusCounters()}}saveLeadFromForm(){const a=document.getElementById("leadId").value,e=document.getElementById("fullName").value,s=document.getElementById("email").value,t=document.getElementById("phone").value,n=document.getElementById("statusSelect").value,d=new Date,i={id:a?parseInt(a):this.nextId++,name:e,email:s,phone:t,status:n,date:d.toLocaleDateString("en-US",{day:"numeric",month:"long",year:"numeric"}),time:d.toLocaleTimeString("en-US",{hour:"numeric",minute:"2-digit",hour12:!0}),avatar:this.imagePreview||"assets/images/avatar/user-1.png"};if(a)for(const l in this.leads){const r=this.leads[l].findIndex(m=>m.id===parseInt(a));if(r!==-1){const m=this.leads[l][r].status;this.imagePreview||(i.avatar=this.leads[l][r].avatar),this.leads[l].splice(r,1),m!==i.status?this.leads[i.status].push(i):this.leads[l].splice(r,0,i);break}}else this.leads[n].push(i);document.getElementById("leadForm").reset(),document.getElementById("leadId").value="",document.getElementById("saveLeadBtn").textContent="Add Lead",this.resetImagePreview();const u=document.getElementById("createLeadModal");window.bootstrap.Modal.getInstance(u).hide(),this.renderAllContainers(),this.updateStatusCounters()}resetImagePreview(){this.imagePreview=null;const a=document.querySelector("label.avatar"),e=document.getElementById("uploadIcon");if(a){e&&(e.style.display="inline-block");const s=a.querySelector("img.preview-image");s&&s.remove()}}editLead(a){let e=null;for(const s in this.leads)if(e=this.leads[s].find(t=>t.id===a),e)break;if(e){this.removeExtraModalBackdrops(),document.getElementById("leadId").value=e.id,document.getElementById("fullName").value=e.name,document.getElementById("email").value=e.email,document.getElementById("phone").value=e.phone,document.getElementById("statusSelect").value=e.status,document.getElementById("saveLeadBtn").textContent="Update Lead",this.updateAvatarPreview(e.avatar);const s=document.getElementById("createLeadModal"),t=window.bootstrap.Modal.getInstance(s);t?t.show():new window.bootstrap.Modal(s).show()}}updateAvatarPreview(a){const e=document.querySelector("label.avatar"),s=document.getElementById("uploadIcon");if(e){s&&(s.style.display="none");let t=e.querySelector("img.preview-image");t||(t=document.createElement("img"),t.className="preview-image size-24 rounded-circle",e.appendChild(t)),t.src=a}}removeExtraModalBackdrops(){const a=document.querySelectorAll(".modal-backdrop");if(a.length>0)for(let e=0;e<a.length;e++)a[e].remove()}deleteLead(a){for(const e in this.leads){const s=this.leads[e].findIndex(t=>t.id===a);if(s!==-1){this.leads[e].splice(s,1);break}}this.renderAllContainers(),this.updateStatusCounters(),this.currentLeadId=null}updateStatusCounters(){["new","hot","pending","lost"].forEach(e=>{const s=this.leads[e].length,t=document.querySelector(`h6:has(+ .lead-simplebar[data-status="${e}"]) .badge`);t&&(t.textContent=s)})}renderAllContainers(){["new","hot","pending","lost"].forEach(e=>{const s=document.getElementById(`${e}-leads`);s.innerHTML="",this.leads[e].forEach(t=>{s.appendChild(this.createLeadCard(t))})})}createLeadCard(a){const e=document.createElement("div");return e.className="p-3 bg-body rounded",e.setAttribute("data-id",a.id),e.innerHTML=`
          <div class="d-flex align-items-center gap-3 mb-2">
              <img src="${a.avatar}" loading="lazy" alt="User Image" class="rounded-circle size-12">
              <div class="flex-grow-1">
                  <h6 class="mb-1">${a.name}</h6>
                  <p class="fs-sm text-muted">
                      <i class="ri-time-line"></i>
                      ${a.date} at
                      <span>${a.time}</span>
                  </p>
              </div>
          </div>
          <div class="mb-2">
              <i class="ri-mail-line me-1"></i> <span class="text-muted">${a.email}</span>
          </div>
          <div>
              <i class="ri-phone-line me-1"></i> <span class="text-muted">${a.phone}</span>
          </div>
          <div class="d-flex gap-3 mt-3">
              <a href="#!" class="link link-custom-primary">Edit</a>
              <a href="#!" class="link link-custom-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">Delete</a>
          </div>
      `,e}loadSampleData(){[{id:this.nextId++,name:"Charles Carter",email:"<EMAIL>",phone:"+(145) 0128 2303",status:"new",date:"30 May, 2024",time:"1:30 PM",avatar:"assets/images/avatar/user-1.png"},{id:this.nextId++,name:"Diana Dawson",email:"<EMAIL>",phone:"+(145) 0128 2308",status:"new",date:"31 May, 2024",time:"9:00 PM",avatar:"assets/images/avatar/user-2.png"},{id:this.nextId++,name:"Liam Lee",email:"<EMAIL>",phone:"+(145) 0128 2310",status:"new",date:"1 June, 2024",time:"11:00 AM",avatar:"assets/images/avatar/user-3.png"},{id:this.nextId++,name:"Emma Edwards",email:"<EMAIL>",phone:"+(145) 0128 2311",status:"new",date:"2 June, 2024",time:"10:45 AM",avatar:"assets/images/avatar/user-4.png"},{id:this.nextId++,name:"Noah Nelson",email:"<EMAIL>",phone:"+(145) 0128 2312",status:"new",date:"3 June, 2024",time:"1:15 PM",avatar:"assets/images/avatar/user-5.png"},{id:this.nextId++,name:"Ashton Abigail",email:"<EMAIL>",phone:"+(145) 0128 2301",status:"hot",date:"28 May, 2024",time:"3:45 PM",avatar:"assets/images/avatar/user-6.png"},{id:this.nextId++,name:"Grace Griffin",email:"<EMAIL>",phone:"+(145) 0128 2313",status:"hot",date:"30 May, 2024",time:"2:00 PM",avatar:"assets/images/avatar/user-7.png"},{id:this.nextId++,name:"Henry Hughes",email:"<EMAIL>",phone:"+(145) 0128 2314",status:"hot",date:"1 June, 2024",time:"4:30 PM",avatar:"assets/images/avatar/user-8.png"},{id:this.nextId++,name:"Ivy Ingram",email:"<EMAIL>",phone:"+(145) 0128 2315",status:"hot",date:"2 June, 2024",time:"5:00 PM",avatar:"assets/images/avatar/user-9.png"},{id:this.nextId++,name:"Jackie Johnson",email:"<EMAIL>",phone:"+(145) 0128 2316",status:"hot",date:"3 June, 2024",time:"6:15 PM",avatar:"assets/images/avatar/user-10.png"},{id:this.nextId++,name:"Bethany Bennett",email:"<EMAIL>",phone:"+(145) 0128 2302",status:"pending",date:"29 May, 2024",time:"10:00 PM",avatar:"assets/images/avatar/user-11.png"},{id:this.nextId++,name:"Kevin King",email:"<EMAIL>",phone:"+(145) 0128 2317",status:"pending",date:"30 May, 2024",time:"8:45 PM",avatar:"assets/images/avatar/user-12.png"},{id:this.nextId++,name:"Luna Lane",email:"<EMAIL>",phone:"+(145) 0128 2318",status:"pending",date:"31 May, 2024",time:"2:30 PM",avatar:"assets/images/avatar/user-13.png"},{id:this.nextId++,name:"Mason Miller",email:"<EMAIL>",phone:"+(145) 0128 2319",status:"pending",date:"1 June, 2024",time:"4:00 PM",avatar:"assets/images/avatar/user-14.png"},{id:this.nextId++,name:"Nora Nichols",email:"<EMAIL>",phone:"+(145) 0128 2320",status:"pending",date:"2 June, 2024",time:"12:15 PM",avatar:"assets/images/avatar/user-15.png"},{id:this.nextId++,name:"Ethan Evans",email:"<EMAIL>",phone:"+(145) 0128 2305",status:"lost",date:"1 June, 2024",time:"11:15 PM",avatar:"assets/images/avatar/user-16.png"},{id:this.nextId++,name:"Olivia Owens",email:"<EMAIL>",phone:"+(145) 0128 2321",status:"lost",date:"2 June, 2024",time:"7:45 PM",avatar:"assets/images/avatar/user-17.png"},{id:this.nextId++,name:"Peter Parker",email:"<EMAIL>",phone:"+(145) 0128 2322",status:"lost",date:"3 June, 2024",time:"9:30 PM",avatar:"assets/images/avatar/user-18.png"},{id:this.nextId++,name:"Quinn Queen",email:"<EMAIL>",phone:"+(145) 0128 2323",status:"lost",date:"4 June, 2024",time:"3:00 PM",avatar:"assets/images/avatar/user-19.png"},{id:this.nextId++,name:"Ryan Rogers",email:"<EMAIL>",phone:"+(145) 0128 2324",status:"lost",date:"5 June, 2024",time:"5:45 PM",avatar:"assets/images/avatar/user-20.png"}].forEach(e=>{this.leads[e.status].push(e)})}}document.addEventListener("DOMContentLoaded",()=>{c({icons:g}),document.addEventListener("hidden.bs.modal",function(){const o=document.querySelectorAll(".modal-backdrop");o.length>0&&o.forEach(a=>a.remove())}),window.leadManager=new h});
