import{c as g,i as f}from"../../admin.bundle-DI9_jvUJ.js";import{u as k}from"../../apexcharts.esm-BofaT7g3.js";import"../../main-Cyta4iCA.js";function b(s){const e=getComputedStyle(document.documentElement).getPropertyValue(s).trim();return/^\d{1,3},\s*\d{1,3},\s*\d{1,3}$/.test(e)?`rgb(${e})`:e}var l=[];const x=s=>{const e=JSON.parse(JSON.stringify(s)),t=a=>{for(const i in a)typeof a[i]=="string"&&a[i].startsWith("--dx-")?a[i]=b(a[i]):typeof a[i]=="object"&&a[i]!==null&&t(a[i])};return t(e),e};function u(s=""){s&&document.documentElement.setAttribute("data-colors",s),l.forEach(e=>{const t=JSON.parse(JSON.stringify(e[0].data)),a=x(structuredClone(t));e[0].chart&&e[0].chart.destroy();var i=new k(document.querySelector("#"+e[0].id),a);i.render(),e[0].chart=i})}document.querySelectorAll('input[name="data-colors"]').forEach(s=>{s.addEventListener("change",function(){d(this.value)})});document.querySelectorAll('input[name="data-bs-theme"]').forEach(s=>{s.addEventListener("change",function(){d(this.value)})});var m;(m=document.getElementById("darkModeButton"))==null||m.addEventListener("click",function(){d(this.value)});function d(s){setTimeout(()=>{u(s)},0)}var r={series:[{name:"Net Profit",data:[32,39,43,49,52,58,63,60,66]}],chart:{height:320,type:"bar",toolbar:{show:!1}},plotOptions:{bar:{horizontal:!1,columnWidth:"55%",endingShape:"rounded"}},dataLabels:{enabled:!1},stroke:{show:!0,width:2,colors:["transparent"]},xaxis:{categories:["Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct"]},fill:{opacity:1},yaxis:{show:!1},grid:{show:!1,xaxis:{lines:{show:!1}},yaxis:{lines:{show:!1}},padding:{top:-30,right:0,bottom:-12,left:0}},tooltip:{y:{formatter:function(s){return"$ "+s+"k"}}},colors:["--dx-primary","--dx-success","--dx-warning","--dx-danger","--dx-secondary"]};l.push([{id:"patientVisitChart",data:r}]);var r={series:[44,55,41,18],labels:["Cardiology","Neurology","Orthopedics","Pediatrics"],chart:{height:160,type:"donut"},plotOptions:{pie:{startAngle:-90,endAngle:270}},dataLabels:{enabled:!1},fill:{type:"gradient"},legend:{formatter:function(s,e){return s+" - "+e.w.globals.series[e.seriesIndex]}},responsive:[{breakpoint:480,options:{chart:{width:"100%",height:150},legend:{position:"bottom"}}}],colors:["--dx-primary","--dx-success","--dx-warning","--dx-secondary"]};l.push([{id:"patientDepartmentChart",data:r}]);var r={series:[{name:"Inject Patients",data:[24,32,28,62,67,80,96,106]},{name:"Surgery Patients",data:[5,14,19,27,35,44,22,49]}],chart:{defaultLocale:"en",height:205,type:"line",toolbar:{show:!1}},dataLabels:{enabled:!1},stroke:{curve:"smooth",width:3,lineCap:"butt"},xaxis:{categories:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]},tooltip:{x:{show:!0}},grid:{strokeDashArray:4,position:"back",padding:{top:-20,right:0,bottom:0}},colors:["--dx-primary","--dx-secondary"]};l.push([{id:"patientsHistoryChart",data:r}]);var r={series:[{name:"Birth Case",data:[80,50,30,70,99,36]},{name:"Death Case",data:[10,14,28,16,34,87]},{name:"Accident Case",data:[44,98,54,46,34,22]}],chart:{height:325,type:"radar"},stroke:{width:1},fill:{opacity:.1},xaxis:{categories:["2019","2020","2021","2022","2023","2024"]},colors:["--dx-primary","--dx-danger","--dx-success"]};l.push([{id:"hospitalBirthDeathChart",data:r}]);u();document.addEventListener("DOMContentLoaded",function(){document.querySelectorAll("[data-action]").forEach(e=>{e.addEventListener("click",function(){const t=e.getAttribute("data-user-id"),a=e.getAttribute("data-action");e.closest(".d-flex.align-items-center.gap-2.flex-shrink-0").classList.add("d-none");const o=document.querySelector(`[data-user-id="${t}"][data-badge="${a}"]`);o&&o.classList.remove("d-none")})})});class y{constructor(e){this.tableId=e.tableId,this.searchInputId=e.searchInputId,this.paginationId=e.paginationId,this.showingResultsId=e.showingResultsId,this.checkAllId=e.checkAllId,this.itemsPerPage=e.itemsPerPage||8,this.currentPage=1,this.data=e.data,this.filteredData=[...this.data],this.init()}init(){this.renderTable(),this.initSearch(),this.initCheckAll(),this.initPagination()}renderTable(){const t=document.getElementById(this.tableId).querySelector("tbody"),a=t.querySelector("tr");t.innerHTML="",t.appendChild(a);const i=(this.currentPage-1)*this.itemsPerPage,o=i+this.itemsPerPage,h=this.filteredData.slice(i,o);if(h.length===0){const n=document.createElement("tr");n.innerHTML=`
                <td colspan="9" class="text-center py-4">
                    <div class="d-flex flex-column align-items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" class="mx-auto size-12" viewBox="0 0 48 48">
                            <linearGradient id="SVGID_1__h35ynqzIJzH4_gr1" x1="34.598" x2="15.982" y1="15.982" y2="34.598" gradientUnits="userSpaceOnUse">
                                <stop offset="0" stop-color="#60e8fe"></stop>
                                <stop offset=".033" stop-color="#6ae9fe"></stop>
                                <stop offset=".197" stop-color="#97f0fe"></stop>
                                <stop offset=".362" stop-color="#bdf5ff"></stop>
                                <stop offset=".525" stop-color="#dafaff"></stop>
                                <stop offset=".687" stop-color="#eefdff"></stop>
                                <stop offset=".846" stop-color="#fbfeff"></stop>
                                <stop offset="1" stop-color="#fff"></stop>
                            </linearGradient>
                            <path fill="url(#SVGID_1__h35ynqzIJzH4_gr1)" d="M40.036,33.826L31.68,25.6c0.847-1.739,1.335-3.684,1.335-5.748c0-7.27-5.894-13.164-13.164-13.164
                                S6.688,12.582,6.688,19.852c0,7.27,5.894,13.164,13.164,13.164c2.056,0,3.995-0.485,5.728-1.326l3.914,4.015l4.331,4.331
                                c1.715,1.715,4.496,1.715,6.211,0C41.751,38.321,41.751,35.541,40.036,33.826z"></path>
                            <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3"
                                d="M31.95,25.739l8.086,8.086c1.715,1.715,1.715,4.496,0,6.211l0,0c-1.715,1.715-4.496,1.715-6.211,0
                                l-4.331-4.331"></path>
                            <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3"
                                d="M7.525,24.511c-1.771-4.694-0.767-10.196,3.011-13.975c3.847-3.847,9.48-4.817,14.228-2.912"></path>
                            <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3"
                                d="M30.856,12.603c3.376,5.114,2.814,12.063-1.688,16.565c-4.858,4.858-12.565,5.129-17.741,0.814"></path>
                        </svg>
                        <p class="mt-2 text-center text-gray-500 dark:text-dark-500">No matching records found</p>
                        <p class="text-muted mb-0">We couldn't find any categories matching your search.</p>
                    </div>
                </td>
            `,t.appendChild(n)}else h.forEach((n,c)=>{const p=document.createElement("tr");p.innerHTML=`
                    <td>
                        <div class="form-check check-primary">
                            <input class="form-check-input patient-checkbox" title="checkbox" type="checkbox" id="checkboxData${i+c+1}">
                            <label class="form-check-label d-none" for="checkboxData${i+c+1}">
                                Data ${i+c+1}
                            </label>
                        </div>
                    </td>
                    <td>${n.name}</td>
                    <td>${n.age}</td>
                    <td>${n.phone}</td>
                    <td>${n.email}</td>
                    <td>${n.condition}</td>
                    <td>${n.medications}</td>
                    <td>${n.lastVisit}</td>
                    <td>
                        <div class="d-flex gap-3">
                            <a class="link link-custom-primary" href="apps-hospital-patients-overview.html" aria-label="overview"><i class="ri-eye-line"></i></a>
                            <a class="link link-custom-primary" href="apps-hospital-patients-create.html" aria-label="edit"><i class="ri-edit-2-line"></i></a>
                            <a href="#!" class="link link-custom-danger" aria-label="delete"><i class="ri-delete-bin-6-line"></i></a>
                        </div>
                    </td>
                `,t.appendChild(p)});this.updateResultsCount(),this.updatePagination()}initSearch(){const e=document.getElementById(this.searchInputId);e.addEventListener("input",()=>{const t=e.value.toLowerCase();this.filteredData=this.data.filter(a=>a.name.toLowerCase().includes(t)||a.email.toLowerCase().includes(t)||a.condition.toLowerCase().includes(t)||a.medications.toLowerCase().includes(t)),this.currentPage=1,this.renderTable()})}initCheckAll(){const e=document.getElementById(this.checkAllId);e.addEventListener("change",()=>{document.querySelectorAll(".patient-checkbox").forEach(a=>{a.checked=e.checked})}),document.addEventListener("change",t=>{t.target.classList.contains("patient-checkbox")&&this.updateCheckAllStatus()})}updateCheckAllStatus(){const e=document.getElementById(this.checkAllId),t=document.querySelectorAll(".patient-checkbox"),a=document.querySelectorAll(".patient-checkbox:checked");t.length===0||a.length===0?e.checked=!1:a.length===t.length?e.checked=!0:e.checked=!1}initPagination(){document.getElementById(this.paginationId).addEventListener("click",t=>{if(t.preventDefault(),t.target.tagName==="A"||t.target.closest("a")){const i=(t.target.tagName==="A"?t.target:t.target.closest("a")).textContent.trim();if(i==="Previous")this.currentPage>1&&(this.currentPage--,this.renderTable());else if(i.includes("Next")){const o=Math.ceil(this.filteredData.length/this.itemsPerPage);this.currentPage<o&&(this.currentPage++,this.renderTable())}else{const o=parseInt(i,10);isNaN(o)||(this.currentPage=o,this.renderTable())}}})}updatePagination(){const e=document.getElementById(this.paginationId),t=Math.ceil(this.filteredData.length/this.itemsPerPage);let a="";a+=`
        <li class="page-item ${this.currentPage===1?"disabled":""}">
          <a class="page-link" href="#!"><i data-lucide="chevron-left" class="size-4"></i> Previous</a>
        </li>
      `;for(let i=1;i<=t;i++)a+=`
          <li class="page-item ${this.currentPage===i?"active":""}">
            <a class="page-link" href="#!">${i}</a>
          </li>
        `;a+=`
        <li class="page-item ${this.currentPage===t?"disabled":""}">
          <a class="page-link" href="#!">Next <i data-lucide="chevron-right" class="size-4"></i></a>
        </li>
      `,e.innerHTML=a,g({icons:f})}updateResultsCount(){const e=document.getElementById(this.showingResultsId),t=(this.currentPage-1)*this.itemsPerPage+1,a=Math.min(t+this.itemsPerPage-1,this.filteredData.length);e.innerHTML=`Showing <b class="me-1">${t}-${a}</b>of<b class="ms-1">${this.filteredData.length}</b> Results`}}const v=[{name:"Dorothy Daley",age:45,phone:"******* 4567",email:"<EMAIL>",condition:"Hypertension",medications:"Lisinopril, Amlodipine",lastVisit:"12 Jan, 2024"},{name:"Eve Green",age:41,phone:"******* 9012",email:"<EMAIL>",condition:"Anxiety",medications:"Alprazolam",lastVisit:"10 Jul, 2024"},{name:"Frank Brown",age:36,phone:"******* 0123",email:"<EMAIL>",condition:"Back Pain",medications:"Acetaminophen",lastVisit:"20 Aug, 2024"},{name:"Grace Miller",age:50,phone:"******* 1234",email:"<EMAIL>",condition:"Thyroid Disorder",medications:"Levothyroxine",lastVisit:"01 Sep, 2024"},{name:"Henry Wilson",age:28,phone:"******* 2345",email:"<EMAIL>",condition:"Migraine",medications:"Sumatriptan",lastVisit:"15 Oct, 2024"},{name:"Irene Martinez",age:60,phone:"******* 3456",email:"<EMAIL>",condition:"Osteoporosis",medications:"Alendronate",lastVisit:"20 Nov, 2024"},{name:"Jack Davis",age:34,phone:"******* 4567",email:"<EMAIL>",condition:"Gastroesophageal Reflux Disease",medications:"Omeprazole",lastVisit:"05 Dec, 2024"},{name:"Karen Taylor",age:42,phone:"******* 5678",email:"<EMAIL>",condition:"Chronic Fatigue Syndrome",medications:"Modafinil",lastVisit:"20 Dec, 2024"},{name:"Leonard Harris",age:55,phone:"******* 6789",email:"<EMAIL>",condition:"Diabetes",medications:"Metformin",lastVisit:"10 Jan, 2025"},{name:"Maria Scott",age:39,phone:"******* 7890",email:"<EMAIL>",condition:"Asthma",medications:"Albuterol",lastVisit:"15 Feb, 2025"},{name:"Norman Clark",age:47,phone:"******* 8901",email:"<EMAIL>",condition:"Arthritis",medications:"Ibuprofen",lastVisit:"20 Mar, 2025"},{name:"Olivia Baker",age:33,phone:"******* 9012",email:"<EMAIL>",condition:"Depression",medications:"Sertraline",lastVisit:"25 Apr, 2025"}];document.addEventListener("DOMContentLoaded",()=>{new y({tableId:"patientsTable",searchInputId:"allPatientSearch",paginationId:"pagination",showingResultsId:"showingResults",checkAllId:"checkboxDataAll",data:v,itemsPerPage:8})});
