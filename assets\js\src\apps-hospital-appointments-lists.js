import{c,i as u}from"../../admin.bundle-DI9_jvUJ.js";import"../../main-Cyta4iCA.js";class m{constructor(){this.appointments=[],this.currentPage=1,this.itemsPerPage=10,this.totalPages=0,this.tableBody=document.querySelector("tbody"),this.paginationContainer=document.querySelector(".pagination"),this.resultsInfo=document.getElementById("showingResults"),this.overviewModal=document.getElementById("overviewModal"),this.deleteModal=document.getElementById("deleteModal"),this.currentAppointmentId=null,this.init()}init(){this.loadAppointments(),this.setupEventListeners()}loadAppointments(){this.appointments=[{id:1,patientName:"<PERSON>",patientInitials:"SW",avatar:null,date:"07 June, 2024",time:"02:00PM - 03:00PM",treatment:"Routine check-up",doctor:"Dr. <PERSON>",status:"Pending"},{id:2,patientName:"<PERSON>",patientInitials:"DA",avatar:"assets/images/avatar/user-14.png",date:"08 June, 2024",time:"03:00PM - 04:00PM",treatment:"Skin condition evaluation",doctor:"Dr. <PERSON> <PERSON>",status:"New"},{id:3,patientName:"Olivia Lewis",patientInitials:"OL",avatar:null,date:"09 June, 2024",time:"11:30AM - 12:30PM",treatment:"Vision check-up",doctor:"Dr. Sarah Evans",status:"New"},{id:4,patientName:"James Brown",patientInitials:"JB",avatar:null,date:"10 June, 2024",time:"10:00AM - 11:00AM",treatment:"Dental cleaning",doctor:"Dr. Emily Carter",status:"Pending"},{id:5,patientName:"Linda Taylor",patientInitials:"LT",avatar:"assets/images/avatar/user-18.png",date:"11 June, 2024",time:"01:00PM - 02:00PM",treatment:"Cardiology consultation",doctor:"Dr. Robert Harris",status:"Confirmed"},{id:6,patientName:"Sophia Martinez",patientInitials:"SM",avatar:"assets/images/avatar/user-17.png",date:"12 June, 2024",time:"09:00AM - 10:00AM",treatment:"Blood test",doctor:"Dr. Michael Johnson",status:"Completed"},{id:7,patientName:"Liam Anderson",patientInitials:"LA",avatar:null,date:"13 June, 2024",time:"11:00AM - 12:00PM",treatment:"Skin biopsy",doctor:"Dr. Sarah Evans",status:"Pending"},{id:8,patientName:"Emma Wilson",patientInitials:"EW",avatar:"assets/images/avatar/user-20.png",date:"14 June, 2024",time:"02:00PM - 03:00PM",treatment:"Orthopedic consultation",doctor:"Dr. Emily Carter",status:"Confirmed"},{id:9,patientName:"Noah Davis",patientInitials:"ND",avatar:null,date:"15 June, 2024",time:"01:00PM - 02:00PM",treatment:"Heart check-up",doctor:"Dr. Robert Harris",status:"New"},{id:10,patientName:"Ava Johnson",patientInitials:"AJ",avatar:null,date:"16 June, 2024",time:"03:00PM - 04:00PM",treatment:"Physical therapy",doctor:"Dr. Michael Johnson",status:"Pending"},{id:11,patientName:"William Thompson",patientInitials:"WT",avatar:null,date:"17 June, 2024",time:"10:30AM - 11:30AM",treatment:"General consultation",doctor:"Dr. Michael Johnson",status:"Pending"},{id:12,patientName:"Mia Roberts",patientInitials:"MR",avatar:null,date:"18 June, 2024",time:"09:00AM - 10:00AM",treatment:"Neurological exam",doctor:"Dr. Emily Carter",status:"New"}],this.totalPages=Math.ceil(this.appointments.length/this.itemsPerPage),this.renderPage(1),this.renderPagination()}setupEventListeners(){this.paginationContainer&&this.paginationContainer.addEventListener("click",t=>{if(t.preventDefault(),t.target.classList.contains("page-link")||t.target.parentElement.classList.contains("page-link")){const e=(t.target.classList.contains("page-link")?t.target:t.target.parentElement).textContent.trim();if(e.includes("Previous"))this.currentPage>1&&this.goToPage(this.currentPage-1);else if(e.includes("Next"))this.currentPage<this.totalPages&&this.goToPage(this.currentPage+1);else{const i=parseInt(e);isNaN(i)||this.goToPage(i)}}}),this.tableBody&&this.tableBody.addEventListener("click",t=>{const a=t.target.closest(".btn");if(!a)return;const e=a.closest("tr");if(!e)return;const i=Array.from(this.tableBody.querySelectorAll("tr")).indexOf(e),n=(this.currentPage-1)*this.itemsPerPage+i,r=this.appointments[n];if(a.classList.contains("btn-sub-primary"))this.showAppointmentDetails(r);else if(a.classList.contains("btn-light"))this.editAppointment(r);else if(a.classList.contains("btn-sub-danger")){this.currentAppointmentId=n;const o=this.deleteModal.querySelector(".btn-danger"),l=o.cloneNode(!0);o.parentNode.replaceChild(l,o),l.addEventListener("click",()=>{this.deleteAppointment(this.currentAppointmentId)})}})}goToPage(t){t<1||t>this.totalPages||(this.currentPage=t,this.renderPage(t),this.updatePaginationActive())}renderPage(t){if(!this.tableBody)return;this.tableBody.innerHTML="";const a=(t-1)*this.itemsPerPage,e=Math.min(a+this.itemsPerPage,this.appointments.length);this.updateResultsInfo(a,e);for(let i=a;i<e;i++){const s=this.appointments[i],n=this.createAppointmentRow(s);this.tableBody.appendChild(n)}}createAppointmentRow(t){const a=document.createElement("tr");let e="";switch(t.status){case"New":e="bg-primary-subtle text-primary border border-primary-subtle";break;case"Pending":e="bg-light-subtle text-muted border border-dark-subtle";break;case"Confirmed":e="bg-success-subtle text-success border border-success-subtle";break;case"Completed":e="bg-secondary-subtle text-secondary border border-secondary-subtle";break;default:e="bg-light-subtle text-muted border border-dark-subtle"}let i="";return t.avatar?i=`<img src="${t.avatar}" loading="lazy" alt="" class="size-8 rounded-circle">`:i=`<div class="size-8 rounded-circle bg-light-subtle avatar text-muted fw-semibold fs-12">${t.patientInitials}</div>`,a.innerHTML=`
            <td>
                <div class="d-flex align-items-center gap-3">
                    ${i}
                    <h6 class="mb-0"><a href="#!" class="text-body">${t.patientName}</a></h6>
                </div>
            </td>
            <td>${t.date}</td>
            <td>${t.time}</td>
            <td>${t.treatment}</td>
            <td>${t.doctor}</td>
            <td>
                <span class="badge ${e}">${t.status}</span>
            </td>
            <td>
                <div class="d-flex align-items-center gap-2">
                    <button class="btn btn-sub-primary size-8 btn-icon" data-bs-toggle="modal" data-bs-target="#overviewModal"><i class="ri-eye-line"></i></button>
                    <button class="btn btn-light size-8 btn-icon"><i class="ri-pencil-line"></i></button>
                    <button class="btn btn-sub-danger size-8 btn-icon" data-bs-toggle="modal" data-bs-target="#deleteModal"><i class="ri-delete-bin-line"></i></button>
                </div>
            </td>
        `,a}renderPagination(){if(!this.paginationContainer)return;let t=this.paginationContainer.querySelector("ul");t||(t=document.createElement("ul"),t.className="pagination justify-content-center justify-content-md-end mb-0",this.paginationContainer.appendChild(t)),t.innerHTML="";const a=document.createElement("li");a.className=`page-item ${this.currentPage===1?"disabled":""}`,a.innerHTML='<a class="page-link" href="#!"><i data-lucide="chevron-left" class="size-4"></i> Previous</a>',t.appendChild(a);for(let i=1;i<=Math.min(this.totalPages,3);i++){const s=document.createElement("li");s.className=`page-item ${i===this.currentPage?"active":""}`,s.innerHTML=`<a class="page-link" href="#!">${i}</a>`,t.appendChild(s)}const e=document.createElement("li");e.className=`page-item ${this.currentPage===this.totalPages?"disabled":""}`,e.innerHTML='<a class="page-link" href="#!">Next <i data-lucide="chevron-right" class="size-4"></i></a>',t.appendChild(e),c({icons:u})}updatePaginationActive(){if(!this.paginationContainer)return;const t=this.paginationContainer.querySelector("ul");if(!t)return;const a=t.querySelector("li:first-child");a&&(this.currentPage===1?a.classList.add("disabled"):a.classList.remove("disabled")),t.querySelectorAll("li:not(:first-child):not(:last-child)").forEach((s,n)=>{n+1===this.currentPage?s.classList.add("active"):s.classList.remove("active")});const i=t.querySelector("li:last-child");i&&(this.currentPage===this.totalPages?i.classList.add("disabled"):i.classList.remove("disabled"))}updateResultsInfo(t,a){if(!this.resultsInfo)return;const e=t+1,i=a;this.resultsInfo.innerHTML=`Showing <b class="me-1">${e}-${i}</b>of<b class="ms-1">${this.appointments.length}</b> Results`}showAppointmentDetails(t){if(!this.overviewModal)return;const a=this.overviewModal.querySelector("#patientInfo"),e=this.overviewModal.querySelector("#overViewStatus"),i=this.overviewModal.querySelector("#doctorInfo");if(!a||!e||!i)return;switch(e.textContent=t.status,e.className="",t.status){case"New":e.className="badge bg-primary-subtle text-primary border border-primary-subtle";break;case"Pending":e.className="badge bg-light-subtle text-muted border border-dark-subtle";break;case"Confirmed":e.className="badge bg-success-subtle text-success border border-success-subtle";break;case"Completed":e.className="badge bg-secondary-subtle text-secondary border border-secondary-subtle";break}let s="";t.avatar?s=`<img src="${t.avatar}" loading="lazy" alt="" class="size-10 rounded-circle flex-shrink-0">`:s=`<div class="size-10 rounded-circle bg-light-subtle avatar text-muted fw-semibold fs-12">${t.patientInitials}</div>`,a.innerHTML=`
            ${s}
            <div class="flex-grow-1">
                <h6 class="mb-1">${t.patientName}</h6>
                <p class="text-muted">${t.treatment}</p>
            </div>
        `;const n=this.overviewModal.querySelectorAll(".modal-body > div")[1];if(n){const o=n.querySelector("h6"),l=n.querySelector("p");o&&(o.textContent=t.date),l&&(l.textContent=t.time)}const r=i.querySelector("h6");r&&(r.textContent=t.doctor)}deleteAppointment(t){if(this.appointments.splice(t,1),this.totalPages=Math.ceil(this.appointments.length/this.itemsPerPage),this.currentPage>this.totalPages&&(this.currentPage=this.totalPages),this.totalPages===0&&(this.totalPages=1),this.deleteModal&&typeof bootstrap<"u"){const a=window.bootstrap.Modal.getInstance(this.deleteModal);a&&a.hide()}this.renderPage(this.currentPage),this.renderPagination()}editAppointment(t){alert(`Editing appointment for ${t.patientName}`)}}document.addEventListener("DOMContentLoaded",()=>{const d=new m;window.appointmentManager=d});
