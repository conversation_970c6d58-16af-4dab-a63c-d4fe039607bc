import"../../admin.bundle-DI9_jvUJ.js";/* empty css                               */import"../../form-input-spin.init-BLcZ9-IP.js";import{S as c}from"../../swiper-bundle-BRj3juaA.js";import"../../main-Cyta4iCA.js";VirtualSelect.init({ele:"#categorySelect",options:[{label:"Fashion",value:"Fashion"},{label:"Fruits",value:"fruits"},{label:"Footwear",value:"Footwear"},{label:"Bags",value:"Bags"},{label:"Watch",value:"Watch"}],categorySelect:!0,allowNewOption:!0});VirtualSelect.init({ele:"#brandTypeSelect",options:[{label:"Gucci",value:"Gucci"},{label:"Rolex",value:"Rolex"},{label:"<PERSON>",value:"<PERSON> Klein"},{label:"<PERSON><PERSON>",value:"<PERSON><PERSON>"},{label:"Nike",value:"Nike"},{label:"Adidas",value:"Adidas"}],brandTypeSelect:!0,allowNewOption:!0});VirtualSelect.init({ele:"#sizeSelect",options:[{label:"XS",value:"XS"},{label:"S",value:"S"},{label:"M",value:"M"},{label:"L",value:"L"},{label:"XL",value:"XL"},{label:"2XL",value:"2XL"}],multiple:!0,showValueAsTags:!0});VirtualSelect.init({ele:"#colorsSelect",options:[{label:"Blue",value:"Blue"},{label:"Green",value:"Green"},{label:"Yellow",value:"Yellow"},{label:"Sky",value:"Sky"},{label:"Red",value:"Red"},{label:"Pink",value:"Pink"},{label:"Gray",value:"Gray"},{label:"Purple",value:"Purple"}],multiple:!0});new c(".productSlider",{pagination:{el:".swiper-pagination",clickable:!0}});document.addEventListener("DOMContentLoaded",function(){const e=document.querySelectorAll(".product-size a");e.forEach(l=>{l.addEventListener("click",function(i){i.preventDefault(),e.forEach(t=>{t.classList.remove("active","text-success"),t.classList.add("text-muted")}),this.classList.add("active","text-success"),this.classList.remove("text-muted")})})});document.addEventListener("DOMContentLoaded",function(){const e=document.getElementById("priceInput"),l=document.getElementById("discountInput"),i=document.getElementById("sellingPrice");function t(n){return n.replace(/[^0-9.]/g,"")}function a(){const n=parseFloat(e.value)||0,u=parseFloat(l.value)||0;if(u>100)return l.value=100,a();const o=n*(1-u/100);i.value=o.toFixed(2)}e.addEventListener("input",function(){this.value=t(this.value),a()}),l.addEventListener("input",function(){this.value=t(this.value),a()}),a()});
