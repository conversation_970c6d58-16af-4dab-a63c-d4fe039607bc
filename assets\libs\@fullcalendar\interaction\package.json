{"name": "@fullcalendar/interaction", "version": "6.1.17", "title": "FullCalendar Interaction Plugin", "description": "Calendar functionality for event drag-n-drop, event resizing, date clicking, and date selecting", "keywords": ["calendar", "event", "full-sized", "fullcalendar", "drag-n-drop", "resizing", "selecting"], "homepage": "https://fullcalendar.io/docs/editable", "peerDependencies": {"@fullcalendar/core": "~6.1.17"}, "type": "module", "bugs": "https://fullcalendar.io/reporting-bugs", "repository": {"type": "git", "url": "https://github.com/fullcalendar/fullcalendar.git", "directory": "packages/interaction"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://arshaw.com/"}, "copyright": "2024 <PERSON>", "types": "./index.d.ts", "main": "./index.cjs", "module": "./index.js", "unpkg": "./index.global.min.js", "jsdelivr": "./index.global.min.js", "exports": {"./package.json": "./package.json", "./index.cjs": "./index.cjs", "./index.js": "./index.js", ".": {"types": "./index.d.ts", "require": "./index.cjs", "import": "./index.js"}}, "sideEffects": false}