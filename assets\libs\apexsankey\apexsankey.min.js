!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e(require("@svgdotjs/svg.js")):"function"==typeof define&&define.amd?define(["@svgdotjs/svg.js"],e):(t="undefined"!=typeof globalThis?globalThis:t||self).ApexSankey=e(t.SVG)}(this,(function(t){"use strict";class e{constructor(t,e="apex"){this.canvas=t,this.prefix=e}getSvgString(){return this.canvas.svg().replace(/(<img [\w\W]+?)(>)/g,"$1 />").replace(/(<br)(>)/g,"$1 />").replace(/(<hr)(>)/g,"$1 />")}svgUrl(){const t=this.getSvgString(),e=new Blob([t],{type:"image/svg+xml;charset=utf-8"});return URL.createObjectURL(e)}triggerDownload(t,e){const n=document.createElement("a");n.href=t,n.download=e,document.body.appendChild(n),n.click(),document.body.removeChild(n)}exportToSVG(){this.triggerDownload(this.svgUrl(),`${this.prefix}-${(new Date).getTime()}.svg`)}}var n,r=(n={"À":"A","Á":"A","Â":"A","Ã":"A","Ä":"A","Å":"A","à":"a","á":"a","â":"a","ã":"a","ä":"a","å":"a","Ç":"C","ç":"c","Ð":"D","ð":"d","È":"E","É":"E","Ê":"E","Ë":"E","è":"e","é":"e","ê":"e","ë":"e","Ì":"I","Í":"I","Î":"I","Ï":"I","ì":"i","í":"i","î":"i","ï":"i","Ñ":"N","ñ":"n","Ò":"O","Ó":"O","Ô":"O","Õ":"O","Ö":"O","Ø":"O","ò":"o","ó":"o","ô":"o","õ":"o","ö":"o","ø":"o","Ù":"U","Ú":"U","Û":"U","Ü":"U","ù":"u","ú":"u","û":"u","ü":"u","Ý":"Y","ý":"y","ÿ":"y","Æ":"Ae","æ":"ae","Þ":"Th","þ":"th","ß":"ss","Ā":"A","Ă":"A","Ą":"A","ā":"a","ă":"a","ą":"a","Ć":"C","Ĉ":"C","Ċ":"C","Č":"C","ć":"c","ĉ":"c","ċ":"c","č":"c","Ď":"D","Đ":"D","ď":"d","đ":"d","Ē":"E","Ĕ":"E","Ė":"E","Ę":"E","Ě":"E","ē":"e","ĕ":"e","ė":"e","ę":"e","ě":"e","Ĝ":"G","Ğ":"G","Ġ":"G","Ģ":"G","ĝ":"g","ğ":"g","ġ":"g","ģ":"g","Ĥ":"H","Ħ":"H","ĥ":"h","ħ":"h","Ĩ":"I","Ī":"I","Ĭ":"I","Į":"I","İ":"I","ĩ":"i","ī":"i","ĭ":"i","į":"i","ı":"i","Ĵ":"J","ĵ":"j","Ķ":"K","ķ":"k","ĸ":"k","Ĺ":"L","Ļ":"L","Ľ":"L","Ŀ":"L","Ł":"L","ĺ":"l","ļ":"l","ľ":"l","ŀ":"l","ł":"l","Ń":"N","Ņ":"N","Ň":"N","Ŋ":"N","ń":"n","ņ":"n","ň":"n","ŋ":"n","Ō":"O","Ŏ":"O","Ő":"O","ō":"o","ŏ":"o","ő":"o","Ŕ":"R","Ŗ":"R","Ř":"R","ŕ":"r","ŗ":"r","ř":"r","Ś":"S","Ŝ":"S","Ş":"S","Š":"S","ś":"s","ŝ":"s","ş":"s","š":"s","Ţ":"T","Ť":"T","Ŧ":"T","ţ":"t","ť":"t","ŧ":"t","Ũ":"U","Ū":"U","Ŭ":"U","Ů":"U","Ű":"U","Ų":"U","ũ":"u","ū":"u","ŭ":"u","ů":"u","ű":"u","ų":"u","Ŵ":"W","ŵ":"w","Ŷ":"Y","ŷ":"y","Ÿ":"Y","Ź":"Z","Ż":"Z","Ž":"Z","ź":"z","ż":"z","ž":"z","Ĳ":"IJ","ĳ":"ij","Œ":"Oe","œ":"oe","ŉ":"'n","ſ":"s"},function(t){return null==n?void 0:n[t]}),o="object"==typeof global&&global&&global.Object===Object&&global,i="object"==typeof self&&self&&self.Object===Object&&self,u=o||i||Function("return this")(),a=u.Symbol;var c=Array.isArray,s=Object.prototype,f=s.hasOwnProperty,l=s.toString,d=a?a.toStringTag:void 0;var h=Object.prototype.toString;var p="[object Null]",v="[object Undefined]",g=a?a.toStringTag:void 0;function y(t){return null==t?void 0===t?v:p:g&&g in Object(t)?function(t){var e=f.call(t,d),n=t[d];try{t[d]=void 0;var r=!0}catch(i){}var o=l.call(t);return r&&(e?t[d]=n:delete t[d]),o}(t):function(t){return h.call(t)}(t)}function b(t){return null!=t&&"object"==typeof t}var _="[object Symbol]";function w(t){return"symbol"==typeof t||b(t)&&y(t)==_}var m=1/0,j=a?a.prototype:void 0,x=j?j.toString:void 0;function E(t){if("string"==typeof t)return t;if(c(t))return function(t,e){for(var n=-1,r=null==t?0:t.length,o=Array(r);++n<r;)o[n]=e(t[n],n,t);return o}(t,E)+"";if(w(t))return x?x.call(t):"";var e=t+"";return"0"==e&&1/t==-m?"-0":e}function O(t){return null==t?"":E(t)}var k=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,A=RegExp("[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]","g");var C=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g;var M=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/;var S="\\ud800-\\udfff",$="\\u2700-\\u27bf",L="a-z\\xdf-\\xf6\\xf8-\\xff",z="A-Z\\xc0-\\xd6\\xd8-\\xde",I="\\xac\\xb1\\xd7\\xf7\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf\\u2000-\\u206f \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",N="["+I+"]",F="\\d+",T="["+$+"]",P="["+L+"]",B="[^"+S+I+F+$+L+z+"]",D="(?:\\ud83c[\\udde6-\\uddff]){2}",U="[\\ud800-\\udbff][\\udc00-\\udfff]",W="["+z+"]",V="(?:"+P+"|"+B+")",R="(?:"+W+"|"+B+")",G="(?:['’](?:d|ll|m|re|s|t|ve))?",Z="(?:['’](?:D|LL|M|RE|S|T|VE))?",Y="(?:[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]|\\ud83c[\\udffb-\\udfff])?",H="[\\ufe0e\\ufe0f]?",q=H+Y+("(?:\\u200d(?:"+["[^"+S+"]",D,U].join("|")+")"+H+Y+")*"),X="(?:"+[T,D,U].join("|")+")"+q,J=RegExp([W+"?"+P+"+"+G+"(?="+[N,W,"$"].join("|")+")",R+"+"+Z+"(?="+[N,W+V,"$"].join("|")+")",W+"?"+V+"+"+G,W+"+"+Z,"\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])","\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",F,X].join("|"),"g");function K(t,e,n){return t=O(t),void 0===e?function(t){return M.test(t)}(t)?function(t){return t.match(J)||[]}(t):function(t){return t.match(C)||[]}(t):t.match(e)||[]}var Q=RegExp("['’]","g");var tt,et=(tt=function(t,e,n){return t+(n?"-":"")+e.toLowerCase()},function(t){return function(t,e,n,r){for(var o=-1,i=null==t?0:t.length;++o<i;)n=e(n,t[o],o,t);return n}(K(function(t){return(t=O(t))&&t.replace(k,r).replace(A,"")}(t).replace(Q,"")),tt,"")});const nt=(t="",e,n="")=>{const r=document.getElementById(t);e?null==r||r.setAttribute("style",e):null==r||r.removeAttribute("style"),(null==r?void 0:r.innerHTML.replaceAll("'",'"'))!==n.replaceAll("'",'"')&&r&&(r.innerHTML=n)},rt=(t={})=>{const e=[];for(const n in t){const r=`${et(n)}: ${t[n]};`;e.push(r)}return e.join(" ")};function ot(e,n,r,o){const i=new t.G;return i.attr({"data-parent":o,"data-self":r,transform:void 0!==e&&void 0!==n?`translate(${e}, ${n})`:void 0}),i}function it(){}const ut={export:"data:image/svg+xml,%3csvg%20fill='%23000000'%20width='20px'%20height='20px'%20viewBox='0%200%2024%2024'%20id='export-2'%20xmlns='http://www.w3.org/2000/svg'%20class='icon%20line'%3e%3cpolyline%20id='primary'%20points='15%203%2021%203%2021%209'%20style='fill:%20none;%20stroke:%20rgb(0,%200,%200);%20stroke-linecap:%20round;%20stroke-linejoin:%20round;%20stroke-width:%201.5;'%3e%3c/polyline%3e%3cpath%20id='primary-2'%20data-name='primary'%20d='M21,13v7a1,1,0,0,1-1,1H4a1,1,0,0,1-1-1V4A1,1,0,0,1,4,3h7'%20style='fill:%20none;%20stroke:%20rgb(0,%200,%200);%20stroke-linecap:%20round;%20stroke-linejoin:%20round;%20stroke-width:%201.5;'%3e%3c/path%3e%3cline%20id='primary-3'%20data-name='primary'%20x1='11'%20y1='13'%20x2='21'%20y2='3'%20style='fill:%20none;%20stroke:%20rgb(0,%200,%200);%20stroke-linecap:%20round;%20stroke-linejoin:%20round;%20stroke-width:%201.5;'%3e%3c/line%3e%3c/svg%3e","fit-screen":"data:image/svg+xml,%3csvg%20width='20px'%20height='20px'%20viewBox='0%200%2032%2032'%20id='icon'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpolygon%20points='8%202%202%202%202%208%204%208%204%204%208%204%208%202'/%3e%3cpolygon%20points='24%202%2030%202%2030%208%2028%208%2028%204%2024%204%2024%202'/%3e%3cpolygon%20points='8%2030%202%2030%202%2024%204%2024%204%2028%208%2028%208%2030'/%3e%3cpolygon%20points='24%2030%2030%2030%2030%2024%2028%2024%2028%2028%2024%2028%2024%2030'/%3e%3cpath%20d='M24,24H8a2.0023,2.0023,0,0,1-2-2V10A2.0023,2.0023,0,0,1,8,8H24a2.0023,2.0023,0,0,1,2,2V22A2.0023,2.0023,0,0,1,24,24ZM8,10V22H24V10Z'/%3e%3crect%20fill='none'%20width='32'%20height='32'/%3e%3c/svg%3e","zoom-in":"data:image/svg+xml,%3csvg%20width='20px'%20height='20px'%20viewBox='0%200%2032%2032'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20xmlns:sketch='http://www.bohemiancoding.com/sketch/ns'%3e%3cg%20stroke='none'%20stroke-width='1'%20fill='none'%20fill-rule='evenodd'%3e%3cg%20transform='translate(-308.000000,%20-1139.000000)'%20fill='%23000000'%3e%3cpath%20d='M321.46,1163.45%20C315.17,1163.45%20310.07,1158.44%20310.07,1152.25%20C310.07,1146.06%20315.17,1141.04%20321.46,1141.04%20C327.75,1141.04%20332.85,1146.06%20332.85,1152.25%20C332.85,1158.44%20327.75,1163.45%20321.46,1163.45%20L321.46,1163.45%20Z%20M339.688,1169.25%20L331.429,1161.12%20C333.592,1158.77%20334.92,1155.67%20334.92,1152.25%20C334.92,1144.93%20328.894,1139%20321.46,1139%20C314.026,1139%20308,1144.93%20308,1152.25%20C308,1159.56%20314.026,1165.49%20321.46,1165.49%20C324.672,1165.49%20327.618,1164.38%20329.932,1162.53%20L338.225,1170.69%20C338.629,1171.09%20339.284,1171.09%20339.688,1170.69%20C340.093,1170.3%20340.093,1169.65%20339.688,1169.25%20L339.688,1169.25%20Z%20M326.519,1151.41%20L322.522,1151.41%20L322.522,1147.41%20C322.522,1146.85%20322.075,1146.41%20321.523,1146.41%20C320.972,1146.41%20320.524,1146.85%20320.524,1147.41%20L320.524,1151.41%20L316.529,1151.41%20C315.978,1151.41%20315.53,1151.59%20315.53,1152.14%20C315.53,1152.7%20315.978,1153.41%20316.529,1153.41%20L320.524,1153.41%20L320.524,1157.41%20C320.524,1157.97%20320.972,1158.41%20321.523,1158.41%20C322.075,1158.41%20322.522,1157.97%20322.522,1157.41%20L322.522,1153.41%20L326.519,1153.41%20C327.07,1153.41%20327.518,1152.96%20327.518,1152.41%20C327.518,1151.86%20327.07,1151.41%20326.519,1151.41%20L326.519,1151.41%20Z'%20/%3e%3c/g%3e%3c/g%3e%3c/svg%3e","zoom-out":"data:image/svg+xml,%3csvg%20width='20px'%20height='20px'%20viewBox='0%200%2032%2032'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%3e%3cg%20stroke='none'%20stroke-width='1'%20fill='none'%20fill-rule='evenodd'%20%3e%3cg%20transform='translate(-360.000000,%20-1139.000000)'%20fill='%23000000'%3e%3cpath%20d='M373.46,1163.45%20C367.17,1163.45%20362.071,1158.44%20362.071,1152.25%20C362.071,1146.06%20367.17,1141.04%20373.46,1141.04%20C379.75,1141.04%20384.85,1146.06%20384.85,1152.25%20C384.85,1158.44%20379.75,1163.45%20373.46,1163.45%20L373.46,1163.45%20Z%20M391.688,1169.25%20L383.429,1161.12%20C385.592,1158.77%20386.92,1155.67%20386.92,1152.25%20C386.92,1144.93%20380.894,1139%20373.46,1139%20C366.026,1139%20360,1144.93%20360,1152.25%20C360,1159.56%20366.026,1165.49%20373.46,1165.49%20C376.672,1165.49%20379.618,1164.38%20381.932,1162.53%20L390.225,1170.69%20C390.629,1171.09%20391.284,1171.09%20391.688,1170.69%20C392.093,1170.3%20392.093,1169.65%20391.688,1169.25%20L391.688,1169.25%20Z%20M378.689,1151.41%20L368.643,1151.41%20C368.102,1151.41%20367.663,1151.84%20367.663,1152.37%20C367.663,1152.9%20368.102,1153.33%20368.643,1153.33%20L378.689,1153.33%20C379.23,1153.33%20379.669,1152.9%20379.669,1152.37%20C379.669,1151.84%20379.23,1151.41%20378.689,1151.41%20L378.689,1151.41%20Z'%20/%3e%3c/g%3e%3c/g%3e%3c/svg%3e"};class at{constructor(t,n,r="apex"){this.element=t,this.canvas=n,this.prefix=r,this.export=new e(n,r)}createToolbarItem(t,e){const n=document.createElement("div"),r=new Image;r.src=e,n.id=t,n.append(r);const o=rt({alignItems:"center",backgroundColor:"#FFFFFF",border:"1px solid #BCBCBC",cursor:"pointer",display:"flex",height:"30px",justifyContent:"center",width:"30px"});return n.setAttribute("style",o),n}render({enableExport:t=!1,enableFitscreen:e=!1,enableZoom:n=!1,onFitscreen:r=it,onZoom:o=it}){var i;const u=document.createElement("div");u.id="toolbar";const a=rt({display:"flex",gap:"5px",position:"absolute",right:"10px",top:"10px"});if(u.setAttribute("style",a),t){const t=this.createToolbarItem("export",ut.export);t.addEventListener("click",(()=>{this.export.exportToSVG()})),u.append(t)}if(n){const t=this.createToolbarItem("zoom-in",ut["zoom-in"]),e=this.createToolbarItem("zoom-out",ut["zoom-out"]);t.addEventListener("click",(()=>{o(.1)})),e.addEventListener("click",(()=>{o(-.1)})),u.append(t,e)}if(e){const t=this.createToolbarItem("fit-screen",ut["fit-screen"]);t.addEventListener("click",(()=>{r()})),u.append(t)}null==(i=this.element)||i.append(u)}}function ct(t,e){if(null===e.w)return 0;if(null===e.v)return 0;if(e.w===e.v)return Math.PI/2;const n=t.node(e.v),r=t.node(e.w);return Math.atan2(r.y-n.y,r.x-n.x)}function st(t,e){return t===e||t!=t&&e!=e}function ft(t,e){for(var n=t.length;n--;)if(st(t[n][0],e))return n;return-1}var lt=Array.prototype.splice;function dt(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}function ht(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}dt.prototype.clear=function(){this.__data__=[],this.size=0},dt.prototype.delete=function(t){var e=this.__data__,n=ft(e,t);return!(n<0)&&(n==e.length-1?e.pop():lt.call(e,n,1),--this.size,!0)},dt.prototype.get=function(t){var e=this.__data__,n=ft(e,t);return n<0?void 0:e[n][1]},dt.prototype.has=function(t){return ft(this.__data__,t)>-1},dt.prototype.set=function(t,e){var n=this.__data__,r=ft(n,t);return r<0?(++this.size,n.push([t,e])):n[r][1]=e,this};var pt="[object AsyncFunction]",vt="[object Function]",gt="[object GeneratorFunction]",yt="[object Proxy]";function bt(t){if(!ht(t))return!1;var e=y(t);return e==vt||e==gt||e==pt||e==yt}var _t,wt=u["__core-js_shared__"],mt=(_t=/[^.]+$/.exec(wt&&wt.keys&&wt.keys.IE_PROTO||""))?"Symbol(src)_1."+_t:"";var jt=Function.prototype.toString;function xt(t){if(null!=t){try{return jt.call(t)}catch(e){}try{return t+""}catch(e){}}return""}var Et=/^\[object .+?Constructor\]$/,Ot=Function.prototype,kt=Object.prototype,At=Ot.toString,Ct=kt.hasOwnProperty,Mt=RegExp("^"+At.call(Ct).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function St(t){return!(!ht(t)||(e=t,mt&&mt in e))&&(bt(t)?Mt:Et).test(xt(t));var e}function $t(t,e){var n=function(t,e){return null==t?void 0:t[e]}(t,e);return St(n)?n:void 0}var Lt=$t(u,"Map"),zt=$t(Object,"create");var It=Object.prototype.hasOwnProperty;var Nt=Object.prototype.hasOwnProperty;function Ft(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}function Tt(t,e){var n,r,o=t.__data__;return("string"==(r=typeof(n=e))||"number"==r||"symbol"==r||"boolean"==r?"__proto__"!==n:null===n)?o["string"==typeof e?"string":"hash"]:o.map}function Pt(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}Ft.prototype.clear=function(){this.__data__=zt?zt(null):{},this.size=0},Ft.prototype.delete=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e},Ft.prototype.get=function(t){var e=this.__data__;if(zt){var n=e[t];return"__lodash_hash_undefined__"===n?void 0:n}return It.call(e,t)?e[t]:void 0},Ft.prototype.has=function(t){var e=this.__data__;return zt?void 0!==e[t]:Nt.call(e,t)},Ft.prototype.set=function(t,e){var n=this.__data__;return this.size+=this.has(t)?0:1,n[t]=zt&&void 0===e?"__lodash_hash_undefined__":e,this},Pt.prototype.clear=function(){this.size=0,this.__data__={hash:new Ft,map:new(Lt||dt),string:new Ft}},Pt.prototype.delete=function(t){var e=Tt(this,t).delete(t);return this.size-=e?1:0,e},Pt.prototype.get=function(t){return Tt(this,t).get(t)},Pt.prototype.has=function(t){return Tt(this,t).has(t)},Pt.prototype.set=function(t,e){var n=Tt(this,t),r=n.size;return n.set(t,e),this.size+=n.size==r?0:1,this};function Bt(t){var e=this.__data__=new dt(t);this.size=e.size}Bt.prototype.clear=function(){this.__data__=new dt,this.size=0},Bt.prototype.delete=function(t){var e=this.__data__,n=e.delete(t);return this.size=e.size,n},Bt.prototype.get=function(t){return this.__data__.get(t)},Bt.prototype.has=function(t){return this.__data__.has(t)},Bt.prototype.set=function(t,e){var n=this.__data__;if(n instanceof dt){var r=n.__data__;if(!Lt||r.length<199)return r.push([t,e]),this.size=++n.size,this;n=this.__data__=new Pt(r)}return n.set(t,e),this.size=n.size,this};function Dt(t){var e=-1,n=null==t?0:t.length;for(this.__data__=new Pt;++e<n;)this.add(t[e])}function Ut(t,e){for(var n=-1,r=null==t?0:t.length;++n<r;)if(e(t[n],n,t))return!0;return!1}Dt.prototype.add=Dt.prototype.push=function(t){return this.__data__.set(t,"__lodash_hash_undefined__"),this},Dt.prototype.has=function(t){return this.__data__.has(t)};var Wt=1,Vt=2;function Rt(t,e,n,r,o,i){var u=n&Wt,a=t.length,c=e.length;if(a!=c&&!(u&&c>a))return!1;var s=i.get(t),f=i.get(e);if(s&&f)return s==e&&f==t;var l=-1,d=!0,h=n&Vt?new Dt:void 0;for(i.set(t,e),i.set(e,t);++l<a;){var p=t[l],v=e[l];if(r)var g=u?r(v,p,l,e,t,i):r(p,v,l,t,e,i);if(void 0!==g){if(g)continue;d=!1;break}if(h){if(!Ut(e,(function(t,e){if(u=e,!h.has(u)&&(p===t||o(p,t,n,r,i)))return h.push(e);var u}))){d=!1;break}}else if(p!==v&&!o(p,v,n,r,i)){d=!1;break}}return i.delete(t),i.delete(e),d}var Gt=u.Uint8Array;function Zt(t){var e=-1,n=Array(t.size);return t.forEach((function(t,r){n[++e]=[r,t]})),n}function Yt(t){var e=-1,n=Array(t.size);return t.forEach((function(t){n[++e]=t})),n}var Ht=1,qt=2,Xt="[object Boolean]",Jt="[object Date]",Kt="[object Error]",Qt="[object Map]",te="[object Number]",ee="[object RegExp]",ne="[object Set]",re="[object String]",oe="[object Symbol]",ie="[object ArrayBuffer]",ue="[object DataView]",ae=a?a.prototype:void 0,ce=ae?ae.valueOf:void 0;var se=Object.prototype.propertyIsEnumerable,fe=Object.getOwnPropertySymbols,le=fe?function(t){return null==t?[]:(t=Object(t),function(t,e){for(var n=-1,r=null==t?0:t.length,o=0,i=[];++n<r;){var u=t[n];e(u,n,t)&&(i[o++]=u)}return i}(fe(t),(function(e){return se.call(t,e)})))}:function(){return[]};function de(t){return b(t)&&"[object Arguments]"==y(t)}var he=Object.prototype,pe=he.hasOwnProperty,ve=he.propertyIsEnumerable,ge=de(function(){return arguments}())?de:function(t){return b(t)&&pe.call(t,"callee")&&!ve.call(t,"callee")};var ye="object"==typeof exports&&exports&&!exports.nodeType&&exports,be=ye&&"object"==typeof module&&module&&!module.nodeType&&module,_e=be&&be.exports===ye?u.Buffer:void 0,we=(_e?_e.isBuffer:void 0)||function(){return!1},me=9007199254740991,je=/^(?:0|[1-9]\d*)$/;function xe(t,e){var n=typeof t;return!!(e=null==e?me:e)&&("number"==n||"symbol"!=n&&je.test(t))&&t>-1&&t%1==0&&t<e}var Ee=9007199254740991;function Oe(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=Ee}var ke={};ke["[object Float32Array]"]=ke["[object Float64Array]"]=ke["[object Int8Array]"]=ke["[object Int16Array]"]=ke["[object Int32Array]"]=ke["[object Uint8Array]"]=ke["[object Uint8ClampedArray]"]=ke["[object Uint16Array]"]=ke["[object Uint32Array]"]=!0,ke["[object Arguments]"]=ke["[object Array]"]=ke["[object ArrayBuffer]"]=ke["[object Boolean]"]=ke["[object DataView]"]=ke["[object Date]"]=ke["[object Error]"]=ke["[object Function]"]=ke["[object Map]"]=ke["[object Number]"]=ke["[object Object]"]=ke["[object RegExp]"]=ke["[object Set]"]=ke["[object String]"]=ke["[object WeakMap]"]=!1;var Ae,Ce="object"==typeof exports&&exports&&!exports.nodeType&&exports,Me=Ce&&"object"==typeof module&&module&&!module.nodeType&&module,Se=Me&&Me.exports===Ce&&o.process,$e=function(){try{var t=Me&&Me.require&&Me.require("util").types;return t||Se&&Se.binding&&Se.binding("util")}catch(e){}}(),Le=$e&&$e.isTypedArray,ze=Le?(Ae=Le,function(t){return Ae(t)}):function(t){return b(t)&&Oe(t.length)&&!!ke[y(t)]},Ie=Object.prototype.hasOwnProperty;function Ne(t,e){var n=c(t),r=!n&&ge(t),o=!n&&!r&&we(t),i=!n&&!r&&!o&&ze(t),u=n||r||o||i,a=u?function(t,e){for(var n=-1,r=Array(t);++n<t;)r[n]=e(n);return r}(t.length,String):[],s=a.length;for(var f in t)!Ie.call(t,f)||u&&("length"==f||o&&("offset"==f||"parent"==f)||i&&("buffer"==f||"byteLength"==f||"byteOffset"==f)||xe(f,s))||a.push(f);return a}var Fe=Object.prototype;var Te=function(t,e){return function(n){return t(e(n))}}(Object.keys,Object),Pe=Object.prototype.hasOwnProperty;function Be(t){if(n=(e=t)&&e.constructor,e!==("function"==typeof n&&n.prototype||Fe))return Te(t);var e,n,r=[];for(var o in Object(t))Pe.call(t,o)&&"constructor"!=o&&r.push(o);return r}function De(t){return null!=(e=t)&&Oe(e.length)&&!bt(e)?Ne(t):Be(t);var e}function Ue(t){return function(t,e,n){var r=e(t);return c(t)?r:function(t,e){for(var n=-1,r=e.length,o=t.length;++n<r;)t[o+n]=e[n];return t}(r,n(t))}(t,De,le)}var We=1,Ve=Object.prototype.hasOwnProperty;var Re=$t(u,"DataView"),Ge=$t(u,"Promise"),Ze=$t(u,"Set"),Ye=$t(u,"WeakMap"),He="[object Map]",qe="[object Promise]",Xe="[object Set]",Je="[object WeakMap]",Ke="[object DataView]",Qe=xt(Re),tn=xt(Lt),en=xt(Ge),nn=xt(Ze),rn=xt(Ye),on=y;(Re&&on(new Re(new ArrayBuffer(1)))!=Ke||Lt&&on(new Lt)!=He||Ge&&on(Ge.resolve())!=qe||Ze&&on(new Ze)!=Xe||Ye&&on(new Ye)!=Je)&&(on=function(t){var e=y(t),n="[object Object]"==e?t.constructor:void 0,r=n?xt(n):"";if(r)switch(r){case Qe:return Ke;case tn:return He;case en:return qe;case nn:return Xe;case rn:return Je}return e});var un=1,an="[object Arguments]",cn="[object Array]",sn="[object Object]",fn=Object.prototype.hasOwnProperty;function ln(t,e,n,r,o,i){var u=c(t),a=c(e),s=u?cn:on(t),f=a?cn:on(e),l=(s=s==an?sn:s)==sn,d=(f=f==an?sn:f)==sn,h=s==f;if(h&&we(t)){if(!we(e))return!1;u=!0,l=!1}if(h&&!l)return i||(i=new Bt),u||ze(t)?Rt(t,e,n,r,o,i):function(t,e,n,r,o,i,u){switch(n){case ue:if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)return!1;t=t.buffer,e=e.buffer;case ie:return!(t.byteLength!=e.byteLength||!i(new Gt(t),new Gt(e)));case Xt:case Jt:case te:return st(+t,+e);case Kt:return t.name==e.name&&t.message==e.message;case ee:case re:return t==e+"";case Qt:var a=Zt;case ne:var c=r&Ht;if(a||(a=Yt),t.size!=e.size&&!c)return!1;var s=u.get(t);if(s)return s==e;r|=qt,u.set(t,e);var f=Rt(a(t),a(e),r,o,i,u);return u.delete(t),f;case oe:if(ce)return ce.call(t)==ce.call(e)}return!1}(t,e,s,n,r,o,i);if(!(n&un)){var p=l&&fn.call(t,"__wrapped__"),v=d&&fn.call(e,"__wrapped__");if(p||v){var g=p?t.value():t,y=v?e.value():e;return i||(i=new Bt),o(g,y,n,r,i)}}return!!h&&(i||(i=new Bt),function(t,e,n,r,o,i){var u=n&We,a=Ue(t),c=a.length;if(c!=Ue(e).length&&!u)return!1;for(var s=c;s--;){var f=a[s];if(!(u?f in e:Ve.call(e,f)))return!1}var l=i.get(t),d=i.get(e);if(l&&d)return l==e&&d==t;var h=!0;i.set(t,e),i.set(e,t);for(var p=u;++s<c;){var v=t[f=a[s]],g=e[f];if(r)var y=u?r(g,v,f,e,t,i):r(v,g,f,t,e,i);if(!(void 0===y?v===g||o(v,g,n,r,i):y)){h=!1;break}p||(p="constructor"==f)}if(h&&!p){var b=t.constructor,_=e.constructor;b==_||!("constructor"in t)||!("constructor"in e)||"function"==typeof b&&b instanceof b&&"function"==typeof _&&_ instanceof _||(h=!1)}return i.delete(t),i.delete(e),h}(t,e,n,r,o,i))}function dn(t,e,n,r,o){return t===e||(null==t||null==e||!b(t)&&!b(e)?t!=t&&e!=e:ln(t,e,n,r,dn,o))}var hn=1,pn=2;function vn(t){return t==t&&!ht(t)}function gn(t,e){return function(n){return null!=n&&(n[t]===e&&(void 0!==e||t in Object(n)))}}function yn(t){var e=function(t){for(var e=De(t),n=e.length;n--;){var r=e[n],o=t[r];e[n]=[r,o,vn(o)]}return e}(t);return 1==e.length&&e[0][2]?gn(e[0][0],e[0][1]):function(n){return n===t||function(t,e,n,r){var o=n.length,i=o;if(null==t)return!i;for(t=Object(t);o--;){var u=n[o];if(u[2]?u[1]!==t[u[0]]:!(u[0]in t))return!1}for(;++o<i;){var a=(u=n[o])[0],c=t[a],s=u[1];if(u[2]){if(void 0===c&&!(a in t))return!1}else{var f=new Bt;if(!dn(s,c,hn|pn,r,f))return!1}}return!0}(n,0,e)}}var bn=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,_n=/^\w*$/;function wn(t,e){if(c(t))return!1;var n=typeof t;return!("number"!=n&&"symbol"!=n&&"boolean"!=n&&null!=t&&!w(t))||(_n.test(t)||!bn.test(t)||null!=e&&t in Object(e))}var mn="Expected a function";function jn(t,e){if("function"!=typeof t||null!=e&&"function"!=typeof e)throw new TypeError(mn);var n=function(){var r=arguments,o=e?e.apply(this,r):r[0],i=n.cache;if(i.has(o))return i.get(o);var u=t.apply(this,r);return n.cache=i.set(o,u)||i,u};return n.cache=new(jn.Cache||Pt),n}jn.Cache=Pt;var xn=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,En=/\\(\\)?/g,On=function(t){var e=jn(t,(function(t){return 500===n.size&&n.clear(),t})),n=e.cache;return e}((function(t){var e=[];return 46===t.charCodeAt(0)&&e.push(""),t.replace(xn,(function(t,n,r,o){e.push(r?o.replace(En,"$1"):n||t)})),e}));function kn(t,e){return c(t)?t:wn(t,e)?[t]:On(O(t))}var An=1/0;function Cn(t){if("string"==typeof t||w(t))return t;var e=t+"";return"0"==e&&1/t==-An?"-0":e}function Mn(t,e){for(var n=0,r=(e=kn(e,t)).length;null!=t&&n<r;)t=t[Cn(e[n++])];return n&&n==r?t:void 0}function Sn(t,e){return null!=t&&e in Object(t)}function $n(t,e){return null!=t&&function(t,e,n){for(var r=-1,o=(e=kn(e,t)).length,i=!1;++r<o;){var u=Cn(e[r]);if(!(i=null!=t&&n(t,u)))break;t=t[u]}return i||++r!=o?i:!!(o=null==t?0:t.length)&&Oe(o)&&xe(u,o)&&(c(t)||ge(t))}(t,e,Sn)}var Ln=1,zn=2;function In(t,e){return wn(t)&&vn(e)?gn(Cn(t),e):function(n){var r=function(t,e,n){var r=null==t?void 0:Mn(t,e);return void 0===r?n:r}(n,t);return void 0===r&&r===e?$n(n,t):dn(e,r,Ln|zn)}}function Nn(t){return t}function Fn(t){return wn(t)?(e=Cn(t),function(t){return null==t?void 0:t[e]}):function(t){return function(e){return Mn(e,t)}}(t);var e}function Tn(t,e){return t&&t.length?function(t,e){for(var n,r=-1,o=t.length;++r<o;){var i=e(t[r]);void 0!==i&&(n=void 0===n?i:n+i)}return n}(t,"function"==typeof(n=e)?n:null==n?Nn:"object"==typeof n?c(n)?In(n[0],n[1]):yn(n):Fn(n)):0;var n}function Pn(t,e){const n=t.inEdges(e),r=t.outEdges(e),o=new Map;[...n,...r].forEach((t=>{o.has(t.name)||o.set(t.name,[]),o.get(t.name).push(t)}));const i=new Map(Array.from(o.entries()).map((([n,r])=>{const o=Tn(r,(e=>t.edge(e).value));return[n,Tn(r,(n=>t.edge(n).value*function(n){if(n.v===e)return t.node(n.w).y;if(n.w===e)return t.node(n.v).y;throw new Error}(n)))/o]}))),u=Array.from(i.keys());return u.sort(),u}function Bn(t,e=!0){return function(n,r){const o=ct(t,n),i=ct(t,r),u=e?1:-1;return n.v===r.v&&n.w===r.w?"number"==typeof n.name&&"number"==typeof r.name?n.name-r.name:"string"==typeof n.name&&"string"==typeof r.name?n.name.localeCompare(r.name):0:Math.abs(o-i)<.001?n.v&&n.w&&r.v&&r.w?n.w===r.w?-1*(t.node(n.v).y-t.node(r.v).y):n.v===r.v?-1*(t.node(n.w).y-t.node(r.w).y):0:0:u*(o-i)}}function Dn(t,e,n=!0){return function(r,o){if(r.name!==o.name)return e.indexOf(r.name)-e.indexOf(o.name);const i=ct(t,r),u=ct(t,o),a=n?1:-1;return Math.abs(i-u)<.001?r.v&&r.w&&o.v&&o.w?r.w===o.w?a*(i>0?-1:1)*(t.node(r.v).y-t.node(o.v).y):r.v===o.v?a*(i>0?-1:1)*(t.node(r.w).y-t.node(o.w).y):0:0:a*(i-u)}}function Un(t,e){let n=null;for(let r=0;r<t.length;++r)if(e(t[r])){n=r;break}return n}function Wn(t,e){if(0===t.length)return;let n,r=0;for(let i=t.length-1;i>=0;--i)n=t[i].dy/2,t[i][e]-n<r&&(t[i][e]=Math.min(t[i].Rmax,r+n)),r=t[i][e]+n;let o=t[0].Rmax+t[0].dy/2;for(let i=0;i<t.length;++i)n=t[i].dy/2,t[i][e]+n>o&&(t[i][e]=Math.max(n,o-n)),o=t[i][e]-n}function Vn(t){return function(e){!function(t,e){t.nodes().forEach((n=>{const r=t.node(n);let o=0,i=0;r.outgoing.forEach((n=>{const i=t.edge(n);i.x0=r.x+e.nodeWidth/2,i.y0=r.y+o+i.dy/2,i.d0=r.direction||"r",o+=i.dy})),r.incoming.forEach((n=>{const o=t.edge(n);o.x1=r.x-e.nodeWidth/2,o.y1=r.y+i+o.dy/2,o.d1=r.direction||"r",i+=o.dy}))}))}(e,t),function(t){t.nodes().forEach((e=>{const n=t.node(e),r=n.outgoing.map((e=>t.edge(e))),o=n.incoming.map((e=>t.edge(e)));r.sort(((t,e)=>t.y0-e.y0)),o.sort(((t,e)=>t.y1-e.y1)),Rn(r,"r0"),Rn(o,"r1")}))}(e);const n=[];return e.edges().forEach((t=>{const r=e.edge(t);r.id=`${t.v}-${t.w}-${t.name}`,n.push(r)})),n}}function Rn(t,e){t.forEach((t=>{t.Rmax=function(t){const e=t.x1-t.x0,n=t.y1-t.y0;return t.d0!==t.d1?Math.abs(n)/2.1:0!==n?(e*e+n*n)/Math.abs(4*n):1/0}(t),t[e]=Math.max(t.dy/2,t.d0===t.d1?.6*t.Rmax:5+t.dy/2)}));let n=Un(t,"r0"===e?t=>t.y1>t.y0:t=>t.y0>t.y1);if(null===n&&(n=t.length),Wn(t.slice(n),e),n>0){const r=[];for(let e=n-1;e>=0;e--)r.push(t[e]);Wn(r,e)}}function Gn(t,e,n,r,o){if(0===r.length)return;const i=r.map((n=>{const r=`__${e.v}_${e.w}_${n}`;return t.setNode(r,{data:null,direction:o,dummy:!0,rank:n}),r})),u=[e.v,...i,e.w];u.forEach(((r,o)=>{o+1<u.length&&t.setEdge(u[o],u[o+1],{data:t.edge(e).data,origEdge:e,origLabel:n,source:t.node(e.v),target:t.node(e.w),value:t.edge(e).value},e.name)})),t.removeEdge(e)}function Zn(t,e,n){return function(t,e,n){let r,o,i;e.length>n.length?(r=e,i=n):(r=n,i=e);const u=[];r.forEach((e=>{i.forEach(((n,r)=>{(t.hasEdge(e,n)||t.hasEdge(n,e))&&u.push(r)}))}));let a=1;for(;a<o;)a*=2;const c=2*a-1;a-=1;const s=new Array(c);for(let l=0;l<c;l++)s[l]=0;let f=0;return u.forEach((t=>{let e=t+a;for(s[e]++;e>0;)e%2&&(f+=s[e+1]),e=Math.floor((e-1)/2),s[e]++})),f}(t,e,n)}var Yn,Hn,qn,Xn,Jn,Kn,Qn,tr,er,nr,rr,or,ir,ur,ar,cr,sr,fr,lr,dr,hr,pr,vr,gr,yr,br,_r,wr,mr,jr,xr,Er,Or,kr,Ar,Cr,Mr,Sr,$r,Lr,zr,Ir,Nr,Fr,Tr,Pr,Br,Dr,Ur,Wr,Vr,Rr,Gr,Zr,Yr,Hr,qr,Xr,Jr,Kr,Qr,to,eo,no,ro,oo,io,uo,ao,co,so,fo,lo,ho,po,vo,go,yo,bo,_o,wo,mo,jo,xo,Eo,Oo,ko,Ao,Co,Mo,So,$o,Lo,zo,Io,No,Fo,To,Po,Bo,Do,Uo,Wo,Vo,Ro,Go,Zo,Yo,Ho="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function qo(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}function Xo(){if(Xn)return qn;return Xn=1,qn=function(t,e){return t===e||t!=t&&e!=e}}function Jo(){if(Kn)return Jn;Kn=1;var t=Xo();return Jn=function(e,n){for(var r=e.length;r--;)if(t(e[r][0],n))return r;return-1}}function Ko(){if(cr)return ar;cr=1;var t=Hn?Yn:(Hn=1,Yn=function(){this.__data__=[],this.size=0}),e=function(){if(tr)return Qn;tr=1;var t=Jo(),e=Array.prototype.splice;return Qn=function(n){var r=this.__data__,o=t(r,n);return!(o<0||(o==r.length-1?r.pop():e.call(r,o,1),--this.size,0))}}(),n=function(){if(nr)return er;nr=1;var t=Jo();return er=function(e){var n=this.__data__,r=t(n,e);return r<0?void 0:n[r][1]}}(),r=function(){if(or)return rr;or=1;var t=Jo();return rr=function(e){return t(this.__data__,e)>-1}}(),o=function(){if(ur)return ir;ur=1;var t=Jo();return ir=function(e,n){var r=this.__data__,o=t(r,e);return o<0?(++this.size,r.push([e,n])):r[o][1]=n,this}}();function i(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}return i.prototype.clear=t,i.prototype.delete=e,i.prototype.get=n,i.prototype.has=r,i.prototype.set=o,ar=i}function Qo(){if(br)return yr;br=1;var t="object"==typeof Ho&&Ho&&Ho.Object===Object&&Ho;return yr=t}function ti(){if(wr)return _r;wr=1;var t=Qo(),e="object"==typeof self&&self&&self.Object===Object&&self,n=t||e||Function("return this")();return _r=n}function ei(){if(jr)return mr;jr=1;var t=ti().Symbol;return mr=t}function ni(){if(Cr)return Ar;Cr=1;var t=ei(),e=function(){if(Er)return xr;Er=1;var t=ei(),e=Object.prototype,n=e.hasOwnProperty,r=e.toString,o=t?t.toStringTag:void 0;return xr=function(t){var e=n.call(t,o),i=t[o];try{t[o]=void 0;var u=!0}catch(c){}var a=r.call(t);return u&&(e?t[o]=i:delete t[o]),a}}(),n=function(){if(kr)return Or;kr=1;var t=Object.prototype.toString;return Or=function(e){return t.call(e)}}(),r=t?t.toStringTag:void 0;return Ar=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":r&&r in Object(t)?e(t):n(t)}}function ri(){if(Sr)return Mr;return Sr=1,Mr=function(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}}function oi(){if(Lr)return $r;Lr=1;var t=ni(),e=ri();return $r=function(n){if(!e(n))return!1;var r=t(n);return"[object Function]"==r||"[object GeneratorFunction]"==r||"[object AsyncFunction]"==r||"[object Proxy]"==r}}function ii(){if(Fr)return Nr;Fr=1;var t=function(){if(Ir)return zr;Ir=1;var t=ti()["__core-js_shared__"];return zr=t}(),e=function(){var e=/[^.]+$/.exec(t&&t.keys&&t.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();return Nr=function(t){return!!e&&e in t},Nr}function ui(){if(Pr)return Tr;Pr=1;var t=Function.prototype.toString;return Tr=function(e){if(null!=e){try{return t.call(e)}catch(n){}try{return e+""}catch(n){}}return""},Tr}function ai(){if(Rr)return Vr;Rr=1;var t=function(){if(Dr)return Br;Dr=1;var t=oi(),e=ii(),n=ri(),r=ui(),o=/^\[object .+?Constructor\]$/,i=Function.prototype,u=Object.prototype,a=i.toString,c=u.hasOwnProperty,s=RegExp("^"+a.call(c).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");return Br=function(i){return!(!n(i)||e(i))&&(t(i)?s:o).test(r(i))}}(),e=(Wr||(Wr=1,Ur=function(t,e){return null==t?void 0:t[e]}),Ur);return Vr=function(n,r){var o=e(n,r);return t(o)?o:void 0},Vr}function ci(){if(Zr)return Gr;Zr=1;var t=ai()(ti(),"Map");return Gr=t}function si(){if(Hr)return Yr;Hr=1;var t=ai()(Object,"create");return Yr=t}function fi(){if(uo)return io;uo=1;var t=function(){if(Xr)return qr;Xr=1;var t=si();return qr=function(){this.__data__=t?t(null):{},this.size=0}}(),e=Kr?Jr:(Kr=1,Jr=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e}),n=function(){if(to)return Qr;to=1;var t=si(),e=Object.prototype.hasOwnProperty;return Qr=function(n){var r=this.__data__;if(t){var o=r[n];return"__lodash_hash_undefined__"===o?void 0:o}return e.call(r,n)?r[n]:void 0}}(),r=function(){if(no)return eo;no=1;var t=si(),e=Object.prototype.hasOwnProperty;return eo=function(n){var r=this.__data__;return t?void 0!==r[n]:e.call(r,n)}}(),o=function(){if(oo)return ro;oo=1;var t=si();return ro=function(e,n){var r=this.__data__;return this.size+=this.has(e)?0:1,r[e]=t&&void 0===n?"__lodash_hash_undefined__":n,this}}();function i(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}return i.prototype.clear=t,i.prototype.delete=e,i.prototype.get=n,i.prototype.has=r,i.prototype.set=o,io=i}function li(){if(ho)return lo;ho=1;var t=fo?so:(fo=1,so=function(t){var e=typeof t;return"string"==e||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==t:null===t});return lo=function(e,n){var r=e.__data__;return t(n)?r["string"==typeof n?"string":"hash"]:r.map}}function di(){if(xo)return jo;xo=1;var t=function(){if(co)return ao;co=1;var t=fi(),e=Ko(),n=ci();return ao=function(){this.size=0,this.__data__={hash:new t,map:new(n||e),string:new t}}}(),e=function(){if(vo)return po;vo=1;var t=li();return po=function(e){var n=t(this,e).delete(e);return this.size-=n?1:0,n}}(),n=function(){if(yo)return go;yo=1;var t=li();return go=function(e){return t(this,e).get(e)}}(),r=function(){if(_o)return bo;_o=1;var t=li();return bo=function(e){return t(this,e).has(e)}}(),o=function(){if(mo)return wo;mo=1;var t=li();return wo=function(e,n){var r=t(this,e),o=r.size;return r.set(e,n),this.size+=r.size==o?0:1,this}}();function i(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}return i.prototype.clear=t,i.prototype.delete=e,i.prototype.get=n,i.prototype.has=r,i.prototype.set=o,jo=i}function hi(){if(Ao)return ko;Ao=1;var t=Ko(),e=function(){if(fr)return sr;fr=1;var t=Ko();return sr=function(){this.__data__=new t,this.size=0}}(),n=dr?lr:(dr=1,lr=function(t){var e=this.__data__,n=e.delete(t);return this.size=e.size,n}),r=pr?hr:(pr=1,hr=function(t){return this.__data__.get(t)}),o=gr?vr:(gr=1,vr=function(t){return this.__data__.has(t)}),i=function(){if(Oo)return Eo;Oo=1;var t=Ko(),e=ci(),n=di();return Eo=function(r,o){var i=this.__data__;if(i instanceof t){var u=i.__data__;if(!e||u.length<199)return u.push([r,o]),this.size=++i.size,this;i=this.__data__=new n(u)}return i.set(r,o),this.size=i.size,this}}();function u(e){var n=this.__data__=new t(e);this.size=n.size}return u.prototype.clear=e,u.prototype.delete=n,u.prototype.get=r,u.prototype.has=o,u.prototype.set=i,ko=u}function pi(){if(Mo)return Co;return Mo=1,Co=function(t,e){for(var n=-1,r=null==t?0:t.length;++n<r&&!1!==e(t[n],n,t););return t}}function vi(){if($o)return So;$o=1;var t=ai(),e=function(){try{var e=t(Object,"defineProperty");return e({},"",{}),e}catch(n){}}();return So=e}function gi(){if(zo)return Lo;zo=1;var t=vi();return Lo=function(e,n,r){"__proto__"==n&&t?t(e,n,{configurable:!0,enumerable:!0,value:r,writable:!0}):e[n]=r},Lo}function yi(){if(No)return Io;No=1;var t=gi(),e=Xo(),n=Object.prototype.hasOwnProperty;return Io=function(r,o,i){var u=r[o];n.call(r,o)&&e(u,i)&&(void 0!==i||o in r)||t(r,o,i)},Io}function bi(){if(To)return Fo;To=1;var t=yi(),e=gi();return Fo=function(n,r,o,i){var u=!o;o||(o={});for(var a=-1,c=r.length;++a<c;){var s=r[a],f=i?i(o[s],n[s],s,o,n):void 0;void 0===f&&(f=n[s]),u?e(o,s,f):t(o,s,f)}return o},Fo}function _i(){if(Uo)return Do;return Uo=1,Do=function(t){return null!=t&&"object"==typeof t}}function wi(){if(Go)return Ro;Go=1;var t=function(){if(Vo)return Wo;Vo=1;var t=ni(),e=_i();return Wo=function(n){return e(n)&&"[object Arguments]"==t(n)}}(),e=_i(),n=Object.prototype,r=n.hasOwnProperty,o=n.propertyIsEnumerable,i=t(function(){return arguments}())?t:function(t){return e(t)&&r.call(t,"callee")&&!o.call(t,"callee")};return Ro=i}function mi(){if(Yo)return Zo;Yo=1;var t=Array.isArray;return Zo=t}var ji,xi,Ei,Oi,ki,Ai,Ci,Mi,Si,$i,Li,zi={exports:{}};function Ii(){return Ei||(Ei=1,t=zi,e=zi.exports,n=ti(),r=xi?ji:(xi=1,ji=function(){return!1}),o=e&&!e.nodeType&&e,i=o&&t&&!t.nodeType&&t,u=i&&i.exports===o?n.Buffer:void 0,a=(u?u.isBuffer:void 0)||r,t.exports=a),zi.exports;var t,e,n,r,o,i,u,a}function Ni(){if(ki)return Oi;ki=1;var t=/^(?:0|[1-9]\d*)$/;return Oi=function(e,n){var r=typeof e;return!!(n=null==n?9007199254740991:n)&&("number"==r||"symbol"!=r&&t.test(e))&&e>-1&&e%1==0&&e<n}}function Fi(){if(Ci)return Ai;Ci=1;return Ai=function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=9007199254740991}}function Ti(){if(Li)return $i;return Li=1,$i=function(t){return function(e){return t(e)}},$i}zi.exports;var Pi,Bi,Di,Ui,Wi,Vi,Ri,Gi,Zi,Yi,Hi,qi,Xi,Ji,Ki,Qi,tu,eu,nu,ru,ou,iu,uu,au,cu,su,fu,lu={exports:{}};function du(){return Pi||(Pi=1,t=lu,e=lu.exports,n=Qo(),r=e&&!e.nodeType&&e,o=r&&t&&!t.nodeType&&t,i=o&&o.exports===r&&n.process,u=function(){try{var t=o&&o.require&&o.require("util").types;return t||i&&i.binding&&i.binding("util")}catch(e){}}(),t.exports=u),lu.exports;var t,e,n,r,o,i,u}function hu(){if(Di)return Bi;Di=1;var t=function(){if(Si)return Mi;Si=1;var t=ni(),e=Fi(),n=_i(),r={};return r["[object Float32Array]"]=r["[object Float64Array]"]=r["[object Int8Array]"]=r["[object Int16Array]"]=r["[object Int32Array]"]=r["[object Uint8Array]"]=r["[object Uint8ClampedArray]"]=r["[object Uint16Array]"]=r["[object Uint32Array]"]=!0,r["[object Arguments]"]=r["[object Array]"]=r["[object ArrayBuffer]"]=r["[object Boolean]"]=r["[object DataView]"]=r["[object Date]"]=r["[object Error]"]=r["[object Function]"]=r["[object Map]"]=r["[object Number]"]=r["[object Object]"]=r["[object RegExp]"]=r["[object Set]"]=r["[object String]"]=r["[object WeakMap]"]=!1,Mi=function(o){return n(o)&&e(o.length)&&!!r[t(o)]}}(),e=Ti(),n=du(),r=n&&n.isTypedArray,o=r?e(r):t;return Bi=o}function pu(){if(Wi)return Ui;Wi=1;var t=Bo?Po:(Bo=1,Po=function(t,e){for(var n=-1,r=Array(t);++n<t;)r[n]=e(n);return r}),e=wi(),n=mi(),r=Ii(),o=Ni(),i=hu(),u=Object.prototype.hasOwnProperty;return Ui=function(a,c){var s=n(a),f=!s&&e(a),l=!s&&!f&&r(a),d=!s&&!f&&!l&&i(a),h=s||f||l||d,p=h?t(a.length,String):[],v=p.length;for(var g in a)!c&&!u.call(a,g)||h&&("length"==g||l&&("offset"==g||"parent"==g)||d&&("buffer"==g||"byteLength"==g||"byteOffset"==g)||o(g,v))||p.push(g);return p}}function vu(){if(Ri)return Vi;Ri=1;var t=Object.prototype;return Vi=function(e){var n=e&&e.constructor;return e===("function"==typeof n&&n.prototype||t)}}function gu(){if(Zi)return Gi;return Zi=1,Gi=function(t,e){return function(n){return t(e(n))}},Gi}function yu(){if(Xi)return qi;Xi=1;var t=vu(),e=function(){if(Hi)return Yi;Hi=1;var t=gu()(Object.keys,Object);return Yi=t}(),n=Object.prototype.hasOwnProperty;return qi=function(r){if(!t(r))return e(r);var o=[];for(var i in Object(r))n.call(r,i)&&"constructor"!=i&&o.push(i);return o},qi}function bu(){if(Ki)return Ji;Ki=1;var t=oi(),e=Fi();return Ji=function(n){return null!=n&&e(n.length)&&!t(n)}}function _u(){if(tu)return Qi;tu=1;var t=pu(),e=yu(),n=bu();return Qi=function(r){return n(r)?t(r):e(r)},Qi}function wu(){if(uu)return iu;uu=1;var t=ri(),e=vu(),n=(ou||(ou=1,ru=function(t){var e=[];if(null!=t)for(var n in Object(t))e.push(n);return e}),ru),r=Object.prototype.hasOwnProperty;return iu=function(o){if(!t(o))return n(o);var i=e(o),u=[];for(var a in o)("constructor"!=a||!i&&r.call(o,a))&&u.push(a);return u},iu}function mu(){if(cu)return au;cu=1;var t=pu(),e=wu(),n=bu();return au=function(r){return n(r)?t(r,!0):e(r)},au}lu.exports;var ju,xu,Eu,Ou,ku,Au,Cu,Mu,Su,$u,Lu,zu,Iu,Nu,Fu,Tu,Pu,Bu,Du,Uu,Wu,Vu,Ru,Gu,Zu,Yu,Hu,qu,Xu,Ju,Ku,Qu,ta,ea,na,ra,oa,ia,ua,aa,ca,sa,fa,la,da,ha,pa,va,ga,ya,ba,_a,wa,ma,ja,xa,Ea,Oa,ka,Aa,Ca,Ma,Sa,$a,La,za,Ia,Na,Fa,Ta,Pa,Ba,Da,Ua,Wa,Va,Ra,Ga,Za,Ya,Ha,qa,Xa,Ja,Ka,Qa,tc,ec,nc,rc,oc,ic,uc,ac,cc,sc,fc,lc,dc,hc,pc,vc,gc,yc,bc,_c,wc,mc,jc,xc,Ec,Oc,kc,Ac,Cc,Mc,Sc,$c,Lc,zc,Ic,Nc,Fc,Tc,Pc,Bc,Dc,Uc,Wc,Vc,Rc,Gc,Zc,Yc,Hc,qc,Xc,Jc,Kc,Qc,ts,es,ns,rs,os,is,us,as,cs,ss,fs,ls,ds,hs,ps,vs,gs,ys,bs,_s,ws,ms,js,xs,Es,Os,ks,As,Cs,Ms,Ss,$s,Ls,zs,Is,Ns,Fs,Ts,Ps,Bs,Ds,Us,Ws,Vs,Rs,Gs,Zs,Ys,Hs,qs,Xs,Js,Ks,Qs,tf,ef,nf,rf,of,uf,af,cf,sf,ff,lf,df,hf,pf,vf,gf,yf,bf,_f,wf,mf,jf,xf,Ef,Of,kf,Af,Cf,Mf,Sf,$f,Lf,zf,If,Nf,Ff,Tf,Pf,Bf,Df,Uf,Wf,Vf,Rf,Gf,Zf,Yf={exports:{}};function Hf(){if(ku)return Ou;return ku=1,Ou=function(t,e){for(var n=-1,r=null==t?0:t.length,o=0,i=[];++n<r;){var u=t[n];e(u,n,t)&&(i[o++]=u)}return i}}function qf(){if(Cu)return Au;return Cu=1,Au=function(){return[]}}function Xf(){if(Su)return Mu;Su=1;var t=Hf(),e=qf(),n=Object.prototype.propertyIsEnumerable,r=Object.getOwnPropertySymbols,o=r?function(e){return null==e?[]:(e=Object(e),t(r(e),(function(t){return n.call(e,t)})))}:e;return Mu=o}function Jf(){if(Iu)return zu;return Iu=1,zu=function(t,e){for(var n=-1,r=e.length,o=t.length;++n<r;)t[o+n]=e[n];return t}}function Kf(){if(Fu)return Nu;Fu=1;var t=gu()(Object.getPrototypeOf,Object);return Nu=t}function Qf(){if(Pu)return Tu;Pu=1;var t=Jf(),e=Kf(),n=Xf(),r=qf(),o=Object.getOwnPropertySymbols?function(r){for(var o=[];r;)t(o,n(r)),r=e(r);return o}:r;return Tu=o}function tl(){if(Wu)return Uu;Wu=1;var t=Jf(),e=mi();return Uu=function(n,r,o){var i=r(n);return e(n)?i:t(i,o(n))},Uu}function el(){if(Ru)return Vu;Ru=1;var t=tl(),e=Xf(),n=_u();return Vu=function(r){return t(r,n,e)},Vu}function nl(){if(Ku)return Ju;Ku=1;var t=ai()(ti(),"Set");return Ju=t}function rl(){if(na)return ea;na=1;var t=function(){if(Hu)return Yu;Hu=1;var t=ai()(ti(),"DataView");return Yu=t}(),e=ci(),n=function(){if(Xu)return qu;Xu=1;var t=ai()(ti(),"Promise");return qu=t}(),r=nl(),o=function(){if(ta)return Qu;ta=1;var t=ai()(ti(),"WeakMap");return Qu=t}(),i=ni(),u=ui(),a="[object Map]",c="[object Promise]",s="[object Set]",f="[object WeakMap]",l="[object DataView]",d=u(t),h=u(e),p=u(n),v=u(r),g=u(o),y=i;return(t&&y(new t(new ArrayBuffer(1)))!=l||e&&y(new e)!=a||n&&y(n.resolve())!=c||r&&y(new r)!=s||o&&y(new o)!=f)&&(y=function(t){var e=i(t),n="[object Object]"==e?t.constructor:void 0,r=n?u(n):"";if(r)switch(r){case d:return l;case h:return a;case p:return c;case v:return s;case g:return f}return e}),ea=y}function ol(){if(ua)return ia;ua=1;var t=ti().Uint8Array;return ia=t}function il(){if(ca)return aa;ca=1;var t=ol();return aa=function(e){var n=new e.constructor(e.byteLength);return new t(n).set(new t(e)),n}}function ul(){if(ba)return ya;ba=1;var t=il(),e=function(){if(fa)return sa;fa=1;var t=il();return sa=function(e,n){var r=n?t(e.buffer):e.buffer;return new e.constructor(r,e.byteOffset,e.byteLength)}}(),n=function(){if(da)return la;da=1;var t=/\w*$/;return la=function(e){var n=new e.constructor(e.source,t.exec(e));return n.lastIndex=e.lastIndex,n}}(),r=function(){if(pa)return ha;pa=1;var t=ei(),e=t?t.prototype:void 0,n=e?e.valueOf:void 0;return ha=function(t){return n?Object(n.call(t)):{}}}(),o=function(){if(ga)return va;ga=1;var t=il();return va=function(e,n){var r=n?t(e.buffer):e.buffer;return new e.constructor(r,e.byteOffset,e.length)}}();return ya=function(i,u,a){var c=i.constructor;switch(u){case"[object ArrayBuffer]":return t(i);case"[object Boolean]":case"[object Date]":return new c(+i);case"[object DataView]":return e(i,a);case"[object Float32Array]":case"[object Float64Array]":case"[object Int8Array]":case"[object Int16Array]":case"[object Int32Array]":case"[object Uint8Array]":case"[object Uint8ClampedArray]":case"[object Uint16Array]":case"[object Uint32Array]":return o(i,a);case"[object Map]":case"[object Set]":return new c;case"[object Number]":case"[object String]":return new c(i);case"[object RegExp]":return n(i);case"[object Symbol]":return r(i)}},ya}function al(){if(wa)return _a;wa=1;var t=ri(),e=Object.create,n=function(){function n(){}return function(r){if(!t(r))return{};if(e)return e(r);n.prototype=r;var o=new n;return n.prototype=void 0,o}}();return _a=n}function cl(){if(ka)return Oa;ka=1;var t=function(){if(Ea)return xa;Ea=1;var t=rl(),e=_i();return xa=function(n){return e(n)&&"[object Map]"==t(n)}}(),e=Ti(),n=du(),r=n&&n.isMap,o=r?e(r):t;return Oa=o}function sl(){if(Sa)return Ma;Sa=1;var t=function(){if(Ca)return Aa;Ca=1;var t=rl(),e=_i();return Aa=function(n){return e(n)&&"[object Set]"==t(n)}}(),e=Ti(),n=du(),r=n&&n.isSet,o=r?e(r):t;return Ma=o}function fl(){if(La)return $a;La=1;var t,e,n,r,o,i,u,a=hi(),c=pi(),s=yi(),f=function(){if(nu)return eu;nu=1;var t=bi(),e=_u();return eu=function(n,r){return n&&t(r,e(r),n)},eu}(),l=function(){if(fu)return su;fu=1;var t=bi(),e=mu();return su=function(n,r){return n&&t(r,e(r),n)},su}(),d=(ju||(ju=1,t=Yf,e=Yf.exports,n=ti(),r=e&&!e.nodeType&&e,o=r&&t&&!t.nodeType&&t,i=o&&o.exports===r?n.Buffer:void 0,u=i?i.allocUnsafe:void 0,t.exports=function(t,e){if(e)return t.slice();var n=t.length,r=u?u(n):new t.constructor(n);return t.copy(r),r}),Yf.exports),h=Eu?xu:(Eu=1,xu=function(t,e){var n=-1,r=t.length;for(e||(e=Array(r));++n<r;)e[n]=t[n];return e}),p=function(){if(Lu)return $u;Lu=1;var t=bi(),e=Xf();return $u=function(n,r){return t(n,e(n),r)},$u}(),v=function(){if(Du)return Bu;Du=1;var t=bi(),e=Qf();return Bu=function(n,r){return t(n,e(n),r)},Bu}(),g=el(),y=function(){if(Zu)return Gu;Zu=1;var t=tl(),e=Qf(),n=mu();return Gu=function(r){return t(r,n,e)},Gu}(),b=rl(),_=function(){if(oa)return ra;oa=1;var t=Object.prototype.hasOwnProperty;return ra=function(e){var n=e.length,r=new e.constructor(n);return n&&"string"==typeof e[0]&&t.call(e,"index")&&(r.index=e.index,r.input=e.input),r}}(),w=ul(),m=function(){if(ja)return ma;ja=1;var t=al(),e=Kf(),n=vu();return ma=function(r){return"function"!=typeof r.constructor||n(r)?{}:t(e(r))},ma}(),j=mi(),x=Ii(),E=cl(),O=ri(),k=sl(),A=_u(),C=mu(),M="[object Arguments]",S="[object Function]",$="[object Object]",L={};return L[M]=L["[object Array]"]=L["[object ArrayBuffer]"]=L["[object DataView]"]=L["[object Boolean]"]=L["[object Date]"]=L["[object Float32Array]"]=L["[object Float64Array]"]=L["[object Int8Array]"]=L["[object Int16Array]"]=L["[object Int32Array]"]=L["[object Map]"]=L["[object Number]"]=L[$]=L["[object RegExp]"]=L["[object Set]"]=L["[object String]"]=L["[object Symbol]"]=L["[object Uint8Array]"]=L["[object Uint8ClampedArray]"]=L["[object Uint16Array]"]=L["[object Uint32Array]"]=!0,L["[object Error]"]=L[S]=L["[object WeakMap]"]=!1,$a=function t(e,n,r,o,i,u){var z,I=1&n,N=2&n,F=4&n;if(r&&(z=i?r(e,o,i,u):r(e)),void 0!==z)return z;if(!O(e))return e;var T=j(e);if(T){if(z=_(e),!I)return h(e,z)}else{var P=b(e),B=P==S||"[object GeneratorFunction]"==P;if(x(e))return d(e,I);if(P==$||P==M||B&&!i){if(z=N||B?{}:m(e),!I)return N?v(e,l(z,e)):p(e,f(z,e))}else{if(!L[P])return i?e:{};z=w(e,P,I)}}u||(u=new a);var D=u.get(e);if(D)return D;u.set(e,z),k(e)?e.forEach((function(o){z.add(t(o,n,r,o,e,u))})):E(e)&&e.forEach((function(o,i){z.set(i,t(o,n,r,i,e,u))}));var U=T?void 0:(F?N?y:g:N?C:A)(e);return c(U||e,(function(o,i){U&&(o=e[i=o]),s(z,i,t(o,n,r,i,e,u))})),z},$a}function ll(){if(Fa)return Na;return Fa=1,Na=function(t){return function(){return t}}}function dl(){if(Da)return Ba;Da=1;var t=(Pa||(Pa=1,Ta=function(t){return function(e,n,r){for(var o=-1,i=Object(e),u=r(e),a=u.length;a--;){var c=u[t?a:++o];if(!1===n(i[c],c,i))break}return e}}),Ta),e=t();return Ba=e}function hl(){if(Wa)return Ua;Wa=1;var t=dl(),e=_u();return Ua=function(n,r){return n&&t(n,r,e)},Ua}function pl(){if(Za)return Ga;Za=1;var t=hl(),e=function(){if(Ra)return Va;Ra=1;var t=bu();return Va=function(e,n){return function(r,o){if(null==r)return r;if(!t(r))return e(r,o);for(var i=r.length,u=n?i:-1,a=Object(r);(n?u--:++u<i)&&!1!==o(a[u],u,a););return r}}}()(t);return Ga=e}function vl(){if(Ha)return Ya;return Ha=1,Ya=function(t){return t}}function gl(){if(Ka)return Ja;Ka=1;var t=pi(),e=pl(),n=function(){if(Xa)return qa;Xa=1;var t=vl();return qa=function(e){return"function"==typeof e?e:t}}(),r=mi();return Ja=function(o,i){return(r(o)?t:e)(o,n(i))}}function yl(){if(cc)return ac;cc=1;var t=di(),e=oc?rc:(oc=1,rc=function(t){return this.__data__.set(t,"__lodash_hash_undefined__"),this}),n=uc?ic:(uc=1,ic=function(t){return this.__data__.has(t)});function r(e){var n=-1,r=null==e?0:e.length;for(this.__data__=new t;++n<r;)this.add(e[n])}return r.prototype.add=r.prototype.push=e,r.prototype.has=n,ac=r}function bl(){if(dc)return lc;return dc=1,lc=function(t,e){return t.has(e)}}function _l(){if(pc)return hc;pc=1;var t=yl(),e=fc?sc:(fc=1,sc=function(t,e){for(var n=-1,r=null==t?0:t.length;++n<r;)if(e(t[n],n,t))return!0;return!1}),n=bl();return hc=function(r,o,i,u,a,c){var s=1&i,f=r.length,l=o.length;if(f!=l&&!(s&&l>f))return!1;var d=c.get(r),h=c.get(o);if(d&&h)return d==o&&h==r;var p=-1,v=!0,g=2&i?new t:void 0;for(c.set(r,o),c.set(o,r);++p<f;){var y=r[p],b=o[p];if(u)var _=s?u(b,y,p,o,r,c):u(y,b,p,r,o,c);if(void 0!==_){if(_)continue;v=!1;break}if(g){if(!e(o,(function(t,e){if(!n(g,e)&&(y===t||a(y,t,i,u,c)))return g.push(e)}))){v=!1;break}}else if(y!==b&&!a(y,b,i,u,c)){v=!1;break}}return c.delete(r),c.delete(o),v}}function wl(){if(bc)return yc;return bc=1,yc=function(t){var e=-1,n=Array(t.size);return t.forEach((function(t){n[++e]=t})),n}}function ml(){if(wc)return _c;wc=1;var t=ei(),e=ol(),n=Xo(),r=_l(),o=gc?vc:(gc=1,vc=function(t){var e=-1,n=Array(t.size);return t.forEach((function(t,r){n[++e]=[r,t]})),n}),i=wl(),u=t?t.prototype:void 0,a=u?u.valueOf:void 0;return _c=function(t,u,c,s,f,l,d){switch(c){case"[object DataView]":if(t.byteLength!=u.byteLength||t.byteOffset!=u.byteOffset)return!1;t=t.buffer,u=u.buffer;case"[object ArrayBuffer]":return!(t.byteLength!=u.byteLength||!l(new e(t),new e(u)));case"[object Boolean]":case"[object Date]":case"[object Number]":return n(+t,+u);case"[object Error]":return t.name==u.name&&t.message==u.message;case"[object RegExp]":case"[object String]":return t==u+"";case"[object Map]":var h=o;case"[object Set]":var p=1&s;if(h||(h=i),t.size!=u.size&&!p)return!1;var v=d.get(t);if(v)return v==u;s|=2,d.set(t,u);var g=r(h(t),h(u),s,f,l,d);return d.delete(t),g;case"[object Symbol]":if(a)return a.call(t)==a.call(u)}return!1},_c}function jl(){if(Ec)return xc;Ec=1;var t=hi(),e=_l(),n=ml(),r=function(){if(jc)return mc;jc=1;var t=el(),e=Object.prototype.hasOwnProperty;return mc=function(n,r,o,i,u,a){var c=1&o,s=t(n),f=s.length;if(f!=t(r).length&&!c)return!1;for(var l=f;l--;){var d=s[l];if(!(c?d in r:e.call(r,d)))return!1}var h=a.get(n),p=a.get(r);if(h&&p)return h==r&&p==n;var v=!0;a.set(n,r),a.set(r,n);for(var g=c;++l<f;){var y=n[d=s[l]],b=r[d];if(i)var _=c?i(b,y,d,r,n,a):i(y,b,d,n,r,a);if(!(void 0===_?y===b||u(y,b,o,i,a):_)){v=!1;break}g||(g="constructor"==d)}if(v&&!g){var w=n.constructor,m=r.constructor;w==m||!("constructor"in n)||!("constructor"in r)||"function"==typeof w&&w instanceof w&&"function"==typeof m&&m instanceof m||(v=!1)}return a.delete(n),a.delete(r),v},mc}(),o=rl(),i=mi(),u=Ii(),a=hu(),c="[object Arguments]",s="[object Array]",f="[object Object]",l=Object.prototype.hasOwnProperty;return xc=function(d,h,p,v,g,y){var b=i(d),_=i(h),w=b?s:o(d),m=_?s:o(h),j=(w=w==c?f:w)==f,x=(m=m==c?f:m)==f,E=w==m;if(E&&u(d)){if(!u(h))return!1;b=!0,j=!1}if(E&&!j)return y||(y=new t),b||a(d)?e(d,h,p,v,g,y):n(d,h,w,p,v,g,y);if(!(1&p)){var O=j&&l.call(d,"__wrapped__"),k=x&&l.call(h,"__wrapped__");if(O||k){var A=O?d.value():d,C=k?h.value():h;return y||(y=new t),g(A,C,p,v,y)}}return!!E&&(y||(y=new t),r(d,h,p,v,g,y))},xc}function xl(){if(kc)return Oc;kc=1;var t=jl(),e=_i();return Oc=function n(r,o,i,u,a){return r===o||(null==r||null==o||!e(r)&&!e(o)?r!=r&&o!=o:t(r,o,i,u,n,a))},Oc}function El(){if(Sc)return Mc;Sc=1;var t=ri();return Mc=function(e){return e==e&&!t(e)}}function Ol(){if(Ic)return zc;return Ic=1,zc=function(t,e){return function(n){return null!=n&&(n[t]===e&&(void 0!==e||t in Object(n)))}},zc}function kl(){if(Fc)return Nc;Fc=1;var t=function(){if(Cc)return Ac;Cc=1;var t=hi(),e=xl();return Ac=function(n,r,o,i){var u=o.length,a=u,c=!i;if(null==n)return!a;for(n=Object(n);u--;){var s=o[u];if(c&&s[2]?s[1]!==n[s[0]]:!(s[0]in n))return!1}for(;++u<a;){var f=(s=o[u])[0],l=n[f],d=s[1];if(c&&s[2]){if(void 0===l&&!(f in n))return!1}else{var h=new t;if(i)var p=i(l,d,f,n,r,h);if(!(void 0===p?e(d,l,3,i,h):p))return!1}}return!0},Ac}(),e=function(){if(Lc)return $c;Lc=1;var t=El(),e=_u();return $c=function(n){for(var r=e(n),o=r.length;o--;){var i=r[o],u=n[i];r[o]=[i,u,t(u)]}return r},$c}(),n=Ol();return Nc=function(r){var o=e(r);return 1==o.length&&o[0][2]?n(o[0][0],o[0][1]):function(e){return e===r||t(e,r,o)}},Nc}function Al(){if(Pc)return Tc;Pc=1;var t=ni(),e=_i();return Tc=function(n){return"symbol"==typeof n||e(n)&&"[object Symbol]"==t(n)}}function Cl(){if(Dc)return Bc;Dc=1;var t=mi(),e=Al(),n=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,r=/^\w*$/;return Bc=function(o,i){if(t(o))return!1;var u=typeof o;return!("number"!=u&&"symbol"!=u&&"boolean"!=u&&null!=o&&!e(o))||(r.test(o)||!n.test(o)||null!=i&&o in Object(i))},Bc}function Ml(){if(Rc)return Vc;Rc=1;var t=function(){if(Wc)return Uc;Wc=1;var t=di();function e(n,r){if("function"!=typeof n||null!=r&&"function"!=typeof r)throw new TypeError("Expected a function");var o=function(){var t=arguments,e=r?r.apply(this,t):t[0],i=o.cache;if(i.has(e))return i.get(e);var u=n.apply(this,t);return o.cache=i.set(e,u)||i,u};return o.cache=new(e.Cache||t),o}return e.Cache=t,Uc=e}();return Vc=function(e){var n=t(e,(function(t){return 500===r.size&&r.clear(),t})),r=n.cache;return n},Vc}function Sl(){if(Hc)return Yc;return Hc=1,Yc=function(t,e){for(var n=-1,r=null==t?0:t.length,o=Array(r);++n<r;)o[n]=e(t[n],n,t);return o}}function $l(){if(Kc)return Jc;Kc=1;var t=function(){if(Xc)return qc;Xc=1;var t=ei(),e=Sl(),n=mi(),r=Al(),o=t?t.prototype:void 0,i=o?o.toString:void 0;return qc=function t(o){if("string"==typeof o)return o;if(n(o))return e(o,t)+"";if(r(o))return i?i.call(o):"";var u=o+"";return"0"==u&&1/o==-1/0?"-0":u},qc}();return Jc=function(e){return null==e?"":t(e)}}function Ll(){if(ts)return Qc;ts=1;var t=mi(),e=Cl(),n=function(){if(Zc)return Gc;Zc=1;var t=Ml(),e=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,n=/\\(\\)?/g,r=t((function(t){var r=[];return 46===t.charCodeAt(0)&&r.push(""),t.replace(e,(function(t,e,o,i){r.push(o?i.replace(n,"$1"):e||t)})),r}));return Gc=r}(),r=$l();return Qc=function(o,i){return t(o)?o:e(o,i)?[o]:n(r(o))},Qc}function zl(){if(ns)return es;ns=1;var t=Al();return es=function(e){if("string"==typeof e||t(e))return e;var n=e+"";return"0"==n&&1/e==-1/0?"-0":n}}function Il(){if(os)return rs;os=1;var t=Ll(),e=zl();return rs=function(n,r){for(var o=0,i=(r=t(r,n)).length;null!=n&&o<i;)n=n[e(r[o++])];return o&&o==i?n:void 0},rs}function Nl(){if(fs)return ss;fs=1;var t=Ll(),e=wi(),n=mi(),r=Ni(),o=Fi(),i=zl();return ss=function(u,a,c){for(var s=-1,f=(a=t(a,u)).length,l=!1;++s<f;){var d=i(a[s]);if(!(l=null!=u&&c(u,d)))break;u=u[d]}return l||++s!=f?l:!!(f=null==u?0:u.length)&&o(f)&&r(d,f)&&(n(u)||e(u))},ss}function Fl(){if(ds)return ls;ds=1;var t=(cs||(cs=1,as=function(t,e){return null!=t&&e in Object(t)}),as),e=Nl();return ls=function(n,r){return null!=n&&e(n,r,t)},ls}function Tl(){if(ps)return hs;ps=1;var t=xl(),e=function(){if(us)return is;us=1;var t=Il();return is=function(e,n,r){var o=null==e?void 0:t(e,n);return void 0===o?r:o},is}(),n=Fl(),r=Cl(),o=El(),i=Ol(),u=zl();return hs=function(a,c){return r(a)&&o(c)?i(u(a),c):function(r){var o=e(r,a);return void 0===o&&o===c?n(r,a):t(c,o,3)}},hs}function Pl(){if(gs)return vs;return gs=1,vs=function(t){return function(e){return null==e?void 0:e[t]}},vs}function Bl(){if(ws)return _s;ws=1;var t=Pl(),e=function(){if(bs)return ys;bs=1;var t=Il();return ys=function(e){return function(n){return t(n,e)}},ys}(),n=Cl(),r=zl();return _s=function(o){return n(o)?t(r(o)):e(o)}}function Dl(){if(js)return ms;js=1;var t=kl(),e=Tl(),n=vl(),r=mi(),o=Bl();return ms=function(i){return"function"==typeof i?i:null==i?n:"object"==typeof i?r(i)?e(i[0],i[1]):t(i):o(i)}}function Ul(){if(Ks)return Js;Ks=1;var t=function(){if(Zs)return Gs;Zs=1;var t=Pl()("length");return Gs=t}(),e=function(){if(Hs)return Ys;Hs=1;var t=RegExp("[\\u200d\\ud800-\\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");return Ys=function(e){return t.test(e)}}(),n=function(){if(Xs)return qs;Xs=1;var t="\\ud800-\\udfff",e="["+t+"]",n="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",r="\\ud83c[\\udffb-\\udfff]",o="[^"+t+"]",i="(?:\\ud83c[\\udde6-\\uddff]){2}",u="[\\ud800-\\udbff][\\udc00-\\udfff]",a="(?:"+n+"|"+r+")?",c="[\\ufe0e\\ufe0f]?",s=c+a+"(?:\\u200d(?:"+[o,i,u].join("|")+")"+c+a+")*",f="(?:"+[o+n+"?",n,i,u,e].join("|")+")",l=RegExp(r+"(?="+r+")|"+f+s,"g");return qs=function(t){for(var e=l.lastIndex=0;l.test(t);)++e;return e}}();return Js=function(r){return e(r)?n(r):t(r)}}function Wl(){if(af)return uf;af=1;var t=Jf(),e=function(){if(of)return rf;of=1;var t=ei(),e=wi(),n=mi(),r=t?t.isConcatSpreadable:void 0;return rf=function(t){return n(t)||e(t)||!!(r&&t&&t[r])}}();return uf=function n(r,o,i,u,a){var c=-1,s=r.length;for(i||(i=e),a||(a=[]);++c<s;){var f=r[c];o>0&&i(f)?o>1?n(f,o-1,i,u,a):t(a,f):u||(a[a.length]=f)}return a},uf}function Vl(){if(lf)return ff;lf=1;var t=(sf||(sf=1,cf=function(t,e,n){switch(n.length){case 0:return t.call(e);case 1:return t.call(e,n[0]);case 2:return t.call(e,n[0],n[1]);case 3:return t.call(e,n[0],n[1],n[2])}return t.apply(e,n)}),cf),e=Math.max;return ff=function(n,r,o){return r=e(void 0===r?n.length-1:r,0),function(){for(var i=arguments,u=-1,a=e(i.length-r,0),c=Array(a);++u<a;)c[u]=i[r+u];u=-1;for(var s=Array(r+1);++u<r;)s[u]=i[u];return s[r]=o(c),t(n,this,s)}},ff}function Rl(){if(yf)return gf;yf=1;var t=function(){if(hf)return df;hf=1;var t=ll(),e=vi(),n=vl(),r=e?function(n,r){return e(n,"toString",{configurable:!0,enumerable:!1,value:t(r),writable:!0})}:n;return df=r}(),e=function(){if(vf)return pf;vf=1;var t=Date.now;return pf=function(e){var n=0,r=0;return function(){var o=t(),i=16-(o-r);if(r=o,i>0){if(++n>=800)return arguments[0]}else n=0;return e.apply(void 0,arguments)}},pf}(),n=e(t);return gf=n}function Gl(){if(Af)return kf;Af=1;var t=mf?wf:(mf=1,wf=function(t,e,n,r){for(var o=t.length,i=n+(r?1:-1);r?i--:++i<o;)if(e(t[i],i,t))return i;return-1}),e=xf?jf:(xf=1,jf=function(t){return t!=t}),n=Of?Ef:(Of=1,Ef=function(t,e,n){for(var r=n-1,o=t.length;++r<o;)if(t[r]===e)return r;return-1});return kf=function(r,o,i){return o==o?n(r,o,i):t(r,e,i)}}function Zl(){if(Nf)return If;Nf=1;var t=nl(),e=zf?Lf:(zf=1,Lf=function(){}),n=wl(),r=t&&1/n(new t([,-0]))[1]==1/0?function(e){return new t(e)}:e;return If=r}function Yl(){if(Tf)return Ff;Tf=1;var t=yl(),e=function(){if(Mf)return Cf;Mf=1;var t=Gl();return Cf=function(e,n){return!(null==e||!e.length)&&t(e,n,0)>-1}}(),n=$f?Sf:($f=1,Sf=function(t,e,n){for(var r=-1,o=null==t?0:t.length;++r<o;)if(n(e,t[r]))return!0;return!1}),r=bl(),o=Zl(),i=wl();return Ff=function(u,a,c){var s=-1,f=e,l=u.length,d=!0,h=[],p=h;if(c)d=!1,f=n;else if(l>=200){var v=a?null:o(u);if(v)return i(v);d=!1,f=r,p=new t}else p=a?[]:h;t:for(;++s<l;){var g=u[s],y=a?a(g):g;if(g=c||0!==g?g:0,d&&y==y){for(var b=p.length;b--;)if(p[b]===y)continue t;a&&p.push(y),h.push(g)}else f(p,y,c)||(p!==h&&p.push(y),h.push(g))}return h}}Yf.exports;try{Zf={clone:function(){if(Ia)return za;Ia=1;var t=fl();return za=function(e){return t(e,4)}}(),constant:ll(),each:tc?Qa:(tc=1,Qa=gl()),filter:function(){if(Es)return xs;Es=1;var t=Hf(),e=function(){if(nc)return ec;nc=1;var t=pl();return ec=function(e,n){var r=[];return t(e,(function(t,e,o){n(t,e,o)&&r.push(t)})),r}}(),n=Dl(),r=mi();return xs=function(o,i){return(r(o)?t:e)(o,n(i,3))}}(),has:function(){if(Cs)return As;Cs=1;var t=function(){if(ks)return Os;ks=1;var t=Object.prototype.hasOwnProperty;return Os=function(e,n){return null!=e&&t.call(e,n)},Os}(),e=Nl();return As=function(n,r){return null!=n&&e(n,r,t)},As}(),isArray:mi(),isEmpty:function(){if(Ss)return Ms;Ss=1;var t=yu(),e=rl(),n=wi(),r=mi(),o=bu(),i=Ii(),u=vu(),a=hu(),c=Object.prototype.hasOwnProperty;return Ms=function(s){if(null==s)return!0;if(o(s)&&(r(s)||"string"==typeof s||"function"==typeof s.splice||i(s)||a(s)||n(s)))return!s.length;var f=e(s);if("[object Map]"==f||"[object Set]"==f)return!s.size;if(u(s))return!t(s).length;for(var l in s)if(c.call(s,l))return!1;return!0}}(),isFunction:oi(),isUndefined:Ls?$s:(Ls=1,$s=function(t){return void 0===t}),keys:_u(),map:function(){if(Fs)return Ns;Fs=1;var t=Sl(),e=Dl(),n=function(){if(Is)return zs;Is=1;var t=pl(),e=bu();return zs=function(n,r){var o=-1,i=e(n)?Array(n.length):[];return t(n,(function(t,e,n){i[++o]=r(t,e,n)})),i}}(),r=mi();return Ns=function(o,i){return(r(o)?t:n)(o,e(i,3))}}(),reduce:function(){if(Ws)return Us;Ws=1;var t=Ps?Ts:(Ps=1,Ts=function(t,e,n,r){var o=-1,i=null==t?0:t.length;for(r&&i&&(n=t[++o]);++o<i;)n=e(n,t[o],o,t);return n}),e=pl(),n=Dl(),r=Ds?Bs:(Ds=1,Bs=function(t,e,n,r,o){return o(t,(function(t,o,i){n=r?(r=!1,t):e(n,t,o,i)})),n}),o=mi();return Us=function(i,u,a){var c=o(i)?t:r,s=arguments.length<3;return c(i,n(u,4),a,s,e)},Us}(),size:function(){if(tf)return Qs;tf=1;var t=yu(),e=rl(),n=bu(),r=function(){if(Rs)return Vs;Rs=1;var t=ni(),e=mi(),n=_i();return Vs=function(r){return"string"==typeof r||!e(r)&&n(r)&&"[object String]"==t(r)}}(),o=Ul();return Qs=function(i){if(null==i)return 0;if(n(i))return r(i)?o(i):i.length;var u=e(i);return"[object Map]"==u||"[object Set]"==u?i.size:t(i).length}}(),transform:function(){if(nf)return ef;nf=1;var t=pi(),e=al(),n=hl(),r=Dl(),o=Kf(),i=mi(),u=Ii(),a=oi(),c=ri(),s=hu();return ef=function(f,l,d){var h=i(f),p=h||u(f)||s(f);if(l=r(l,4),null==d){var v=f&&f.constructor;d=p?h?new v:[]:c(f)&&a(v)?e(o(f)):{}}return(p?t:n)(f,(function(t,e,n){return l(d,t,e,n)})),d},ef}(),union:function(){if(Uf)return Df;Uf=1;var t=Wl(),e=function(){if(_f)return bf;_f=1;var t=vl(),e=Vl(),n=Rl();return bf=function(r,o){return n(e(r,o,t),r+"")},bf}(),n=Yl(),r=function(){if(Bf)return Pf;Bf=1;var t=bu(),e=_i();return Pf=function(n){return e(n)&&t(n)}}(),o=e((function(e){return n(t(e,1,r,!0))}));return Df=o}(),values:function(){if(Gf)return Rf;Gf=1;var t=function(){if(Vf)return Wf;Vf=1;var t=Sl();return Wf=function(e,n){return t(n,(function(t){return e[t]}))},Wf}(),e=_u();return Rf=function(n){return null==n?[]:t(n,e(n))},Rf}()}}catch(jh){}Zf||(Zf=window._);var Hl=Zf,ql=Hl,Xl=td,Jl="\0",Kl="\0",Ql="";function td(t){this._isDirected=!ql.has(t,"directed")||t.directed,this._isMultigraph=!!ql.has(t,"multigraph")&&t.multigraph,this._isCompound=!!ql.has(t,"compound")&&t.compound,this._label=void 0,this._defaultNodeLabelFn=ql.constant(void 0),this._defaultEdgeLabelFn=ql.constant(void 0),this._nodes={},this._isCompound&&(this._parent={},this._children={},this._children[Kl]={}),this._in={},this._preds={},this._out={},this._sucs={},this._edgeObjs={},this._edgeLabels={}}function ed(t,e){t[e]?t[e]++:t[e]=1}function nd(t,e){--t[e]||delete t[e]}function rd(t,e,n,r){var o=""+e,i=""+n;if(!t&&o>i){var u=o;o=i,i=u}return o+Ql+i+Ql+(ql.isUndefined(r)?Jl:r)}function od(t,e){return rd(t,e.v,e.w,e.name)}td.prototype._nodeCount=0,td.prototype._edgeCount=0,td.prototype.isDirected=function(){return this._isDirected},td.prototype.isMultigraph=function(){return this._isMultigraph},td.prototype.isCompound=function(){return this._isCompound},td.prototype.setGraph=function(t){return this._label=t,this},td.prototype.graph=function(){return this._label},td.prototype.setDefaultNodeLabel=function(t){return ql.isFunction(t)||(t=ql.constant(t)),this._defaultNodeLabelFn=t,this},td.prototype.nodeCount=function(){return this._nodeCount},td.prototype.nodes=function(){return ql.keys(this._nodes)},td.prototype.sources=function(){var t=this;return ql.filter(this.nodes(),(function(e){return ql.isEmpty(t._in[e])}))},td.prototype.sinks=function(){var t=this;return ql.filter(this.nodes(),(function(e){return ql.isEmpty(t._out[e])}))},td.prototype.setNodes=function(t,e){var n=arguments,r=this;return ql.each(t,(function(t){n.length>1?r.setNode(t,e):r.setNode(t)})),this},td.prototype.setNode=function(t,e){return ql.has(this._nodes,t)?(arguments.length>1&&(this._nodes[t]=e),this):(this._nodes[t]=arguments.length>1?e:this._defaultNodeLabelFn(t),this._isCompound&&(this._parent[t]=Kl,this._children[t]={},this._children[Kl][t]=!0),this._in[t]={},this._preds[t]={},this._out[t]={},this._sucs[t]={},++this._nodeCount,this)},td.prototype.node=function(t){return this._nodes[t]},td.prototype.hasNode=function(t){return ql.has(this._nodes,t)},td.prototype.removeNode=function(t){var e=this;if(ql.has(this._nodes,t)){var n=function(t){e.removeEdge(e._edgeObjs[t])};delete this._nodes[t],this._isCompound&&(this._removeFromParentsChildList(t),delete this._parent[t],ql.each(this.children(t),(function(t){e.setParent(t)})),delete this._children[t]),ql.each(ql.keys(this._in[t]),n),delete this._in[t],delete this._preds[t],ql.each(ql.keys(this._out[t]),n),delete this._out[t],delete this._sucs[t],--this._nodeCount}return this},td.prototype.setParent=function(t,e){if(!this._isCompound)throw new Error("Cannot set parent in a non-compound graph");if(ql.isUndefined(e))e=Kl;else{for(var n=e+="";!ql.isUndefined(n);n=this.parent(n))if(n===t)throw new Error("Setting "+e+" as parent of "+t+" would create a cycle");this.setNode(e)}return this.setNode(t),this._removeFromParentsChildList(t),this._parent[t]=e,this._children[e][t]=!0,this},td.prototype._removeFromParentsChildList=function(t){delete this._children[this._parent[t]][t]},td.prototype.parent=function(t){if(this._isCompound){var e=this._parent[t];if(e!==Kl)return e}},td.prototype.children=function(t){if(ql.isUndefined(t)&&(t=Kl),this._isCompound){var e=this._children[t];if(e)return ql.keys(e)}else{if(t===Kl)return this.nodes();if(this.hasNode(t))return[]}},td.prototype.predecessors=function(t){var e=this._preds[t];if(e)return ql.keys(e)},td.prototype.successors=function(t){var e=this._sucs[t];if(e)return ql.keys(e)},td.prototype.neighbors=function(t){var e=this.predecessors(t);if(e)return ql.union(e,this.successors(t))},td.prototype.isLeaf=function(t){return 0===(this.isDirected()?this.successors(t):this.neighbors(t)).length},td.prototype.filterNodes=function(t){var e=new this.constructor({directed:this._isDirected,multigraph:this._isMultigraph,compound:this._isCompound});e.setGraph(this.graph());var n=this;ql.each(this._nodes,(function(n,r){t(r)&&e.setNode(r,n)})),ql.each(this._edgeObjs,(function(t){e.hasNode(t.v)&&e.hasNode(t.w)&&e.setEdge(t,n.edge(t))}));var r={};function o(t){var i=n.parent(t);return void 0===i||e.hasNode(i)?(r[t]=i,i):i in r?r[i]:o(i)}return this._isCompound&&ql.each(e.nodes(),(function(t){e.setParent(t,o(t))})),e},td.prototype.setDefaultEdgeLabel=function(t){return ql.isFunction(t)||(t=ql.constant(t)),this._defaultEdgeLabelFn=t,this},td.prototype.edgeCount=function(){return this._edgeCount},td.prototype.edges=function(){return ql.values(this._edgeObjs)},td.prototype.setPath=function(t,e){var n=this,r=arguments;return ql.reduce(t,(function(t,o){return r.length>1?n.setEdge(t,o,e):n.setEdge(t,o),o})),this},td.prototype.setEdge=function(){var t,e,n,r,o=!1,i=arguments[0];"object"==typeof i&&null!==i&&"v"in i?(t=i.v,e=i.w,n=i.name,2===arguments.length&&(r=arguments[1],o=!0)):(t=i,e=arguments[1],n=arguments[3],arguments.length>2&&(r=arguments[2],o=!0)),t=""+t,e=""+e,ql.isUndefined(n)||(n=""+n);var u=rd(this._isDirected,t,e,n);if(ql.has(this._edgeLabels,u))return o&&(this._edgeLabels[u]=r),this;if(!ql.isUndefined(n)&&!this._isMultigraph)throw new Error("Cannot set a named edge when isMultigraph = false");this.setNode(t),this.setNode(e),this._edgeLabels[u]=o?r:this._defaultEdgeLabelFn(t,e,n);var a=function(t,e,n,r){var o=""+e,i=""+n;if(!t&&o>i){var u=o;o=i,i=u}var a={v:o,w:i};r&&(a.name=r);return a}(this._isDirected,t,e,n);return t=a.v,e=a.w,Object.freeze(a),this._edgeObjs[u]=a,ed(this._preds[e],t),ed(this._sucs[t],e),this._in[e][u]=a,this._out[t][u]=a,this._edgeCount++,this},td.prototype.edge=function(t,e,n){var r=1===arguments.length?od(this._isDirected,arguments[0]):rd(this._isDirected,t,e,n);return this._edgeLabels[r]},td.prototype.hasEdge=function(t,e,n){var r=1===arguments.length?od(this._isDirected,arguments[0]):rd(this._isDirected,t,e,n);return ql.has(this._edgeLabels,r)},td.prototype.removeEdge=function(t,e,n){var r=1===arguments.length?od(this._isDirected,arguments[0]):rd(this._isDirected,t,e,n),o=this._edgeObjs[r];return o&&(t=o.v,e=o.w,delete this._edgeLabels[r],delete this._edgeObjs[r],nd(this._preds[e],t),nd(this._sucs[t],e),delete this._in[e][r],delete this._out[t][r],this._edgeCount--),this},td.prototype.inEdges=function(t,e){var n=this._in[t];if(n){var r=ql.values(n);return e?ql.filter(r,(function(t){return t.v===e})):r}},td.prototype.outEdges=function(t,e){var n=this._out[t];if(n){var r=ql.values(n);return e?ql.filter(r,(function(t){return t.w===e})):r}},td.prototype.nodeEdges=function(t,e){var n=this.inEdges(t,e);if(n)return n.concat(this.outEdges(t,e))};var id={Graph:Xl,version:"2.1.8"},ud=Hl,ad=Xl,cd={write:function(t){var e={options:{directed:t.isDirected(),multigraph:t.isMultigraph(),compound:t.isCompound()},nodes:sd(t),edges:fd(t)};ud.isUndefined(t.graph())||(e.value=ud.clone(t.graph()));return e},read:function(t){var e=new ad(t.options).setGraph(t.value);return ud.each(t.nodes,(function(t){e.setNode(t.v,t.value),t.parent&&e.setParent(t.v,t.parent)})),ud.each(t.edges,(function(t){e.setEdge({v:t.v,w:t.w,name:t.name},t.value)})),e}};function sd(t){return ud.map(t.nodes(),(function(e){var n=t.node(e),r=t.parent(e),o={v:e};return ud.isUndefined(n)||(o.value=n),ud.isUndefined(r)||(o.parent=r),o}))}function fd(t){return ud.map(t.edges(),(function(e){var n=t.edge(e),r={v:e.v,w:e.w};return ud.isUndefined(e.name)||(r.name=e.name),ud.isUndefined(n)||(r.value=n),r}))}var ld=Hl,dd=function(t){var e,n={},r=[];function o(r){ld.has(n,r)||(n[r]=!0,e.push(r),ld.each(t.successors(r),o),ld.each(t.predecessors(r),o))}return ld.each(t.nodes(),(function(t){e=[],o(t),e.length&&r.push(e)})),r};var hd=Hl,pd=vd;function vd(){this._arr=[],this._keyIndices={}}vd.prototype.size=function(){return this._arr.length},vd.prototype.keys=function(){return this._arr.map((function(t){return t.key}))},vd.prototype.has=function(t){return hd.has(this._keyIndices,t)},vd.prototype.priority=function(t){var e=this._keyIndices[t];if(void 0!==e)return this._arr[e].priority},vd.prototype.min=function(){if(0===this.size())throw new Error("Queue underflow");return this._arr[0].key},vd.prototype.add=function(t,e){var n=this._keyIndices;if(t=String(t),!hd.has(n,t)){var r=this._arr,o=r.length;return n[t]=o,r.push({key:t,priority:e}),this._decrease(o),!0}return!1},vd.prototype.removeMin=function(){this._swap(0,this._arr.length-1);var t=this._arr.pop();return delete this._keyIndices[t.key],this._heapify(0),t.key},vd.prototype.decrease=function(t,e){var n=this._keyIndices[t];if(e>this._arr[n].priority)throw new Error("New priority is greater than current priority. Key: "+t+" Old: "+this._arr[n].priority+" New: "+e);this._arr[n].priority=e,this._decrease(n)},vd.prototype._heapify=function(t){var e=this._arr,n=2*t,r=n+1,o=t;n<e.length&&(o=e[n].priority<e[o].priority?n:o,r<e.length&&(o=e[r].priority<e[o].priority?r:o),o!==t&&(this._swap(t,o),this._heapify(o)))},vd.prototype._decrease=function(t){for(var e,n=this._arr,r=n[t].priority;0!==t&&!(n[e=t>>1].priority<r);)this._swap(t,e),t=e},vd.prototype._swap=function(t,e){var n=this._arr,r=this._keyIndices,o=n[t],i=n[e];n[t]=i,n[e]=o,r[i.key]=t,r[o.key]=e};var gd=pd,yd=function(t,e,n,r){return function(t,e,n,r){var o,i,u={},a=new gd,c=function(t){var e=t.v!==o?t.v:t.w,r=u[e],c=n(t),s=i.distance+c;if(c<0)throw new Error("dijkstra does not allow negative edge weights. Bad edge: "+t+" Weight: "+c);s<r.distance&&(r.distance=s,r.predecessor=o,a.decrease(e,s))};t.nodes().forEach((function(t){var n=t===e?0:Number.POSITIVE_INFINITY;u[t]={distance:n},a.add(t,n)}));for(;a.size()>0&&(o=a.removeMin(),(i=u[o]).distance!==Number.POSITIVE_INFINITY);)r(o).forEach(c);return u}(t,String(e),n||bd,r||function(e){return t.outEdges(e)})},bd=Hl.constant(1);var _d=yd,wd=Hl,md=function(t,e,n){return wd.transform(t.nodes(),(function(r,o){r[o]=_d(t,o,e,n)}),{})};var jd=Hl,xd=function(t){var e=0,n=[],r={},o=[];function i(u){var a=r[u]={onStack:!0,lowlink:e,index:e++};if(n.push(u),t.successors(u).forEach((function(t){jd.has(r,t)?r[t].onStack&&(a.lowlink=Math.min(a.lowlink,r[t].index)):(i(t),a.lowlink=Math.min(a.lowlink,r[t].lowlink))})),a.lowlink===a.index){var c,s=[];do{c=n.pop(),r[c].onStack=!1,s.push(c)}while(u!==c);o.push(s)}}return t.nodes().forEach((function(t){jd.has(r,t)||i(t)})),o};var Ed=Hl,Od=xd,kd=function(t){return Ed.filter(Od(t),(function(e){return e.length>1||1===e.length&&t.hasEdge(e[0],e[0])}))};var Ad=function(t,e,n){return function(t,e,n){var r={},o=t.nodes();return o.forEach((function(t){r[t]={},r[t][t]={distance:0},o.forEach((function(e){t!==e&&(r[t][e]={distance:Number.POSITIVE_INFINITY})})),n(t).forEach((function(n){var o=n.v===t?n.w:n.v,i=e(n);r[t][o]={distance:i,predecessor:t}}))})),o.forEach((function(t){var e=r[t];o.forEach((function(n){var i=r[n];o.forEach((function(n){var r=i[t],o=e[n],u=i[n],a=r.distance+o.distance;a<u.distance&&(u.distance=a,u.predecessor=o.predecessor)}))}))})),r}(t,e||Cd,n||function(e){return t.outEdges(e)})},Cd=Hl.constant(1);var Md=Hl,Sd=$d;function $d(t){var e={},n={},r=[];if(Md.each(t.sinks(),(function o(i){if(Md.has(n,i))throw new Ld;Md.has(e,i)||(n[i]=!0,e[i]=!0,Md.each(t.predecessors(i),o),delete n[i],r.push(i))})),Md.size(e)!==t.nodeCount())throw new Ld;return r}function Ld(){}$d.CycleException=Ld,Ld.prototype=new Error;var zd=Sd;var Id=Hl,Nd=function(t,e,n){Id.isArray(e)||(e=[e]);var r=(t.isDirected()?t.successors:t.neighbors).bind(t),o=[],i={};return Id.each(e,(function(e){if(!t.hasNode(e))throw new Error("Graph does not have node: "+e);Fd(t,e,"post"===n,i,r,o)})),o};function Fd(t,e,n,r,o,i){Id.has(r,e)||(r[e]=!0,n||i.push(e),Id.each(o(e),(function(e){Fd(t,e,n,r,o,i)})),n&&i.push(e))}var Td=Nd;var Pd=Nd;var Bd=Hl,Dd=Xl,Ud=pd;var Wd={Graph:id.Graph,json:cd,alg:{components:dd,dijkstra:yd,dijkstraAll:md,findCycles:kd,floydWarshall:Ad,isAcyclic:function(t){try{zd(t)}catch(jh){if(jh instanceof zd.CycleException)return!1;throw jh}return!0},postorder:function(t,e){return Td(t,e,"post")},preorder:function(t,e){return Pd(t,e,"pre")},prim:function(t,e){var n,r=new Dd,o={},i=new Ud;function u(t){var r=t.v===n?t.w:t.v,u=i.priority(r);if(void 0!==u){var a=e(t);a<u&&(o[r]=n,i.decrease(r,a))}}if(0===t.nodeCount())return r;Bd.each(t.nodes(),(function(t){i.add(t,Number.POSITIVE_INFINITY),r.setNode(t)})),i.decrease(t.nodes()[0],0);var a=!1;for(;i.size()>0;){if(n=i.removeMin(),Bd.has(o,n))r.setEdge(n,o[n]);else{if(a)throw new Error("Input graph is not connected: "+t);a=!0}t.nodeEdges(n).forEach(u)}return r},tarjan:xd,topsort:Sd},version:id.version};function Vd(t,e){let n=!0;for(;n;){n=!1;for(let r=0;r<e.length;++r)for(let o=0;o<e[r].length-1;++o){const i=Rd(t,e,r);Gd(e[r],o,o+1);Rd(t,e,r)<i?n=!0:Gd(e[r],o,o+1)}}}function Rd(t,e,n){let r=0;return n>0&&(r+=Zn(t,e[n-1],e[n])),n+1<e.length&&(r+=Zn(t,e[n],e[n+1])),r}function Gd(t,e,n){const r=t[e];t[e]=t[n],t[n]=r}function Zd(t){const e=Math.floor(t.length/2);if(0===t.length)return-1;if(t.length%2==1)return t[e];if(2===t.length)return(t[0]+t[1])/2;const n=t[e-1]-t[0],r=t[t.length-1]-t[e];return(t[e-1]*r+t[e]*n)/(n+r)}function Yd(t,e,n=1,r=!1){if(n>0)for(let o=1;o<e.length;++o){const n=new Map;e[o].forEach((i=>{const u=Zd(Hd(t,e,o,o-1,i,r));n.set(i,u)})),qd(e[o],n)}else for(let o=e.length-2;o>=0;--o){const n=new Map;e[o].forEach((i=>{const u=Zd(Hd(t,e,o,o+1,i,r));n.set(i,u)})),qd(e[o],n)}}function Hd(t,e,n,r,o,i=!1){const u=e[n],a=e[r],c=[];return a.forEach(((e,n)=>{t.nodeEdges(e,o).length>0&&c.push(n)})),0===c.length&&i&&u.forEach(((e,n)=>{t.nodeEdges(e,o).length>0&&c.push(n+.5)})),c.sort(((t,e)=>t-e)),c}function qd(t,e){const n=new Map(t.map(((t,e)=>[t,e])));for(let r=1;r<t.length;++r)for(let o=r;o>0;--o){let r=o-1,i=e.get(t[r]),u=e.get(t[o]);for(;-1===(i=e.get(t[r]))&&r>0;)r--;if(-1===u||-1===i)break;if(i===u&&(i=n.get(t[r]),u=n.get(t[o])),u>=i)break;[t[o],t[r]]=[t[r],t[o]]}}function Xd(t,e=25){const n=function(t){const e=[];let n=t.nodes().filter((e=>0===t.node(e).rank)).concat(t.sources());return 0===n.length&&(n=t.nodes().slice(0,1)),Wd.alg.preorder(t,n).forEach((n=>{const r=t.node(n).rank;for(;r>=e.length;)e.push([]);e[r].push(n)})),e}(t);let r=n,o=0;for(;o++<e;)Yd(t,n,o%2==0?1:0),Vd(t,n),Jd(t,n)<Jd(t,r)&&(r=Kd(n));return r=r.map((t=>[t])),r}function Jd(t,e){let n=0;for(let r=0;r<e.length-1;++r)n+=Zn(t,e[r],e[r+1]);return n}function Kd(t){const e=[];for(const n of t){const t=[];e.push(t);for(const e of n)t.push(e)}return e}function Qd(){let t=[100,100],e=null,n=.5,r=function(t,e,n){return 1};const o=function(t){return t.data.value},i=function(t){return t.data?t.data.direction:"r"};function u(a,c){null===e&&u.scaleToFit(a,c),th(a,o,i),function(t,e,n){t.nodes().forEach((e=>{const r=t.node(e);r.dy=r.value*n})),t.edges().forEach((r=>{const o=t.edge(r);o.value=e(o),o.dy=o.value*n}))}(a,o,e);const s=eh(a,c);c.forEach((e=>{let o=0;e.forEach(((e,i)=>{const u=s[i]/Tn(s)*t[1],c=0===o?0:n*u/5,f=u-2*c,l=Tn(e,(t=>a.node(t).dy)),d=e.map(((t,n)=>a.node(t).value&&e[n+1]?r(e[n],e[n+1],a):0)),h=Math.max(0,f-l),p=Tn(d)?h/Tn(d):0;let v=o+c;1===e.length&&(v+=(f-a.node(e[0]).dy)/2);let g=Number.MAX_VALUE;e.forEach(((t,e)=>{const n=a.node(t);n.y=v,n.spaceAbove=g,n.spaceBelow=d[e]*p,v+=n.dy+n.spaceBelow,g=n.spaceBelow,n.data&&void 0!==n.data.forceY&&(n.y=c+n.data.forceY*(f-n.dy))})),e.length>0&&(a.node(e[e.length-1]).spaceBelow=Number.MAX_VALUE),o+=u}))}));const f=function(t,e){const n=[];return e.forEach(((r,o)=>{if(o===e.length-1)return;let i=0;r.forEach((e=>{e.forEach((e=>{t.outEdges(e).forEach((e=>{const n=t.edge(e).dy,r=t.node(e.w).y-t.node(e.v).y,o=Math.abs(r)-n,u=n*n-o*o,a=u>=0?Math.sqrt(u):n;a>i&&(i=a)}))}))})),n.push(i)})),n}(a,c);!function(t,e,n,r){const o=Tn(r);let i;if(o>n)i=r.map((t=>n*t/o));else{const t=n-o;i=r.map((n=>n+t/(e.length-1)))}let u=0;e.forEach(((e,n)=>{e.forEach((e=>{e.forEach((e=>{t.node(e).x=u}))})),u+=i[n]}))}(a,c,t[0],f);const l=[];return a.nodes().forEach((t=>{const e=a.node(t);e.id=t,l.push(e)})),l}return u.scaleToFit=function(r,u){th(r,o,i);const a=Tn(eh(r,u));a<=0?e=1:(e=t[1]/a,1!==n&&(e*=1-n))},u.size=function(e){return arguments.length?(t=e,u):t},u.separation=function(t){return arguments.length?(r=bt(t)?t:()=>t,u):r},u.whitespace=function(t){return arguments.length?(n=t,u):n},u.scale=function(t){return arguments.length?(e=t,u):e},u}function th(t,e,n){t.nodes().forEach((r=>{const o=Tn(t.inEdges(r),(n=>e(t.edge(n)))),i=Tn(t.outEdges(r),(n=>e(t.edge(n))));let u=t.node(r);u||t.setNode(r,u={}),u.value=Math.max(o,i),void 0===u.direction&&(u.direction=n(u)||"r")}))}function eh(t,e){if(0===e.length||0===e[0].length)return[];const n=e[0].length,r=new Array(n);for(let o=0;o<n;o++)r[o]=0;return e.forEach((e=>{e.forEach(((e,n)=>{const o=Tn(e,(e=>t.node(e).value));r[n]=Math.max(r[n],o)}))})),r}function nh(t,e){return t<e}function rh(t){return t&&t.length?function(t,e,n){for(var r=-1,o=t.length;++r<o;){var i=t[r],u=e(i);if(null!=u&&(void 0===a?u==u&&!w(u):n(u,a)))var a=u,c=i}return c}(t,Nn,nh):void 0}var oh=function(){for(var t=0;t<arguments.length;t++)if(void 0!==arguments[t])return arguments[t]};const ih=qo(oh);function uh(t,e){const n=function(t,e){const n=new Set,r=new Wd.Graph({directed:!0}),o=[];if(!t.hasNode(e))throw Error("node not in graph");return ah(t,e,n,r,o),t.nodes().forEach((e=>{n.has(e)||ah(t,e,n,r,o)})),o.forEach(((t,e)=>{r.node(t).thread=e+1<o.length?o[e+1]:o[0]})),r}(t,e);return t.edges().forEach((e=>{const r=function(t,e,n){const r=t.node(e),o=t.node(n);if(r.depth<o.depth){let e=r.thread;for(;t.node(e).depth>r.depth;){if(e===n)return 1;e=t.node(e).thread}}else if(o.depth<r.depth){let n=o.thread;for(;t.node(n).depth>o.depth;){if(n===e)return-1;n=t.node(n).thread}}return 0}(n,e.v,e.w);if(r<0){const n=t.edge(e)||{};n.reversed=!0,t.removeEdge(e),t.setEdge(e.w,e.v,n)}})),t}function ah(t,e,n,r,o,i=0){if(!n.has(e)){n.add(e),o.push(e),r.setNode(e,{depth:i});t.outEdges(e).map((t=>t.w)).forEach((u=>{n.has(u)||r.setEdge(e,u,{delta:1}),ah(t,u,n,r,o,i+1)}))}}function ch(t,e){if(0===t.nodes().length)return;const n=function(t,e){const n=new Wd.Graph({directed:!0});let r=!1;for(const u of e)if("min"===u.type){r=!0;break}if(!r){const n=t.sources();e=n.length>0?[{nodes:[n[0]],type:"min"},...e]:[{nodes:[t.nodes()[0]],type:"min"},...e]}const o=new Map;let i=0;return e.forEach((t=>{if(!t.nodes||0===t.nodes.length)return;const e=""+i++;t.nodes.forEach((t=>{o.set(t,e)})),n.setNode(e,{nodes:t.nodes,type:t.type})})),t.nodes().forEach((t=>{if(!o.has(t)){const e=""+i++,r={nodes:[t],type:"same"};o.set(t,e),n.setNode(e,r)}})),t.edges().forEach((e=>{const r=o.has(e.v)?o.get(e.v):e.v,i=o.has(e.w)?o.get(e.w):e.w,u=t.node(e.v)||{},a=t.node(e.w)||{},c=n.edge(r,i)||{delta:0};r===i?(c.delta=0,n.setEdge(r,i,c)):"l"===u.direction?(c.delta=Math.max(c.delta,"l"===a.direction?1:0),n.setEdge(i,r,c)):(c.delta=Math.max(c.delta,"l"===a.direction?0:1),n.setEdge(r,i,c))})),n}(t,e);!function(t){t.sources().forEach((e=>{"0"!==e&&t.setEdge("0",e,{delta:0,temp:!0})}))}(n),uh(n,"0"),function(t){var e;const n=t.sources(),r=new Set,o=new Set;for(t.edges().forEach((t=>{t.v===t.w&&o.add(t)}));n.length>0;){const i=n.shift();r.add(i);let u=t.node(i);u||t.setNode(i,u={}),u.rank=0;for(const n of t.inEdges(i)){const r=ih(null==(e=t.edge(n)||{})?void 0:e.delta,1);u.rank=Math.max(u.rank,t.node(n.v).rank+r)}for(const e of t.outEdges(i))o.add(e);for(const e of t.nodes())n.indexOf(e)<0&&!r.has(e)&&!t.inEdges(e).some((t=>!o.has(t)))&&n.push(e)}}(n),function(t){function e(e){const n=t.node(e),r=rh(t.outEdges(e).map((e=>t.node(e.w).rank-t.edge(e).delta)));void 0!==r&&(n.rank=r)}t.edges().forEach((n=>{t.edge(n).temp&&e(n.w)}))}(n),t.nodes().forEach((e=>{t.node(e)||t.setNode(e,{})})),n.nodes().forEach((e=>{const r=n.node(e);r.nodes.forEach((e=>{t.node(e).rank=r.rank}))}))}function sh(t){const e=Qd(),n=Vn(t);let r=[],o=[],i=null;function u(t=[],u=[],a={}){const s=function(t=[],e=[]){const n=new Wd.Graph({directed:!0,multigraph:!0});return e.forEach((t=>{n.setEdge(t.source,t.target,{data:t,points:[]},t.type)})),n.nodes().forEach((t=>n.setNode(t,{data:{}}))),t.forEach((t=>{n.hasNode(t.id)&&(n.node(t.id).data=t,void 0!==t.direction&&(n.node(t.id).direction=t.direction))})),n.edges().forEach((t=>{n.edge(t).source=n.node(t.v),n.edge(t).target=n.node(t.w)})),n}(u,t);var f;return a.order?(ch(s,a.rankSets||[]),i=a.order,i.length>0&&i[0].length>0&&!c(i[0][0])&&(i=i.map((t=>[t]))),i=i.map((t=>t.map((t=>t.filter((t=>void 0!==s.node(t)))))))):(ch(s,a.rankSets||[]),(f=s).edges().forEach((t=>{const e=f.node(t.v),n=f.node(t.w),r=f.edge(t);let o=e.rank;const i=[];if(o+1<=n.rank){for("l"===e.direction&&i.push(o);++o<n.rank;)i.push(o);"l"===n.direction&&i.push(o),Gn(f,t,r,i,"r")}else if(o>n.rank){for("l"!==e.direction&&i.push(o);o-- >n.rank+1;)i.push(o);"l"!==n.direction&&i.push(o),Gn(f,t,r,i,"l")}})),i=Xd(s)),r=e(s,i),function(t,{alignLinkTypes:e=!1}={}){t.nodes().forEach((n=>{const r=t.node(n);if(r.incoming=t.inEdges(n),r.outgoing=t.outEdges(n),e){const e=Pn(t,n);r.incoming.sort(Dn(t,e,!1)),r.outgoing.sort(Dn(t,e,!0))}else r.incoming.sort(Bn(t,!1)),r.outgoing.sort(Bn(t,!0))}))}(s,{alignLinkTypes:a.alignLinkTypes||!1}),o=n(s),function(t){t.nodes().filter((t=>t.startsWith("__"))).forEach((e=>{var n;let r=t.node(e);if(!r)return;let o=(null==(n=t.inEdges(e))?void 0:n.map((e=>t.edge(e))))||[];o.forEach((e=>{e.origLabel.dy=e.dy,e.origLabel.x0=e.x0,e.origLabel.y0=e.y0,e.origLabel.r0=e.r0,e.origLabel.d0=e.d0,e.origLabel.value=e.value,t.setEdge(e.origEdge,e.origLabel)}));let i,u=o.map((t=>t.r1));for(;r.dummy;)o=t.outEdges(e).map((e=>t.edge(e))),o.forEach(((t,e)=>{t.origLabel.points||(t.origLabel.points=[]),t.origLabel.points.push({d:t.d0,r0:t.r0,r1:u[e],x:r.x,y:t.y0})})),u=o.map((t=>t.r1)),i=t.successors(e)[0],t.removeNode(e),r=t.node(e=i);o.forEach((t=>{t.origLabel.x1=t.x1,t.origLabel.y1=t.y1,t.origLabel.r1=t.r1,t.origLabel.d1=t.d1}))}))}(s),function(t){t.edges().forEach((e=>{const n=t.edge(e);n.points.unshift({d:n.d0,ro:n.r0,x:n.x0,y:n.y0}),n.points.push({d:n.d1,ri:n.r1,x:n.x1,y:n.y1})}))}(s),s}return u.nodes=function(){return r},u.links=function(){return o},u.order=function(){return i},u.size=function(t){return arguments.length?(e.size(t),u):e.size()},u.separation=function(t){return arguments.length?(e.separation(t),u):e.separation()},u.whitespace=function(t){return arguments.length?(e.whitespace(t),u):e.whitespace()},u.edgeValue=function(t){return u},u.scale=function(t){return arguments.length?(e.scale(t),u):e.scale()},u}const fh=["#ACD5FC","#E37816","#07CC80","#0871D4","#CB8E55","#9FAE29","#427565","#6D55A6","#FEF445","#E177C2","#F48B7B","#D74B05","#AA896F","#14A14D","#C5114D"];var lh="[object Number]";function dh(t){return function(t){return"number"==typeof t||b(t)&&y(t)==lh}(t)&&t!=+t}const hh=t=>0===t.dy?0:2;function ph(t){const e=Math.max(hh(t),t.dy)/2;let{x0:n}=t,{x1:r}=t,{y0:o}=t,{y1:i}=t;r<n&&([n,r]=[r,n],[o,i]=[i,o]);const u=i>o?1:-1,a=function(t){const e=t.x1-t.x0,n=t.y1-t.y0;return[t.dy/2,(e*e+n*n)/Math.abs(4*n)]}(t),c=Math.max(a[0],Math.min(a[1],(r-n)/3));let s=Math.max(a[0],Math.min(a[1],t.r0||c)),f=Math.max(a[0],Math.min(a[1],t.r1||c));const l=r-n,d=i-o-u*(s+f),h=Math.sqrt(l*l+d*d),p=-u*Math.acos(Math.min(1,(s+f)/h)),v=Math.atan2(d,l);let g=Math.PI/2+u*(v+p),y=e*u*Math.sin(g),b=e*Math.cos(g),_=n+1*s*Math.sin(Math.abs(g)),w=r-1*f*Math.sin(Math.abs(g)),m=o+s*u*(1-Math.cos(g)),j=i-f*u*(1-Math.cos(g));function x(t,n){const r=t*(i-o)>0?1:0;let u=1*t*(i-o)>0?n+e:n-e;return 0===g&&(u=n),`A${u} ${u} ${Math.abs(g)} 0 ${r} `}(dh(g)||Math.abs(g)<.001)&&(g=0,s=0,f=0,_=n,w=r,m=o,j=i,y=0,b=e);return`M${[n,o-e]} ${x(1,s)}${[_+y,m-b]} L${[w+y,j-b]} ${x(-1,f)}${[r,i-e]} L${[r,i+e]} ${x(1,f)}${[w-y,j+b]} L${[_-y,m+b]} ${x(-1,s)}${[n,o+e]} Z`}function vh(t){var e;if(1===(null==(e=t.points)?void 0:e.length))return function(t){const e=t.points[0],n=Math.max(hh(t),t.dy)/2;return"down-right"===e.style?`M${[e.x-20,e.y-n]} L${[e.x,e.y-n]} L${[e.x,e.y+n]} L${[e.x-20,e.y+n]} Z`:"right-down"===e.style?`M${[e.x,e.y-n]} L${[e.x+20,e.y-n]} L${[e.x+20,e.y+n]} L${[e.x,e.y+n]} Z`:null}(t);let n,r="";for(let o=0;o<t.points.length-1;++o)n={d0:t.points[o].d,d1:t.points[o+1].d,dy:t.dy,r0:t.points[o].ro,r1:t.points[o+1].ri,x0:t.points[o].x,x1:t.points[o+1].x,y0:t.points[o].y,y1:t.points[o+1].y},r+=ph(n);return r}const gh=(t,e)=>{const n=void 0!==t.title?t.title:t.id;return e&&n.length>e?`${n.substr(0,15)}...`:n};function yh(e,{fontColor:n,maxRank:r,nodeWidth:o}){const{data:i}=e,u=function(t,e){let n=!1,r=!1;return t.rank<e/2&&(r=!0,n=!1),{right:r,top:n}}(e,r),{x:a,y:c}=function(t,e,n){const r=e.top?-25:t.dy/2-10;let o;return o=e.top?e.right?4:-4:e.right?10+n/2:-n/2-10,{x:t.x+o,y:t.y+r}}(e,u,o),s=function(e="",{fontColor:n="#666767",pointerEvents:r="all",textAnchor:o="start",x:i,y:u}){const a=new t.Text;return a.font({fill:n}),a.tspan(e),void 0!==i&&void 0!==u&&a.move(i,u),a.attr({"pointer-events":r,"text-anchor":o}),a}(gh(i,u.top?25:15),{fontColor:n,pointerEvents:"none",textAnchor:u.top?"middle":u.right?"start":"end",x:a,y:c});return s}function bh(e,n,r){var o,i;const{dy:u,x:a,y:c}=e,{fontColor:s,fontSize:f,nodeBorderColor:l,nodeBorderWidth:d,nodeWidth:h,onNodeClick:p}=r,v=new t.G({class:"node"}),g=function({borderColor:e="#caced0",color:n="#97cbe6",height:r=0,opacity:o=1,radius:i=0,width:u=0,x:a=0,y:c=0}={}){const s=new t.Rect;return s.attr({height:r,opacity:o,rx:i,ry:i,width:u,x:a,y:c}),s.fill(n),s.stroke({color:e,width:1}),s}({height:u,width:h,x:a-h/2,y:c});if(g.fill(null==(o=e.data)?void 0:o.color),g.stroke({color:l??(null==(i=e.data)?void 0:i.color),width:d}),g.on("click",(()=>{"function"==typeof p&&p(e)})),v.add(g),e.data&&u>Number.parseInt(f,10)){const t=yh(e,{fontColor:s,maxRank:n,nodeWidth:h});v.add(t)}return v}class _h{constructor(e,n){this.element=e,this.options=n,this.canvas=t.SVG().addTo(e).size("100%","100%").viewbox(`-${this.spacing} -${this.spacing+this.getYShift()} ${this.width+2*this.spacing} ${this.height+this.spacing+this.getYShift()}`).attr({style:this.options.canvasStyle})}getYShift(){return this.options.enableToolbar?30:0}add(t){this.canvas.add(t)}clear(){this.canvas.clear().viewbox(`-${this.spacing} -${this.spacing+this.getYShift()} ${this.width+2*this.spacing} ${this.height+this.spacing+this.getYShift()}`)}resetViewBox(){this.canvas.viewbox(`-${this.spacing} -${this.spacing+this.getYShift()} ${this.width+2*this.spacing} ${this.height+this.spacing+this.getYShift()}`)}updateViewBox(t,e,n,r){this.canvas.viewbox(`${t} ${e} ${n} ${r}`)}get height(){return this.options.viewPortHeight}get spacing(){return this.options.spacing}get width(){return this.options.viewPortWidth}}class wh extends _h{construct(t){const{nodeWidth:e,spacing:n,viewPortHeight:r,viewPortWidth:o}=this.options,i=sh({nodeWidth:e});i.size([o-e,r-n]);const u=t.nodes.map(((t,e)=>{const n=e%fh.length;return{...t,color:t.color??fh[n],colorIndex:n}})),a=i(t.edges,u,t.options);this.maxRank=i.order().length-1,this.graph=a}render({keepOldPosition:t=!1}={}){var e,n;const{enableTooltip:r,fontColor:o,fontFamily:i,fontSize:u,fontWeight:a,nodeBorderColor:c,nodeBorderWidth:s,nodeWidth:f,onNodeClick:l,tooltipId:d}=this.options,h=this.canvas.viewbox();this.clear();const p=rt({color:o,fontFamily:i,fontSize:u,fontWeight:a}),v=ot(f/2,0);v.attr("style",p);const g=(null==(e=this.graph)?void 0:e.nodes())||[],y=(null==(n=this.graph)?void 0:n.edges())||[],b=ot();y.forEach((t=>{this.renderEdge(t,b)})),v.add(b);const _=ot();if(g.forEach((t=>{const e=this.graph.node(t);if(e.dummy)return;const n=bh(e,this.maxRank,{fontColor:o,fontSize:u,nodeBorderColor:c,nodeBorderWidth:s,nodeWidth:f,onNodeClick:l});_.add(n)})),v.add(_),this.add(v),t&&this.updateViewBox(h.x,h.y,h.width,h.height),r){const t=((t="apex-tooltip-container")=>{const e=document.getElementById(t)||document.createElement("div");return e.id=t,e})(d);(document.body||document.getElementsByTagName("body")[0]).append(t)}}renderEdge(e,n){const{edgeGradientFill:r,edgeOpacity:o,enableTooltip:i,fontColor:u,fontFamily:a,fontSize:c,fontWeight:s,nodeWidth:f,tooltipBGColor:l,tooltipBorderColor:d,tooltipId:h,tooltipTemplate:p}=this.options,v=this.graph.edge(e.v,e.w,e.name),g=this.graph.node(e.v),y=this.graph.node(e.w),b=v.source,_=v.target;g.dummy&&(v.x0-=f/2),y.dummy&&(v.x1+=f/2);const w=function(e,{edgeColor:n,id:r="",opacity:o=1}={}){const i=new t.Path({d:e});return i.id(r),i.fill(n).opacity(o),i}(vh(v),{edgeColor:this.canvas.gradient("linear",(t=>{var e,n,o;t.stop(0,null==(e=b.data)?void 0:e.color),t.stop(1,r?null==(n=_.data)?void 0:n.color:null==(o=b.data)?void 0:o.color)})),id:`${e.v}-${e.w}`,opacity:o});if(i&&b.data&&_.data){const e=p?p({source:{...b.data,value:b.value},target:{..._.data,value:_.value},value:v.value}):null;w.on("mousemove",(n=>{var r;const o=rt({color:u,fontFamily:a,fontSize:c,fontWeight:s}),i=(({bgColor:t,borderColor:e,maxWidth:n,padding:r,x:o,y:i})=>{const u=["position: absolute;",`left: ${o+20}px;`,`top: ${i+20}px;`,`border: 1px solid ${e};`,"border-radius: 5px;"];return t&&u.push(`background-color: ${t};`),n&&u.push(`max-width: ${n}px`),void 0!==r&&u.push(`padding: ${r}px`),u.join(" ")})({bgColor:l,borderColor:d,x:n.pageX,y:n.pageY});null==(r=t.SVG(`[id="${n.target.id}"]`))||r.front().opacity(1),nt(h,i+o,e)})),w.on("mouseout",(e=>{var n;nt(h),null==(n=t.SVG(`[id="${e.target.id}"]`))||n.front().opacity(o)}))}n.add(w)}}const mh={canvasStyle:"border: 1px solid #caced0; box-sizing: border-box",edgeGradientFill:!0,edgeOpacity:.4,enableToolbar:!0,enableTooltip:!0,fontColor:"#212121",fontFamily:"",fontSize:"14px",fontWeight:"400",height:"auto",nodeBorderColor:null,nodeBorderWidth:1,nodeWidth:20,onNodeClick:void 0,spacing:20,tooltipBGColor:"#FFFFFF",tooltipBorderColor:"#BCBCBC",tooltipId:"apexsankey-tooltip-container",tooltipTemplate:({source:t,target:e,value:n})=>`\n      <div style='display:flex;align-items:center;gap:5px;padding:5px 10px;'>\n        <div style='width:12px;height:12px;background-color:${t.color}'></div>\n        <div>${t.title}</div>\n        <div>=></div>\n        <div style='width:12px;height:12px;background-color:${e.color}'></div>\n        <div>${e.title}</div>\n        <div>: ${n}</div> \n      </div>\n    `,viewPortHeight:500,viewPortWidth:800,width:"100%"};return class{constructor(t,e){this.element=t,this.options=e,this.options={...mh,...e};const{height:n,width:r}=this.options;this.graph=new wh(this.element,this.options);let o=!1,i=0;"string"==typeof r&&/^\d+(\.\d+)?%$/.test(r)?(i=Number(r.substring(0,r.length-1)),o=!0):i=Number(r);const u="auto"===n?i/1.6:n;this.element.style.width=`${i}${o?"%":"px"}`,this.element.style.height=`${u}${o?"%":"px"}`,this.element.style.position="relative"}render(t){if(!this.element)throw new Error("Element not found");if(this.graph.construct(t),this.graph.render(),this.options.enableToolbar){new at(this.element,this.graph.canvas,"apex-sankey").render({enableExport:!0})}return this.graph}}}));
