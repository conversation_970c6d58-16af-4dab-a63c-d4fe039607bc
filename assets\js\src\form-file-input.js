import"../../admin.bundle-DI9_jvUJ.js";import{r as o,p as e,a as i,b as r,c as a,d as t}from"../../filepond-plugin-image-preview-DrhefRSI.js";import"../../main-Cyta4iCA.js";o(e,i,r,a);t(document.querySelector("#multipleFileUpload"));t(document.querySelector("#squareUpload"),{labelIdle:'Drag & Drop your picture or <span class="filepond--label-action">Browse</span>',imagePreviewHeight:120,imageCropAspectRatio:"1:1",stylePanelLayout:"compact circle",styleLoadIndicatorPosition:"center bottom",styleProgressIndicatorPosition:"right bottom",styleButtonRemoveItemPosition:"left bottom",styleButtonProcessItemPosition:"right bottom"});
