import{h as e,r as t,c as o,H as i,g as n}from"./p-6befb151.js";import{a as r,D as a,d}from"./p-db18a2a6.js";var c,s="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},h=(function(e){var t=function(e){var t=/(?:^|\s)lang(?:uage)?-([\w-]+)(?=\s|$)/i,o=0,i={},n={manual:e.Prism&&e.Prism.manual,disableWorkerMessageHandler:e.Prism&&e.Prism.disableWorkerMessageHandler,util:{encode:function e(t){return t instanceof r?new r(t.type,e(t.content),t.alias):Array.isArray(t)?t.map(e):t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/\u00a0/g," ")},type:function(e){return Object.prototype.toString.call(e).slice(8,-1)},objId:function(e){return e.__id||Object.defineProperty(e,"__id",{value:++o}),e.__id},clone:function e(t,o){var i,r;switch(o=o||{},n.util.type(t)){case"Object":if(r=n.util.objId(t),o[r])return o[r];for(var a in o[r]=i={},t)t.hasOwnProperty(a)&&(i[a]=e(t[a],o));return i;case"Array":return r=n.util.objId(t),o[r]?o[r]:(o[r]=i=[],t.forEach((function(t,n){i[n]=e(t,o)})),i);default:return t}},getLanguage:function(e){for(;e;){var o=t.exec(e.className);if(o)return o[1].toLowerCase();e=e.parentElement}return"none"},setLanguage:function(e,o){e.className=e.className.replace(RegExp(t,"gi"),""),e.classList.add("language-"+o)},currentScript:function(){if("undefined"==typeof document)return null;if("currentScript"in document)return document.currentScript;try{throw new Error}catch(i){var e=(/at [^(\r\n]*\((.*):[^:]+:[^:]+\)$/i.exec(i.stack)||[])[1];if(e){var t=document.getElementsByTagName("script");for(var o in t)if(t[o].src==e)return t[o]}return null}},isActive:function(e,t,o){for(var i="no-"+t;e;){var n=e.classList;if(n.contains(t))return!0;if(n.contains(i))return!1;e=e.parentElement}return!!o}},languages:{plain:i,plaintext:i,text:i,txt:i,extend:function(e,t){var o=n.util.clone(n.languages[e]);for(var i in t)o[i]=t[i];return o},insertBefore:function(e,t,o,i){var r=(i=i||n.languages)[e],a={};for(var d in r)if(r.hasOwnProperty(d)){if(d==t)for(var c in o)o.hasOwnProperty(c)&&(a[c]=o[c]);o.hasOwnProperty(d)||(a[d]=r[d])}var s=i[e];return i[e]=a,n.languages.DFS(n.languages,(function(t,o){o===s&&t!=e&&(this[t]=a)})),a},DFS:function e(t,o,i,r){r=r||{};var a=n.util.objId;for(var d in t)if(t.hasOwnProperty(d)){o.call(t,d,t[d],i||d);var c=t[d],s=n.util.type(c);"Object"!==s||r[a(c)]?"Array"!==s||r[a(c)]||(r[a(c)]=!0,e(c,o,d,r)):(r[a(c)]=!0,e(c,o,null,r))}}},plugins:{},highlightAll:function(e,t){n.highlightAllUnder(document,e,t)},highlightAllUnder:function(e,t,o){var i={callback:o,container:e,selector:'code[class*="language-"], [class*="language-"] code, code[class*="lang-"], [class*="lang-"] code'};n.hooks.run("before-highlightall",i),i.elements=Array.prototype.slice.apply(i.container.querySelectorAll(i.selector)),n.hooks.run("before-all-elements-highlight",i);for(var r,a=0;r=i.elements[a++];)n.highlightElement(r,!0===t,i.callback)},highlightElement:function(t,o,i){var r=n.util.getLanguage(t),a=n.languages[r];n.util.setLanguage(t,r);var d=t.parentElement;d&&"pre"===d.nodeName.toLowerCase()&&n.util.setLanguage(d,r);var c={element:t,language:r,grammar:a,code:t.textContent};function s(e){c.highlightedCode=e,n.hooks.run("before-insert",c),c.element.innerHTML=c.highlightedCode,n.hooks.run("after-highlight",c),n.hooks.run("complete",c),i&&i.call(c.element)}if(n.hooks.run("before-sanity-check",c),(d=c.element.parentElement)&&"pre"===d.nodeName.toLowerCase()&&!d.hasAttribute("tabindex")&&d.setAttribute("tabindex","0"),!c.code)return n.hooks.run("complete",c),void(i&&i.call(c.element));if(n.hooks.run("before-highlight",c),c.grammar)if(o&&e.Worker){var h=new Worker(n.filename);h.onmessage=function(e){s(e.data)},h.postMessage(JSON.stringify({language:c.language,code:c.code,immediateClose:!0}))}else s(n.highlight(c.code,c.grammar,c.language));else s(n.util.encode(c.code))},highlight:function(e,t,o){var i={code:e,grammar:t,language:o};if(n.hooks.run("before-tokenize",i),!i.grammar)throw new Error('The language "'+i.language+'" has no grammar.');return i.tokens=n.tokenize(i.code,i.grammar),n.hooks.run("after-tokenize",i),r.stringify(n.util.encode(i.tokens),i.language)},tokenize:function(e,t){var o=t.rest;if(o){for(var i in o)t[i]=o[i];delete t.rest}var n=new c;return s(n,n.head,e),d(e,n,t,n.head,0),function(e){for(var t=[],o=e.head.next;o!==e.tail;)t.push(o.value),o=o.next;return t}(n)},hooks:{all:{},add:function(e,t){var o=n.hooks.all;o[e]=o[e]||[],o[e].push(t)},run:function(e,t){var o=n.hooks.all[e];if(o&&o.length)for(var i,r=0;i=o[r++];)i(t)}},Token:r};function r(e,t,o,i){this.type=e,this.content=t,this.alias=o,this.length=0|(i||"").length}function a(e,t,o,i){e.lastIndex=t;var n=e.exec(o);if(n&&i&&n[1]){var r=n[1].length;n.index+=r,n[0]=n[0].slice(r)}return n}function d(e,t,o,i,c,g){for(var l in o)if(o.hasOwnProperty(l)&&o[l]){var u=o[l];u=Array.isArray(u)?u:[u];for(var p=0;p<u.length;++p){if(g&&g.cause==l+","+p)return;var b=u[p],v=b.inside,f=!!b.lookbehind,k=!!b.greedy,m=b.alias;if(k&&!b.pattern.global){var w=b.pattern.toString().match(/[imsuy]*$/)[0];b.pattern=RegExp(b.pattern.source,w+"g")}for(var y=b.pattern||b,x=i.next,F=c;x!==t.tail&&!(g&&F>=g.reach);F+=x.value.length,x=x.next){var $=x.value;if(t.length>e.length)return;if(!($ instanceof r)){var A,j=1;if(k){if(!(A=a(y,F,e,f))||A.index>=e.length)break;var z=A.index,C=A.index+A[0].length,S=F;for(S+=x.value.length;z>=S;)S+=(x=x.next).value.length;if(F=S-=x.value.length,x.value instanceof r)continue;for(var L=x;L!==t.tail&&(S<C||"string"==typeof L.value);L=L.next)j++,S+=L.value.length;j--,$=e.slice(F,S),A.index-=F}else if(!(A=a(y,0,$,f)))continue;var _=A[0],E=$.slice(0,z=A.index),O=$.slice(z+_.length),D=F+$.length;g&&D>g.reach&&(g.reach=D);var T=x.prev;if(E&&(T=s(t,T,E),F+=E.length),h(t,T,j),x=s(t,T,new r(l,v?n.tokenize(_,v):_,m,_)),O&&s(t,x,O),j>1){var M={cause:l+","+p,reach:D};d(e,t,o,x.prev,F,M),g&&M.reach>g.reach&&(g.reach=M.reach)}}}}}}function c(){var e={value:null,prev:null,next:null},t={value:null,prev:e,next:null};e.next=t,this.head=e,this.tail=t,this.length=0}function s(e,t,o){var i=t.next,n={value:o,prev:t,next:i};return t.next=n,i.prev=n,e.length++,n}function h(e,t,o){for(var i=t.next,n=0;n<o&&i!==e.tail;n++)i=i.next;t.next=i,i.prev=t,e.length-=n}if(e.Prism=n,r.stringify=function e(t,o){if("string"==typeof t)return t;if(Array.isArray(t)){var i="";return t.forEach((function(t){i+=e(t,o)})),i}var r={type:t.type,content:e(t.content,o),tag:"span",classes:["token",t.type],attributes:{},language:o},a=t.alias;a&&(Array.isArray(a)?Array.prototype.push.apply(r.classes,a):r.classes.push(a)),n.hooks.run("wrap",r);var d="";for(var c in r.attributes)d+=" "+c+'="'+(r.attributes[c]||"").replace(/"/g,"&quot;")+'"';return"<"+r.tag+' class="'+r.classes.join(" ")+'"'+d+">"+r.content+"</"+r.tag+">"},!e.document)return e.addEventListener?(n.disableWorkerMessageHandler||e.addEventListener("message",(function(t){var o=JSON.parse(t.data),i=o.language,r=o.immediateClose;e.postMessage(n.highlight(o.code,n.languages[i],i)),r&&e.close()}),!1),n):n;var g=n.util.currentScript();function l(){n.manual||n.highlightAll()}if(g&&(n.filename=g.src,g.hasAttribute("data-manual")&&(n.manual=!0)),!n.manual){var u=document.readyState;"loading"===u||"interactive"===u&&g&&g.defer?document.addEventListener("DOMContentLoaded",l):window.requestAnimationFrame?window.requestAnimationFrame(l):window.setTimeout(l,16)}return n}("undefined"!=typeof window?window:"undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope?self:{});
/**
 * Prism: Lightweight, robust, elegant syntax highlighting
 *
 * @license MIT <https://opensource.org/licenses/MIT>
 * <AUTHOR> Verou <https://lea.verou.me>
 * @namespace
 * @public
 */e.exports&&(e.exports=t),void 0!==s&&(s.Prism=t),t.languages.markup={comment:{pattern:/<!--(?:(?!<!--)[\s\S])*?-->/,greedy:!0},prolog:{pattern:/<\?[\s\S]+?\?>/,greedy:!0},doctype:{pattern:/<!DOCTYPE(?:[^>"'[\]]|"[^"]*"|'[^']*')+(?:\[(?:[^<"'\]]|"[^"]*"|'[^']*'|<(?!!--)|<!--(?:[^-]|-(?!->))*-->)*\]\s*)?>/i,greedy:!0,inside:{"internal-subset":{pattern:/(^[^\[]*\[)[\s\S]+(?=\]>$)/,lookbehind:!0,greedy:!0,inside:null},string:{pattern:/"[^"]*"|'[^']*'/,greedy:!0},punctuation:/^<!|>$|[[\]]/,"doctype-tag":/^DOCTYPE/i,name:/[^\s<>'"]+/}},cdata:{pattern:/<!\[CDATA\[[\s\S]*?\]\]>/i,greedy:!0},tag:{pattern:/<\/?(?!\d)[^\s>\/=$<%]+(?:\s(?:\s*[^\s>\/=]+(?:\s*=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+(?=[\s>]))|(?=[\s/>])))+)?\s*\/?>/,greedy:!0,inside:{tag:{pattern:/^<\/?[^\s>\/]+/,inside:{punctuation:/^<\/?/,namespace:/^[^\s>\/:]+:/}},"special-attr":[],"attr-value":{pattern:/=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+)/,inside:{punctuation:[{pattern:/^=/,alias:"attr-equals"},/"|'/]}},punctuation:/\/?>/,"attr-name":{pattern:/[^\s>\/]+/,inside:{namespace:/^[^\s>\/:]+:/}}}},entity:[{pattern:/&[\da-z]{1,8};/i,alias:"named-entity"},/&#x?[\da-f]{1,8};/i]},t.languages.markup.tag.inside["attr-value"].inside.entity=t.languages.markup.entity,t.languages.markup.doctype.inside["internal-subset"].inside=t.languages.markup,t.hooks.add("wrap",(function(e){"entity"===e.type&&(e.attributes.title=e.content.replace(/&amp;/,"&"))})),Object.defineProperty(t.languages.markup.tag,"addInlined",{value:function(e,o){var i={};i["language-"+o]={pattern:/(^<!\[CDATA\[)[\s\S]+?(?=\]\]>$)/i,lookbehind:!0,inside:t.languages[o]},i.cdata=/^<!\[CDATA\[|\]\]>$/i;var n={"included-cdata":{pattern:/<!\[CDATA\[[\s\S]*?\]\]>/i,inside:i}};n["language-"+o]={pattern:/[\s\S]+/,inside:t.languages[o]};var r={};r[e]={pattern:RegExp(/(<__[^>]*>)(?:<!\[CDATA\[(?:[^\]]|\](?!\]>))*\]\]>|(?!<!\[CDATA\[)[\s\S])*?(?=<\/__>)/.source.replace(/__/g,(function(){return e})),"i"),lookbehind:!0,greedy:!0,inside:n},t.languages.insertBefore("markup","cdata",r)}}),Object.defineProperty(t.languages.markup.tag,"addAttribute",{value:function(e,o){t.languages.markup.tag.inside["special-attr"].push({pattern:RegExp(/(^|["'\s])/.source+"(?:"+e+")"+/\s*=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+(?=[\s>]))/.source,"i"),lookbehind:!0,inside:{"attr-name":/^[^\s=]+/,"attr-value":{pattern:/=[\s\S]+/,inside:{value:{pattern:/(^=\s*(["']|(?!["'])))\S[\s\S]*(?=\2$)/,lookbehind:!0,alias:[o,"language-"+o],inside:t.languages[o]},punctuation:[{pattern:/^=/,alias:"attr-equals"},/"|'/]}}}})}}),t.languages.html=t.languages.markup,t.languages.mathml=t.languages.markup,t.languages.svg=t.languages.markup,t.languages.xml=t.languages.extend("markup",{}),t.languages.ssml=t.languages.xml,t.languages.atom=t.languages.xml,t.languages.rss=t.languages.xml,function(e){var t=/(?:"(?:\\(?:\r\n|[\s\S])|[^"\\\r\n])*"|'(?:\\(?:\r\n|[\s\S])|[^'\\\r\n])*')/;e.languages.css={comment:/\/\*[\s\S]*?\*\//,atrule:{pattern:/@[\w-](?:[^;{\s]|\s+(?![\s{]))*(?:;|(?=\s*\{))/,inside:{rule:/^@[\w-]+/,"selector-function-argument":{pattern:/(\bselector\s*\(\s*(?![\s)]))(?:[^()\s]|\s+(?![\s)])|\((?:[^()]|\([^()]*\))*\))+(?=\s*\))/,lookbehind:!0,alias:"selector"},keyword:{pattern:/(^|[^\w-])(?:and|not|only|or)(?![\w-])/,lookbehind:!0}}},url:{pattern:RegExp("\\burl\\((?:"+t.source+"|"+/(?:[^\\\r\n()"']|\\[\s\S])*/.source+")\\)","i"),greedy:!0,inside:{function:/^url/i,punctuation:/^\(|\)$/,string:{pattern:RegExp("^"+t.source+"$"),alias:"url"}}},selector:{pattern:RegExp("(^|[{}\\s])[^{}\\s](?:[^{};\"'\\s]|\\s+(?![\\s{])|"+t.source+")*(?=\\s*\\{)"),lookbehind:!0},string:{pattern:t,greedy:!0},property:{pattern:/(^|[^-\w\xA0-\uFFFF])(?!\s)[-_a-z\xA0-\uFFFF](?:(?!\s)[-\w\xA0-\uFFFF])*(?=\s*:)/i,lookbehind:!0},important:/!important\b/i,function:{pattern:/(^|[^-a-z0-9])[-a-z0-9]+(?=\()/i,lookbehind:!0},punctuation:/[(){};:,]/},e.languages.css.atrule.inside.rest=e.languages.css;var o=e.languages.markup;o&&(o.tag.addInlined("style","css"),o.tag.addAttribute("style","css"))}(t),t.languages.clike={comment:[{pattern:/(^|[^\\])\/\*[\s\S]*?(?:\*\/|$)/,lookbehind:!0,greedy:!0},{pattern:/(^|[^\\:])\/\/.*/,lookbehind:!0,greedy:!0}],string:{pattern:/(["'])(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1/,greedy:!0},"class-name":{pattern:/(\b(?:class|extends|implements|instanceof|interface|new|trait)\s+|\bcatch\s+\()[\w.\\]+/i,lookbehind:!0,inside:{punctuation:/[.\\]/}},keyword:/\b(?:break|catch|continue|do|else|finally|for|function|if|in|instanceof|new|null|return|throw|try|while)\b/,boolean:/\b(?:false|true)\b/,function:/\b\w+(?=\()/,number:/\b0x[\da-f]+\b|(?:\b\d+(?:\.\d*)?|\B\.\d+)(?:e[+-]?\d+)?/i,operator:/[<>]=?|[!=]=?=?|--?|\+\+?|&&?|\|\|?|[?*/~^%]/,punctuation:/[{}[\];(),.:]/},t.languages.javascript=t.languages.extend("clike",{"class-name":[t.languages.clike["class-name"],{pattern:/(^|[^$\w\xA0-\uFFFF])(?!\s)[_$A-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\.(?:constructor|prototype))/,lookbehind:!0}],keyword:[{pattern:/((?:^|\})\s*)catch\b/,lookbehind:!0},{pattern:/(^|[^.]|\.\.\.\s*)\b(?:as|assert(?=\s*\{)|async(?=\s*(?:function\b|\(|[$\w\xA0-\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally(?=\s*(?:\{|$))|for|from(?=\s*(?:['"]|$))|function|(?:get|set)(?=\s*(?:[#\[$\w\xA0-\uFFFF]|$))|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\b/,lookbehind:!0}],function:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*(?:\.\s*(?:apply|bind|call)\s*)?\()/,number:{pattern:RegExp(/(^|[^\w$])/.source+"(?:"+/NaN|Infinity/.source+"|"+/0[bB][01]+(?:_[01]+)*n?/.source+"|"+/0[oO][0-7]+(?:_[0-7]+)*n?/.source+"|"+/0[xX][\dA-Fa-f]+(?:_[\dA-Fa-f]+)*n?/.source+"|"+/\d+(?:_\d+)*n/.source+"|"+/(?:\d+(?:_\d+)*(?:\.(?:\d+(?:_\d+)*)?)?|\.\d+(?:_\d+)*)(?:[Ee][+-]?\d+(?:_\d+)*)?/.source+")"+/(?![\w$])/.source),lookbehind:!0},operator:/--|\+\+|\*\*=?|=>|&&=?|\|\|=?|[!=]==|<<=?|>>>?=?|[-+*/%&|^!=<>]=?|\.{3}|\?\?=?|\?\.?|[~:]/}),t.languages.javascript["class-name"][0].pattern=/(\b(?:class|extends|implements|instanceof|interface|new)\s+)[\w.\\]+/,t.languages.insertBefore("javascript","keyword",{regex:{pattern:/((?:^|[^$\w\xA0-\uFFFF."'\])\s]|\b(?:return|yield))\s*)\/(?:\[(?:[^\]\\\r\n]|\\.)*\]|\\.|[^/\\\[\r\n])+\/[dgimyus]{0,7}(?=(?:\s|\/\*(?:[^*]|\*(?!\/))*\*\/)*(?:$|[\r\n,.;:})\]]|\/\/))/,lookbehind:!0,greedy:!0,inside:{"regex-source":{pattern:/^(\/)[\s\S]+(?=\/[a-z]*$)/,lookbehind:!0,alias:"language-regex",inside:t.languages.regex},"regex-delimiter":/^\/|\/$/,"regex-flags":/^[a-z]+$/}},"function-variable":{pattern:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*[=:]\s*(?:async\s*)?(?:\bfunction\b|(?:\((?:[^()]|\([^()]*\))*\)|(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)\s*=>))/,alias:"function"},parameter:[{pattern:/(function(?:\s+(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)?\s*\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\))/,lookbehind:!0,inside:t.languages.javascript},{pattern:/(^|[^$\w\xA0-\uFFFF])(?!\s)[_$a-z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*=>)/i,lookbehind:!0,inside:t.languages.javascript},{pattern:/(\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\)\s*=>)/,lookbehind:!0,inside:t.languages.javascript},{pattern:/((?:\b|\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\w\xA0-\uFFFF]))(?:(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*\s*)\(\s*|\]\s*\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\)\s*\{)/,lookbehind:!0,inside:t.languages.javascript}],constant:/\b[A-Z](?:[A-Z_]|\dx?)*\b/}),t.languages.insertBefore("javascript","string",{hashbang:{pattern:/^#!.*/,greedy:!0,alias:"comment"},"template-string":{pattern:/`(?:\\[\s\S]|\$\{(?:[^{}]|\{(?:[^{}]|\{[^}]*\})*\})+\}|(?!\$\{)[^\\`])*`/,greedy:!0,inside:{"template-punctuation":{pattern:/^`|`$/,alias:"string"},interpolation:{pattern:/((?:^|[^\\])(?:\\{2})*)\$\{(?:[^{}]|\{(?:[^{}]|\{[^}]*\})*\})+\}/,lookbehind:!0,inside:{"interpolation-punctuation":{pattern:/^\$\{|\}$/,alias:"punctuation"},rest:t.languages.javascript}},string:/[\s\S]+/}},"string-property":{pattern:/((?:^|[,{])[ \t]*)(["'])(?:\\(?:\r\n|[\s\S])|(?!\2)[^\\\r\n])*\2(?=\s*:)/m,lookbehind:!0,greedy:!0,alias:"property"}}),t.languages.insertBefore("javascript","operator",{"literal-property":{pattern:/((?:^|[,{])[ \t]*)(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*:)/m,lookbehind:!0,alias:"property"}}),t.languages.markup&&(t.languages.markup.tag.addInlined("script","javascript"),t.languages.markup.tag.addAttribute(/on(?:abort|blur|change|click|composition(?:end|start|update)|dblclick|error|focus(?:in|out)?|key(?:down|up)|load|mouse(?:down|enter|leave|move|out|over|up)|reset|resize|scroll|select|slotchange|submit|unload|wheel)/.source,"javascript")),t.languages.js=t.languages.javascript,function(){if(void 0!==t&&"undefined"!=typeof document){Element.prototype.matches||(Element.prototype.matches=Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector);var e={js:"javascript",py:"python",rb:"ruby",ps1:"powershell",psm1:"powershell",sh:"bash",bat:"batch",h:"c",tex:"latex"},o="data-src-status",i="loaded",n='pre[data-src]:not([data-src-status="loaded"]):not([data-src-status="loading"])';t.hooks.add("before-highlightall",(function(e){e.selector+=", "+n})),t.hooks.add("before-sanity-check",(function(r){var a=r.element;if(a.matches(n)){r.code="",a.setAttribute(o,"loading");var d=a.appendChild(document.createElement("CODE"));d.textContent="Loading…";var c=a.getAttribute("data-src"),s=r.language;if("none"===s){var h=(/\.(\w+)$/.exec(c)||[,"none"])[1];s=e[h]||h}t.util.setLanguage(d,s),t.util.setLanguage(a,s);var g=t.plugins.autoloader;g&&g.loadLanguages(s),function(e){var n=new XMLHttpRequest;n.open("GET",e,!0),n.onreadystatechange=function(){4==n.readyState&&(n.status<400&&n.responseText?function(e){a.setAttribute(o,i);var n=function(e){var t=/^\s*(\d+)\s*(?:(,)\s*(?:(\d+)\s*)?)?$/.exec(e||"");if(t){var o=Number(t[1]),i=t[3];return t[2]?i?[o,Number(i)]:[o,void 0]:[o,o]}}(a.getAttribute("data-range"));if(n){var r=e.split(/\r\n?|\n/g),c=n[0],s=null==n[1]?r.length:n[1];c<0&&(c+=r.length),c=Math.max(0,Math.min(c-1,r.length)),s<0&&(s+=r.length),s=Math.max(0,Math.min(s,r.length)),e=r.slice(c,s).join("\n"),a.hasAttribute("data-start")||a.setAttribute("data-start",String(c+1))}d.textContent=e,t.highlightElement(d)}(n.responseText):function(e){a.setAttribute(o,"failed"),d.textContent=e}(n.status>=400?"✖ Error "+n.status+" while fetching file: "+n.statusText:"✖ Error: File does not exist or is empty"))},n.send(null)}(c)}})),t.plugins.fileHighlight={highlight:function(e){for(var o,i=(e||document).querySelectorAll(n),r=0;o=i[r++];)t.highlightElement(o)}};var r=!1;t.fileHighlight=function(){r||(console.warn("Prism.fileHighlight is deprecated. Use `Prism.plugins.fileHighlight.highlight` instead."),r=!0),t.plugins.fileHighlight.highlight.apply(this,arguments)}}}()}(c={path:undefined,exports:{},require:function(){return function(){throw new Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs")}()}}),c.exports);const g=({highlightLines:e,refCode:t})=>{if(!t.hasChildNodes())return;const{rows:o,rowsGroup:i}=l({highlightLines:e});if(o.length<=0)return;let n=0,r=-1,a=-1;Array.from(t.childNodes).forEach((e=>{n=e.offsetTop>r?n+1:n,r=e.offsetTop,a=-1===a||a>e.offsetHeight?e.offsetHeight:a;const t=e.offsetHeight>a?n+1:n;o.indexOf(t)>-1&&e.classList.add("highlight",`group-${i[`row_${t}`]}`)})),t.classList.add("animate")},l=({highlightLines:e})=>{const t=e.split(" ");if(!t||t.length<=0)return{rows:[],rowsGroup:{}};const o=[];let i={};return t.forEach(((e,t)=>{const n=e.replace(/-/g,",").split(",");if(n&&n.length>=1){const e=parseInt(n[0],0),r=parseInt(n[1],0);for(let n=e;n<=(isNaN(r)?e:r);n++)o.push(n),(!(`row_${n}`in i)||i[`row_${n}`]>t)&&(i[`row_${n}`]=t)}})),{rows:o,rowsGroup:i}},u=async e=>{var t;e===a.UBUNTU&&await(t="google-fonts-ubuntu","https://fonts.googleapis.com/css?family=Ubuntu|Ubuntu+Mono&display=swap",new Promise(((e,o)=>{if(document.getElementById(t))return void e("CSS already loaded.");let i=document.createElement("link");i.id=t,i.setAttribute("rel","stylesheet"),i.setAttribute("href","https://fonts.googleapis.com/css?family=Ubuntu|Ubuntu+Mono&display=swap"),i.addEventListener("load",(()=>e("CSS loaded."))),i.addEventListener("error",(()=>o("Error loading css."))),i.addEventListener("abort",(()=>o("CSS loading aborted."))),document.head.appendChild(i)})))},p=e=>{const{prismjs_cdn:t}=window;return`${null!=t?t:"https://unpkg.com/prismjs@latest"}/components/prism-${e}.min.js`},b=({style:t})=>e("style",null,`\n      :host ${t};\n    `),v=({start:t,end:o})=>e("style",null,`\n      ${void 0!==t&&void 0!==o?`code.highlight > :nth-child(n+${t+1}):nth-child(-n+${o+1}) *`:"div.container code.highlight > div.highlight *"} {\n        background: var(--deckgo-highlight-code-line-background);\n        border-top: var(--deckgo-highlight-code-line-border-top);\n        border-bottom: var(--deckgo-highlight-code-line-border-bottom);\n        font-weight: var(--deckgo-highlight-code-line-font-weight);\n        opacity: var(--deckgo-highlight-code-line-opacity, 1);\n      }\n\n      ${void 0!==t&&void 0!==o?`code.highlight > div.line-number:nth-child(n+${t+1}):nth-child(-n+${o+1}):before`:"div.container code.highlight > div.highlight:before"} {\n        color: var(--deckgo-highlight-code-line-numbers-color, var(--deckgo-highlight-code-token-comment, #6272a4));\n      }\n    `),f=class{constructor(e){t(this,e),this.prismLanguageLoaded=o(this,"prismLanguageLoaded",7),this.prismLanguageError=o(this,"prismLanguageError",7),this.language="javascript",this.lineNumbers=!1,this.terminal=a.CARBON,this.editable=!1,this.theme=r.DRACULA,this.parseAfterUpdate=!1,this.loaded=!1,this.highlightGroup=void 0,this.revealProgress="start",this.highlightRows=void 0}componentWillLoad(){Promise.all([u(this.terminal),this.loadTheme()]).then((()=>{}))}async componentDidLoad(){const e=this.languageDidLoad();await this.loadLanguages(),e&&this.parse()}componentDidUpdate(){this.parseAfterUpdate&&(this.parse(),this.parseAfterUpdate=!1)}async loadTheme(){if(this.terminal!==a.CARBON||!this.theme)return void(this.themeStyle=void 0);const{theme:e}=await function(e){if(e&&void 0!==e)switch(e){case r["3024-NIGHT"]:return import("./p-5a00b34d.js");case r["A11Y-DARK"]:return import("./p-70432c9b.js");case r["BASE16-DARK"]:return import("./p-dfa65a46.js");case r["BASE16-LIGHT"]:return import("./p-c52ed8a7.js");case r.BLACKBOARD:return import("./p-ed7fefad.js");case r.COBALT:return import("./p-b202519d.js");case r.DUOTONE:return import("./p-715392b7.js");case r.HOPSCOTCH:return import("./p-03e743b8.js");case r.LUCARIO:return import("./p-91f4abb3.js");case r.MATERIAL:return import("./p-4b65de6f.js");case r.MONOKAI:return import("./p-843d3b32.js");case r["NIGHT-OWL"]:return import("./p-156aed1b.js");case r.NORD:return import("./p-41e94ce1.js");case r["OCEANIC-NEXT"]:return import("./p-78419e24.js");case r["ONE-DARK"]:return import("./p-64e01e86.js");case r["ONE-LIGHT"]:return import("./p-d13f2b1b.js");case r.PANDA:return import("./p-e0dc0bdd.js");case r.PARAISO:return import("./p-c6ba419c.js");case r.SETI:return import("./p-ef3329f7.js");case r["SHADES-OF-PURPLE"]:return import("./p-245859e6.js");case r["SOLARIZED-DARK"]:return import("./p-8d981c38.js");case r["SOLARIZED-LIGHT"]:return import("./p-24b57a1e.js");case r.SYNTHWAVE:return import("./p-a1a7f34d.js");case r.TWILIGHT:return import("./p-4fb7968b.js");case r.VERMINAL:return import("./p-e8346d14.js");case r.VSCODE:return import("./p-bdcab1e8.js");case r.YETI:return import("./p-6c746f14.js");case r.ZENBURN:return import("./p-2794be05.js");default:return import("./p-8aaafd70.js")}}(this.theme);this.themeStyle=e}onLanguageLoaded({detail:e}){this.language!==e||this.loaded||(this.parse(),this.loaded=!0)}async onLanguageError({detail:e}){this.language===e&&(this.language="javascript",this.prismLanguageLoaded.emit(this.language))}parse(){this.language&&d[this.language]&&this.parseSlottedCode()}languageDidLoad(){return!(!document||!this.language||""===this.language)&&!!document.querySelector("[deckdeckgo-prism-loaded='"+this.language+"']")}async onLanguage(){await this.loadLanguages(!0)}async loadLanguages(e=!1){if(this.loaded=!1,!this.language||!d[this.language])return void console.error(`Language ${this.language} is not supported`);const t=await this.loadRequiredLanguages();"attached"!==t&&("error"!==t?"loaded"!==await(({lang:e,reload:t=!1,prismLanguageLoaded:o})=>new Promise((async i=>{if(!document||!e||""===e)return void i("error");if("javascript"===e)return o.emit("javascript"),void i("loaded");if(document.querySelector("[deckdeckgo-prism='"+e+"']"))return t&&o.emit(e),void i("loaded");const n=document.createElement("script");n.onload=()=>{n.setAttribute("deckdeckgo-prism-loaded",e),o.emit(e)},n.onerror=()=>{n.parentElement&&n.parentElement.removeChild(n),i("error")};const r=d[e],a=r.main?r.main:e;n.src=p(a),n.setAttribute("deckdeckgo-prism",a),n.defer=!0,document.head.appendChild(n),n.addEventListener("load",(()=>i("loaded")),{once:!0})})))({lang:this.language,reload:e,prismLanguageLoaded:this.prismLanguageLoaded})&&this.fallbackJavascript():this.fallbackJavascript())}fallbackJavascript(){console.error("A required script for the language could not be fetched therefore, falling back to JavaScript to display code anyway."),this.prismLanguageError.emit(this.language)}async loadRequiredLanguages(){if(!this.language)return"error";const e=d[this.language];if(!e.require||e.require.length<=0)return"loaded";const t=e.require.map((e=>(({lang:e})=>new Promise((t=>{let o=document.querySelector(`deckdeckgo-prism-${e}`);o?t(o.hasAttribute("deckdeckgo-prism-loaded")?"loaded":"attached"):(o=document.createElement("script"),o.setAttribute("deckdeckgo-prism",e),o.defer=!0,o.src=p(e),o.addEventListener("load",(()=>{o.setAttribute("deckdeckgo-prism-loaded",e),t("loaded")})),o.addEventListener("error",(()=>t("error"))),o.addEventListener("abort",(()=>t("abort"))),document.head.appendChild(o))})))({lang:e}))),o=await Promise.all(t);return void 0!==o.find((e=>["error","abort"].includes(e)))?"error":void 0!==o.find((e=>"loaded"!==e))?"attached":"loaded"}onLineNumbersChange(){this.parse()}async onCarbonChange(){this.parseAfterUpdate=!0,await u(this.terminal)}async load(){this.language&&""!==this.language&&("javascript"!==this.language?document.querySelector("[deckdeckgo-prism-loaded='"+this.language+"']")?this.parse():await this.loadLanguages():this.parse())}parseSlottedCode(){var e;const t=this.el.querySelector("[slot='code']");t&&(({refContainer:e,refCode:t,code:o,lineNumbers:i,highlightLines:n,language:r})=>{if(!o||void 0===o||""===o)return;if(!e)return;e.children[0].textContent="";const a=RegExp(/\n(?!$)/g);o.split(a).forEach(((o,a,d)=>{a===d.length-1&&(({refContainer:e,refCode:t,highlightLines:o})=>{if(o&&!(o.length<=0))if(window&&"ResizeObserver"in window){const i=new ResizeObserver((()=>{g({refCode:t,highlightLines:o}),i.disconnect()}));i.observe(e)}else setTimeout((()=>{g({refCode:t,highlightLines:o})}),100)})({refContainer:e,refCode:t,highlightLines:n});let c=document.createElement("div");i&&c.classList.add("line-number");const s=h.highlight(o,h.languages[r],r);c.innerHTML=s&&""!==s?s:"​";const l=Array.from(c.childNodes).map((e=>{if("#text"===e.nodeName){const t=document.createElement("span");return t.append(e),t}return e}));c.textContent="",c.append(...l),e.children[0].appendChild(c)}))})(Object.assign(Object.assign({},this.parseCodeOptions()),{code:null===(e=null==t?void 0:t.innerHTML)||void 0===e?void 0:e.replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&")}))}parseCodeOptions(){return{refContainer:this.refContainer,refCode:this.refCode,lineNumbers:this.lineNumbers,highlightLines:this.highlightLines,language:this.language}}async reveal(){await this.nextHighlight()}async hide(){await this.prevHighlight()}async revealAll(){this.highlightGroup=void 0,this.highlightRows=void 0,this.revealProgress="start"}async hideAll(){await this.revealAll()}async nextHighlight(){if("end"!==this.revealProgress){if(await this.selectNextGroupHighlight(this.highlightGroup+1||0),void 0!==this.highlightRows)return this.highlightGroup=this.highlightGroup+1||0,void(this.revealProgress="partial");this.revealProgress="end"}}async prevHighlight(){if(0===this.highlightGroup)return this.highlightGroup=void 0,this.highlightRows=void 0,void(this.revealProgress="start");this.highlightGroup="end"===this.revealProgress?this.highlightGroup:this.highlightGroup-1,await this.selectNextGroupHighlight(this.highlightGroup),void 0!==this.highlightRows&&(this.revealProgress="partial")}async selectNextGroupHighlight(e){var t;const o=null===(t=this.refCode)||void 0===t?void 0:t.querySelectorAll(`.group-${e}`);if(!o||o.length<=0)return void(this.highlightRows=void 0);const i=Array.from(this.refCode.children);this.highlightRows={start:i.indexOf(o[0]),end:i.indexOf(o[o.length-1])}}render(){var t;const o={"deckgo-highlight-code-carbon":this.terminal===a.CARBON,"deckgo-highlight-code-ubuntu":this.terminal===a.UBUNTU,"deckgo-highlight-code-papyrs":this.terminal===a.PAPYRS};return this.terminal===a.CARBON&&(o[`deckgo-highlight-code-theme-${this.theme}`]=!0),e(i,{class:o},this.renderCarbon(),this.renderUbuntu(),this.renderHighlightStyle(),e("div",{class:"container",ref:e=>this.refContainer=e},e("code",{class:(null===(t=this.highlightLines)||void 0===t?void 0:t.length)>0?"highlight":void 0,ref:e=>this.refCode=e}),e("slot",{name:"code"}),this.editable&&e("deckgo-highlight-code-edit",{label:this.editableLabel})))}renderHighlightStyle(){if(this.highlightLines&&!(this.highlightLines.length<=0))return e(v,Object.assign({},this.highlightRows))}renderCarbon(){if(this.terminal===a.CARBON)return[e(b,{style:this.themeStyle}),e("div",{class:"carbon"},this.renderCarbonCircle("red"),this.renderCarbonCircle("yellow"),this.renderCarbonCircle("green"))]}renderCarbonCircle(t){return e("div",{class:t})}renderUbuntu(){if(this.terminal===a.UBUNTU)return e("div",{class:"ubuntu"},this.renderUbuntuCircle("close"),this.renderUbuntuCircle("minimize"),this.renderUbuntuCircle("maximize"),e("p",null,e("slot",{name:"user"})))}renderUbuntuCircle(t){return e("div",{class:t},e("span",{innerHTML:"close"===t?"&#10005;":"minimize"===t?"&#9472;":"&#9723;"}))}get el(){return n(this)}static get watchers(){return{theme:["loadTheme"],language:["onLanguage"],lineNumbers:["onLineNumbersChange"],terminal:["onCarbonChange"]}}};f.style='code[class*="language-"],pre[class*="language-"]{color:black;background:none;text-shadow:0 1px white;font-family:Consolas, Monaco, \'Andale Mono\', \'Ubuntu Mono\', monospace;font-size:1em;text-align:left;white-space:pre;word-spacing:normal;word-break:normal;word-wrap:normal;line-height:1.5;-moz-tab-size:4;-o-tab-size:4;tab-size:4;-webkit-hyphens:none;-moz-hyphens:none;-ms-hyphens:none;hyphens:none}pre[class*="language-"]::-moz-selection,pre[class*="language-"] ::-moz-selection,code[class*="language-"]::-moz-selection,code[class*="language-"] ::-moz-selection{text-shadow:none;background:#b3d4fc}pre[class*="language-"]::selection,pre[class*="language-"] ::selection,code[class*="language-"]::selection,code[class*="language-"] ::selection{text-shadow:none;background:#b3d4fc}@media print{code[class*="language-"],pre[class*="language-"]{text-shadow:none}}pre[class*="language-"]{padding:1em;margin:.5em 0;overflow:auto}:not(pre)>code[class*="language-"],pre[class*="language-"]{background:#f5f2f0}:not(pre)>code[class*="language-"]{padding:.1em;border-radius:.3em;white-space:normal}.token.comment,.token.prolog,.token.doctype,.token.cdata{color:slategray}.token.punctuation{color:#999}.token.namespace{opacity:.7}.token.property,.token.tag,.token.boolean,.token.number,.token.constant,.token.symbol,.token.deleted{color:#905}.token.selector,.token.attr-name,.token.string,.token.char,.token.builtin,.token.inserted{color:#690}.token.operator,.token.entity,.token.url,.language-css .token.string,.style .token.string{color:#9a6e3a;background:hsla(0, 0%, 100%, .5)}.token.atrule,.token.attr-value,.token.keyword{color:#07a}.token.function,.token.class-name{color:#DD4A68}.token.regex,.token.important,.token.variable{color:#e90}.token.important,.token.bold{font-weight:bold}.token.italic{font-style:italic}.token.entity{cursor:help}:host ::slotted([slot=code]){display:none}:host(.deckgo-highlight-code-carbon){display:var(--deckgo-highlight-code-carbon-display, block);overflow:var(--deckgo-highlight-code-carbon-overflow, auto);border:var(--deckgo-highlight-code-carbon-border);border-radius:var(--deckgo-highlight-code-carbon-border-radius, 4px);background:var(--deckgo-highlight-code-carbon-background, #282a36);color:var(--deckgo-highlight-code-carbon-color, white);box-shadow:var(--deckgo-highlight-code-carbon-box-shadow, 0 4px 16px 0 rgba(0, 0, 0, 0.12));margin:var(--deckgo-highlight-code-carbon-margin, 16px 0)}:host(.deckgo-highlight-code-carbon) div.container{margin:var(--deckgo-highlight-code-margin, 0 0 1em)}:host(.deckgo-highlight-code-carbon) ::slotted([slot=code]){color:var(--deckgo-highlight-code-carbon-color, white)}:host(.deckgo-highlight-code-ubuntu){display:var(--deckgo-highlight-code-ubuntu-display, block);overflow:var(--deckgo-highlight-code-ubuntu-overflow, auto);border:var(--deckgo-highlight-code-ubuntu-border);border-radius:var(--deckgo-highlight-code-ubuntu-border-radius, 6px 6px 0 0);background:var(--deckgo-highlight-code-ubuntu-background, #4c1e3d);color:var(--deckgo-highlight-code-ubuntu-color, #ddd);box-shadow:var(--deckgo-highlight-code-ubuntu-box-shadow, 0 4px 16px 0 rgba(0, 0, 0, 0.12));margin:var(--deckgo-highlight-code-ubuntu-margin, 16px 0)}:host(.deckgo-highlight-code-ubuntu) div.container{margin:var(--deckgo-highlight-code-margin, 0 0 16px);padding:var(--deckgo-highlight-code-padding, 2px 0 0);background:transparent}:host(.deckgo-highlight-code-ubuntu) div.container code{font-family:var(--deckgo-highlight-code-font-family, "Ubuntu mono")}:host(.deckgo-highlight-code-ubuntu) div.container code>div.line-number:before{background:var(--deckgo-highlight-code-ubuntu-background, #4c1e3d)}:host(.deckgo-highlight-code-ubuntu) ::slotted([slot=code]){color:var(--deckgo-highlight-code-ubuntu-color, #ddd)}div.container{color:var(--deckgo-highlight-code-color, inherit);background:var(--deckgo-highlight-code-background);padding:var(--deckgo-highlight-code-padding, 0 16px);border-radius:var(--deckgo-highlight-code-border-radius);margin:var(--deckgo-highlight-code-margin, 16px 0);transform-origin:bottom left;transition:all 0.2s ease-in-out;transform:scale(var(--deckgo-highlight-code-zoom, 1));direction:var(--deckgo-highlight-code-direction, ltr);text-align:var(--deckgo-highlight-code-text-align, start);width:var(--deckgo-highlight-code-container-width);height:var(--deckgo-highlight-code-container-height);display:var(--deckgo-highlight-code-container-display, block);justify-content:var(--deckgo-highlight-code-container-justify-content);flex-direction:var(--deckgo-highlight-code-container-flex-direction);align-items:var(--deckgo-highlight-code-container-align-items)}div.container code{overflow-y:var(--deckgo-highlight-code-scroll, auto);white-space:var(--deckgo-highlight-code-white-space, pre-wrap);font-size:var(--deckgo-highlight-code-font-size);font-family:var(--deckgo-highlight-code-font-family, monospace);line-height:var(--deckgo-highlight-code-line-height);display:var(--deckgo-highlight-code-display, block);counter-reset:linenumber;height:var(--deckgo-highlight-code-height, 100%);width:var(--deckgo-highlight-code-width);}div.container code>div.line-number{counter-increment:linenumber;position:relative;padding-left:3.5em}div.container code>div.line-number:before{content:counter(linenumber);display:inline-block;position:absolute;top:0;bottom:0;left:0;width:2.5em;background:var(--deckgo-highlight-code-line-numbers-background);border-right:var(--deckgo-highlight-code-line-numbers-border-right, 1px solid rgba(var(--deckgo-highlight-code-token-comment-rgb, 98, 114, 164), 0.32));color:var(--deckgo-lowlight-code-line-numbers-color, rgba(var(--deckgo-highlight-code-token-comment-rgb, 98, 114, 164), 0.32))}div.container code span.deckgo-highlight-code-anchor-hidden{visibility:hidden}div.container code.highlight div>*{color:var(--deckgo-lowlight-code-line-color);background:var(--deckgo-lowlight-code-line-background);border-top:var(--deckgo-lowlight-code-line-border-top);border-bottom:var(--deckgo-lowlight-code-line-border-bottom);font-weight:var(--deckgo-lowlight-code-line-font-weight);opacity:var(--deckgo-lowlight-code-line-opacity, 0.32)}div.container code.highlight.animate div>*{transition:var(--deckgo-highlight-code-line-transition, all 0.35s ease-in)}div.container code.highlight.animate>div.line-number:before{transition:var(--deckgo-highlight-code-line-transition, all 0.35s ease-in)}div.container code .language-css .token.string:not(.deckgo-highlight-code-line),div.container code .style .token.string:not(.deckgo-highlight-code-line),div.container code .token.entity:not(.deckgo-highlight-code-line),div.container code .token.operator:not(.deckgo-highlight-code-line),div.container code .token.url:not(.deckgo-highlight-code-line){background:inherit}div.container code .token.comment,div.container code .token.prolog,div.container code .token.doctype,div.container code .token.cdata{color:var(--deckgo-highlight-code-token-comment, #6272a4)}div.container code .token.punctuation{color:var(--deckgo-highlight-code-token-punctuation, #6272a4)}div.container code .token.property,div.container code .token.tag,div.container code .token.boolean,div.container code .token.number,div.container code .token.constant,div.container code .token.symbol,div.container code .token.deleted{color:var(--deckgo-highlight-code-token-property, #bd93f9)}div.container code .token.selector,div.container code .token.attr-name,div.container code .token.string,div.container code .token.char,div.container code .token.builtin,div.container code .token.inserted{color:var(--deckgo-highlight-code-token-selector, #50fa7b)}div.container code .token.operator,div.container code .token.entity,div.container code .token.url,div.container code .language-css .token.string,div.container code .style .token.string{color:var(--deckgo-highlight-code-token-operator, #ff79c6)}div.container code .token.atrule,div.container code .token.attr-value,div.container code .token.keyword{color:var(--deckgo-highlight-code-token-atrule, #ff79c6)}div.container code .token.function,div.container code .token.class-name{color:var(--deckgo-highlight-code-token-function, #ffb86c)}div.container code .token.regex,div.container code .token.important,div.container code .token.variable{color:var(--deckgo-highlight-code-token-regex, #f1fa8c)}div.carbon{display:flex;justify-content:flex-start;padding:var(--deckgo-highlight-code-carbon-header-padding, 0.5em 1em);margin:var(--deckgo-highlight-code-carbon-header-margin, 0)}div.carbon>div{display:var(--deckgo-highlight-code-carbon-toolbar-display, block);width:var(--deckgo-highlight-code-carbon-header-button-width, 0.75em);height:var(--deckgo-highlight-code-carbon-header-button-height, 0.75em);border-radius:var(--deckgo-highlight-code-carbon-header-button-border-radius, 50%);margin:var(--deckgo-highlight-code-carbon-header-button-margin, 0.5em 0.375em 0.5em 0)}div.carbon>div.red{background:var(--deckgo-highlight-code-carbon-header-button-red-background, #ff5f56)}div.carbon>div.yellow{background:var(--deckgo-highlight-code-carbon-header-button-yellow-background, #ffbd2e)}div.carbon>div.green{background:var(--deckgo-highlight-code-carbon-header-button-green-background, #27c93f)}div.ubuntu{display:flex;justify-content:flex-start;align-items:center;padding:var(--deckgo-highlight-code-ubuntu-header-padding, 0 0.5em);height:var(--deckgo-highlight-code-ubuntu-header-height, 25px);background:var(--deckgo-highlight-code-ubuntu-header-background, linear-gradient(#504b45 0%, #3c3b37 100%));font-family:var(--deckgo-highlight-code-ubuntu-header-font-family, "Ubuntu")}div.ubuntu>div{display:flex;align-items:center;justify-content:center;width:var(--deckgo-highlight-code-ubuntu-header-button-width, 12px);height:var(--deckgo-highlight-code-ubuntu-header-button-height, 12px);border-radius:var(--deckgo-highlight-code-ubuntu-header-button-border-radius, 50%);margin:var(--deckgo-highlight-code-ubuntu-header-button-margin, 0 0.25em 0 0);font-size:var(--deckgo-highlight-code-ubuntu-header-button-font-size, 0.4375em);color:var(--deckgo-highlight-code-ubuntu-header-button-color, black);text-shadow:var(--deckgo-highlight-code-ubuntu-header-button-text-shadow, 0px 1px 0px rgba(255, 255, 255, 0.2));box-shadow:var(--deckgo-highlight-code-ubuntu-header-button-box-shadow, 0px 0px 1px 0px #41403a, 0px 1px 1px 0px #474642)}div.ubuntu>div.close{background:var(--deckgo-highlight-code-ubuntu-header-button-close-background, linear-gradient(#f37458 0%, #de4c12 100%));border:var(--deckgo-highlight-code-ubuntu-header-button-close-border)}div.ubuntu>div.minimize{background:var(--deckgo-highlight-code-ubuntu-header-button-minimize-background, linear-gradient(#7d7871 0%, #595953 100%));border:var(--deckgo-highlight-code-ubuntu-header-button-minimize-border)}div.ubuntu>div.maximize{background:var(--deckgo-highlight-code-ubuntu-header-button-maximize-background, linear-gradient(#7d7871 0%, #595953 100%));border:var(--deckgo-highlight-code-ubuntu-header-button-maximize-border)}div.ubuntu>div.close span,div.ubuntu>div.minimize span,div.ubuntu>div.maximize span{display:var(--deckgo-highlight-code-ubuntu-header-button-span-display, inherit)}div.ubuntu>p{color:var(--deckgo-highlight-code-ubuntu-header-user-color, #d5d0ce);font-size:var(--deckgo-highlight-code-ubuntu-header-user-font-size, 12px);line-height:var(--deckgo-highlight-code-ubuntu-header-user-line-height, 14px);margin:var(--deckgo-highlight-code-ubuntu-header-user-margin, 0 0 1px 4px)}:host(.deckgo-highlight-code-papyrs){display:block;overflow:auto;border-radius:0;background:#172121;color:white;border:1px solid black;box-shadow:3px 3px black;margin:1.25rem 0;--deckgo-highlight-code-token-atrule:#fde74c;--deckgo-highlight-code-token-comment:#ef476f;--deckgo-highlight-code-token-comment-rgb:92, 158, 173;--deckgo-highlight-code-token-function:white;--deckgo-highlight-code-token-operator:#fde74c;--deckgo-highlight-code-token-property:#ef476f;--deckgo-highlight-code-token-punctuation:#ef476f;--deckgo-highlight-code-token-selector:#7dbbc3;--deckgo-highlight-code-token-regex:#fff6bc}:host(.deckgo-highlight-code-papyrs) div.container{margin:0.75rem 0}:host(.deckgo-highlight-code-carbon) ::slotted([slot=code]){color:white}';const k=class{constructor(e){t(this,e),this.editCode=o(this,"editCode",7)}render(){return e("button",{onClick:()=>this.editCode.emit(),"aria-label":this.label||"Edit code",part:"edit-button"},e("svg",{xmlns:"http://www.w3.org/2000/svg",height:"24px",viewBox:"0 0 24 24",width:"24px",fill:"currentColor",part:"edit-icon"},e("path",{d:"M0 0h24v24H0z",fill:"none"}),e("path",{d:"M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"})))}};k.style=":host{display:block;position:absolute;inset:auto 0 0 auto}button{width:1.6rem;height:1.6rem;display:flex;justify-content:center;align-items:center;margin:0 16px;border-radius:50%;background:#f4f5f8;color:#000000;border:1px solid transparent;outline:none;box-shadow:0 4px 16px 0 rgba(0, 0, 0, 0.12);isolation:isolate;overflow:hidden;cursor:pointer;transition:transform 0.15s ease-out}button:active{box-shadow:none;transform:translateX(1px) translateY(1px)}";export{f as deckgo_highlight_code,k as deckgo_highlight_code_edit}