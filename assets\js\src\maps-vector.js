import"../../admin.bundle-DI9_jvUJ.js";import"../../main-Cyta4iCA.js";function a(){document.getElementById("basicMap").innerHTML="",new jsVectorMap({selector:"#basicMap",map:"world",onRegionTipShow:function(t,e,o){const n=regionsData[o]||e.data("name");e.html(n)}})}a();window.addEventListener("resize",a);function s(){document.getElementById("markersMap").innerHTML="",new jsVectorMap({selector:"#markersMap",map:"world",markers:[{coords:[-14.235,-51.9253]},{coords:[35.8617,104.1954]}],selectedMarkers:[0]})}s();window.addEventListener("resize",s);function i(){document.getElementById("imageMarkersMap").innerHTML="",new jsVectorMap({selector:"#imageMarkersMap",map:"world",markers:[{name:"Egypt",coords:[26.8206,30.8025],style:{initial:{image:"assets/images/others/pin.png"}}},{name:"United States",coords:[37.0902,-95.7129],style:{initial:{image:"assets/images/others/pin.png"}}},{name:"United Kingdom",coords:[55.3781,3.436],style:{initial:{image:"assets/images/others/pin.png"}}}]})}i();window.addEventListener("resize",i);function c(){document.getElementById("lineStyleMap").innerHTML="",new jsVectorMap({selector:"#lineStyleMap",map:"world",lineStyle:{stroke:"#676767",strokeWidth:1.5,fill:"#ff5566",fillOpacity:1,strokeDasharray:"6 3 6",animation:!0},markers:[{name:"Brazil",coords:[-14.235,-51.9253],style:{fill:"red"}},{name:"Greenland",coords:[71.7069,-42.6043],style:{fill:"green"}},{name:"Egypt",coords:[26.8206,30.8025],style:{fill:"blue"}},{name:"United States",coords:[37.0902,-95.7129],style:{fill:"purple"}},{name:"Norway",coords:[60.472,8.4689],style:{fill:"yellow"}}]}).addLines([{from:"Brazil",to:"Greenland"},{from:"Greenland",to:"Egypt"},{from:"Egypt",to:"United States"},{from:"United States",to:"Norway"},{from:"Norway",to:"Brazil"}])}c();window.addEventListener("resize",c);function l(){document.getElementById("tooltipMap").innerHTML="",new jsVectorMap({selector:"#tooltipMap",map:"world",onRegionTooltipShow(t,e,o){e.text(`<h6 class="mb-2">${e.text()} - Country</h6><p class="text-xs">Get better UX with SRBThemes.</p>`,!0)}})}l();window.addEventListener("resize",l);function d(){document.getElementById("dataSeriesMap").innerHTML="",new jsVectorMap({selector:"#dataSeriesMap",map:"world",markers:[{coords:[61,105]},{coords:[72,-42]},{coords:[56,-106]},{coords:[31.5,34.8]},{coords:[-14.235,-51.9253]},{coords:[35.8617,104.1954]}],series:{markers:[{attribute:"fill",legend:{title:"Something (marker)"},scale:{mScale1:"#ffc371",mScale2:"#c79efd"},values:{0:"mScale1",1:"mScale2",2:"mScale2"}}]}})}d();window.addEventListener("resize",d);function m(){document.getElementById("userLocationMap").innerHTML="",new jsVectorMap({selector:"#userLocationMap",map:"world",onRegionTipShow:function(t,e,o){const n=e.html();e.html(n)},async onLoaded(t){const o=await(await fetch("https://ipinfo.io/geo")).json(),n=o.city,p=o.country,u=o.ip;document.querySelector("#request-location").addEventListener("click",async()=>{t.removeMarkers(),navigator.geolocation?navigator.geolocation.getCurrentPosition(async r=>{const M=[r.coords.latitude,r.coords.longitude],g=`• ${n}-${p}(${u})`;t.addMarkers({coords:M,name:g})},r=>{console.error("Error getting location: ",r),alert("Unable to retrieve your location.")}):alert("Geolocation is not supported by this browser.")}),document.querySelector("#clear-location").addEventListener("click",()=>{t.removeMarkers()})}})}m();window.addEventListener("resize",m);
