import{g as ns}from"../../admin.bundle-DI9_jvUJ.js";import"../../main-Cyta4iCA.js";(function(){try{if(typeof document<"u"){var o=document.createElement("style");o.appendChild(document.createTextNode(".ce-hint--align-start{text-align:left}.ce-hint--align-center{text-align:center}.ce-hint__description{opacity:.6;margin-top:3px}")),document.head.appendChild(o)}}catch(e){console.error("vite-plugin-css-injected-by-js",e)}})();var Ye=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function xt(o){return o&&o.__esModule&&Object.prototype.hasOwnProperty.call(o,"default")?o.default:o}function rs(o){if(o.__esModule)return o;var e=o.default;if(typeof e=="function"){var t=function n(){return this instanceof n?Reflect.construct(e,arguments,this.constructor):e.apply(this,arguments)};t.prototype=e.prototype}else t={};return Object.defineProperty(t,"__esModule",{value:!0}),Object.keys(o).forEach(function(n){var r=Object.getOwnPropertyDescriptor(o,n);Object.defineProperty(t,n,r.get?r:{enumerable:!0,get:function(){return o[n]}})}),t}function Ft(){}Object.assign(Ft,{default:Ft,register:Ft,revert:function(){},__esModule:!0});Element.prototype.matches||(Element.prototype.matches=Element.prototype.matchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.oMatchesSelector||Element.prototype.webkitMatchesSelector||function(o){const e=(this.document||this.ownerDocument).querySelectorAll(o);let t=e.length;for(;--t>=0&&e.item(t)!==this;);return t>-1});Element.prototype.closest||(Element.prototype.closest=function(o){let e=this;if(!document.documentElement.contains(e))return null;do{if(e.matches(o))return e;e=e.parentElement||e.parentNode}while(e!==null);return null});Element.prototype.prepend||(Element.prototype.prepend=function(o){const e=document.createDocumentFragment();Array.isArray(o)||(o=[o]),o.forEach(t=>{const n=t instanceof Node;e.appendChild(n?t:document.createTextNode(t))}),this.insertBefore(e,this.firstChild)});Element.prototype.scrollIntoViewIfNeeded||(Element.prototype.scrollIntoViewIfNeeded=function(o){o=arguments.length===0?!0:!!o;const e=this.parentNode,t=window.getComputedStyle(e,null),n=parseInt(t.getPropertyValue("border-top-width")),r=parseInt(t.getPropertyValue("border-left-width")),i=this.offsetTop-e.offsetTop<e.scrollTop,s=this.offsetTop-e.offsetTop+this.clientHeight-n>e.scrollTop+e.clientHeight,a=this.offsetLeft-e.offsetLeft<e.scrollLeft,c=this.offsetLeft-e.offsetLeft+this.clientWidth-r>e.scrollLeft+e.clientWidth,l=i&&!s;(i||s)&&o&&(e.scrollTop=this.offsetTop-e.offsetTop-e.clientHeight/2-n+this.clientHeight/2),(a||c)&&o&&(e.scrollLeft=this.offsetLeft-e.offsetLeft-e.clientWidth/2-r+this.clientWidth/2),(i||s||a||c)&&!o&&this.scrollIntoView(l)});window.requestIdleCallback=window.requestIdleCallback||function(o){const e=Date.now();return setTimeout(function(){o({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-e))}})},1)};window.cancelIdleCallback=window.cancelIdleCallback||function(o){clearTimeout(o)};let is=(o=21)=>crypto.getRandomValues(new Uint8Array(o)).reduce((e,t)=>(t&=63,t<36?e+=t.toString(36):t<62?e+=(t-26).toString(36).toUpperCase():t>62?e+="-":e+="_",e),"");var vr=(o=>(o.VERBOSE="VERBOSE",o.INFO="INFO",o.WARN="WARN",o.ERROR="ERROR",o))(vr||{});const N={BACKSPACE:8,TAB:9,ENTER:13,ESC:27,LEFT:37,UP:38,DOWN:40,RIGHT:39,DELETE:46},ss={LEFT:0};function Xe(o,e,t="log",n,r="color: inherit"){if(!("console"in window)||!window.console[t])return;const i=["info","log","warn","error"].includes(t),s=[];switch(Xe.logLevel){case"ERROR":if(t!=="error")return;break;case"WARN":if(!["error","warn"].includes(t))return;break;case"INFO":if(!i||o)return;break}n&&s.push(n);const a="Editor.js 2.31.0-rc.7";o&&(i?(s.unshift(`line-height: 1em;
            color: #006FEA;
            display: inline-block;
            font-size: 11px;
            line-height: 1em;
            background-color: #fff;
            padding: 4px 9px;
            border-radius: 30px;
            border: 1px solid rgba(56, 138, 229, 0.16);
            margin: 4px 5px 4px 0;`,r),e=`%c${a}%c ${e}`):e=`( ${a} )${e}`);try{i?n?console[t](`${e} %o`,...s):console[t](e,...s):console[t](e)}catch{}}Xe.logLevel="VERBOSE";function as(o){Xe.logLevel=o}const D=Xe.bind(window,!1),ee=Xe.bind(window,!0);function Ee(o){return Object.prototype.toString.call(o).match(/\s([a-zA-Z]+)/)[1].toLowerCase()}function U(o){return Ee(o)==="function"||Ee(o)==="asyncfunction"}function Y(o){return Ee(o)==="object"}function pe(o){return Ee(o)==="string"}function ls(o){return Ee(o)==="boolean"}function Zn(o){return Ee(o)==="number"}function Jn(o){return Ee(o)==="undefined"}function te(o){return o?Object.keys(o).length===0&&o.constructor===Object:!0}function br(o){return o>47&&o<58||o===32||o===13||o===229||o>64&&o<91||o>95&&o<112||o>185&&o<193||o>218&&o<223}async function cs(o,e=()=>{},t=()=>{}){async function n(r,i,s){try{await r.function(r.data),await i(Jn(r.data)?{}:r.data)}catch{s(Jn(r.data)?{}:r.data)}}return o.reduce(async(r,i)=>(await r,n(i,e,t)),Promise.resolve())}function yr(o){return Array.prototype.slice.call(o)}function ut(o,e){return function(){const t=this,n=arguments;window.setTimeout(()=>o.apply(t,n),e)}}function ds(o){return o.name.split(".").pop()}function us(o){return/^[-\w]+\/([-+\w]+|\*)$/.test(o)}function Qn(o,e,t){let n;return(...r)=>{const i=this,s=()=>{n=null,o.apply(i,r)};window.clearTimeout(n),n=window.setTimeout(s,e)}}function Jt(o,e,t=void 0){let n,r,i,s=null,a=0;t||(t={});const c=function(){a=t.leading===!1?0:Date.now(),s=null,i=o.apply(n,r),s||(n=r=null)};return function(){const l=Date.now();!a&&t.leading===!1&&(a=l);const d=e-(l-a);return n=this,r=arguments,d<=0||d>e?(s&&(clearTimeout(s),s=null),a=l,i=o.apply(n,r),s||(n=r=null)):!s&&t.trailing!==!1&&(s=setTimeout(c,d)),i}}function hs(){const o={win:!1,mac:!1,x11:!1,linux:!1},e=Object.keys(o).find(t=>window.navigator.appVersion.toLowerCase().indexOf(t)!==-1);return e&&(o[e]=!0),o}function ht(o){return o[0].toUpperCase()+o.slice(1)}function Qt(o,...e){if(!e.length)return o;const t=e.shift();if(Y(o)&&Y(t))for(const n in t)Y(t[n])?(o[n]||Object.assign(o,{[n]:{}}),Qt(o[n],t[n])):Object.assign(o,{[n]:t[n]});return Qt(o,...e)}function co(o){const e=hs();return o=o.replace(/shift/gi,"⇧").replace(/backspace/gi,"⌫").replace(/enter/gi,"⏎").replace(/up/gi,"↑").replace(/left/gi,"→").replace(/down/gi,"↓").replace(/right/gi,"←").replace(/escape/gi,"⎋").replace(/insert/gi,"Ins").replace(/delete/gi,"␡").replace(/\+/gi," + "),e.mac?o=o.replace(/ctrl|cmd/gi,"⌘").replace(/alt/gi,"⌥"):o=o.replace(/cmd/gi,"Ctrl").replace(/windows/gi,"WIN"),o}function ps(o){try{return new URL(o).href}catch{}return o.substring(0,2)==="//"?window.location.protocol+o:window.location.origin+o}function fs(){return is(10)}function gs(o){window.open(o,"_blank")}function ms(o=""){return`${o}${Math.floor(Math.random()*1e8).toString(16)}`}function eo(o,e,t){const n=`«${e}» is deprecated and will be removed in the next major release. Please use the «${t}» instead.`;o&&ee(n,"warn")}function Re(o,e,t){const n=t.value?"value":"get",r=t[n],i=`#${e}Cache`;if(t[n]=function(...s){return this[i]===void 0&&(this[i]=r.apply(this,...s)),this[i]},n==="get"&&t.set){const s=t.set;t.set=function(a){delete o[i],s.apply(this,a)}}return t}const kr=650;function De(){return window.matchMedia(`(max-width: ${kr}px)`).matches}const to=typeof window<"u"&&window.navigator&&window.navigator.platform&&(/iP(ad|hone|od)/.test(window.navigator.platform)||window.navigator.platform==="MacIntel"&&window.navigator.maxTouchPoints>1);function vs(o,e){const t=Array.isArray(o)||Y(o),n=Array.isArray(e)||Y(e);return t||n?JSON.stringify(o)===JSON.stringify(e):o===e}let S=class X{static isSingleTag(e){return e.tagName&&["AREA","BASE","BR","COL","COMMAND","EMBED","HR","IMG","INPUT","KEYGEN","LINK","META","PARAM","SOURCE","TRACK","WBR"].includes(e.tagName)}static isLineBreakTag(e){return e&&e.tagName&&["BR","WBR"].includes(e.tagName)}static make(e,t=null,n={}){const r=document.createElement(e);if(Array.isArray(t)){const i=t.filter(s=>s!==void 0);r.classList.add(...i)}else t&&r.classList.add(t);for(const i in n)Object.prototype.hasOwnProperty.call(n,i)&&(r[i]=n[i]);return r}static text(e){return document.createTextNode(e)}static append(e,t){Array.isArray(t)?t.forEach(n=>e.appendChild(n)):e.appendChild(t)}static prepend(e,t){Array.isArray(t)?(t=t.reverse(),t.forEach(n=>e.prepend(n))):e.prepend(t)}static swap(e,t){const n=document.createElement("div"),r=e.parentNode;r.insertBefore(n,e),r.insertBefore(e,t),r.insertBefore(t,n),r.removeChild(n)}static find(e=document,t){return e.querySelector(t)}static get(e){return document.getElementById(e)}static findAll(e=document,t){return e.querySelectorAll(t)}static get allInputsSelector(){return"[contenteditable=true], textarea, input:not([type]), "+["text","password","email","number","search","tel","url"].map(e=>`input[type="${e}"]`).join(", ")}static findAllInputs(e){return yr(e.querySelectorAll(X.allInputsSelector)).reduce((t,n)=>X.isNativeInput(n)||X.containsOnlyInlineElements(n)?[...t,n]:[...t,...X.getDeepestBlockElements(n)],[])}static getDeepestNode(e,t=!1){const n=t?"lastChild":"firstChild",r=t?"previousSibling":"nextSibling";if(e&&e.nodeType===Node.ELEMENT_NODE&&e[n]){let i=e[n];if(X.isSingleTag(i)&&!X.isNativeInput(i)&&!X.isLineBreakTag(i))if(i[r])i=i[r];else if(i.parentNode[r])i=i.parentNode[r];else return i.parentNode;return this.getDeepestNode(i,t)}return e}static isElement(e){return Zn(e)?!1:e&&e.nodeType&&e.nodeType===Node.ELEMENT_NODE}static isFragment(e){return Zn(e)?!1:e&&e.nodeType&&e.nodeType===Node.DOCUMENT_FRAGMENT_NODE}static isContentEditable(e){return e.contentEditable==="true"}static isNativeInput(e){const t=["INPUT","TEXTAREA"];return e&&e.tagName?t.includes(e.tagName):!1}static canSetCaret(e){let t=!0;if(X.isNativeInput(e))switch(e.type){case"file":case"checkbox":case"radio":case"hidden":case"submit":case"button":case"image":case"reset":t=!1;break}else t=X.isContentEditable(e);return t}static isNodeEmpty(e,t){let n;return this.isSingleTag(e)&&!this.isLineBreakTag(e)?!1:(this.isElement(e)&&this.isNativeInput(e)?n=e.value:n=e.textContent.replace("​",""),t&&(n=n.replace(new RegExp(t,"g"),"")),n.length===0)}static isLeaf(e){return e?e.childNodes.length===0:!1}static isEmpty(e,t){const n=[e];for(;n.length>0;)if(e=n.shift(),!!e){if(this.isLeaf(e)&&!this.isNodeEmpty(e,t))return!1;e.childNodes&&n.push(...Array.from(e.childNodes))}return!0}static isHTMLString(e){const t=X.make("div");return t.innerHTML=e,t.childElementCount>0}static getContentLength(e){return X.isNativeInput(e)?e.value.length:e.nodeType===Node.TEXT_NODE?e.length:e.textContent.length}static get blockElements(){return["address","article","aside","blockquote","canvas","div","dl","dt","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","hr","li","main","nav","noscript","ol","output","p","pre","ruby","section","table","tbody","thead","tr","tfoot","ul","video"]}static containsOnlyInlineElements(e){let t;pe(e)?(t=document.createElement("div"),t.innerHTML=e):t=e;const n=r=>!X.blockElements.includes(r.tagName.toLowerCase())&&Array.from(r.children).every(n);return Array.from(t.children).every(n)}static getDeepestBlockElements(e){return X.containsOnlyInlineElements(e)?[e]:Array.from(e.children).reduce((t,n)=>[...t,...X.getDeepestBlockElements(n)],[])}static getHolder(e){return pe(e)?document.getElementById(e):e}static isAnchor(e){return e.tagName.toLowerCase()==="a"}static offset(e){const t=e.getBoundingClientRect(),n=window.pageXOffset||document.documentElement.scrollLeft,r=window.pageYOffset||document.documentElement.scrollTop,i=t.top+r,s=t.left+n;return{top:i,left:s,bottom:i+t.height,right:s+t.width}}};function bs(o){return!/[^\t\n\r ]/.test(o)}function ys(o){const e=window.getComputedStyle(o),t=parseFloat(e.fontSize),n=parseFloat(e.lineHeight)||t*1.2,r=parseFloat(e.paddingTop),i=parseFloat(e.borderTopWidth),s=parseFloat(e.marginTop),a=t*.8,c=(n-t)/2;return s+i+r+c+a}function wr(o){o.dataset.empty=S.isEmpty(o)?"true":"false"}const ks={blockTunes:{toggler:{"Click to tune":"","or drag to move":""}},inlineToolbar:{converter:{"Convert to":""}},toolbar:{toolbox:{Add:""}},popover:{Filter:"","Nothing found":"","Convert to":""}},ws={Text:"",Link:"",Bold:"",Italic:""},xs={link:{"Add a link":""},stub:{"The block can not be displayed correctly.":""}},Es={delete:{Delete:"","Click to delete":""},moveUp:{"Move up":""},moveDown:{"Move down":""}},xr={ui:ks,toolNames:ws,tools:xs,blockTunes:Es},Er=class Me{static ui(e,t){return Me._t(e,t)}static t(e,t){return Me._t(e,t)}static setDictionary(e){Me.currentDictionary=e}static _t(e,t){const n=Me.getNamespace(e);return!n||!n[t]?t:n[t]}static getNamespace(e){return e.split(".").reduce((t,n)=>!t||!Object.keys(t).length?{}:t[n],Me.currentDictionary)}};Er.currentDictionary=xr;let G=Er;class Cr extends Error{}let Ge=class{constructor(){this.subscribers={}}on(e,t){e in this.subscribers||(this.subscribers[e]=[]),this.subscribers[e].push(t)}once(e,t){e in this.subscribers||(this.subscribers[e]=[]);const n=r=>{const i=t(r),s=this.subscribers[e].indexOf(n);return s!==-1&&this.subscribers[e].splice(s,1),i};this.subscribers[e].push(n)}emit(e,t){te(this.subscribers)||!this.subscribers[e]||this.subscribers[e].reduce((n,r)=>{const i=r(n);return i!==void 0?i:n},t)}off(e,t){if(this.subscribers[e]===void 0){console.warn(`EventDispatcher .off(): there is no subscribers for event "${e.toString()}". Probably, .off() called before .on()`);return}for(let n=0;n<this.subscribers[e].length;n++)if(this.subscribers[e][n]===t){delete this.subscribers[e][n];break}}destroy(){this.subscribers={}}};function de(o){Object.setPrototypeOf(this,{get id(){return o.id},get name(){return o.name},get config(){return o.config},get holder(){return o.holder},get isEmpty(){return o.isEmpty},get selected(){return o.selected},set stretched(e){o.stretched=e},get stretched(){return o.stretched},get focusable(){return o.focusable},call(e,t){return o.call(e,t)},save(){return o.save()},validate(e){return o.validate(e)},dispatchChange(){o.dispatchChange()},getActiveToolboxEntry(){return o.getActiveToolboxEntry()}})}let Ze=class{constructor(){this.allListeners=[]}on(e,t,n,r=!1){const i=ms("l"),s={id:i,element:e,eventType:t,handler:n,options:r};if(!this.findOne(e,t,n))return this.allListeners.push(s),e.addEventListener(t,n,r),i}off(e,t,n,r){const i=this.findAll(e,t,n);i.forEach((s,a)=>{const c=this.allListeners.indexOf(i[a]);c>-1&&(this.allListeners.splice(c,1),s.element.removeEventListener(s.eventType,s.handler,s.options))})}offById(e){const t=this.findById(e);t&&t.element.removeEventListener(t.eventType,t.handler,t.options)}findOne(e,t,n){const r=this.findAll(e,t,n);return r.length>0?r[0]:null}findAll(e,t,n){let r;const i=e?this.findByEventTarget(e):[];return e&&t&&n?r=i.filter(s=>s.eventType===t&&s.handler===n):e&&t?r=i.filter(s=>s.eventType===t):r=i,r}removeAll(){this.allListeners.map(e=>{e.element.removeEventListener(e.eventType,e.handler,e.options)}),this.allListeners=[]}destroy(){this.removeAll()}findByEventTarget(e){return this.allListeners.filter(t=>{if(t.element===e)return t})}findByType(e){return this.allListeners.filter(t=>{if(t.eventType===e)return t})}findByHandler(e){return this.allListeners.filter(t=>{if(t.handler===e)return t})}findById(e){return this.allListeners.find(t=>t.id===e)}},j=class Sr{constructor({config:e,eventsDispatcher:t}){if(this.nodes={},this.listeners=new Ze,this.readOnlyMutableListeners={on:(n,r,i,s=!1)=>{this.mutableListenerIds.push(this.listeners.on(n,r,i,s))},clearAll:()=>{for(const n of this.mutableListenerIds)this.listeners.offById(n);this.mutableListenerIds=[]}},this.mutableListenerIds=[],new.target===Sr)throw new TypeError("Constructors for abstract class Module are not allowed.");this.config=e,this.eventsDispatcher=t}set state(e){this.Editor=e}removeAllNodes(){for(const e in this.nodes){const t=this.nodes[e];t instanceof HTMLElement&&t.remove()}}get isRtl(){return this.config.i18n.direction==="rtl"}},P=class fe{constructor(){this.instance=null,this.selection=null,this.savedSelectionRange=null,this.isFakeBackgroundEnabled=!1,this.commandBackground="backColor",this.commandRemoveFormat="removeFormat"}static get CSS(){return{editorWrapper:"codex-editor",editorZone:"codex-editor__redactor"}}static get anchorNode(){const e=window.getSelection();return e?e.anchorNode:null}static get anchorElement(){const e=window.getSelection();if(!e)return null;const t=e.anchorNode;return t?S.isElement(t)?t:t.parentElement:null}static get anchorOffset(){const e=window.getSelection();return e?e.anchorOffset:null}static get isCollapsed(){const e=window.getSelection();return e?e.isCollapsed:null}static get isAtEditor(){return this.isSelectionAtEditor(fe.get())}static isSelectionAtEditor(e){if(!e)return!1;let t=e.anchorNode||e.focusNode;t&&t.nodeType===Node.TEXT_NODE&&(t=t.parentNode);let n=null;return t&&t instanceof Element&&(n=t.closest(`.${fe.CSS.editorZone}`)),n?n.nodeType===Node.ELEMENT_NODE:!1}static isRangeAtEditor(e){if(!e)return;let t=e.startContainer;t&&t.nodeType===Node.TEXT_NODE&&(t=t.parentNode);let n=null;return t&&t instanceof Element&&(n=t.closest(`.${fe.CSS.editorZone}`)),n?n.nodeType===Node.ELEMENT_NODE:!1}static get isSelectionExists(){return!!fe.get().anchorNode}static get range(){return this.getRangeFromSelection(this.get())}static getRangeFromSelection(e){return e&&e.rangeCount?e.getRangeAt(0):null}static get rect(){let e=document.selection,t,n={x:0,y:0,width:0,height:0};if(e&&e.type!=="Control")return e=e,t=e.createRange(),n.x=t.boundingLeft,n.y=t.boundingTop,n.width=t.boundingWidth,n.height=t.boundingHeight,n;if(!window.getSelection)return D("Method window.getSelection is not supported","warn"),n;if(e=window.getSelection(),e.rangeCount===null||isNaN(e.rangeCount))return D("Method SelectionUtils.rangeCount is not supported","warn"),n;if(e.rangeCount===0)return n;if(t=e.getRangeAt(0).cloneRange(),t.getBoundingClientRect&&(n=t.getBoundingClientRect()),n.x===0&&n.y===0){const r=document.createElement("span");if(r.getBoundingClientRect){r.appendChild(document.createTextNode("​")),t.insertNode(r),n=r.getBoundingClientRect();const i=r.parentNode;i.removeChild(r),i.normalize()}}return n}static get text(){return window.getSelection?window.getSelection().toString():""}static get(){return window.getSelection()}static setCursor(e,t=0){const n=document.createRange(),r=window.getSelection();return S.isNativeInput(e)?S.canSetCaret(e)?(e.focus(),e.selectionStart=e.selectionEnd=t,e.getBoundingClientRect()):void 0:(n.setStart(e,t),n.setEnd(e,t),r.removeAllRanges(),r.addRange(n),n.getBoundingClientRect())}static isRangeInsideContainer(e){const t=fe.range;return t===null?!1:e.contains(t.startContainer)}static addFakeCursor(){const e=fe.range;if(e===null)return;const t=S.make("span","codex-editor__fake-cursor");t.dataset.mutationFree="true",e.collapse(),e.insertNode(t)}static isFakeCursorInsideContainer(e){return S.find(e,".codex-editor__fake-cursor")!==null}static removeFakeCursor(e=document.body){const t=S.find(e,".codex-editor__fake-cursor");t&&t.remove()}removeFakeBackground(){this.isFakeBackgroundEnabled&&(this.isFakeBackgroundEnabled=!1,document.execCommand(this.commandRemoveFormat))}setFakeBackground(){document.execCommand(this.commandBackground,!1,"#a8d6ff"),this.isFakeBackgroundEnabled=!0}save(){this.savedSelectionRange=fe.range}restore(){if(!this.savedSelectionRange)return;const e=window.getSelection();e.removeAllRanges(),e.addRange(this.savedSelectionRange)}clearSaved(){this.savedSelectionRange=null}collapseToEnd(){const e=window.getSelection(),t=document.createRange();t.selectNodeContents(e.focusNode),t.collapse(!1),e.removeAllRanges(),e.addRange(t)}findParentTag(e,t,n=10){const r=window.getSelection();let i=null;return!r||!r.anchorNode||!r.focusNode?null:([r.anchorNode,r.focusNode].forEach(s=>{let a=n;for(;a>0&&s.parentNode&&!(s.tagName===e&&(i=s,t&&s.classList&&!s.classList.contains(t)&&(i=null),i));)s=s.parentNode,a--}),i)}expandToTag(e){const t=window.getSelection();t.removeAllRanges();const n=document.createRange();n.selectNodeContents(e),t.addRange(n)}};function Cs(o,e){const{type:t,target:n,addedNodes:r,removedNodes:i}=o;return o.type==="attributes"&&o.attributeName==="data-empty"?!1:!!(e.contains(n)||t==="childList"&&(Array.from(r).some(s=>s===e)||Array.from(i).some(s=>s===e)))}const oo="redactor dom changed",Tr="block changed",Br="fake cursor is about to be toggled",Or="fake cursor have been set",Ke="editor mobile layout toggled";function no(o,e){if(!o.conversionConfig)return!1;const t=o.conversionConfig[e];return U(t)||pe(t)}function pt(o,e){return no(o.tool,e)}function _r(o,e){return Object.entries(o).some(([t,n])=>e[t]&&vs(e[t],n))}async function Ir(o,e){const t=(await o.save()).data,n=e.find(r=>r.name===o.name);return n!==void 0&&!no(n,"export")?[]:e.reduce((r,i)=>{if(!no(i,"import")||i.toolbox===void 0)return r;const s=i.toolbox.filter(a=>{if(te(a)||a.icon===void 0)return!1;if(a.data!==void 0){if(_r(a.data,t))return!1}else if(i.name===o.name)return!1;return!0});return r.push({...i,toolbox:s}),r},[])}function er(o,e){return o.mergeable?o.name===e.name?!0:pt(e,"export")&&pt(o,"import"):!1}function Ss(o,e){const t=e==null?void 0:e.export;return U(t)?t(o):pe(t)?o[t]:(t!==void 0&&D("Conversion «export» property must be a string or function. String means key of saved data object to export. Function should export processed string to export."),"")}function tr(o,e,t){const n=e==null?void 0:e.import;return U(n)?n(o,t):pe(n)?{[n]:o}:(n!==void 0&&D("Conversion «import» property must be a string or function. String means key of tool data to import. Function accepts a imported string and return composed tool data."),{})}var $=(o=>(o.Default="default",o.Separator="separator",o.Html="html",o))($||{}),ue=(o=>(o.APPEND_CALLBACK="appendCallback",o.RENDERED="rendered",o.MOVED="moved",o.UPDATED="updated",o.REMOVED="removed",o.ON_PASTE="onPaste",o))(ue||{});let he=class ge extends Ge{constructor({id:e=fs(),data:t,tool:n,readOnly:r,tunesData:i},s){super(),this.cachedInputs=[],this.toolRenderedElement=null,this.tunesInstances=new Map,this.defaultTunesInstances=new Map,this.unavailableTunesData={},this.inputIndex=0,this.editorEventBus=null,this.handleFocus=()=>{this.dropInputsCache(),this.updateCurrentInput()},this.didMutated=(a=void 0)=>{const c=a===void 0,l=a instanceof InputEvent;!c&&!l&&this.detectToolRootChange(a);let d;c||l?d=!0:d=!(a.length>0&&a.every(u=>{const{addedNodes:p,removedNodes:g,target:b}=u;return[...Array.from(p),...Array.from(g),b].some(f=>(S.isElement(f)||(f=f.parentElement),f&&f.closest('[data-mutation-free="true"]')!==null))})),d&&(this.dropInputsCache(),this.updateCurrentInput(),this.toggleInputsEmptyMark(),this.call("updated"),this.emit("didMutated",this))},this.name=n.name,this.id=e,this.settings=n.settings,this.config=n.settings.config||{},this.editorEventBus=s||null,this.blockAPI=new de(this),this.tool=n,this.toolInstance=n.create(t,this.blockAPI,r),this.tunes=n.tunes,this.composeTunes(i),this.holder=this.compose(),window.requestIdleCallback(()=>{this.watchBlockMutations(),this.addInputEvents(),this.toggleInputsEmptyMark()})}static get CSS(){return{wrapper:"ce-block",wrapperStretched:"ce-block--stretched",content:"ce-block__content",selected:"ce-block--selected",dropTarget:"ce-block--drop-target"}}get inputs(){if(this.cachedInputs.length!==0)return this.cachedInputs;const e=S.findAllInputs(this.holder);return this.inputIndex>e.length-1&&(this.inputIndex=e.length-1),this.cachedInputs=e,e}get currentInput(){return this.inputs[this.inputIndex]}set currentInput(e){const t=this.inputs.findIndex(n=>n===e||n.contains(e));t!==-1&&(this.inputIndex=t)}get firstInput(){return this.inputs[0]}get lastInput(){const e=this.inputs;return e[e.length-1]}get nextInput(){return this.inputs[this.inputIndex+1]}get previousInput(){return this.inputs[this.inputIndex-1]}get data(){return this.save().then(e=>e&&!te(e.data)?e.data:{})}get sanitize(){return this.tool.sanitizeConfig}get mergeable(){return U(this.toolInstance.merge)}get focusable(){return this.inputs.length!==0}get isEmpty(){const e=S.isEmpty(this.pluginsContent,"/"),t=!this.hasMedia;return e&&t}get hasMedia(){const e=["img","iframe","video","audio","source","input","textarea","twitterwidget"];return!!this.holder.querySelector(e.join(","))}set selected(e){var t,n;this.holder.classList.toggle(ge.CSS.selected,e);const r=e===!0&&P.isRangeInsideContainer(this.holder),i=e===!1&&P.isFakeCursorInsideContainer(this.holder);(r||i)&&((t=this.editorEventBus)==null||t.emit(Br,{state:e}),r?P.addFakeCursor():P.removeFakeCursor(this.holder),(n=this.editorEventBus)==null||n.emit(Or,{state:e}))}get selected(){return this.holder.classList.contains(ge.CSS.selected)}set stretched(e){this.holder.classList.toggle(ge.CSS.wrapperStretched,e)}get stretched(){return this.holder.classList.contains(ge.CSS.wrapperStretched)}set dropTarget(e){this.holder.classList.toggle(ge.CSS.dropTarget,e)}get pluginsContent(){return this.toolRenderedElement}call(e,t){if(U(this.toolInstance[e])){e==="appendCallback"&&D("`appendCallback` hook is deprecated and will be removed in the next major release. Use `rendered` hook instead","warn");try{this.toolInstance[e].call(this.toolInstance,t)}catch(n){D(`Error during '${e}' call: ${n.message}`,"error")}}}async mergeWith(e){await this.toolInstance.merge(e)}async save(){const e=await this.toolInstance.save(this.pluginsContent),t=this.unavailableTunesData;[...this.tunesInstances.entries(),...this.defaultTunesInstances.entries()].forEach(([i,s])=>{if(U(s.save))try{t[i]=s.save()}catch(a){D(`Tune ${s.constructor.name} save method throws an Error %o`,"warn",a)}});const n=window.performance.now();let r;return Promise.resolve(e).then(i=>(r=window.performance.now(),{id:this.id,tool:this.name,data:i,tunes:t,time:r-n})).catch(i=>{D(`Saving process for ${this.name} tool failed due to the ${i}`,"log","red")})}async validate(e){let t=!0;return this.toolInstance.validate instanceof Function&&(t=await this.toolInstance.validate(e)),t}getTunes(){const e=[],t=[],n=typeof this.toolInstance.renderSettings=="function"?this.toolInstance.renderSettings():[];return S.isElement(n)?e.push({type:$.Html,element:n}):Array.isArray(n)?e.push(...n):e.push(n),[...this.tunesInstances.values(),...this.defaultTunesInstances.values()].map(r=>r.render()).forEach(r=>{S.isElement(r)?t.push({type:$.Html,element:r}):Array.isArray(r)?t.push(...r):t.push(r)}),{toolTunes:e,commonTunes:t}}updateCurrentInput(){this.currentInput=S.isNativeInput(document.activeElement)||!P.anchorNode?document.activeElement:P.anchorNode}dispatchChange(){this.didMutated()}destroy(){this.unwatchBlockMutations(),this.removeInputEvents(),super.destroy(),U(this.toolInstance.destroy)&&this.toolInstance.destroy()}async getActiveToolboxEntry(){const e=this.tool.toolbox;if(e.length===1)return Promise.resolve(this.tool.toolbox[0]);const t=await this.data,n=e;return n==null?void 0:n.find(r=>_r(r.data,t))}async exportDataAsString(){const e=await this.data;return Ss(e,this.tool.conversionConfig)}compose(){const e=S.make("div",ge.CSS.wrapper),t=S.make("div",ge.CSS.content),n=this.toolInstance.render();e.setAttribute("data-cy","block-wrapper"),e.dataset.id=this.id,this.toolRenderedElement=n,t.appendChild(this.toolRenderedElement);let r=t;return[...this.tunesInstances.values(),...this.defaultTunesInstances.values()].forEach(i=>{if(U(i.wrap))try{r=i.wrap(r)}catch(s){D(`Tune ${i.constructor.name} wrap method throws an Error %o`,"warn",s)}}),e.appendChild(r),e}composeTunes(e){Array.from(this.tunes.values()).forEach(t=>{(t.isInternal?this.defaultTunesInstances:this.tunesInstances).set(t.name,t.create(e[t.name],this.blockAPI))}),Object.entries(e).forEach(([t,n])=>{this.tunesInstances.has(t)||(this.unavailableTunesData[t]=n)})}addInputEvents(){this.inputs.forEach(e=>{e.addEventListener("focus",this.handleFocus),S.isNativeInput(e)&&e.addEventListener("input",this.didMutated)})}removeInputEvents(){this.inputs.forEach(e=>{e.removeEventListener("focus",this.handleFocus),S.isNativeInput(e)&&e.removeEventListener("input",this.didMutated)})}watchBlockMutations(){var e;this.redactorDomChangedCallback=t=>{const{mutations:n}=t;n.some(r=>Cs(r,this.toolRenderedElement))&&this.didMutated(n)},(e=this.editorEventBus)==null||e.on(oo,this.redactorDomChangedCallback)}unwatchBlockMutations(){var e;(e=this.editorEventBus)==null||e.off(oo,this.redactorDomChangedCallback)}detectToolRootChange(e){e.forEach(t=>{if(Array.from(t.removedNodes).includes(this.toolRenderedElement)){const n=t.addedNodes[t.addedNodes.length-1];this.toolRenderedElement=n}})}dropInputsCache(){this.cachedInputs=[]}toggleInputsEmptyMark(){this.inputs.forEach(wr)}};class Ts extends j{constructor(){super(...arguments),this.insert=(e=this.config.defaultBlock,t={},n={},r,i,s,a)=>{const c=this.Editor.BlockManager.insert({id:a,tool:e,data:t,index:r,needToFocus:i,replace:s});return new de(c)},this.composeBlockData=async e=>{const t=this.Editor.Tools.blockTools.get(e);return new he({tool:t,api:this.Editor.API,readOnly:!0,data:{},tunesData:{}}).data},this.update=async(e,t,n)=>{const{BlockManager:r}=this.Editor,i=r.getBlockById(e);if(i===void 0)throw new Error(`Block with id "${e}" not found`);const s=await r.update(i,t,n);return new de(s)},this.convert=async(e,t,n)=>{var r,i;const{BlockManager:s,Tools:a}=this.Editor,c=s.getBlockById(e);if(!c)throw new Error(`Block with id "${e}" not found`);const l=a.blockTools.get(c.name),d=a.blockTools.get(t);if(!d)throw new Error(`Block Tool with type "${t}" not found`);const u=((r=l==null?void 0:l.conversionConfig)==null?void 0:r.export)!==void 0,p=((i=d.conversionConfig)==null?void 0:i.import)!==void 0;if(u&&p){const g=await s.convert(c,t,n);return new de(g)}else{const g=[u?!1:ht(c.name),p?!1:ht(t)].filter(Boolean).join(" and ");throw new Error(`Conversion from "${c.name}" to "${t}" is not possible. ${g} tool(s) should provide a "conversionConfig"`)}},this.insertMany=(e,t=this.Editor.BlockManager.blocks.length-1)=>{this.validateIndex(t);const n=e.map(({id:r,type:i,data:s})=>this.Editor.BlockManager.composeBlock({id:r,tool:i||this.config.defaultBlock,data:s}));return this.Editor.BlockManager.insertMany(n,t),n.map(r=>new de(r))}}get methods(){return{clear:()=>this.clear(),render:e=>this.render(e),renderFromHTML:e=>this.renderFromHTML(e),delete:e=>this.delete(e),swap:(e,t)=>this.swap(e,t),move:(e,t)=>this.move(e,t),getBlockByIndex:e=>this.getBlockByIndex(e),getById:e=>this.getById(e),getCurrentBlockIndex:()=>this.getCurrentBlockIndex(),getBlockIndex:e=>this.getBlockIndex(e),getBlocksCount:()=>this.getBlocksCount(),getBlockByElement:e=>this.getBlockByElement(e),stretchBlock:(e,t=!0)=>this.stretchBlock(e,t),insertNewBlock:()=>this.insertNewBlock(),insert:this.insert,insertMany:this.insertMany,update:this.update,composeBlockData:this.composeBlockData,convert:this.convert}}getBlocksCount(){return this.Editor.BlockManager.blocks.length}getCurrentBlockIndex(){return this.Editor.BlockManager.currentBlockIndex}getBlockIndex(e){const t=this.Editor.BlockManager.getBlockById(e);if(!t){ee("There is no block with id `"+e+"`","warn");return}return this.Editor.BlockManager.getBlockIndex(t)}getBlockByIndex(e){const t=this.Editor.BlockManager.getBlockByIndex(e);if(t===void 0){ee("There is no block at index `"+e+"`","warn");return}return new de(t)}getById(e){const t=this.Editor.BlockManager.getBlockById(e);return t===void 0?(ee("There is no block with id `"+e+"`","warn"),null):new de(t)}getBlockByElement(e){const t=this.Editor.BlockManager.getBlock(e);if(t===void 0){ee("There is no block corresponding to element `"+e+"`","warn");return}return new de(t)}swap(e,t){D("`blocks.swap()` method is deprecated and will be removed in the next major release. Use `block.move()` method instead","info"),this.Editor.BlockManager.swap(e,t)}move(e,t){this.Editor.BlockManager.move(e,t)}delete(e=this.Editor.BlockManager.currentBlockIndex){try{const t=this.Editor.BlockManager.getBlockByIndex(e);this.Editor.BlockManager.removeBlock(t)}catch(t){ee(t,"warn");return}this.Editor.BlockManager.blocks.length===0&&this.Editor.BlockManager.insert(),this.Editor.BlockManager.currentBlock&&this.Editor.Caret.setToBlock(this.Editor.BlockManager.currentBlock,this.Editor.Caret.positions.END),this.Editor.Toolbar.close()}async clear(){await this.Editor.BlockManager.clear(!0),this.Editor.InlineToolbar.close()}async render(e){if(e===void 0||e.blocks===void 0)throw new Error("Incorrect data passed to the render() method");this.Editor.ModificationsObserver.disable(),await this.Editor.BlockManager.clear(),await this.Editor.Renderer.render(e.blocks),this.Editor.ModificationsObserver.enable()}renderFromHTML(e){return this.Editor.BlockManager.clear(),this.Editor.Paste.processText(e,!0)}stretchBlock(e,t=!0){eo(!0,"blocks.stretchBlock()","BlockAPI");const n=this.Editor.BlockManager.getBlockByIndex(e);n&&(n.stretched=t)}insertNewBlock(){D("Method blocks.insertNewBlock() is deprecated and it will be removed in the next major release. Use blocks.insert() instead.","warn"),this.insert()}validateIndex(e){if(typeof e!="number")throw new Error("Index should be a number");if(e<0)throw new Error("Index should be greater than or equal to 0");if(e===null)throw new Error("Index should be greater than or equal to 0")}}function Bs(o,e){return typeof o=="number"?e.BlockManager.getBlockByIndex(o):typeof o=="string"?e.BlockManager.getBlockById(o):e.BlockManager.getBlockById(o.id)}class Os extends j{constructor(){super(...arguments),this.setToFirstBlock=(e=this.Editor.Caret.positions.DEFAULT,t=0)=>this.Editor.BlockManager.firstBlock?(this.Editor.Caret.setToBlock(this.Editor.BlockManager.firstBlock,e,t),!0):!1,this.setToLastBlock=(e=this.Editor.Caret.positions.DEFAULT,t=0)=>this.Editor.BlockManager.lastBlock?(this.Editor.Caret.setToBlock(this.Editor.BlockManager.lastBlock,e,t),!0):!1,this.setToPreviousBlock=(e=this.Editor.Caret.positions.DEFAULT,t=0)=>this.Editor.BlockManager.previousBlock?(this.Editor.Caret.setToBlock(this.Editor.BlockManager.previousBlock,e,t),!0):!1,this.setToNextBlock=(e=this.Editor.Caret.positions.DEFAULT,t=0)=>this.Editor.BlockManager.nextBlock?(this.Editor.Caret.setToBlock(this.Editor.BlockManager.nextBlock,e,t),!0):!1,this.setToBlock=(e,t=this.Editor.Caret.positions.DEFAULT,n=0)=>{const r=Bs(e,this.Editor);return r===void 0?!1:(this.Editor.Caret.setToBlock(r,t,n),!0)},this.focus=(e=!1)=>e?this.setToLastBlock(this.Editor.Caret.positions.END):this.setToFirstBlock(this.Editor.Caret.positions.START)}get methods(){return{setToFirstBlock:this.setToFirstBlock,setToLastBlock:this.setToLastBlock,setToPreviousBlock:this.setToPreviousBlock,setToNextBlock:this.setToNextBlock,setToBlock:this.setToBlock,focus:this.focus}}}class _s extends j{get methods(){return{emit:(e,t)=>this.emit(e,t),off:(e,t)=>this.off(e,t),on:(e,t)=>this.on(e,t)}}on(e,t){this.eventsDispatcher.on(e,t)}emit(e,t){this.eventsDispatcher.emit(e,t)}off(e,t){this.eventsDispatcher.off(e,t)}}let Is=class Mr extends j{static getNamespace(e,t){return t?`blockTunes.${e}`:`tools.${e}`}get methods(){return{t:()=>{ee("I18n.t() method can be accessed only from Tools","warn")}}}getMethodsForTool(e,t){return Object.assign(this.methods,{t:n=>G.t(Mr.getNamespace(e,t),n)})}};class Ms extends j{get methods(){return{blocks:this.Editor.BlocksAPI.methods,caret:this.Editor.CaretAPI.methods,tools:this.Editor.ToolsAPI.methods,events:this.Editor.EventsAPI.methods,listeners:this.Editor.ListenersAPI.methods,notifier:this.Editor.NotifierAPI.methods,sanitizer:this.Editor.SanitizerAPI.methods,saver:this.Editor.SaverAPI.methods,selection:this.Editor.SelectionAPI.methods,styles:this.Editor.StylesAPI.classes,toolbar:this.Editor.ToolbarAPI.methods,inlineToolbar:this.Editor.InlineToolbarAPI.methods,tooltip:this.Editor.TooltipAPI.methods,i18n:this.Editor.I18nAPI.methods,readOnly:this.Editor.ReadOnlyAPI.methods,ui:this.Editor.UiAPI.methods}}getMethodsForTool(e,t){return Object.assign(this.methods,{i18n:this.Editor.I18nAPI.getMethodsForTool(e,t)})}}class Ls extends j{get methods(){return{close:()=>this.close(),open:()=>this.open()}}open(){this.Editor.InlineToolbar.tryToShow()}close(){this.Editor.InlineToolbar.close()}}class Ps extends j{get methods(){return{on:(e,t,n,r)=>this.on(e,t,n,r),off:(e,t,n,r)=>this.off(e,t,n,r),offById:e=>this.offById(e)}}on(e,t,n,r){return this.listeners.on(e,t,n,r)}off(e,t,n,r){this.listeners.off(e,t,n,r)}offById(e){this.listeners.offById(e)}}var Lr={exports:{}};(function(o,e){(function(t,n){o.exports=n()})(window,function(){return function(t){var n={};function r(i){if(n[i])return n[i].exports;var s=n[i]={i,l:!1,exports:{}};return t[i].call(s.exports,s,s.exports,r),s.l=!0,s.exports}return r.m=t,r.c=n,r.d=function(i,s,a){r.o(i,s)||Object.defineProperty(i,s,{enumerable:!0,get:a})},r.r=function(i){typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(i,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(i,"__esModule",{value:!0})},r.t=function(i,s){if(1&s&&(i=r(i)),8&s||4&s&&typeof i=="object"&&i&&i.__esModule)return i;var a=Object.create(null);if(r.r(a),Object.defineProperty(a,"default",{enumerable:!0,value:i}),2&s&&typeof i!="string")for(var c in i)r.d(a,c,(function(l){return i[l]}).bind(null,c));return a},r.n=function(i){var s=i&&i.__esModule?function(){return i.default}:function(){return i};return r.d(s,"a",s),s},r.o=function(i,s){return Object.prototype.hasOwnProperty.call(i,s)},r.p="/",r(r.s=0)}([function(t,n,r){r(1),t.exports=function(){var i=r(6),s="cdx-notify--bounce-in",a=null;return{show:function(c){if(c.message){(function(){if(a)return!0;a=i.getWrapper(),document.body.appendChild(a)})();var l=null,d=c.time||8e3;switch(c.type){case"confirm":l=i.confirm(c);break;case"prompt":l=i.prompt(c);break;default:l=i.alert(c),window.setTimeout(function(){l.remove()},d)}a.appendChild(l),l.classList.add(s)}}}}()},function(t,n,r){var i=r(2);typeof i=="string"&&(i=[[t.i,i,""]]);var s={hmr:!0,transform:void 0,insertInto:void 0};r(4)(i,s),i.locals&&(t.exports=i.locals)},function(t,n,r){(t.exports=r(3)(!1)).push([t.i,`.cdx-notify--error{background:#fffbfb!important}.cdx-notify--error::before{background:#fb5d5d!important}.cdx-notify__input{max-width:130px;padding:5px 10px;background:#f7f7f7;border:0;border-radius:3px;font-size:13px;color:#656b7c;outline:0}.cdx-notify__input:-ms-input-placeholder{color:#656b7c}.cdx-notify__input::placeholder{color:#656b7c}.cdx-notify__input:focus:-ms-input-placeholder{color:rgba(101,107,124,.3)}.cdx-notify__input:focus::placeholder{color:rgba(101,107,124,.3)}.cdx-notify__button{border:none;border-radius:3px;font-size:13px;padding:5px 10px;cursor:pointer}.cdx-notify__button:last-child{margin-left:10px}.cdx-notify__button--cancel{background:#f2f5f7;box-shadow:0 2px 1px 0 rgba(16,19,29,0);color:#656b7c}.cdx-notify__button--cancel:hover{background:#eee}.cdx-notify__button--confirm{background:#34c992;box-shadow:0 1px 1px 0 rgba(18,49,35,.05);color:#fff}.cdx-notify__button--confirm:hover{background:#33b082}.cdx-notify__btns-wrapper{display:-ms-flexbox;display:flex;-ms-flex-flow:row nowrap;flex-flow:row nowrap;margin-top:5px}.cdx-notify__cross{position:absolute;top:5px;right:5px;width:10px;height:10px;padding:5px;opacity:.54;cursor:pointer}.cdx-notify__cross::after,.cdx-notify__cross::before{content:'';position:absolute;left:9px;top:5px;height:12px;width:2px;background:#575d67}.cdx-notify__cross::before{transform:rotate(-45deg)}.cdx-notify__cross::after{transform:rotate(45deg)}.cdx-notify__cross:hover{opacity:1}.cdx-notifies{position:fixed;z-index:2;bottom:20px;left:20px;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen,Ubuntu,Cantarell,"Fira Sans","Droid Sans","Helvetica Neue",sans-serif}.cdx-notify{position:relative;width:220px;margin-top:15px;padding:13px 16px;background:#fff;box-shadow:0 11px 17px 0 rgba(23,32,61,.13);border-radius:5px;font-size:14px;line-height:1.4em;word-wrap:break-word}.cdx-notify::before{content:'';position:absolute;display:block;top:0;left:0;width:3px;height:calc(100% - 6px);margin:3px;border-radius:5px;background:0 0}@keyframes bounceIn{0%{opacity:0;transform:scale(.3)}50%{opacity:1;transform:scale(1.05)}70%{transform:scale(.9)}100%{transform:scale(1)}}.cdx-notify--bounce-in{animation-name:bounceIn;animation-duration:.6s;animation-iteration-count:1}.cdx-notify--success{background:#fafffe!important}.cdx-notify--success::before{background:#41ffb1!important}`,""])},function(t,n){t.exports=function(r){var i=[];return i.toString=function(){return this.map(function(s){var a=function(c,l){var d=c[1]||"",u=c[3];if(!u)return d;if(l&&typeof btoa=="function"){var p=(b=u,"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(b))))+" */"),g=u.sources.map(function(f){return"/*# sourceURL="+u.sourceRoot+f+" */"});return[d].concat(g).concat([p]).join(`
`)}var b;return[d].join(`
`)}(s,r);return s[2]?"@media "+s[2]+"{"+a+"}":a}).join("")},i.i=function(s,a){typeof s=="string"&&(s=[[null,s,""]]);for(var c={},l=0;l<this.length;l++){var d=this[l][0];typeof d=="number"&&(c[d]=!0)}for(l=0;l<s.length;l++){var u=s[l];typeof u[0]=="number"&&c[u[0]]||(a&&!u[2]?u[2]=a:a&&(u[2]="("+u[2]+") and ("+a+")"),i.push(u))}},i}},function(t,n,r){var i,s,a={},c=(i=function(){return window&&document&&document.all&&!window.atob},function(){return s===void 0&&(s=i.apply(this,arguments)),s}),l=function(x){var C={};return function(I){if(typeof I=="function")return I();if(C[I]===void 0){var E=(function(B){return document.querySelector(B)}).call(this,I);if(window.HTMLIFrameElement&&E instanceof window.HTMLIFrameElement)try{E=E.contentDocument.head}catch{E=null}C[I]=E}return C[I]}}(),d=null,u=0,p=[],g=r(5);function b(x,C){for(var I=0;I<x.length;I++){var E=x[I],B=a[E.id];if(B){B.refs++;for(var _=0;_<B.parts.length;_++)B.parts[_](E.parts[_]);for(;_<E.parts.length;_++)B.parts.push(y(E.parts[_],C))}else{var L=[];for(_=0;_<E.parts.length;_++)L.push(y(E.parts[_],C));a[E.id]={id:E.id,refs:1,parts:L}}}}function f(x,C){for(var I=[],E={},B=0;B<x.length;B++){var _=x[B],L=C.base?_[0]+C.base:_[0],M={css:_[1],media:_[2],sourceMap:_[3]};E[L]?E[L].parts.push(M):I.push(E[L]={id:L,parts:[M]})}return I}function m(x,C){var I=l(x.insertInto);if(!I)throw new Error("Couldn't find a style target. This probably means that the value for the 'insertInto' parameter is invalid.");var E=p[p.length-1];if(x.insertAt==="top")E?E.nextSibling?I.insertBefore(C,E.nextSibling):I.appendChild(C):I.insertBefore(C,I.firstChild),p.push(C);else if(x.insertAt==="bottom")I.appendChild(C);else{if(typeof x.insertAt!="object"||!x.insertAt.before)throw new Error(`[Style Loader]

 Invalid value for parameter 'insertAt' ('options.insertAt') found.
 Must be 'top', 'bottom', or Object.
 (https://github.com/webpack-contrib/style-loader#insertat)
`);var B=l(x.insertInto+" "+x.insertAt.before);I.insertBefore(C,B)}}function w(x){if(x.parentNode===null)return!1;x.parentNode.removeChild(x);var C=p.indexOf(x);C>=0&&p.splice(C,1)}function h(x){var C=document.createElement("style");return x.attrs.type===void 0&&(x.attrs.type="text/css"),v(C,x.attrs),m(x,C),C}function v(x,C){Object.keys(C).forEach(function(I){x.setAttribute(I,C[I])})}function y(x,C){var I,E,B,_;if(C.transform&&x.css){if(!(_=C.transform(x.css)))return function(){};x.css=_}if(C.singleton){var L=u++;I=d||(d=h(C)),E=O.bind(null,I,L,!1),B=O.bind(null,I,L,!0)}else x.sourceMap&&typeof URL=="function"&&typeof URL.createObjectURL=="function"&&typeof URL.revokeObjectURL=="function"&&typeof Blob=="function"&&typeof btoa=="function"?(I=function(M){var A=document.createElement("link");return M.attrs.type===void 0&&(M.attrs.type="text/css"),M.attrs.rel="stylesheet",v(A,M.attrs),m(M,A),A}(C),E=(function(M,A,H){var R=H.css,oe=H.sourceMap,ts=A.convertToAbsoluteUrls===void 0&&oe;(A.convertToAbsoluteUrls||ts)&&(R=g(R)),oe&&(R+=`
/*# sourceMappingURL=data:application/json;base64,`+btoa(unescape(encodeURIComponent(JSON.stringify(oe))))+" */");var os=new Blob([R],{type:"text/css"}),Gn=M.href;M.href=URL.createObjectURL(os),Gn&&URL.revokeObjectURL(Gn)}).bind(null,I,C),B=function(){w(I),I.href&&URL.revokeObjectURL(I.href)}):(I=h(C),E=(function(M,A){var H=A.css,R=A.media;if(R&&M.setAttribute("media",R),M.styleSheet)M.styleSheet.cssText=H;else{for(;M.firstChild;)M.removeChild(M.firstChild);M.appendChild(document.createTextNode(H))}}).bind(null,I),B=function(){w(I)});return E(x),function(M){if(M){if(M.css===x.css&&M.media===x.media&&M.sourceMap===x.sourceMap)return;E(x=M)}else B()}}t.exports=function(x,C){if(typeof DEBUG<"u"&&DEBUG&&typeof document!="object")throw new Error("The style-loader cannot be used in a non-browser environment");(C=C||{}).attrs=typeof C.attrs=="object"?C.attrs:{},C.singleton||typeof C.singleton=="boolean"||(C.singleton=c()),C.insertInto||(C.insertInto="head"),C.insertAt||(C.insertAt="bottom");var I=f(x,C);return b(I,C),function(E){for(var B=[],_=0;_<I.length;_++){var L=I[_];(M=a[L.id]).refs--,B.push(M)}for(E&&b(f(E,C),C),_=0;_<B.length;_++){var M;if((M=B[_]).refs===0){for(var A=0;A<M.parts.length;A++)M.parts[A]();delete a[M.id]}}}};var k,T=(k=[],function(x,C){return k[x]=C,k.filter(Boolean).join(`
`)});function O(x,C,I,E){var B=I?"":E.css;if(x.styleSheet)x.styleSheet.cssText=T(C,B);else{var _=document.createTextNode(B),L=x.childNodes;L[C]&&x.removeChild(L[C]),L.length?x.insertBefore(_,L[C]):x.appendChild(_)}}},function(t,n){t.exports=function(r){var i=typeof window<"u"&&window.location;if(!i)throw new Error("fixUrls requires window.location");if(!r||typeof r!="string")return r;var s=i.protocol+"//"+i.host,a=s+i.pathname.replace(/\/[^\/]*$/,"/");return r.replace(/url\s*\(((?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)\)/gi,function(c,l){var d,u=l.trim().replace(/^"(.*)"$/,function(p,g){return g}).replace(/^'(.*)'$/,function(p,g){return g});return/^(#|data:|http:\/\/|https:\/\/|file:\/\/\/|\s*$)/i.test(u)?c:(d=u.indexOf("//")===0?u:u.indexOf("/")===0?s+u:a+u.replace(/^\.\//,""),"url("+JSON.stringify(d)+")")})}},function(t,n,r){var i,s,a,c,l,d,u,p,g;t.exports=(i="cdx-notifies",s="cdx-notify",a="cdx-notify__cross",c="cdx-notify__button--confirm",l="cdx-notify__button--cancel",d="cdx-notify__input",u="cdx-notify__button",p="cdx-notify__btns-wrapper",{alert:g=function(b){var f=document.createElement("DIV"),m=document.createElement("DIV"),w=b.message,h=b.style;return f.classList.add(s),h&&f.classList.add(s+"--"+h),f.innerHTML=w,m.classList.add(a),m.addEventListener("click",f.remove.bind(f)),f.appendChild(m),f},confirm:function(b){var f=g(b),m=document.createElement("div"),w=document.createElement("button"),h=document.createElement("button"),v=f.querySelector("."+a),y=b.cancelHandler,k=b.okHandler;return m.classList.add(p),w.innerHTML=b.okText||"Confirm",h.innerHTML=b.cancelText||"Cancel",w.classList.add(u),h.classList.add(u),w.classList.add(c),h.classList.add(l),y&&typeof y=="function"&&(h.addEventListener("click",y),v.addEventListener("click",y)),k&&typeof k=="function"&&w.addEventListener("click",k),w.addEventListener("click",f.remove.bind(f)),h.addEventListener("click",f.remove.bind(f)),m.appendChild(w),m.appendChild(h),f.appendChild(m),f},prompt:function(b){var f=g(b),m=document.createElement("div"),w=document.createElement("button"),h=document.createElement("input"),v=f.querySelector("."+a),y=b.cancelHandler,k=b.okHandler;return m.classList.add(p),w.innerHTML=b.okText||"Ok",w.classList.add(u),w.classList.add(c),h.classList.add(d),b.placeholder&&h.setAttribute("placeholder",b.placeholder),b.default&&(h.value=b.default),b.inputType&&(h.type=b.inputType),y&&typeof y=="function"&&v.addEventListener("click",y),k&&typeof k=="function"&&w.addEventListener("click",function(){k(h.value)}),w.addEventListener("click",f.remove.bind(f)),m.appendChild(h),m.appendChild(w),f.appendChild(m),f},getWrapper:function(){var b=document.createElement("DIV");return b.classList.add(i),b}})}])})})(Lr);var As=Lr.exports;const Ns=xt(As);class js{show(e){Ns.show(e)}}class Rs extends j{constructor({config:e,eventsDispatcher:t}){super({config:e,eventsDispatcher:t}),this.notifier=new js}get methods(){return{show:e=>this.show(e)}}show(e){return this.notifier.show(e)}}class Ds extends j{get methods(){const e=()=>this.isEnabled;return{toggle:t=>this.toggle(t),get isEnabled(){return e()}}}toggle(e){return this.Editor.ReadOnly.toggle(e)}get isEnabled(){return this.Editor.ReadOnly.isEnabled}}var Pr={exports:{}};(function(o,e){(function(t,n){o.exports=n()})(Ye,function(){function t(u){var p=u.tags,g=Object.keys(p),b=g.map(function(f){return typeof p[f]}).every(function(f){return f==="object"||f==="boolean"||f==="function"});if(!b)throw new Error("The configuration was invalid");this.config=u}var n=["P","LI","TD","TH","DIV","H1","H2","H3","H4","H5","H6","PRE"];function r(u){return n.indexOf(u.nodeName)!==-1}var i=["A","B","STRONG","I","EM","SUB","SUP","U","STRIKE"];function s(u){return i.indexOf(u.nodeName)!==-1}t.prototype.clean=function(u){const p=document.implementation.createHTMLDocument(),g=p.createElement("div");return g.innerHTML=u,this._sanitize(p,g),g.innerHTML},t.prototype._sanitize=function(u,p){var g=a(u,p),b=g.firstChild();if(b)do{if(b.nodeType===Node.TEXT_NODE)if(b.data.trim()===""&&(b.previousElementSibling&&r(b.previousElementSibling)||b.nextElementSibling&&r(b.nextElementSibling))){p.removeChild(b),this._sanitize(u,p);break}else continue;if(b.nodeType===Node.COMMENT_NODE){p.removeChild(b),this._sanitize(u,p);break}var f=s(b),m;f&&(m=Array.prototype.some.call(b.childNodes,r));var w=!!p.parentNode,h=r(p)&&r(b)&&w,v=b.nodeName.toLowerCase(),y=c(this.config,v,b),k=f&&m;if(k||l(b,y)||!this.config.keepNestedBlockElements&&h){if(!(b.nodeName==="SCRIPT"||b.nodeName==="STYLE"))for(;b.childNodes.length>0;)p.insertBefore(b.childNodes[0],b);p.removeChild(b),this._sanitize(u,p);break}for(var T=0;T<b.attributes.length;T+=1){var O=b.attributes[T];d(O,y,b)&&(b.removeAttribute(O.name),T=T-1)}this._sanitize(u,b)}while(b=g.nextSibling())};function a(u,p){return u.createTreeWalker(p,NodeFilter.SHOW_TEXT|NodeFilter.SHOW_ELEMENT|NodeFilter.SHOW_COMMENT,null,!1)}function c(u,p,g){return typeof u.tags[p]=="function"?u.tags[p](g):u.tags[p]}function l(u,p){return typeof p>"u"?!0:typeof p=="boolean"?!p:!1}function d(u,p,g){var b=u.name.toLowerCase();return p===!0?!1:typeof p[b]=="function"?!p[b](u.value,g):typeof p[b]>"u"||p[b]===!1?!0:typeof p[b]=="string"?p[b]!==u.value:!1}return t})})(Pr);var Fs=Pr.exports;const Hs=xt(Fs);function uo(o,e){return o.map(t=>{const n=U(e)?e(t.tool):e;return te(n)||(t.data=ho(t.data,n)),t})}function ae(o,e={}){const t={tags:e};return new Hs(t).clean(o)}function ho(o,e){return Array.isArray(o)?$s(o,e):Y(o)?Us(o,e):pe(o)?zs(o,e):o}function $s(o,e){return o.map(t=>ho(t,e))}function Us(o,e){const t={};for(const n in o){if(!Object.prototype.hasOwnProperty.call(o,n))continue;const r=o[n],i=Ws(e[n])?e[n]:e;t[n]=ho(r,i)}return t}function zs(o,e){return Y(e)?ae(o,e):e===!1?ae(o,{}):o}function Ws(o){return Y(o)||ls(o)||U(o)}class qs extends j{get methods(){return{clean:(e,t)=>this.clean(e,t)}}clean(e,t){return ae(e,t)}}class Ys extends j{get methods(){return{save:()=>this.save()}}save(){const e="Editor's content can not be saved in read-only mode";return this.Editor.ReadOnly.isEnabled?(ee(e,"warn"),Promise.reject(new Error(e))):this.Editor.Saver.save()}}class Ks extends j{constructor(){super(...arguments),this.selectionUtils=new P}get methods(){return{findParentTag:(e,t)=>this.findParentTag(e,t),expandToTag:e=>this.expandToTag(e),save:()=>this.selectionUtils.save(),restore:()=>this.selectionUtils.restore(),setFakeBackground:()=>this.selectionUtils.setFakeBackground(),removeFakeBackground:()=>this.selectionUtils.removeFakeBackground()}}findParentTag(e,t){return this.selectionUtils.findParentTag(e,t)}expandToTag(e){this.selectionUtils.expandToTag(e)}}class Vs extends j{get methods(){return{getBlockTools:()=>Array.from(this.Editor.Tools.blockTools.values())}}}class Xs extends j{get classes(){return{block:"cdx-block",inlineToolButton:"ce-inline-tool",inlineToolButtonActive:"ce-inline-tool--active",input:"cdx-input",loader:"cdx-loader",button:"cdx-button",settingsButton:"cdx-settings-button",settingsButtonActive:"cdx-settings-button--active"}}}class Gs extends j{get methods(){return{close:()=>this.close(),open:()=>this.open(),toggleBlockSettings:e=>this.toggleBlockSettings(e),toggleToolbox:e=>this.toggleToolbox(e)}}open(){this.Editor.Toolbar.moveAndOpen()}close(){this.Editor.Toolbar.close()}toggleBlockSettings(e){if(this.Editor.BlockManager.currentBlockIndex===-1){ee("Could't toggle the Toolbar because there is no block selected ","warn");return}e??!this.Editor.BlockSettings.opened?(this.Editor.Toolbar.moveAndOpen(),this.Editor.BlockSettings.open()):this.Editor.BlockSettings.close()}toggleToolbox(e){if(this.Editor.BlockManager.currentBlockIndex===-1){ee("Could't toggle the Toolbox because there is no block selected ","warn");return}e??!this.Editor.Toolbar.toolbox.opened?(this.Editor.Toolbar.moveAndOpen(),this.Editor.Toolbar.toolbox.open()):this.Editor.Toolbar.toolbox.close()}}var Ar={exports:{}};/*!
 * CodeX.Tooltips
 * 
 * @version 1.0.5
 * 
 * @licence MIT
 * <AUTHOR> <https://codex.so>
 * 
 * 
 */(function(o,e){(function(t,n){o.exports=n()})(window,function(){return function(t){var n={};function r(i){if(n[i])return n[i].exports;var s=n[i]={i,l:!1,exports:{}};return t[i].call(s.exports,s,s.exports,r),s.l=!0,s.exports}return r.m=t,r.c=n,r.d=function(i,s,a){r.o(i,s)||Object.defineProperty(i,s,{enumerable:!0,get:a})},r.r=function(i){typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(i,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(i,"__esModule",{value:!0})},r.t=function(i,s){if(1&s&&(i=r(i)),8&s||4&s&&typeof i=="object"&&i&&i.__esModule)return i;var a=Object.create(null);if(r.r(a),Object.defineProperty(a,"default",{enumerable:!0,value:i}),2&s&&typeof i!="string")for(var c in i)r.d(a,c,(function(l){return i[l]}).bind(null,c));return a},r.n=function(i){var s=i&&i.__esModule?function(){return i.default}:function(){return i};return r.d(s,"a",s),s},r.o=function(i,s){return Object.prototype.hasOwnProperty.call(i,s)},r.p="",r(r.s=0)}([function(t,n,r){t.exports=r(1)},function(t,n,r){r.r(n),r.d(n,"default",function(){return i});class i{constructor(){this.nodes={wrapper:null,content:null},this.showed=!1,this.offsetTop=10,this.offsetLeft=10,this.offsetRight=10,this.hidingDelay=0,this.handleWindowScroll=()=>{this.showed&&this.hide(!0)},this.loadStyles(),this.prepare(),window.addEventListener("scroll",this.handleWindowScroll,{passive:!0})}get CSS(){return{tooltip:"ct",tooltipContent:"ct__content",tooltipShown:"ct--shown",placement:{left:"ct--left",bottom:"ct--bottom",right:"ct--right",top:"ct--top"}}}show(a,c,l){this.nodes.wrapper||this.prepare(),this.hidingTimeout&&clearTimeout(this.hidingTimeout);const d=Object.assign({placement:"bottom",marginTop:0,marginLeft:0,marginRight:0,marginBottom:0,delay:70,hidingDelay:0},l);if(d.hidingDelay&&(this.hidingDelay=d.hidingDelay),this.nodes.content.innerHTML="",typeof c=="string")this.nodes.content.appendChild(document.createTextNode(c));else{if(!(c instanceof Node))throw Error("[CodeX Tooltip] Wrong type of «content» passed. It should be an instance of Node or String. But "+typeof c+" given.");this.nodes.content.appendChild(c)}switch(this.nodes.wrapper.classList.remove(...Object.values(this.CSS.placement)),d.placement){case"top":this.placeTop(a,d);break;case"left":this.placeLeft(a,d);break;case"right":this.placeRight(a,d);break;case"bottom":default:this.placeBottom(a,d)}d&&d.delay?this.showingTimeout=setTimeout(()=>{this.nodes.wrapper.classList.add(this.CSS.tooltipShown),this.showed=!0},d.delay):(this.nodes.wrapper.classList.add(this.CSS.tooltipShown),this.showed=!0)}hide(a=!1){if(this.hidingDelay&&!a)return this.hidingTimeout&&clearTimeout(this.hidingTimeout),void(this.hidingTimeout=setTimeout(()=>{this.hide(!0)},this.hidingDelay));this.nodes.wrapper.classList.remove(this.CSS.tooltipShown),this.showed=!1,this.showingTimeout&&clearTimeout(this.showingTimeout)}onHover(a,c,l){a.addEventListener("mouseenter",()=>{this.show(a,c,l)}),a.addEventListener("mouseleave",()=>{this.hide()})}destroy(){this.nodes.wrapper.remove(),window.removeEventListener("scroll",this.handleWindowScroll)}prepare(){this.nodes.wrapper=this.make("div",this.CSS.tooltip),this.nodes.content=this.make("div",this.CSS.tooltipContent),this.append(this.nodes.wrapper,this.nodes.content),this.append(document.body,this.nodes.wrapper)}loadStyles(){const a="codex-tooltips-style";if(document.getElementById(a))return;const c=r(2),l=this.make("style",null,{textContent:c.toString(),id:a});this.prepend(document.head,l)}placeBottom(a,c){const l=a.getBoundingClientRect(),d=l.left+a.clientWidth/2-this.nodes.wrapper.offsetWidth/2,u=l.bottom+window.pageYOffset+this.offsetTop+c.marginTop;this.applyPlacement("bottom",d,u)}placeTop(a,c){const l=a.getBoundingClientRect(),d=l.left+a.clientWidth/2-this.nodes.wrapper.offsetWidth/2,u=l.top+window.pageYOffset-this.nodes.wrapper.clientHeight-this.offsetTop;this.applyPlacement("top",d,u)}placeLeft(a,c){const l=a.getBoundingClientRect(),d=l.left-this.nodes.wrapper.offsetWidth-this.offsetLeft-c.marginLeft,u=l.top+window.pageYOffset+a.clientHeight/2-this.nodes.wrapper.offsetHeight/2;this.applyPlacement("left",d,u)}placeRight(a,c){const l=a.getBoundingClientRect(),d=l.right+this.offsetRight+c.marginRight,u=l.top+window.pageYOffset+a.clientHeight/2-this.nodes.wrapper.offsetHeight/2;this.applyPlacement("right",d,u)}applyPlacement(a,c,l){this.nodes.wrapper.classList.add(this.CSS.placement[a]),this.nodes.wrapper.style.left=c+"px",this.nodes.wrapper.style.top=l+"px"}make(a,c=null,l={}){const d=document.createElement(a);Array.isArray(c)?d.classList.add(...c):c&&d.classList.add(c);for(const u in l)l.hasOwnProperty(u)&&(d[u]=l[u]);return d}append(a,c){Array.isArray(c)?c.forEach(l=>a.appendChild(l)):a.appendChild(c)}prepend(a,c){Array.isArray(c)?(c=c.reverse()).forEach(l=>a.prepend(l)):a.prepend(c)}}},function(t,n){t.exports=`.ct{z-index:999;opacity:0;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;pointer-events:none;-webkit-transition:opacity 50ms ease-in,-webkit-transform 70ms cubic-bezier(.215,.61,.355,1);transition:opacity 50ms ease-in,-webkit-transform 70ms cubic-bezier(.215,.61,.355,1);transition:opacity 50ms ease-in,transform 70ms cubic-bezier(.215,.61,.355,1);transition:opacity 50ms ease-in,transform 70ms cubic-bezier(.215,.61,.355,1),-webkit-transform 70ms cubic-bezier(.215,.61,.355,1);will-change:opacity,top,left;-webkit-box-shadow:0 8px 12px 0 rgba(29,32,43,.17),0 4px 5px -3px rgba(5,6,12,.49);box-shadow:0 8px 12px 0 rgba(29,32,43,.17),0 4px 5px -3px rgba(5,6,12,.49);border-radius:9px}.ct,.ct:before{position:absolute;top:0;left:0}.ct:before{content:"";bottom:0;right:0;background-color:#1d202b;z-index:-1;border-radius:4px}@supports(-webkit-mask-box-image:url("")){.ct:before{border-radius:0;-webkit-mask-box-image:url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"><path d="M10.71 0h2.58c3.02 0 4.64.42 6.1 1.2a8.18 8.18 0 013.4 3.4C23.6 6.07 24 7.7 24 10.71v2.58c0 3.02-.42 4.64-1.2 6.1a8.18 8.18 0 01-3.4 3.4c-1.47.8-3.1 1.21-6.11 1.21H10.7c-3.02 0-4.64-.42-6.1-1.2a8.18 8.18 0 01-3.4-3.4C.4 17.93 0 16.3 0 13.29V10.7c0-3.02.42-4.64 1.2-6.1a8.18 8.18 0 013.4-3.4C6.07.4 7.7 0 10.71 0z"/></svg>') 48% 41% 37.9% 53.3%}}@media (--mobile){.ct{display:none}}.ct__content{padding:6px 10px;color:#cdd1e0;font-size:12px;text-align:center;letter-spacing:.02em;line-height:1em}.ct:after{content:"";width:8px;height:8px;position:absolute;background-color:#1d202b;z-index:-1}.ct--bottom{-webkit-transform:translateY(5px);transform:translateY(5px)}.ct--bottom:after{top:-3px;left:50%;-webkit-transform:translateX(-50%) rotate(-45deg);transform:translateX(-50%) rotate(-45deg)}.ct--top{-webkit-transform:translateY(-5px);transform:translateY(-5px)}.ct--top:after{top:auto;bottom:-3px;left:50%;-webkit-transform:translateX(-50%) rotate(-45deg);transform:translateX(-50%) rotate(-45deg)}.ct--left{-webkit-transform:translateX(-5px);transform:translateX(-5px)}.ct--left:after{top:50%;left:auto;right:0;-webkit-transform:translate(41.6%,-50%) rotate(-45deg);transform:translate(41.6%,-50%) rotate(-45deg)}.ct--right{-webkit-transform:translateX(5px);transform:translateX(5px)}.ct--right:after{top:50%;left:0;-webkit-transform:translate(-41.6%,-50%) rotate(-45deg);transform:translate(-41.6%,-50%) rotate(-45deg)}.ct--shown{opacity:1;-webkit-transform:none;transform:none}`}]).default})})(Ar);var Zs=Ar.exports;const Js=xt(Zs);let ne=null;function po(){ne||(ne=new Js)}function Qs(o,e,t){po(),ne==null||ne.show(o,e,t)}function ft(o=!1){po(),ne==null||ne.hide(o)}function gt(o,e,t){po(),ne==null||ne.onHover(o,e,t)}function ea(){ne==null||ne.destroy(),ne=null}class ta extends j{constructor({config:e,eventsDispatcher:t}){super({config:e,eventsDispatcher:t})}get methods(){return{show:(e,t,n)=>this.show(e,t,n),hide:()=>this.hide(),onHover:(e,t,n)=>this.onHover(e,t,n)}}show(e,t,n){Qs(e,t,n)}hide(){ft()}onHover(e,t,n){gt(e,t,n)}}class oa extends j{get methods(){return{nodes:this.editorNodes}}get editorNodes(){return{wrapper:this.Editor.UI.nodes.wrapper,redactor:this.Editor.UI.nodes.redactor}}}function Nr(o,e){const t={};return Object.entries(o).forEach(([n,r])=>{if(Y(r)){const i=e?`${e}.${n}`:n;Object.values(r).every(s=>pe(s))?t[n]=i:t[n]=Nr(r,i);return}t[n]=r}),t}const Q=Nr(xr);function na(o,e){const t={};return Object.keys(o).forEach(n=>{const r=e[n];r!==void 0?t[r]=o[n]:t[n]=o[n]}),t}const jr=class ze{constructor(e,t){this.cursor=-1,this.items=[],this.items=e||[],this.focusedCssClass=t}get currentItem(){return this.cursor===-1?null:this.items[this.cursor]}setCursor(e){e<this.items.length&&e>=-1&&(this.dropCursor(),this.cursor=e,this.items[this.cursor].classList.add(this.focusedCssClass))}setItems(e){this.items=e}next(){this.cursor=this.leafNodesAndReturnIndex(ze.directions.RIGHT)}previous(){this.cursor=this.leafNodesAndReturnIndex(ze.directions.LEFT)}dropCursor(){this.cursor!==-1&&(this.items[this.cursor].classList.remove(this.focusedCssClass),this.cursor=-1)}leafNodesAndReturnIndex(e){if(this.items.length===0)return this.cursor;let t=this.cursor;return t===-1?t=e===ze.directions.RIGHT?-1:0:this.items[t].classList.remove(this.focusedCssClass),e===ze.directions.RIGHT?t=(t+1)%this.items.length:t=(this.items.length+t-1)%this.items.length,S.canSetCaret(this.items[t])&&ut(()=>P.setCursor(this.items[t]),50)(),this.items[t].classList.add(this.focusedCssClass),t}};jr.directions={RIGHT:"right",LEFT:"left"};let Fe=jr,mt=class ro{constructor(e){this.iterator=null,this.activated=!1,this.flipCallbacks=[],this.onKeyDown=t=>{if(this.isEventReadyForHandling(t))switch(ro.usedKeys.includes(t.keyCode)&&t.preventDefault(),t.keyCode){case N.TAB:this.handleTabPress(t);break;case N.LEFT:case N.UP:this.flipLeft();break;case N.RIGHT:case N.DOWN:this.flipRight();break;case N.ENTER:this.handleEnterPress(t);break}},this.iterator=new Fe(e.items,e.focusedItemClass),this.activateCallback=e.activateCallback,this.allowedKeys=e.allowedKeys||ro.usedKeys}get isActivated(){return this.activated}static get usedKeys(){return[N.TAB,N.LEFT,N.RIGHT,N.ENTER,N.UP,N.DOWN]}activate(e,t){this.activated=!0,e&&this.iterator.setItems(e),t!==void 0&&this.iterator.setCursor(t),document.addEventListener("keydown",this.onKeyDown,!0)}deactivate(){this.activated=!1,this.dropCursor(),document.removeEventListener("keydown",this.onKeyDown)}focusFirst(){this.dropCursor(),this.flipRight()}flipLeft(){this.iterator.previous(),this.flipCallback()}flipRight(){this.iterator.next(),this.flipCallback()}hasFocus(){return!!this.iterator.currentItem}onFlip(e){this.flipCallbacks.push(e)}removeOnFlip(e){this.flipCallbacks=this.flipCallbacks.filter(t=>t!==e)}dropCursor(){this.iterator.dropCursor()}isEventReadyForHandling(e){return this.activated&&this.allowedKeys.includes(e.keyCode)}handleTabPress(e){switch(e.shiftKey?Fe.directions.LEFT:Fe.directions.RIGHT){case Fe.directions.RIGHT:this.flipRight();break;case Fe.directions.LEFT:this.flipLeft();break}}handleEnterPress(e){this.activated&&(this.iterator.currentItem&&(e.stopPropagation(),e.preventDefault(),this.iterator.currentItem.click()),U(this.activateCallback)&&this.activateCallback(this.iterator.currentItem))}flipCallback(){this.iterator.currentItem&&this.iterator.currentItem.scrollIntoViewIfNeeded(),this.flipCallbacks.forEach(e=>e())}};const ra='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M9 12L9 7.1C9 7.04477 9.04477 7 9.1 7H10.4C11.5 7 14 7.1 14 9.5C14 9.5 14 12 11 12M9 12V16.8C9 16.9105 9.08954 17 9.2 17H12.5C14 17 15 16 15 14.5C15 11.7046 11 12 11 12M9 12H11"/></svg>',ia='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M7 10L11.8586 14.8586C11.9367 14.9367 12.0633 14.9367 12.1414 14.8586L17 10"/></svg>',sa='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M14.5 17.5L9.64142 12.6414C9.56331 12.5633 9.56331 12.4367 9.64142 12.3586L14.5 7.5"/></svg>',aa='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M9.58284 17.5L14.4414 12.6414C14.5195 12.5633 14.5195 12.4367 14.4414 12.3586L9.58284 7.5"/></svg>',la='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M7 15L11.8586 10.1414C11.9367 10.0633 12.0633 10.0633 12.1414 10.1414L17 15"/></svg>',ca='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M8 8L12 12M12 12L16 16M12 12L16 8M12 12L8 16"/></svg>',da='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><circle cx="12" cy="12" r="4" stroke="currentColor" stroke-width="2"/></svg>',ua='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M13.34 10C12.4223 12.7337 11 17 11 17"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M14.21 7H14.2"/></svg>',or='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M7.69998 12.6L7.67896 12.62C6.53993 13.7048 6.52012 15.5155 7.63516 16.625V16.625C8.72293 17.7073 10.4799 17.7102 11.5712 16.6314L13.0263 15.193C14.0703 14.1609 14.2141 12.525 13.3662 11.3266L13.22 11.12"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M16.22 11.12L16.3564 10.9805C17.2895 10.0265 17.3478 8.5207 16.4914 7.49733V7.49733C15.5691 6.39509 13.9269 6.25143 12.8271 7.17675L11.3901 8.38588C10.0935 9.47674 9.95706 11.4241 11.0888 12.6852L11.12 12.72"/></svg>',ha='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2.6" d="M9.40999 7.29999H9.4"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2.6" d="M14.6 7.29999H14.59"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2.6" d="M9.30999 12H9.3"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2.6" d="M14.6 12H14.59"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2.6" d="M9.40999 16.7H9.4"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2.6" d="M14.6 16.7H14.59"/></svg>',pa='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M12 7V12M12 17V12M17 12H12M12 12H7"/></svg>',Rr='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M11.5 17.5L5 11M5 11V15.5M5 11H9.5"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M12.5 6.5L19 13M19 13V8.5M19 13H14.5"/></svg>',fa='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><circle cx="10.5" cy="10.5" r="5.5" stroke="currentColor" stroke-width="2"/><line x1="15.4142" x2="19" y1="15" y2="18.5858" stroke="currentColor" stroke-linecap="round" stroke-width="2"/></svg>',ga='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M15.7795 11.5C15.7795 11.5 16.053 11.1962 16.5497 10.6722C17.4442 9.72856 17.4701 8.2475 16.5781 7.30145V7.30145C15.6482 6.31522 14.0873 6.29227 13.1288 7.25073L11.8796 8.49999"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M8.24517 12.3883C8.24517 12.3883 7.97171 12.6922 7.47504 13.2161C6.58051 14.1598 6.55467 15.6408 7.44666 16.5869V16.5869C8.37653 17.5731 9.93744 17.5961 10.8959 16.6376L12.1452 15.3883"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M17.7802 15.1032L16.597 14.9422C16.0109 14.8624 15.4841 15.3059 15.4627 15.8969L15.4199 17.0818"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M6.39064 9.03238L7.58432 9.06668C8.17551 9.08366 8.6522 8.58665 8.61056 7.99669L8.5271 6.81397"/><line x1="12.1142" x2="11.7" y1="12.2" y2="11.7858" stroke="currentColor" stroke-linecap="round" stroke-width="2"/></svg>',ma='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><rect width="14" height="14" x="5" y="5" stroke="currentColor" stroke-width="2" rx="4"/><line x1="12" x2="12" y1="9" y2="12" stroke="currentColor" stroke-linecap="round" stroke-width="2"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M12 15.02V15.01"/></svg>',va="__",ba="--";function ye(o){return(e,t)=>[[o,e].filter(n=>!!n).join(va),t].filter(n=>!!n).join(ba)}const He=ye("ce-hint"),$e={root:He(),alignedStart:He(null,"align-left"),alignedCenter:He(null,"align-center"),title:He("title"),description:He("description")};class ya{constructor(e){this.nodes={root:S.make("div",[$e.root,e.alignment==="center"?$e.alignedCenter:$e.alignedStart]),title:S.make("div",$e.title,{textContent:e.title})},this.nodes.root.appendChild(this.nodes.title),e.description!==void 0&&(this.nodes.description=S.make("div",$e.description,{textContent:e.description}),this.nodes.root.appendChild(this.nodes.description))}getElement(){return this.nodes.root}}let fo=class{constructor(e){this.params=e}get name(){if(this.params!==void 0&&"name"in this.params)return this.params.name}destroy(){ft()}onChildrenOpen(){var e;this.params!==void 0&&"children"in this.params&&typeof((e=this.params.children)==null?void 0:e.onOpen)=="function"&&this.params.children.onOpen()}onChildrenClose(){var e;this.params!==void 0&&"children"in this.params&&typeof((e=this.params.children)==null?void 0:e.onClose)=="function"&&this.params.children.onClose()}handleClick(){var e,t;this.params!==void 0&&"onActivate"in this.params&&((t=(e=this.params).onActivate)==null||t.call(e,this.params))}addHint(e,t){const n=new ya(t);gt(e,n.getElement(),{placement:t.position,hidingDelay:100})}get children(){var e;return this.params!==void 0&&"children"in this.params&&((e=this.params.children)==null?void 0:e.items)!==void 0?this.params.children.items:[]}get hasChildren(){return this.children.length>0}get isChildrenOpen(){var e;return this.params!==void 0&&"children"in this.params&&((e=this.params.children)==null?void 0:e.isOpen)===!0}get isChildrenFlippable(){var e;return!(this.params===void 0||!("children"in this.params)||((e=this.params.children)==null?void 0:e.isFlippable)===!1)}get isChildrenSearchable(){var e;return this.params!==void 0&&"children"in this.params&&((e=this.params.children)==null?void 0:e.searchable)===!0}get closeOnActivate(){return this.params!==void 0&&"closeOnActivate"in this.params&&this.params.closeOnActivate}get isActive(){return this.params===void 0||!("isActive"in this.params)?!1:typeof this.params.isActive=="function"?this.params.isActive():this.params.isActive===!0}};const J=ye("ce-popover-item"),z={container:J(),active:J(null,"active"),disabled:J(null,"disabled"),focused:J(null,"focused"),hidden:J(null,"hidden"),confirmationState:J(null,"confirmation"),noHover:J(null,"no-hover"),noFocus:J(null,"no-focus"),title:J("title"),secondaryTitle:J("secondary-title"),icon:J("icon"),iconTool:J("icon","tool"),iconChevronRight:J("icon","chevron-right"),wobbleAnimation:ye("wobble")()};let we=class extends fo{constructor(e,t){super(e),this.params=e,this.nodes={root:null,icon:null},this.confirmationState=null,this.removeSpecialFocusBehavior=()=>{var n;(n=this.nodes.root)==null||n.classList.remove(z.noFocus)},this.removeSpecialHoverBehavior=()=>{var n;(n=this.nodes.root)==null||n.classList.remove(z.noHover)},this.onErrorAnimationEnd=()=>{var n,r;(n=this.nodes.icon)==null||n.classList.remove(z.wobbleAnimation),(r=this.nodes.icon)==null||r.removeEventListener("animationend",this.onErrorAnimationEnd)},this.nodes.root=this.make(e,t)}get isDisabled(){return this.params.isDisabled===!0}get toggle(){return this.params.toggle}get title(){return this.params.title}get isConfirmationStateEnabled(){return this.confirmationState!==null}get isFocused(){return this.nodes.root===null?!1:this.nodes.root.classList.contains(z.focused)}getElement(){return this.nodes.root}handleClick(){if(this.isConfirmationStateEnabled&&this.confirmationState!==null){this.activateOrEnableConfirmationMode(this.confirmationState);return}this.activateOrEnableConfirmationMode(this.params)}toggleActive(e){var t;(t=this.nodes.root)==null||t.classList.toggle(z.active,e)}toggleHidden(e){var t;(t=this.nodes.root)==null||t.classList.toggle(z.hidden,e)}reset(){this.isConfirmationStateEnabled&&this.disableConfirmationMode()}onFocus(){this.disableSpecialHoverAndFocusBehavior()}make(e,t){var n,r;const i=(t==null?void 0:t.wrapperTag)||"div",s=S.make(i,z.container,{type:i==="button"?"button":void 0});return e.name&&(s.dataset.itemName=e.name),this.nodes.icon=S.make("div",[z.icon,z.iconTool],{innerHTML:e.icon||da}),s.appendChild(this.nodes.icon),e.title!==void 0&&s.appendChild(S.make("div",z.title,{innerHTML:e.title||""})),e.secondaryLabel&&s.appendChild(S.make("div",z.secondaryTitle,{textContent:e.secondaryLabel})),this.hasChildren&&s.appendChild(S.make("div",[z.icon,z.iconChevronRight],{innerHTML:aa})),this.isActive&&s.classList.add(z.active),e.isDisabled&&s.classList.add(z.disabled),e.hint!==void 0&&((n=t==null?void 0:t.hint)==null?void 0:n.enabled)!==!1&&this.addHint(s,{...e.hint,position:((r=t==null?void 0:t.hint)==null?void 0:r.position)||"right"}),s}enableConfirmationMode(e){if(this.nodes.root===null)return;const t={...this.params,...e,confirmation:"confirmation"in e?e.confirmation:void 0},n=this.make(t);this.nodes.root.innerHTML=n.innerHTML,this.nodes.root.classList.add(z.confirmationState),this.confirmationState=e,this.enableSpecialHoverAndFocusBehavior()}disableConfirmationMode(){if(this.nodes.root===null)return;const e=this.make(this.params);this.nodes.root.innerHTML=e.innerHTML,this.nodes.root.classList.remove(z.confirmationState),this.confirmationState=null,this.disableSpecialHoverAndFocusBehavior()}enableSpecialHoverAndFocusBehavior(){var e,t,n;(e=this.nodes.root)==null||e.classList.add(z.noHover),(t=this.nodes.root)==null||t.classList.add(z.noFocus),(n=this.nodes.root)==null||n.addEventListener("mouseleave",this.removeSpecialHoverBehavior,{once:!0})}disableSpecialHoverAndFocusBehavior(){var e;this.removeSpecialFocusBehavior(),this.removeSpecialHoverBehavior(),(e=this.nodes.root)==null||e.removeEventListener("mouseleave",this.removeSpecialHoverBehavior)}activateOrEnableConfirmationMode(e){var t;if(!("confirmation"in e)||e.confirmation===void 0)try{(t=e.onActivate)==null||t.call(e,e),this.disableConfirmationMode()}catch{this.animateError()}else this.enableConfirmationMode(e.confirmation)}animateError(){var e,t,n;(e=this.nodes.icon)!=null&&e.classList.contains(z.wobbleAnimation)||((t=this.nodes.icon)==null||t.classList.add(z.wobbleAnimation),(n=this.nodes.icon)==null||n.addEventListener("animationend",this.onErrorAnimationEnd))}};const Ht=ye("ce-popover-item-separator"),$t={container:Ht(),line:Ht("line"),hidden:Ht(null,"hidden")};class Dr extends fo{constructor(){super(),this.nodes={root:S.make("div",$t.container),line:S.make("div",$t.line)},this.nodes.root.appendChild(this.nodes.line)}getElement(){return this.nodes.root}toggleHidden(e){var t;(t=this.nodes.root)==null||t.classList.toggle($t.hidden,e)}}var le=(o=>(o.Closed="closed",o.ClosedOnActivate="closed-on-activate",o))(le||{});const V=ye("ce-popover"),W={popover:V(),popoverContainer:V("container"),popoverOpenTop:V(null,"open-top"),popoverOpenLeft:V(null,"open-left"),popoverOpened:V(null,"opened"),search:V("search"),nothingFoundMessage:V("nothing-found-message"),nothingFoundMessageDisplayed:V("nothing-found-message","displayed"),items:V("items"),overlay:V("overlay"),overlayHidden:V("overlay","hidden"),popoverNested:V(null,"nested"),getPopoverNestedClass:o=>V(null,`nested-level-${o.toString()}`),popoverInline:V(null,"inline"),popoverHeader:V("header")};var Ne=(o=>(o.NestingLevel="--nesting-level",o.PopoverHeight="--popover-height",o.InlinePopoverWidth="--inline-popover-width",o.TriggerItemLeft="--trigger-item-left",o.TriggerItemTop="--trigger-item-top",o))(Ne||{});const nr=ye("ce-popover-item-html"),rr={root:nr(),hidden:nr(null,"hidden")};let Ve=class extends fo{constructor(e,t){var n,r;super(e),this.nodes={root:S.make("div",rr.root)},this.nodes.root.appendChild(e.element),e.name&&(this.nodes.root.dataset.itemName=e.name),e.hint!==void 0&&((n=t==null?void 0:t.hint)==null?void 0:n.enabled)!==!1&&this.addHint(this.nodes.root,{...e.hint,position:((r=t==null?void 0:t.hint)==null?void 0:r.position)||"right"})}getElement(){return this.nodes.root}toggleHidden(e){var t;(t=this.nodes.root)==null||t.classList.toggle(rr.hidden,e)}getControls(){const e=this.nodes.root.querySelectorAll(`button, ${S.allInputsSelector}`);return Array.from(e)}};class Fr extends Ge{constructor(e,t={}){super(),this.params=e,this.itemsRenderParams=t,this.listeners=new Ze,this.messages={nothingFound:"Nothing found",search:"Search"},this.items=this.buildItems(e.items),e.messages&&(this.messages={...this.messages,...e.messages}),this.nodes={},this.nodes.popoverContainer=S.make("div",[W.popoverContainer]),this.nodes.nothingFoundMessage=S.make("div",[W.nothingFoundMessage],{textContent:this.messages.nothingFound}),this.nodes.popoverContainer.appendChild(this.nodes.nothingFoundMessage),this.nodes.items=S.make("div",[W.items]),this.items.forEach(n=>{const r=n.getElement();r!==null&&this.nodes.items.appendChild(r)}),this.nodes.popoverContainer.appendChild(this.nodes.items),this.listeners.on(this.nodes.popoverContainer,"click",n=>this.handleClick(n)),this.nodes.popover=S.make("div",[W.popover,this.params.class]),this.nodes.popover.appendChild(this.nodes.popoverContainer)}get itemsDefault(){return this.items.filter(e=>e instanceof we)}getElement(){return this.nodes.popover}show(){this.nodes.popover.classList.add(W.popoverOpened),this.search!==void 0&&this.search.focus()}hide(){this.nodes.popover.classList.remove(W.popoverOpened),this.nodes.popover.classList.remove(W.popoverOpenTop),this.itemsDefault.forEach(e=>e.reset()),this.search!==void 0&&this.search.clear(),this.emit(le.Closed)}destroy(){var e;this.items.forEach(t=>t.destroy()),this.nodes.popover.remove(),this.listeners.removeAll(),(e=this.search)==null||e.destroy()}activateItemByName(e){const t=this.items.find(n=>n.name===e);this.handleItemClick(t)}buildItems(e){return e.map(t=>{switch(t.type){case $.Separator:return new Dr;case $.Html:return new Ve(t,this.itemsRenderParams[$.Html]);default:return new we(t,this.itemsRenderParams[$.Default])}})}getTargetItem(e){return this.items.filter(t=>t instanceof we||t instanceof Ve).find(t=>{const n=t.getElement();return n===null?!1:e.composedPath().includes(n)})}handleItemClick(e){if(!("isDisabled"in e&&e.isDisabled)){if(e.hasChildren){this.showNestedItems(e),"handleClick"in e&&typeof e.handleClick=="function"&&e.handleClick();return}this.itemsDefault.filter(t=>t!==e).forEach(t=>t.reset()),"handleClick"in e&&typeof e.handleClick=="function"&&e.handleClick(),this.toggleItemActivenessIfNeeded(e),e.closeOnActivate&&(this.hide(),this.emit(le.ClosedOnActivate))}}handleClick(e){const t=this.getTargetItem(e);t!==void 0&&this.handleItemClick(t)}toggleItemActivenessIfNeeded(e){if(e instanceof we&&(e.toggle===!0&&e.toggleActive(),typeof e.toggle=="string")){const t=this.itemsDefault.filter(n=>n.toggle===e.toggle);if(t.length===1){e.toggleActive();return}t.forEach(n=>{n.toggleActive(n===e)})}}}var vt=(o=>(o.Search="search",o))(vt||{});const Ut=ye("cdx-search-field"),zt={wrapper:Ut(),icon:Ut("icon"),input:Ut("input")};class ka extends Ge{constructor({items:e,placeholder:t}){super(),this.listeners=new Ze,this.items=e,this.wrapper=S.make("div",zt.wrapper);const n=S.make("div",zt.icon,{innerHTML:fa});this.input=S.make("input",zt.input,{placeholder:t,tabIndex:-1}),this.wrapper.appendChild(n),this.wrapper.appendChild(this.input),this.listeners.on(this.input,"input",()=>{this.searchQuery=this.input.value,this.emit(vt.Search,{query:this.searchQuery,items:this.foundItems})})}getElement(){return this.wrapper}focus(){this.input.focus()}clear(){this.input.value="",this.searchQuery="",this.emit(vt.Search,{query:"",items:this.foundItems})}destroy(){this.listeners.removeAll()}get foundItems(){return this.items.filter(e=>this.checkItem(e))}checkItem(e){var t,n;const r=((t=e.title)==null?void 0:t.toLowerCase())||"",i=(n=this.searchQuery)==null?void 0:n.toLowerCase();return i!==void 0?r.includes(i):!1}}var wa=Object.defineProperty,xa=Object.getOwnPropertyDescriptor,Ea=(o,e,t,n)=>{for(var r=xa(e,t),i=o.length-1,s;i>=0;i--)(s=o[i])&&(r=s(e,t,r)||r);return r&&wa(e,t,r),r};const Hr=class $r extends Fr{constructor(e,t){super(e,t),this.nestingLevel=0,this.nestedPopoverTriggerItem=null,this.previouslyHoveredItem=null,this.scopeElement=document.body,this.hide=()=>{var n;super.hide(),this.destroyNestedPopoverIfExists(),(n=this.flipper)==null||n.deactivate(),this.previouslyHoveredItem=null},this.onFlip=()=>{const n=this.itemsDefault.find(r=>r.isFocused);n==null||n.onFocus()},this.onSearch=n=>{var r;const i=n.query==="",s=n.items.length===0;this.items.forEach(c=>{let l=!1;c instanceof we?l=!n.items.includes(c):(c instanceof Dr||c instanceof Ve)&&(l=s||!i),c.toggleHidden(l)}),this.toggleNothingFoundMessage(s);const a=n.query===""?this.flippableElements:n.items.map(c=>c.getElement());(r=this.flipper)!=null&&r.isActivated&&(this.flipper.deactivate(),this.flipper.activate(a))},e.nestingLevel!==void 0&&(this.nestingLevel=e.nestingLevel),this.nestingLevel>0&&this.nodes.popover.classList.add(W.popoverNested),e.scopeElement!==void 0&&(this.scopeElement=e.scopeElement),this.nodes.popoverContainer!==null&&this.listeners.on(this.nodes.popoverContainer,"mouseover",n=>this.handleHover(n)),e.searchable&&this.addSearch(),e.flippable!==!1&&(this.flipper=new mt({items:this.flippableElements,focusedItemClass:z.focused,allowedKeys:[N.TAB,N.UP,N.DOWN,N.ENTER]}),this.flipper.onFlip(this.onFlip))}hasFocus(){return this.flipper===void 0?!1:this.flipper.hasFocus()}get scrollTop(){return this.nodes.items===null?0:this.nodes.items.scrollTop}get offsetTop(){return this.nodes.popoverContainer===null?0:this.nodes.popoverContainer.offsetTop}show(){var e;this.nodes.popover.style.setProperty(Ne.PopoverHeight,this.size.height+"px"),this.shouldOpenBottom||this.nodes.popover.classList.add(W.popoverOpenTop),this.shouldOpenRight||this.nodes.popover.classList.add(W.popoverOpenLeft),super.show(),(e=this.flipper)==null||e.activate(this.flippableElements)}destroy(){this.hide(),super.destroy()}showNestedItems(e){this.nestedPopover!==null&&this.nestedPopover!==void 0||(this.nestedPopoverTriggerItem=e,this.showNestedPopoverForItem(e))}handleHover(e){const t=this.getTargetItem(e);t!==void 0&&this.previouslyHoveredItem!==t&&(this.destroyNestedPopoverIfExists(),this.previouslyHoveredItem=t,t.hasChildren&&this.showNestedPopoverForItem(t))}setTriggerItemPosition(e,t){const n=t.getElement(),r=(n?n.offsetTop:0)-this.scrollTop,i=this.offsetTop+r;e.style.setProperty(Ne.TriggerItemTop,i+"px")}destroyNestedPopoverIfExists(){var e,t;this.nestedPopover===void 0||this.nestedPopover===null||(this.nestedPopover.off(le.ClosedOnActivate,this.hide),this.nestedPopover.hide(),this.nestedPopover.destroy(),this.nestedPopover.getElement().remove(),this.nestedPopover=null,(e=this.flipper)==null||e.activate(this.flippableElements),(t=this.nestedPopoverTriggerItem)==null||t.onChildrenClose())}showNestedPopoverForItem(e){var t;this.nestedPopover=new $r({searchable:e.isChildrenSearchable,items:e.children,nestingLevel:this.nestingLevel+1,flippable:e.isChildrenFlippable,messages:this.messages}),e.onChildrenOpen(),this.nestedPopover.on(le.ClosedOnActivate,this.hide);const n=this.nestedPopover.getElement();return this.nodes.popover.appendChild(n),this.setTriggerItemPosition(n,e),n.style.setProperty(Ne.NestingLevel,this.nestedPopover.nestingLevel.toString()),this.nestedPopover.show(),(t=this.flipper)==null||t.deactivate(),this.nestedPopover}get shouldOpenBottom(){if(this.nodes.popover===void 0||this.nodes.popover===null)return!1;const e=this.nodes.popoverContainer.getBoundingClientRect(),t=this.scopeElement.getBoundingClientRect(),n=this.size.height,r=e.top+n,i=e.top-n,s=Math.min(window.innerHeight,t.bottom);return i<t.top||r<=s}get shouldOpenRight(){if(this.nodes.popover===void 0||this.nodes.popover===null)return!1;const e=this.nodes.popover.getBoundingClientRect(),t=this.scopeElement.getBoundingClientRect(),n=this.size.width,r=e.right+n,i=e.left-n,s=Math.min(window.innerWidth,t.right);return i<t.left||r<=s}get size(){var e;const t={height:0,width:0};if(this.nodes.popover===null)return t;const n=this.nodes.popover.cloneNode(!0);n.style.visibility="hidden",n.style.position="absolute",n.style.top="-1000px",n.classList.add(W.popoverOpened),(e=n.querySelector("."+W.popoverNested))==null||e.remove(),document.body.appendChild(n);const r=n.querySelector("."+W.popoverContainer);return t.height=r.offsetHeight,t.width=r.offsetWidth,n.remove(),t}get flippableElements(){return this.items.map(e=>{if(e instanceof we)return e.getElement();if(e instanceof Ve)return e.getControls()}).flat().filter(e=>e!=null)}addSearch(){this.search=new ka({items:this.itemsDefault,placeholder:this.messages.search}),this.search.on(vt.Search,this.onSearch);const e=this.search.getElement();e.classList.add(W.search),this.nodes.popoverContainer.insertBefore(e,this.nodes.popoverContainer.firstChild)}toggleNothingFoundMessage(e){this.nodes.nothingFoundMessage.classList.toggle(W.nothingFoundMessageDisplayed,e)}};Ea([Re],Hr.prototype,"size");let go=Hr;class Ca extends go{constructor(e){const t=!De();super({...e,class:W.popoverInline},{[$.Default]:{wrapperTag:"button",hint:{position:"top",alignment:"center",enabled:t}},[$.Html]:{hint:{position:"top",alignment:"center",enabled:t}}}),this.items.forEach(n=>{!(n instanceof we)&&!(n instanceof Ve)||n.hasChildren&&n.isChildrenOpen&&this.showNestedItems(n)})}get offsetLeft(){return this.nodes.popoverContainer===null?0:this.nodes.popoverContainer.offsetLeft}show(){this.nestingLevel===0&&this.nodes.popover.style.setProperty(Ne.InlinePopoverWidth,this.size.width+"px"),super.show()}handleHover(){}setTriggerItemPosition(e,t){const n=t.getElement(),r=n?n.offsetLeft:0,i=this.offsetLeft+r;e.style.setProperty(Ne.TriggerItemLeft,i+"px")}showNestedItems(e){if(this.nestedPopoverTriggerItem===e){this.destroyNestedPopoverIfExists(),this.nestedPopoverTriggerItem=null;return}super.showNestedItems(e)}showNestedPopoverForItem(e){const t=super.showNestedPopoverForItem(e);return t.getElement().classList.add(W.getPopoverNestedClass(t.nestingLevel)),t}handleItemClick(e){var t;e!==this.nestedPopoverTriggerItem&&((t=this.nestedPopoverTriggerItem)==null||t.handleClick(),super.destroyNestedPopoverIfExists()),super.handleItemClick(e)}}const Ur=class We{constructor(){this.scrollPosition=null}lock(){to?this.lockHard():document.body.classList.add(We.CSS.scrollLocked)}unlock(){to?this.unlockHard():document.body.classList.remove(We.CSS.scrollLocked)}lockHard(){this.scrollPosition=window.pageYOffset,document.documentElement.style.setProperty("--window-scroll-offset",`${this.scrollPosition}px`),document.body.classList.add(We.CSS.scrollLockedHard)}unlockHard(){document.body.classList.remove(We.CSS.scrollLockedHard),this.scrollPosition!==null&&window.scrollTo(0,this.scrollPosition),this.scrollPosition=null}};Ur.CSS={scrollLocked:"ce-scroll-locked",scrollLockedHard:"ce-scroll-locked--hard"};let Sa=Ur;const Wt=ye("ce-popover-header"),qt={root:Wt(),text:Wt("text"),backButton:Wt("back-button")};class Ta{constructor({text:e,onBackButtonClick:t}){this.listeners=new Ze,this.text=e,this.onBackButtonClick=t,this.nodes={root:S.make("div",[qt.root]),backButton:S.make("button",[qt.backButton]),text:S.make("div",[qt.text])},this.nodes.backButton.innerHTML=sa,this.nodes.root.appendChild(this.nodes.backButton),this.listeners.on(this.nodes.backButton,"click",this.onBackButtonClick),this.nodes.text.innerText=this.text,this.nodes.root.appendChild(this.nodes.text)}getElement(){return this.nodes.root}destroy(){this.nodes.root.remove(),this.listeners.destroy()}}class Ba{constructor(){this.history=[]}push(e){this.history.push(e)}pop(){return this.history.pop()}get currentTitle(){return this.history.length===0?"":this.history[this.history.length-1].title}get currentItems(){return this.history.length===0?[]:this.history[this.history.length-1].items}reset(){for(;this.history.length>1;)this.pop()}}let zr=class extends Fr{constructor(e){super(e,{[$.Default]:{hint:{enabled:!1}},[$.Html]:{hint:{enabled:!1}}}),this.scrollLocker=new Sa,this.history=new Ba,this.isHidden=!0,this.nodes.overlay=S.make("div",[W.overlay,W.overlayHidden]),this.nodes.popover.insertBefore(this.nodes.overlay,this.nodes.popover.firstChild),this.listeners.on(this.nodes.overlay,"click",()=>{this.hide()}),this.history.push({items:e.items})}show(){this.nodes.overlay.classList.remove(W.overlayHidden),super.show(),this.scrollLocker.lock(),this.isHidden=!1}hide(){this.isHidden||(super.hide(),this.nodes.overlay.classList.add(W.overlayHidden),this.scrollLocker.unlock(),this.history.reset(),this.isHidden=!0)}destroy(){super.destroy(),this.scrollLocker.unlock()}showNestedItems(e){this.updateItemsAndHeader(e.children,e.title),this.history.push({title:e.title,items:e.children})}updateItemsAndHeader(e,t){if(this.header!==null&&this.header!==void 0&&(this.header.destroy(),this.header=null),t!==void 0){this.header=new Ta({text:t,onBackButtonClick:()=>{this.history.pop(),this.updateItemsAndHeader(this.history.currentItems,this.history.currentTitle)}});const n=this.header.getElement();n!==null&&this.nodes.popoverContainer.insertBefore(n,this.nodes.popoverContainer.firstChild)}this.items.forEach(n=>{var r;return(r=n.getElement())==null?void 0:r.remove()}),this.items=this.buildItems(e),this.items.forEach(n=>{var r;const i=n.getElement();i!==null&&((r=this.nodes.items)==null||r.appendChild(i))})}};class Oa extends j{constructor(){super(...arguments),this.opened=!1,this.selection=new P,this.popover=null,this.close=()=>{this.opened&&(this.opened=!1,P.isAtEditor||this.selection.restore(),this.selection.clearSaved(),!this.Editor.CrossBlockSelection.isCrossBlockSelectionStarted&&this.Editor.BlockManager.currentBlock&&this.Editor.BlockSelection.unselectBlock(this.Editor.BlockManager.currentBlock),this.eventsDispatcher.emit(this.events.closed),this.popover&&(this.popover.off(le.Closed,this.onPopoverClose),this.popover.destroy(),this.popover.getElement().remove(),this.popover=null))},this.onPopoverClose=()=>{this.close()}}get events(){return{opened:"block-settings-opened",closed:"block-settings-closed"}}get CSS(){return{settings:"ce-settings"}}get flipper(){var e;if(this.popover!==null)return"flipper"in this.popover?(e=this.popover)==null?void 0:e.flipper:void 0}make(){this.nodes.wrapper=S.make("div",[this.CSS.settings]),this.nodes.wrapper.setAttribute("data-cy","block-tunes"),this.eventsDispatcher.on(Ke,this.close)}destroy(){this.removeAllNodes(),this.listeners.destroy(),this.eventsDispatcher.off(Ke,this.close)}async open(e=this.Editor.BlockManager.currentBlock){var t;this.opened=!0,this.selection.save(),this.Editor.BlockSelection.selectBlock(e),this.Editor.BlockSelection.clearCache();const{toolTunes:n,commonTunes:r}=e.getTunes();this.eventsDispatcher.emit(this.events.opened);const i=De()?zr:go;this.popover=new i({searchable:!0,items:await this.getTunesItems(e,r,n),scopeElement:this.Editor.API.methods.ui.nodes.redactor,messages:{nothingFound:G.ui(Q.ui.popover,"Nothing found"),search:G.ui(Q.ui.popover,"Filter")}}),this.popover.on(le.Closed,this.onPopoverClose),(t=this.nodes.wrapper)==null||t.append(this.popover.getElement()),this.popover.show()}getElement(){return this.nodes.wrapper}async getTunesItems(e,t,n){const r=[];n!==void 0&&n.length>0&&(r.push(...n),r.push({type:$.Separator}));const i=Array.from(this.Editor.Tools.blockTools.values()),s=(await Ir(e,i)).reduce((a,c)=>(c.toolbox.forEach(l=>{a.push({icon:l.icon,title:G.t(Q.toolNames,l.title),name:c.name,closeOnActivate:!0,onActivate:async()=>{const{BlockManager:d,Caret:u,Toolbar:p}=this.Editor,g=await d.convert(e,c.name,l.data);p.close(),u.setToBlock(g,u.positions.END)}})}),a),[]);return s.length>0&&(r.push({icon:Rr,name:"convert-to",title:G.ui(Q.ui.popover,"Convert to"),children:{searchable:!0,items:s}}),r.push({type:$.Separator})),r.push(...t),r.map(a=>this.resolveTuneAliases(a))}resolveTuneAliases(e){if(e.type===$.Separator||e.type===$.Html)return e;const t=na(e,{label:"title"});return e.confirmation&&(t.confirmation=this.resolveTuneAliases(e.confirmation)),t}}var Wr={exports:{}};/*!
 * Library for handling keyboard shortcuts
 * @copyright CodeX (https://codex.so)
 * @license MIT
 * <AUTHOR> (https://codex.so)
 * @version 1.2.0
 */(function(o,e){(function(t,n){o.exports=n()})(window,function(){return function(t){var n={};function r(i){if(n[i])return n[i].exports;var s=n[i]={i,l:!1,exports:{}};return t[i].call(s.exports,s,s.exports,r),s.l=!0,s.exports}return r.m=t,r.c=n,r.d=function(i,s,a){r.o(i,s)||Object.defineProperty(i,s,{enumerable:!0,get:a})},r.r=function(i){typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(i,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(i,"__esModule",{value:!0})},r.t=function(i,s){if(1&s&&(i=r(i)),8&s||4&s&&typeof i=="object"&&i&&i.__esModule)return i;var a=Object.create(null);if(r.r(a),Object.defineProperty(a,"default",{enumerable:!0,value:i}),2&s&&typeof i!="string")for(var c in i)r.d(a,c,(function(l){return i[l]}).bind(null,c));return a},r.n=function(i){var s=i&&i.__esModule?function(){return i.default}:function(){return i};return r.d(s,"a",s),s},r.o=function(i,s){return Object.prototype.hasOwnProperty.call(i,s)},r.p="",r(r.s=0)}([function(t,n,r){function i(c,l){for(var d=0;d<l.length;d++){var u=l[d];u.enumerable=u.enumerable||!1,u.configurable=!0,"value"in u&&(u.writable=!0),Object.defineProperty(c,u.key,u)}}function s(c,l,d){return l&&i(c.prototype,l),d&&i(c,d),c}r.r(n);var a=function(){function c(l){var d=this;(function(u,p){if(!(u instanceof p))throw new TypeError("Cannot call a class as a function")})(this,c),this.commands={},this.keys={},this.name=l.name,this.parseShortcutName(l.name),this.element=l.on,this.callback=l.callback,this.executeShortcut=function(u){d.execute(u)},this.element.addEventListener("keydown",this.executeShortcut,!1)}return s(c,null,[{key:"supportedCommands",get:function(){return{SHIFT:["SHIFT"],CMD:["CMD","CONTROL","COMMAND","WINDOWS","CTRL"],ALT:["ALT","OPTION"]}}},{key:"keyCodes",get:function(){return{0:48,1:49,2:50,3:51,4:52,5:53,6:54,7:55,8:56,9:57,A:65,B:66,C:67,D:68,E:69,F:70,G:71,H:72,I:73,J:74,K:75,L:76,M:77,N:78,O:79,P:80,Q:81,R:82,S:83,T:84,U:85,V:86,W:87,X:88,Y:89,Z:90,BACKSPACE:8,ENTER:13,ESCAPE:27,LEFT:37,UP:38,RIGHT:39,DOWN:40,INSERT:45,DELETE:46,".":190}}}]),s(c,[{key:"parseShortcutName",value:function(l){l=l.split("+");for(var d=0;d<l.length;d++){l[d]=l[d].toUpperCase();var u=!1;for(var p in c.supportedCommands)if(c.supportedCommands[p].includes(l[d])){u=this.commands[p]=!0;break}u||(this.keys[l[d]]=!0)}for(var g in c.supportedCommands)this.commands[g]||(this.commands[g]=!1)}},{key:"execute",value:function(l){var d,u={CMD:l.ctrlKey||l.metaKey,SHIFT:l.shiftKey,ALT:l.altKey},p=!0;for(d in this.commands)this.commands[d]!==u[d]&&(p=!1);var g,b=!0;for(g in this.keys)b=b&&l.keyCode===c.keyCodes[g];p&&b&&this.callback(l)}},{key:"remove",value:function(){this.element.removeEventListener("keydown",this.executeShortcut)}}]),c}();n.default=a}]).default})})(Wr);var _a=Wr.exports;const Ia=xt(_a);class Ma{constructor(){this.registeredShortcuts=new Map}add(e){if(this.findShortcut(e.on,e.name))throw Error(`Shortcut ${e.name} is already registered for ${e.on}. Please remove it before add a new handler.`);const t=new Ia({name:e.name,on:e.on,callback:e.handler}),n=this.registeredShortcuts.get(e.on)||[];this.registeredShortcuts.set(e.on,[...n,t])}remove(e,t){const n=this.findShortcut(e,t);if(!n)return;n.remove();const r=this.registeredShortcuts.get(e);this.registeredShortcuts.set(e,r.filter(i=>i!==n))}findShortcut(e,t){return(this.registeredShortcuts.get(e)||[]).find(({name:n})=>n===t)}}const je=new Ma;var La=Object.defineProperty,Pa=Object.getOwnPropertyDescriptor,qr=(o,e,t,n)=>{for(var r=Pa(e,t),i=o.length-1,s;i>=0;i--)(s=o[i])&&(r=s(e,t,r)||r);return r&&La(e,t,r),r},ot=(o=>(o.Opened="toolbox-opened",o.Closed="toolbox-closed",o.BlockAdded="toolbox-block-added",o))(ot||{});const mo=class Yr extends Ge{constructor({api:e,tools:t,i18nLabels:n}){super(),this.opened=!1,this.listeners=new Ze,this.popover=null,this.handleMobileLayoutToggle=()=>{this.destroyPopover(),this.initPopover()},this.onPopoverClose=()=>{this.opened=!1,this.emit("toolbox-closed")},this.api=e,this.tools=t,this.i18nLabels=n,this.enableShortcuts(),this.nodes={toolbox:S.make("div",Yr.CSS.toolbox)},this.initPopover(),this.nodes.toolbox.setAttribute("data-cy","toolbox"),this.api.events.on(Ke,this.handleMobileLayoutToggle)}get isEmpty(){return this.toolsToBeDisplayed.length===0}static get CSS(){return{toolbox:"ce-toolbox"}}getElement(){return this.nodes.toolbox}hasFocus(){if(this.popover!==null)return"hasFocus"in this.popover?this.popover.hasFocus():void 0}destroy(){var e;super.destroy(),this.nodes&&this.nodes.toolbox&&this.nodes.toolbox.remove(),this.removeAllShortcuts(),(e=this.popover)==null||e.off(le.Closed,this.onPopoverClose),this.listeners.destroy(),this.api.events.off(Ke,this.handleMobileLayoutToggle)}toolButtonActivated(e,t){this.insertNewBlock(e,t)}open(){var e;this.isEmpty||((e=this.popover)==null||e.show(),this.opened=!0,this.emit("toolbox-opened"))}close(){var e;(e=this.popover)==null||e.hide(),this.opened=!1,this.emit("toolbox-closed")}toggle(){this.opened?this.close():this.open()}initPopover(){var e;const t=De()?zr:go;this.popover=new t({scopeElement:this.api.ui.nodes.redactor,searchable:!0,messages:{nothingFound:this.i18nLabels.nothingFound,search:this.i18nLabels.filter},items:this.toolboxItemsToBeDisplayed}),this.popover.on(le.Closed,this.onPopoverClose),(e=this.nodes.toolbox)==null||e.append(this.popover.getElement())}destroyPopover(){this.popover!==null&&(this.popover.hide(),this.popover.off(le.Closed,this.onPopoverClose),this.popover.destroy(),this.popover=null),this.nodes.toolbox!==null&&(this.nodes.toolbox.innerHTML="")}get toolsToBeDisplayed(){const e=[];return this.tools.forEach(t=>{t.toolbox&&e.push(t)}),e}get toolboxItemsToBeDisplayed(){const e=(t,n,r=!0)=>({icon:t.icon,title:G.t(Q.toolNames,t.title||ht(n.name)),name:n.name,onActivate:()=>{this.toolButtonActivated(n.name,t.data)},secondaryLabel:n.shortcut&&r?co(n.shortcut):""});return this.toolsToBeDisplayed.reduce((t,n)=>(Array.isArray(n.toolbox)?n.toolbox.forEach((r,i)=>{t.push(e(r,n,i===0))}):n.toolbox!==void 0&&t.push(e(n.toolbox,n)),t),[])}enableShortcuts(){this.toolsToBeDisplayed.forEach(e=>{const t=e.shortcut;t&&this.enableShortcutForTool(e.name,t)})}enableShortcutForTool(e,t){je.add({name:t,on:this.api.ui.nodes.redactor,handler:async n=>{n.preventDefault();const r=this.api.blocks.getCurrentBlockIndex(),i=this.api.blocks.getBlockByIndex(r);if(i)try{const s=await this.api.blocks.convert(i.id,e);this.api.caret.setToBlock(s,"end");return}catch{}this.insertNewBlock(e)}})}removeAllShortcuts(){this.toolsToBeDisplayed.forEach(e=>{const t=e.shortcut;t&&je.remove(this.api.ui.nodes.redactor,t)})}async insertNewBlock(e,t){const n=this.api.blocks.getCurrentBlockIndex(),r=this.api.blocks.getBlockByIndex(n);if(!r)return;const i=r.isEmpty?n:n+1;let s;if(t){const c=await this.api.blocks.composeBlockData(e);s=Object.assign(c,t)}const a=this.api.blocks.insert(e,s,void 0,i,void 0,r.isEmpty);a.call(ue.APPEND_CALLBACK),this.api.caret.setToBlock(i),this.emit("toolbox-block-added",{block:a}),this.api.toolbar.close()}};qr([Re],mo.prototype,"toolsToBeDisplayed");qr([Re],mo.prototype,"toolboxItemsToBeDisplayed");let Aa=mo;const Kr="block hovered";async function Na(o,e){const t=navigator.keyboard;if(!t)return e;try{return(await t.getLayoutMap()).get(o)||e}catch(n){return console.error(n),e}}class ja extends j{constructor({config:e,eventsDispatcher:t}){super({config:e,eventsDispatcher:t}),this.toolboxInstance=null}get CSS(){return{toolbar:"ce-toolbar",content:"ce-toolbar__content",actions:"ce-toolbar__actions",actionsOpened:"ce-toolbar__actions--opened",toolbarOpened:"ce-toolbar--opened",openedToolboxHolderModifier:"codex-editor--toolbox-opened",plusButton:"ce-toolbar__plus",plusButtonShortcut:"ce-toolbar__plus-shortcut",settingsToggler:"ce-toolbar__settings-btn",settingsTogglerHidden:"ce-toolbar__settings-btn--hidden"}}get opened(){return this.nodes.wrapper.classList.contains(this.CSS.toolbarOpened)}get toolbox(){var e;return{opened:(e=this.toolboxInstance)==null?void 0:e.opened,close:()=>{var t;(t=this.toolboxInstance)==null||t.close()},open:()=>{if(this.toolboxInstance===null){D("toolbox.open() called before initialization is finished","warn");return}this.Editor.BlockManager.currentBlock=this.hoveredBlock,this.toolboxInstance.open()},toggle:()=>{if(this.toolboxInstance===null){D("toolbox.toggle() called before initialization is finished","warn");return}this.toolboxInstance.toggle()},hasFocus:()=>{var t;return(t=this.toolboxInstance)==null?void 0:t.hasFocus()}}}get blockActions(){return{hide:()=>{this.nodes.actions.classList.remove(this.CSS.actionsOpened)},show:()=>{this.nodes.actions.classList.add(this.CSS.actionsOpened)}}}get blockTunesToggler(){return{hide:()=>this.nodes.settingsToggler.classList.add(this.CSS.settingsTogglerHidden),show:()=>this.nodes.settingsToggler.classList.remove(this.CSS.settingsTogglerHidden)}}toggleReadOnly(e){e?(this.destroy(),this.Editor.BlockSettings.destroy(),this.disableModuleBindings()):window.requestIdleCallback(()=>{this.drawUI(),this.enableModuleBindings()},{timeout:2e3})}moveAndOpen(e=this.Editor.BlockManager.currentBlock){if(this.toolboxInstance===null){D("Can't open Toolbar since Editor initialization is not finished yet","warn");return}if(this.toolboxInstance.opened&&this.toolboxInstance.close(),this.Editor.BlockSettings.opened&&this.Editor.BlockSettings.close(),!e)return;this.hoveredBlock=e;const t=e.holder,{isMobile:n}=this.Editor.UI;let r;const i=20,s=e.firstInput,a=t.getBoundingClientRect(),c=s!==void 0?s.getBoundingClientRect():null,l=c!==null?c.top-a.top:null,d=l!==null?l>i:void 0;if(n)r=t.offsetTop+t.offsetHeight;else if(s===void 0||d){const u=parseInt(window.getComputedStyle(e.pluginsContent).paddingTop);r=t.offsetTop+u}else{const u=ys(s),p=parseInt(window.getComputedStyle(this.nodes.plusButton).height,10);r=t.offsetTop+u-p+8+l}this.nodes.wrapper.style.top=`${Math.floor(r)}px`,this.Editor.BlockManager.blocks.length===1&&e.isEmpty?this.blockTunesToggler.hide():this.blockTunesToggler.show(),this.open()}close(){var e,t;this.Editor.ReadOnly.isEnabled||((e=this.nodes.wrapper)==null||e.classList.remove(this.CSS.toolbarOpened),this.blockActions.hide(),(t=this.toolboxInstance)==null||t.close(),this.Editor.BlockSettings.close(),this.reset())}reset(){this.nodes.wrapper.style.top="unset"}open(e=!0){this.nodes.wrapper.classList.add(this.CSS.toolbarOpened),e?this.blockActions.show():this.blockActions.hide()}async make(){this.nodes.wrapper=S.make("div",this.CSS.toolbar),["content","actions"].forEach(i=>{this.nodes[i]=S.make("div",this.CSS[i])}),S.append(this.nodes.wrapper,this.nodes.content),S.append(this.nodes.content,this.nodes.actions),this.nodes.plusButton=S.make("div",this.CSS.plusButton,{innerHTML:pa}),S.append(this.nodes.actions,this.nodes.plusButton),this.readOnlyMutableListeners.on(this.nodes.plusButton,"click",()=>{ft(!0),this.plusButtonClicked()},!1);const e=S.make("div");e.appendChild(document.createTextNode(G.ui(Q.ui.toolbar.toolbox,"Add"))),e.appendChild(S.make("div",this.CSS.plusButtonShortcut,{textContent:"/"})),gt(this.nodes.plusButton,e,{hidingDelay:400}),this.nodes.settingsToggler=S.make("span",this.CSS.settingsToggler,{innerHTML:ha}),S.append(this.nodes.actions,this.nodes.settingsToggler);const t=S.make("div"),n=S.text(G.ui(Q.ui.blockTunes.toggler,"Click to tune")),r=await Na("Slash","/");t.appendChild(n),t.appendChild(S.make("div",this.CSS.plusButtonShortcut,{textContent:co(`CMD + ${r}`)})),gt(this.nodes.settingsToggler,t,{hidingDelay:400}),S.append(this.nodes.actions,this.makeToolbox()),S.append(this.nodes.actions,this.Editor.BlockSettings.getElement()),S.append(this.Editor.UI.nodes.wrapper,this.nodes.wrapper)}makeToolbox(){return this.toolboxInstance=new Aa({api:this.Editor.API.methods,tools:this.Editor.Tools.blockTools,i18nLabels:{filter:G.ui(Q.ui.popover,"Filter"),nothingFound:G.ui(Q.ui.popover,"Nothing found")}}),this.toolboxInstance.on(ot.Opened,()=>{this.Editor.UI.nodes.wrapper.classList.add(this.CSS.openedToolboxHolderModifier)}),this.toolboxInstance.on(ot.Closed,()=>{this.Editor.UI.nodes.wrapper.classList.remove(this.CSS.openedToolboxHolderModifier)}),this.toolboxInstance.on(ot.BlockAdded,({block:e})=>{const{BlockManager:t,Caret:n}=this.Editor,r=t.getBlockById(e.id);r.inputs.length===0&&(r===t.lastBlock?(t.insertAtEnd(),n.setToBlock(t.lastBlock)):n.setToBlock(t.nextBlock))}),this.toolboxInstance.getElement()}plusButtonClicked(){var e;this.Editor.BlockManager.currentBlock=this.hoveredBlock,(e=this.toolboxInstance)==null||e.toggle()}enableModuleBindings(){this.readOnlyMutableListeners.on(this.nodes.settingsToggler,"mousedown",e=>{var t;e.stopPropagation(),this.settingsTogglerClicked(),(t=this.toolboxInstance)!=null&&t.opened&&this.toolboxInstance.close(),ft(!0)},!0),De()||this.eventsDispatcher.on(Kr,e=>{var t;this.Editor.BlockSettings.opened||(t=this.toolboxInstance)!=null&&t.opened||this.moveAndOpen(e.block)})}disableModuleBindings(){this.readOnlyMutableListeners.clearAll()}settingsTogglerClicked(){this.Editor.BlockManager.currentBlock=this.hoveredBlock,this.Editor.BlockSettings.opened?this.Editor.BlockSettings.close():this.Editor.BlockSettings.open(this.hoveredBlock)}drawUI(){this.Editor.BlockSettings.make(),this.make()}destroy(){this.removeAllNodes(),this.toolboxInstance&&this.toolboxInstance.destroy()}}var xe=(o=>(o[o.Block=0]="Block",o[o.Inline=1]="Inline",o[o.Tune=2]="Tune",o))(xe||{}),nt=(o=>(o.Shortcut="shortcut",o.Toolbox="toolbox",o.EnabledInlineTools="inlineToolbar",o.EnabledBlockTunes="tunes",o.Config="config",o))(nt||{}),Vr=(o=>(o.Shortcut="shortcut",o.SanitizeConfig="sanitize",o))(Vr||{}),Le=(o=>(o.IsEnabledLineBreaks="enableLineBreaks",o.Toolbox="toolbox",o.ConversionConfig="conversionConfig",o.IsReadOnlySupported="isReadOnlySupported",o.PasteConfig="pasteConfig",o))(Le||{}),bt=(o=>(o.IsInline="isInline",o.Title="title",o.IsReadOnlySupported="isReadOnlySupported",o))(bt||{}),io=(o=>(o.IsTune="isTune",o))(io||{});let vo=class{constructor({name:e,constructable:t,config:n,api:r,isDefault:i,isInternal:s=!1,defaultPlaceholder:a}){this.api=r,this.name=e,this.constructable=t,this.config=n,this.isDefault=i,this.isInternal=s,this.defaultPlaceholder=a}get settings(){const e=this.config.config||{};return this.isDefault&&!("placeholder"in e)&&this.defaultPlaceholder&&(e.placeholder=this.defaultPlaceholder),e}reset(){if(U(this.constructable.reset))return this.constructable.reset()}prepare(){if(U(this.constructable.prepare))return this.constructable.prepare({toolName:this.name,config:this.settings})}get shortcut(){const e=this.constructable.shortcut;return this.config.shortcut||e}get sanitizeConfig(){return this.constructable.sanitize||{}}isInline(){return this.type===xe.Inline}isBlock(){return this.type===xe.Block}isTune(){return this.type===xe.Tune}};class Ra extends j{constructor({config:e,eventsDispatcher:t}){super({config:e,eventsDispatcher:t}),this.CSS={inlineToolbar:"ce-inline-toolbar"},this.opened=!1,this.popover=null,this.toolbarVerticalMargin=De()?20:6,this.tools=new Map,window.requestIdleCallback(()=>{this.make()},{timeout:2e3})}async tryToShow(e=!1){e&&this.close(),this.allowedToShow()&&(await this.open(),this.Editor.Toolbar.close())}close(){var e,t;if(this.opened){for(const[n,r]of this.tools){const i=this.getToolShortcut(n.name);i!==void 0&&je.remove(this.Editor.UI.nodes.redactor,i),U(r.clear)&&r.clear()}this.tools=new Map,this.reset(),this.opened=!1,(e=this.popover)==null||e.hide(),(t=this.popover)==null||t.destroy(),this.popover=null}}containsNode(e){return this.nodes.wrapper===void 0?!1:this.nodes.wrapper.contains(e)}destroy(){var e;this.removeAllNodes(),(e=this.popover)==null||e.destroy(),this.popover=null}make(){this.nodes.wrapper=S.make("div",[this.CSS.inlineToolbar,...this.isRtl?[this.Editor.UI.CSS.editorRtlFix]:[]]),this.nodes.wrapper.setAttribute("data-cy","inline-toolbar"),S.append(this.Editor.UI.nodes.wrapper,this.nodes.wrapper)}async open(){var e;if(this.opened)return;this.opened=!0,this.popover!==null&&this.popover.destroy(),this.createToolsInstances();const t=await this.getPopoverItems();this.popover=new Ca({items:t,scopeElement:this.Editor.API.methods.ui.nodes.redactor,messages:{nothingFound:G.ui(Q.ui.popover,"Nothing found"),search:G.ui(Q.ui.popover,"Filter")}}),this.move(this.popover.size.width),(e=this.nodes.wrapper)==null||e.append(this.popover.getElement()),this.popover.show()}move(e){const t=P.rect,n=this.Editor.UI.nodes.wrapper.getBoundingClientRect(),r={x:t.x-n.x,y:t.y+t.height-n.top+this.toolbarVerticalMargin};r.x+e+n.x>this.Editor.UI.contentRect.right&&(r.x=this.Editor.UI.contentRect.right-e-n.x),this.nodes.wrapper.style.left=Math.floor(r.x)+"px",this.nodes.wrapper.style.top=Math.floor(r.y)+"px"}reset(){this.nodes.wrapper.style.left="0",this.nodes.wrapper.style.top="0"}allowedToShow(){const e=["IMG","INPUT"],t=P.get(),n=P.text;if(!t||!t.anchorNode||t.isCollapsed||n.length<1)return!1;const r=S.isElement(t.anchorNode)?t.anchorNode:t.anchorNode.parentElement;if(r===null||t!==null&&e.includes(r.tagName))return!1;const i=this.Editor.BlockManager.getBlock(t.anchorNode);return!i||this.getTools().some(s=>i.tool.inlineTools.has(s.name))===!1?!1:r.closest("[contenteditable]")!==null}getTools(){const e=this.Editor.BlockManager.currentBlock;return e?Array.from(e.tool.inlineTools.values()).filter(t=>!(this.Editor.ReadOnly.isEnabled&&t.isReadOnlySupported!==!0)):[]}createToolsInstances(){this.tools=new Map,this.getTools().forEach(e=>{const t=e.create();this.tools.set(e,t)})}async getPopoverItems(){const e=[];let t=0;for(const[n,r]of this.tools){const i=await r.render(),s=this.getToolShortcut(n.name);if(s!==void 0)try{this.enableShortcuts(n.name,s)}catch{}const a=s!==void 0?co(s):void 0,c=G.t(Q.toolNames,n.title||ht(n.name));[i].flat().forEach(l=>{var d,u;const p={name:n.name,onActivate:()=>{this.toolClicked(r)},hint:{title:c,description:a}};if(S.isElement(l)){const g={...p,element:l,type:$.Html};if(U(r.renderActions)){const b=r.renderActions();g.children={isOpen:(d=r.checkState)==null?void 0:d.call(r,P.get()),isFlippable:!1,items:[{type:$.Html,element:b}]}}else(u=r.checkState)==null||u.call(r,P.get());e.push(g)}else if(l.type===$.Html)e.push({...p,...l,type:$.Html});else if(l.type===$.Separator)e.push({type:$.Separator});else{const g={...p,...l,type:$.Default};"children"in g&&t!==0&&e.push({type:$.Separator}),e.push(g),"children"in g&&t<this.tools.size-1&&e.push({type:$.Separator})}}),t++}return e}getToolShortcut(e){const{Tools:t}=this.Editor,n=t.inlineTools.get(e),r=t.internal.inlineTools;return Array.from(r.keys()).includes(e)?this.inlineTools[e][Vr.Shortcut]:n==null?void 0:n.shortcut}enableShortcuts(e,t){je.add({name:t,handler:n=>{var r;const{currentBlock:i}=this.Editor.BlockManager;i&&i.tool.enabledInlineTools&&(n.preventDefault(),(r=this.popover)==null||r.activateItemByName(e))},on:document})}toolClicked(e){var t;const n=P.range;(t=e.surround)==null||t.call(e,n),this.checkToolsState()}checkToolsState(){var e;(e=this.tools)==null||e.forEach(t=>{var n;(n=t.checkState)==null||n.call(t,P.get())})}get inlineTools(){const e={};return Array.from(this.Editor.Tools.inlineTools.entries()).forEach(([t,n])=>{e[t]=n.create()}),e}}function Xr(){const o=window.getSelection();if(o===null)return[null,0];let e=o.focusNode,t=o.focusOffset;return e===null?[null,0]:(e.nodeType!==Node.TEXT_NODE&&e.childNodes.length>0&&(e.childNodes[t]?(e=e.childNodes[t],t=0):(e=e.childNodes[t-1],t=e.textContent.length)),[e,t])}function Gr(o,e,t,n){const r=document.createRange();n==="left"?(r.setStart(o,0),r.setEnd(e,t)):(r.setStart(e,t),r.setEnd(o,o.childNodes.length));const i=r.cloneContents(),s=document.createElement("div");s.appendChild(i);const a=s.textContent||"";return bs(a)}function rt(o){const e=S.getDeepestNode(o);if(e===null||S.isEmpty(o))return!0;if(S.isNativeInput(e))return e.selectionEnd===0;if(S.isEmpty(o))return!0;const[t,n]=Xr();return t===null?!1:Gr(o,t,n,"left")}function it(o){const e=S.getDeepestNode(o,!0);if(e===null)return!0;if(S.isNativeInput(e))return e.selectionEnd===e.value.length;const[t,n]=Xr();return t===null?!1:Gr(o,t,n,"right")}var Zr={},bo={},Et={},Ce={},yo={},ko={};Object.defineProperty(ko,"__esModule",{value:!0});ko.allInputsSelector=Da;function Da(){var o=["text","password","email","number","search","tel","url"];return"[contenteditable=true], textarea, input:not([type]), "+o.map(function(e){return'input[type="'.concat(e,'"]')}).join(", ")}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.allInputsSelector=void 0;var e=ko;Object.defineProperty(o,"allInputsSelector",{enumerable:!0,get:function(){return e.allInputsSelector}})})(yo);var Se={},wo={};Object.defineProperty(wo,"__esModule",{value:!0});wo.isNativeInput=Fa;function Fa(o){var e=["INPUT","TEXTAREA"];return o&&o.tagName?e.includes(o.tagName):!1}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.isNativeInput=void 0;var e=wo;Object.defineProperty(o,"isNativeInput",{enumerable:!0,get:function(){return e.isNativeInput}})})(Se);var Jr={},xo={};Object.defineProperty(xo,"__esModule",{value:!0});xo.append=Ha;function Ha(o,e){Array.isArray(e)?e.forEach(function(t){o.appendChild(t)}):o.appendChild(e)}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.append=void 0;var e=xo;Object.defineProperty(o,"append",{enumerable:!0,get:function(){return e.append}})})(Jr);var Eo={},Co={};Object.defineProperty(Co,"__esModule",{value:!0});Co.blockElements=$a;function $a(){return["address","article","aside","blockquote","canvas","div","dl","dt","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","hr","li","main","nav","noscript","ol","output","p","pre","ruby","section","table","tbody","thead","tr","tfoot","ul","video"]}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.blockElements=void 0;var e=Co;Object.defineProperty(o,"blockElements",{enumerable:!0,get:function(){return e.blockElements}})})(Eo);var Qr={},So={};Object.defineProperty(So,"__esModule",{value:!0});So.calculateBaseline=Ua;function Ua(o){var e=window.getComputedStyle(o),t=parseFloat(e.fontSize),n=parseFloat(e.lineHeight)||t*1.2,r=parseFloat(e.paddingTop),i=parseFloat(e.borderTopWidth),s=parseFloat(e.marginTop),a=t*.8,c=(n-t)/2,l=s+i+r+c+a;return l}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.calculateBaseline=void 0;var e=So;Object.defineProperty(o,"calculateBaseline",{enumerable:!0,get:function(){return e.calculateBaseline}})})(Qr);var ei={},To={},Bo={},Oo={};Object.defineProperty(Oo,"__esModule",{value:!0});Oo.isContentEditable=za;function za(o){return o.contentEditable==="true"}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.isContentEditable=void 0;var e=Oo;Object.defineProperty(o,"isContentEditable",{enumerable:!0,get:function(){return e.isContentEditable}})})(Bo);Object.defineProperty(To,"__esModule",{value:!0});To.canSetCaret=Ya;var Wa=Se,qa=Bo;function Ya(o){var e=!0;if((0,Wa.isNativeInput)(o))switch(o.type){case"file":case"checkbox":case"radio":case"hidden":case"submit":case"button":case"image":case"reset":e=!1;break}else e=(0,qa.isContentEditable)(o);return e}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.canSetCaret=void 0;var e=To;Object.defineProperty(o,"canSetCaret",{enumerable:!0,get:function(){return e.canSetCaret}})})(ei);var Ct={},_o={};function Ka(o,e,t){const n=t.value!==void 0?"value":"get",r=t[n],i=`#${e}Cache`;if(t[n]=function(...s){return this[i]===void 0&&(this[i]=r.apply(this,s)),this[i]},n==="get"&&t.set){const s=t.set;t.set=function(a){delete o[i],s.apply(this,a)}}return t}function ti(){const o={win:!1,mac:!1,x11:!1,linux:!1},e=Object.keys(o).find(t=>window.navigator.appVersion.toLowerCase().indexOf(t)!==-1);return e!==void 0&&(o[e]=!0),o}function Io(o){return o!=null&&o!==""&&(typeof o!="object"||Object.keys(o).length>0)}function Va(o){return!Io(o)}const Xa=()=>typeof window<"u"&&window.navigator!==null&&Io(window.navigator.platform)&&(/iP(ad|hone|od)/.test(window.navigator.platform)||window.navigator.platform==="MacIntel"&&window.navigator.maxTouchPoints>1);function Ga(o){const e=ti();return o=o.replace(/shift/gi,"⇧").replace(/backspace/gi,"⌫").replace(/enter/gi,"⏎").replace(/up/gi,"↑").replace(/left/gi,"→").replace(/down/gi,"↓").replace(/right/gi,"←").replace(/escape/gi,"⎋").replace(/insert/gi,"Ins").replace(/delete/gi,"␡").replace(/\+/gi,"+"),e.mac?o=o.replace(/ctrl|cmd/gi,"⌘").replace(/alt/gi,"⌥"):o=o.replace(/cmd/gi,"Ctrl").replace(/windows/gi,"WIN"),o}function Za(o){return o[0].toUpperCase()+o.slice(1)}function Ja(o){const e=document.createElement("div");e.style.position="absolute",e.style.left="-999px",e.style.bottom="-999px",e.innerHTML=o,document.body.appendChild(e);const t=window.getSelection(),n=document.createRange();if(n.selectNode(e),t===null)throw new Error("Cannot copy text to clipboard");t.removeAllRanges(),t.addRange(n),document.execCommand("copy"),document.body.removeChild(e)}function Qa(o,e,t){let n;return(...r)=>{const i=this,s=()=>{n=void 0,t!==!0&&o.apply(i,r)},a=t===!0&&n!==void 0;window.clearTimeout(n),n=window.setTimeout(s,e),a&&o.apply(i,r)}}function ve(o){return Object.prototype.toString.call(o).match(/\s([a-zA-Z]+)/)[1].toLowerCase()}function el(o){return ve(o)==="boolean"}function oi(o){return ve(o)==="function"||ve(o)==="asyncfunction"}function tl(o){return oi(o)&&/^\s*class\s+/.test(o.toString())}function ol(o){return ve(o)==="number"}function st(o){return ve(o)==="object"}function nl(o){return Promise.resolve(o)===o}function rl(o){return ve(o)==="string"}function il(o){return ve(o)==="undefined"}function so(o,...e){if(!e.length)return o;const t=e.shift();if(st(o)&&st(t))for(const n in t)st(t[n])?(o[n]===void 0&&Object.assign(o,{[n]:{}}),so(o[n],t[n])):Object.assign(o,{[n]:t[n]});return so(o,...e)}function sl(o,e,t){const n=`«${e}» is deprecated and will be removed in the next major release. Please use the «${t}» instead.`;o&&console.warn(n)}function al(o){try{return new URL(o).href}catch{}return o.substring(0,2)==="//"?window.location.protocol+o:window.location.origin+o}function ll(o){return o>47&&o<58||o===32||o===13||o===229||o>64&&o<91||o>95&&o<112||o>185&&o<193||o>218&&o<223}const cl={BACKSPACE:8,TAB:9,ENTER:13,SHIFT:16,CTRL:17,ALT:18,ESC:27,SPACE:32,LEFT:37,UP:38,DOWN:40,RIGHT:39,DELETE:46,META:91,SLASH:191},dl={LEFT:0,WHEEL:1,RIGHT:2,BACKWARD:3,FORWARD:4};let ul=class{constructor(){this.completed=Promise.resolve()}add(o){return new Promise((e,t)=>{this.completed=this.completed.then(o).then(e).catch(t)})}};function hl(o,e,t=void 0){let n,r,i,s=null,a=0;t||(t={});const c=function(){a=t.leading===!1?0:Date.now(),s=null,i=o.apply(n,r),s===null&&(n=r=null)};return function(){const l=Date.now();!a&&t.leading===!1&&(a=l);const d=e-(l-a);return n=this,r=arguments,d<=0||d>e?(s&&(clearTimeout(s),s=null),a=l,i=o.apply(n,r),s===null&&(n=r=null)):!s&&t.trailing!==!1&&(s=setTimeout(c,d)),i}}const pl=Object.freeze(Object.defineProperty({__proto__:null,PromiseQueue:ul,beautifyShortcut:Ga,cacheable:Ka,capitalize:Za,copyTextToClipboard:Ja,debounce:Qa,deepMerge:so,deprecationAssert:sl,getUserOS:ti,getValidUrl:al,isBoolean:el,isClass:tl,isEmpty:Va,isFunction:oi,isIosDevice:Xa,isNumber:ol,isObject:st,isPrintableKey:ll,isPromise:nl,isString:rl,isUndefined:il,keyCodes:cl,mouseButtons:dl,notEmpty:Io,throttle:hl,typeOf:ve},Symbol.toStringTag,{value:"Module"})),Mo=rs(pl);Object.defineProperty(_o,"__esModule",{value:!0});_o.containsOnlyInlineElements=ml;var fl=Mo,gl=Eo;function ml(o){var e;(0,fl.isString)(o)?(e=document.createElement("div"),e.innerHTML=o):e=o;var t=function(n){return!(0,gl.blockElements)().includes(n.tagName.toLowerCase())&&Array.from(n.children).every(t)};return Array.from(e.children).every(t)}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.containsOnlyInlineElements=void 0;var e=_o;Object.defineProperty(o,"containsOnlyInlineElements",{enumerable:!0,get:function(){return e.containsOnlyInlineElements}})})(Ct);var ni={},Lo={},St={},Po={};Object.defineProperty(Po,"__esModule",{value:!0});Po.make=vl;function vl(o,e,t){var n;e===void 0&&(e=null),t===void 0&&(t={});var r=document.createElement(o);if(Array.isArray(e)){var i=e.filter(function(a){return a!==void 0});(n=r.classList).add.apply(n,i)}else e!==null&&r.classList.add(e);for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(r[s]=t[s]);return r}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.make=void 0;var e=Po;Object.defineProperty(o,"make",{enumerable:!0,get:function(){return e.make}})})(St);Object.defineProperty(Lo,"__esModule",{value:!0});Lo.fragmentToString=yl;var bl=St;function yl(o){var e=(0,bl.make)("div");return e.appendChild(o),e.innerHTML}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.fragmentToString=void 0;var e=Lo;Object.defineProperty(o,"fragmentToString",{enumerable:!0,get:function(){return e.fragmentToString}})})(ni);var ri={},Ao={};Object.defineProperty(Ao,"__esModule",{value:!0});Ao.getContentLength=wl;var kl=Se;function wl(o){var e,t;return(0,kl.isNativeInput)(o)?o.value.length:o.nodeType===Node.TEXT_NODE?o.length:(t=(e=o.textContent)===null||e===void 0?void 0:e.length)!==null&&t!==void 0?t:0}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.getContentLength=void 0;var e=Ao;Object.defineProperty(o,"getContentLength",{enumerable:!0,get:function(){return e.getContentLength}})})(ri);var No={},jo={},ir=Ye&&Ye.__spreadArray||function(o,e,t){if(t||arguments.length===2)for(var n=0,r=e.length,i;n<r;n++)(i||!(n in e))&&(i||(i=Array.prototype.slice.call(e,0,n)),i[n]=e[n]);return o.concat(i||Array.prototype.slice.call(e))};Object.defineProperty(jo,"__esModule",{value:!0});jo.getDeepestBlockElements=ii;var xl=Ct;function ii(o){return(0,xl.containsOnlyInlineElements)(o)?[o]:Array.from(o.children).reduce(function(e,t){return ir(ir([],e,!0),ii(t),!0)},[])}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.getDeepestBlockElements=void 0;var e=jo;Object.defineProperty(o,"getDeepestBlockElements",{enumerable:!0,get:function(){return e.getDeepestBlockElements}})})(No);var si={},Ro={},Tt={},Do={};Object.defineProperty(Do,"__esModule",{value:!0});Do.isLineBreakTag=El;function El(o){return["BR","WBR"].includes(o.tagName)}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.isLineBreakTag=void 0;var e=Do;Object.defineProperty(o,"isLineBreakTag",{enumerable:!0,get:function(){return e.isLineBreakTag}})})(Tt);var Bt={},Fo={};Object.defineProperty(Fo,"__esModule",{value:!0});Fo.isSingleTag=Cl;function Cl(o){return["AREA","BASE","BR","COL","COMMAND","EMBED","HR","IMG","INPUT","KEYGEN","LINK","META","PARAM","SOURCE","TRACK","WBR"].includes(o.tagName)}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.isSingleTag=void 0;var e=Fo;Object.defineProperty(o,"isSingleTag",{enumerable:!0,get:function(){return e.isSingleTag}})})(Bt);Object.defineProperty(Ro,"__esModule",{value:!0});Ro.getDeepestNode=ai;var Sl=Se,Tl=Tt,Bl=Bt;function ai(o,e){e===void 0&&(e=!1);var t=e?"lastChild":"firstChild",n=e?"previousSibling":"nextSibling";if(o.nodeType===Node.ELEMENT_NODE&&o[t]){var r=o[t];if((0,Bl.isSingleTag)(r)&&!(0,Sl.isNativeInput)(r)&&!(0,Tl.isLineBreakTag)(r))if(r[n])r=r[n];else if(r.parentNode!==null&&r.parentNode[n])r=r.parentNode[n];else return r.parentNode;return ai(r,e)}return o}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.getDeepestNode=void 0;var e=Ro;Object.defineProperty(o,"getDeepestNode",{enumerable:!0,get:function(){return e.getDeepestNode}})})(si);var li={},Ho={},Je=Ye&&Ye.__spreadArray||function(o,e,t){if(t||arguments.length===2)for(var n=0,r=e.length,i;n<r;n++)(i||!(n in e))&&(i||(i=Array.prototype.slice.call(e,0,n)),i[n]=e[n]);return o.concat(i||Array.prototype.slice.call(e))};Object.defineProperty(Ho,"__esModule",{value:!0});Ho.findAllInputs=Ll;var Ol=Ct,_l=No,Il=yo,Ml=Se;function Ll(o){return Array.from(o.querySelectorAll((0,Il.allInputsSelector)())).reduce(function(e,t){return(0,Ml.isNativeInput)(t)||(0,Ol.containsOnlyInlineElements)(t)?Je(Je([],e,!0),[t],!1):Je(Je([],e,!0),(0,_l.getDeepestBlockElements)(t),!0)},[])}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.findAllInputs=void 0;var e=Ho;Object.defineProperty(o,"findAllInputs",{enumerable:!0,get:function(){return e.findAllInputs}})})(li);var ci={},$o={};Object.defineProperty($o,"__esModule",{value:!0});$o.isCollapsedWhitespaces=Pl;function Pl(o){return!/[^\t\n\r ]/.test(o)}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.isCollapsedWhitespaces=void 0;var e=$o;Object.defineProperty(o,"isCollapsedWhitespaces",{enumerable:!0,get:function(){return e.isCollapsedWhitespaces}})})(ci);var Uo={},zo={};Object.defineProperty(zo,"__esModule",{value:!0});zo.isElement=Nl;var Al=Mo;function Nl(o){return(0,Al.isNumber)(o)?!1:!!o&&!!o.nodeType&&o.nodeType===Node.ELEMENT_NODE}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.isElement=void 0;var e=zo;Object.defineProperty(o,"isElement",{enumerable:!0,get:function(){return e.isElement}})})(Uo);var di={},Wo={},qo={},Yo={};Object.defineProperty(Yo,"__esModule",{value:!0});Yo.isLeaf=jl;function jl(o){return o===null?!1:o.childNodes.length===0}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.isLeaf=void 0;var e=Yo;Object.defineProperty(o,"isLeaf",{enumerable:!0,get:function(){return e.isLeaf}})})(qo);var Ko={},Vo={};Object.defineProperty(Vo,"__esModule",{value:!0});Vo.isNodeEmpty=$l;var Rl=Tt,Dl=Uo,Fl=Se,Hl=Bt;function $l(o,e){var t="";return(0,Hl.isSingleTag)(o)&&!(0,Rl.isLineBreakTag)(o)?!1:((0,Dl.isElement)(o)&&(0,Fl.isNativeInput)(o)?t=o.value:o.textContent!==null&&(t=o.textContent.replace("​","")),e!==void 0&&(t=t.replace(new RegExp(e,"g"),"")),t.trim().length===0)}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.isNodeEmpty=void 0;var e=Vo;Object.defineProperty(o,"isNodeEmpty",{enumerable:!0,get:function(){return e.isNodeEmpty}})})(Ko);Object.defineProperty(Wo,"__esModule",{value:!0});Wo.isEmpty=Wl;var Ul=qo,zl=Ko;function Wl(o,e){o.normalize();for(var t=[o];t.length>0;){var n=t.shift();if(n){if(o=n,(0,Ul.isLeaf)(o)&&!(0,zl.isNodeEmpty)(o,e))return!1;t.push.apply(t,Array.from(o.childNodes))}}return!0}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.isEmpty=void 0;var e=Wo;Object.defineProperty(o,"isEmpty",{enumerable:!0,get:function(){return e.isEmpty}})})(di);var ui={},Xo={};Object.defineProperty(Xo,"__esModule",{value:!0});Xo.isFragment=Yl;var ql=Mo;function Yl(o){return(0,ql.isNumber)(o)?!1:!!o&&!!o.nodeType&&o.nodeType===Node.DOCUMENT_FRAGMENT_NODE}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.isFragment=void 0;var e=Xo;Object.defineProperty(o,"isFragment",{enumerable:!0,get:function(){return e.isFragment}})})(ui);var hi={},Go={};Object.defineProperty(Go,"__esModule",{value:!0});Go.isHTMLString=Vl;var Kl=St;function Vl(o){var e=(0,Kl.make)("div");return e.innerHTML=o,e.childElementCount>0}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.isHTMLString=void 0;var e=Go;Object.defineProperty(o,"isHTMLString",{enumerable:!0,get:function(){return e.isHTMLString}})})(hi);var pi={},Zo={};Object.defineProperty(Zo,"__esModule",{value:!0});Zo.offset=Xl;function Xl(o){var e=o.getBoundingClientRect(),t=window.pageXOffset||document.documentElement.scrollLeft,n=window.pageYOffset||document.documentElement.scrollTop,r=e.top+n,i=e.left+t;return{top:r,left:i,bottom:r+e.height,right:i+e.width}}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.offset=void 0;var e=Zo;Object.defineProperty(o,"offset",{enumerable:!0,get:function(){return e.offset}})})(pi);var fi={},Jo={};Object.defineProperty(Jo,"__esModule",{value:!0});Jo.prepend=Gl;function Gl(o,e){Array.isArray(e)?(e=e.reverse(),e.forEach(function(t){return o.prepend(t)})):o.prepend(e)}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.prepend=void 0;var e=Jo;Object.defineProperty(o,"prepend",{enumerable:!0,get:function(){return e.prepend}})})(fi);(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.prepend=o.offset=o.make=o.isLineBreakTag=o.isSingleTag=o.isNodeEmpty=o.isLeaf=o.isHTMLString=o.isFragment=o.isEmpty=o.isElement=o.isContentEditable=o.isCollapsedWhitespaces=o.findAllInputs=o.isNativeInput=o.allInputsSelector=o.getDeepestNode=o.getDeepestBlockElements=o.getContentLength=o.fragmentToString=o.containsOnlyInlineElements=o.canSetCaret=o.calculateBaseline=o.blockElements=o.append=void 0;var e=yo;Object.defineProperty(o,"allInputsSelector",{enumerable:!0,get:function(){return e.allInputsSelector}});var t=Se;Object.defineProperty(o,"isNativeInput",{enumerable:!0,get:function(){return t.isNativeInput}});var n=Jr;Object.defineProperty(o,"append",{enumerable:!0,get:function(){return n.append}});var r=Eo;Object.defineProperty(o,"blockElements",{enumerable:!0,get:function(){return r.blockElements}});var i=Qr;Object.defineProperty(o,"calculateBaseline",{enumerable:!0,get:function(){return i.calculateBaseline}});var s=ei;Object.defineProperty(o,"canSetCaret",{enumerable:!0,get:function(){return s.canSetCaret}});var a=Ct;Object.defineProperty(o,"containsOnlyInlineElements",{enumerable:!0,get:function(){return a.containsOnlyInlineElements}});var c=ni;Object.defineProperty(o,"fragmentToString",{enumerable:!0,get:function(){return c.fragmentToString}});var l=ri;Object.defineProperty(o,"getContentLength",{enumerable:!0,get:function(){return l.getContentLength}});var d=No;Object.defineProperty(o,"getDeepestBlockElements",{enumerable:!0,get:function(){return d.getDeepestBlockElements}});var u=si;Object.defineProperty(o,"getDeepestNode",{enumerable:!0,get:function(){return u.getDeepestNode}});var p=li;Object.defineProperty(o,"findAllInputs",{enumerable:!0,get:function(){return p.findAllInputs}});var g=ci;Object.defineProperty(o,"isCollapsedWhitespaces",{enumerable:!0,get:function(){return g.isCollapsedWhitespaces}});var b=Bo;Object.defineProperty(o,"isContentEditable",{enumerable:!0,get:function(){return b.isContentEditable}});var f=Uo;Object.defineProperty(o,"isElement",{enumerable:!0,get:function(){return f.isElement}});var m=di;Object.defineProperty(o,"isEmpty",{enumerable:!0,get:function(){return m.isEmpty}});var w=ui;Object.defineProperty(o,"isFragment",{enumerable:!0,get:function(){return w.isFragment}});var h=hi;Object.defineProperty(o,"isHTMLString",{enumerable:!0,get:function(){return h.isHTMLString}});var v=qo;Object.defineProperty(o,"isLeaf",{enumerable:!0,get:function(){return v.isLeaf}});var y=Ko;Object.defineProperty(o,"isNodeEmpty",{enumerable:!0,get:function(){return y.isNodeEmpty}});var k=Tt;Object.defineProperty(o,"isLineBreakTag",{enumerable:!0,get:function(){return k.isLineBreakTag}});var T=Bt;Object.defineProperty(o,"isSingleTag",{enumerable:!0,get:function(){return T.isSingleTag}});var O=St;Object.defineProperty(o,"make",{enumerable:!0,get:function(){return O.make}});var x=pi;Object.defineProperty(o,"offset",{enumerable:!0,get:function(){return x.offset}});var C=fi;Object.defineProperty(o,"prepend",{enumerable:!0,get:function(){return C.prepend}})})(Ce);var Ot={};Object.defineProperty(Ot,"__esModule",{value:!0});Ot.getContenteditableSlice=Jl;var Zl=Ce;function Jl(o,e,t,n,r){var i;r===void 0&&(r=!1);var s=document.createRange();if(n==="left"?(s.setStart(o,0),s.setEnd(e,t)):(s.setStart(e,t),s.setEnd(o,o.childNodes.length)),r===!0){var a=s.extractContents();return(0,Zl.fragmentToString)(a)}var c=s.cloneContents(),l=document.createElement("div");l.appendChild(c);var d=(i=l.textContent)!==null&&i!==void 0?i:"";return d}Object.defineProperty(Et,"__esModule",{value:!0});Et.checkContenteditableSliceForEmptiness=tc;var Ql=Ce,ec=Ot;function tc(o,e,t,n){var r=(0,ec.getContenteditableSlice)(o,e,t,n);return(0,Ql.isCollapsedWhitespaces)(r)}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.checkContenteditableSliceForEmptiness=void 0;var e=Et;Object.defineProperty(o,"checkContenteditableSliceForEmptiness",{enumerable:!0,get:function(){return e.checkContenteditableSliceForEmptiness}})})(bo);var gi={};(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.getContenteditableSlice=void 0;var e=Ot;Object.defineProperty(o,"getContenteditableSlice",{enumerable:!0,get:function(){return e.getContenteditableSlice}})})(gi);var mi={},Qo={};Object.defineProperty(Qo,"__esModule",{value:!0});Qo.focus=nc;var oc=Ce;function nc(o,e){var t,n;if(e===void 0&&(e=!0),(0,oc.isNativeInput)(o)){o.focus();var r=e?0:o.value.length;o.setSelectionRange(r,r)}else{var i=document.createRange(),s=window.getSelection();if(!s)return;var a=function(p){var g=document.createTextNode("");p.appendChild(g),i.setStart(g,0),i.setEnd(g,0)},c=function(p){return p!=null},l=o.childNodes,d=e?l[0]:l[l.length-1];if(c(d)){for(;c(d)&&d.nodeType!==Node.TEXT_NODE;)d=e?d.firstChild:d.lastChild;if(c(d)&&d.nodeType===Node.TEXT_NODE){var u=(n=(t=d.textContent)===null||t===void 0?void 0:t.length)!==null&&n!==void 0?n:0,r=e?0:u;i.setStart(d,r),i.setEnd(d,r)}else a(o)}else a(o);s.removeAllRanges(),s.addRange(i)}}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.focus=void 0;var e=Qo;Object.defineProperty(o,"focus",{enumerable:!0,get:function(){return e.focus}})})(mi);var en={},_t={};Object.defineProperty(_t,"__esModule",{value:!0});_t.getCaretNodeAndOffset=rc;function rc(){var o=window.getSelection();if(o===null)return[null,0];var e=o.focusNode,t=o.focusOffset;return e===null?[null,0]:(e.nodeType!==Node.TEXT_NODE&&e.childNodes.length>0&&(e.childNodes[t]!==void 0?(e=e.childNodes[t],t=0):(e=e.childNodes[t-1],e.textContent!==null&&(t=e.textContent.length))),[e,t])}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.getCaretNodeAndOffset=void 0;var e=_t;Object.defineProperty(o,"getCaretNodeAndOffset",{enumerable:!0,get:function(){return e.getCaretNodeAndOffset}})})(en);var vi={},It={};Object.defineProperty(It,"__esModule",{value:!0});It.getRange=ic;function ic(){var o=window.getSelection();return o&&o.rangeCount?o.getRangeAt(0):null}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.getRange=void 0;var e=It;Object.defineProperty(o,"getRange",{enumerable:!0,get:function(){return e.getRange}})})(vi);var bi={},tn={};Object.defineProperty(tn,"__esModule",{value:!0});tn.isCaretAtEndOfInput=lc;var sr=Ce,sc=en,ac=bo;function lc(o){var e=(0,sr.getDeepestNode)(o,!0);if(e===null)return!0;if((0,sr.isNativeInput)(e))return e.selectionEnd===e.value.length;var t=(0,sc.getCaretNodeAndOffset)(),n=t[0],r=t[1];return n===null?!1:(0,ac.checkContenteditableSliceForEmptiness)(o,n,r,"right")}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.isCaretAtEndOfInput=void 0;var e=tn;Object.defineProperty(o,"isCaretAtEndOfInput",{enumerable:!0,get:function(){return e.isCaretAtEndOfInput}})})(bi);var yi={},on={};Object.defineProperty(on,"__esModule",{value:!0});on.isCaretAtStartOfInput=uc;var Qe=Ce,cc=_t,dc=Et;function uc(o){var e=(0,Qe.getDeepestNode)(o);if(e===null||(0,Qe.isEmpty)(o))return!0;if((0,Qe.isNativeInput)(e))return e.selectionEnd===0;if((0,Qe.isEmpty)(o))return!0;var t=(0,cc.getCaretNodeAndOffset)(),n=t[0],r=t[1];return n===null?!1:(0,dc.checkContenteditableSliceForEmptiness)(o,n,r,"left")}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.isCaretAtStartOfInput=void 0;var e=on;Object.defineProperty(o,"isCaretAtStartOfInput",{enumerable:!0,get:function(){return e.isCaretAtStartOfInput}})})(yi);var ki={},nn={};Object.defineProperty(nn,"__esModule",{value:!0});nn.save=fc;var hc=Ce,pc=It;function fc(){var o=(0,pc.getRange)(),e=(0,hc.make)("span");if(e.id="cursor",e.hidden=!0,!!o)return o.insertNode(e),function(){var t=window.getSelection();t&&(o.setStartAfter(e),o.setEndAfter(e),t.removeAllRanges(),t.addRange(o),setTimeout(function(){e.remove()},150))}}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.save=void 0;var e=nn;Object.defineProperty(o,"save",{enumerable:!0,get:function(){return e.save}})})(ki);(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.save=o.isCaretAtStartOfInput=o.isCaretAtEndOfInput=o.getRange=o.getCaretNodeAndOffset=o.focus=o.getContenteditableSlice=o.checkContenteditableSliceForEmptiness=void 0;var e=bo;Object.defineProperty(o,"checkContenteditableSliceForEmptiness",{enumerable:!0,get:function(){return e.checkContenteditableSliceForEmptiness}});var t=gi;Object.defineProperty(o,"getContenteditableSlice",{enumerable:!0,get:function(){return t.getContenteditableSlice}});var n=mi;Object.defineProperty(o,"focus",{enumerable:!0,get:function(){return n.focus}});var r=en;Object.defineProperty(o,"getCaretNodeAndOffset",{enumerable:!0,get:function(){return r.getCaretNodeAndOffset}});var i=vi;Object.defineProperty(o,"getRange",{enumerable:!0,get:function(){return i.getRange}});var s=bi;Object.defineProperty(o,"isCaretAtEndOfInput",{enumerable:!0,get:function(){return s.isCaretAtEndOfInput}});var a=yi;Object.defineProperty(o,"isCaretAtStartOfInput",{enumerable:!0,get:function(){return a.isCaretAtStartOfInput}});var c=ki;Object.defineProperty(o,"save",{enumerable:!0,get:function(){return c.save}})})(Zr);class gc extends j{keydown(e){switch(this.beforeKeydownProcessing(e),e.keyCode){case N.BACKSPACE:this.backspace(e);break;case N.DELETE:this.delete(e);break;case N.ENTER:this.enter(e);break;case N.DOWN:case N.RIGHT:this.arrowRightAndDown(e);break;case N.UP:case N.LEFT:this.arrowLeftAndUp(e);break;case N.TAB:this.tabPressed(e);break}e.key==="/"&&!e.ctrlKey&&!e.metaKey&&this.slashPressed(e),e.code==="Slash"&&(e.ctrlKey||e.metaKey)&&(e.preventDefault(),this.commandSlashPressed())}beforeKeydownProcessing(e){this.needToolbarClosing(e)&&br(e.keyCode)&&(this.Editor.Toolbar.close(),e.ctrlKey||e.metaKey||e.altKey||e.shiftKey||this.Editor.BlockSelection.clearSelection(e))}keyup(e){e.shiftKey||this.Editor.UI.checkEmptiness()}dragOver(e){const t=this.Editor.BlockManager.getBlockByChildNode(e.target);t.dropTarget=!0}dragLeave(e){const t=this.Editor.BlockManager.getBlockByChildNode(e.target);t.dropTarget=!1}handleCommandC(e){const{BlockSelection:t}=this.Editor;t.anyBlockSelected&&t.copySelectedBlocks(e)}handleCommandX(e){const{BlockSelection:t,BlockManager:n,Caret:r}=this.Editor;t.anyBlockSelected&&t.copySelectedBlocks(e).then(()=>{const i=n.removeSelectedBlocks(),s=n.insertDefaultBlockAtIndex(i,!0);r.setToBlock(s,r.positions.START),t.clearSelection(e)})}tabPressed(e){const{InlineToolbar:t,Caret:n}=this.Editor;t.opened||(e.shiftKey?n.navigatePrevious(!0):n.navigateNext(!0))&&e.preventDefault()}commandSlashPressed(){this.Editor.BlockSelection.selectedBlocks.length>1||this.activateBlockSettings()}slashPressed(e){this.Editor.BlockManager.currentBlock.isEmpty&&(e.preventDefault(),this.Editor.Caret.insertContentAtCaretPosition("/"),this.activateToolbox())}enter(e){const{BlockManager:t,UI:n}=this.Editor,r=t.currentBlock;if(r===void 0||r.tool.isLineBreaksEnabled||n.someToolbarOpened&&n.someFlipperButtonFocused||e.shiftKey&&!to)return;let i=r;r.currentInput!==void 0&&rt(r.currentInput)&&!r.hasMedia?this.Editor.BlockManager.insertDefaultBlockAtIndex(this.Editor.BlockManager.currentBlockIndex):r.currentInput&&it(r.currentInput)?i=this.Editor.BlockManager.insertDefaultBlockAtIndex(this.Editor.BlockManager.currentBlockIndex+1):i=this.Editor.BlockManager.split(),this.Editor.Caret.setToBlock(i),this.Editor.Toolbar.moveAndOpen(i),e.preventDefault()}backspace(e){const{BlockManager:t,Caret:n}=this.Editor,{currentBlock:r,previousBlock:i}=t;if(!(r===void 0||!P.isCollapsed||!r.currentInput||!rt(r.currentInput))){if(e.preventDefault(),this.Editor.Toolbar.close(),r.currentInput!==r.firstInput){n.navigatePrevious();return}if(i!==null){if(i.isEmpty){t.removeBlock(i);return}if(r.isEmpty){t.removeBlock(r);const s=t.currentBlock;n.setToBlock(s,n.positions.END);return}er(i,r)?this.mergeBlocks(i,r):n.setToBlock(i,n.positions.END)}}}delete(e){const{BlockManager:t,Caret:n}=this.Editor,{currentBlock:r,nextBlock:i}=t;if(!(!P.isCollapsed||!it(r.currentInput))){if(e.preventDefault(),this.Editor.Toolbar.close(),r.currentInput!==r.lastInput){n.navigateNext();return}if(i!==null){if(i.isEmpty){t.removeBlock(i);return}if(r.isEmpty){t.removeBlock(r),n.setToBlock(i,n.positions.START);return}er(r,i)?this.mergeBlocks(r,i):n.setToBlock(i,n.positions.START)}}}mergeBlocks(e,t){const{BlockManager:n,Toolbar:r}=this.Editor;e.lastInput!==void 0&&(Zr.focus(e.lastInput,!1),n.mergeBlocks(e,t).then(()=>{r.close()}))}arrowRightAndDown(e){const t=mt.usedKeys.includes(e.keyCode)&&(!e.shiftKey||e.keyCode===N.TAB);if(this.Editor.UI.someToolbarOpened&&t)return;this.Editor.Toolbar.close();const{currentBlock:n}=this.Editor.BlockManager,r=((n==null?void 0:n.currentInput)!==void 0?it(n.currentInput):void 0)||this.Editor.BlockSelection.anyBlockSelected;if(e.shiftKey&&e.keyCode===N.DOWN&&r){this.Editor.CrossBlockSelection.toggleBlockSelectedState();return}if(e.keyCode===N.DOWN||e.keyCode===N.RIGHT&&!this.isRtl?this.Editor.Caret.navigateNext():this.Editor.Caret.navigatePrevious()){e.preventDefault();return}ut(()=>{this.Editor.BlockManager.currentBlock&&this.Editor.BlockManager.currentBlock.updateCurrentInput()},20)(),this.Editor.BlockSelection.clearSelection(e)}arrowLeftAndUp(e){if(this.Editor.UI.someToolbarOpened){if(mt.usedKeys.includes(e.keyCode)&&(!e.shiftKey||e.keyCode===N.TAB))return;this.Editor.UI.closeAllToolbars()}this.Editor.Toolbar.close();const{currentBlock:t}=this.Editor.BlockManager,n=((t==null?void 0:t.currentInput)!==void 0?rt(t.currentInput):void 0)||this.Editor.BlockSelection.anyBlockSelected;if(e.shiftKey&&e.keyCode===N.UP&&n){this.Editor.CrossBlockSelection.toggleBlockSelectedState(!1);return}if(e.keyCode===N.UP||e.keyCode===N.LEFT&&!this.isRtl?this.Editor.Caret.navigatePrevious():this.Editor.Caret.navigateNext()){e.preventDefault();return}ut(()=>{this.Editor.BlockManager.currentBlock&&this.Editor.BlockManager.currentBlock.updateCurrentInput()},20)(),this.Editor.BlockSelection.clearSelection(e)}needToolbarClosing(e){const t=e.keyCode===N.ENTER&&this.Editor.Toolbar.toolbox.opened,n=e.keyCode===N.ENTER&&this.Editor.BlockSettings.opened,r=e.keyCode===N.ENTER&&this.Editor.InlineToolbar.opened,i=e.keyCode===N.TAB;return!(e.shiftKey||i||t||n||r)}activateToolbox(){this.Editor.Toolbar.opened||this.Editor.Toolbar.moveAndOpen(),this.Editor.Toolbar.toolbox.open()}activateBlockSettings(){this.Editor.Toolbar.opened||this.Editor.Toolbar.moveAndOpen(),this.Editor.BlockSettings.opened||this.Editor.BlockSettings.open()}}class Yt{constructor(e){this.blocks=[],this.workingArea=e}get length(){return this.blocks.length}get array(){return this.blocks}get nodes(){return yr(this.workingArea.children)}static set(e,t,n){return isNaN(Number(t))?(Reflect.set(e,t,n),!0):(e.insert(+t,n),!0)}static get(e,t){return isNaN(Number(t))?Reflect.get(e,t):e.get(+t)}push(e){this.blocks.push(e),this.insertToDOM(e)}swap(e,t){const n=this.blocks[t];S.swap(this.blocks[e].holder,n.holder),this.blocks[t]=this.blocks[e],this.blocks[e]=n}move(e,t){const n=this.blocks.splice(t,1)[0],r=e-1,i=Math.max(0,r),s=this.blocks[i];e>0?this.insertToDOM(n,"afterend",s):this.insertToDOM(n,"beforebegin",s),this.blocks.splice(e,0,n);const a=this.composeBlockEvent("move",{fromIndex:t,toIndex:e});n.call(ue.MOVED,a)}insert(e,t,n=!1){if(!this.length){this.push(t);return}e>this.length&&(e=this.length),n&&(this.blocks[e].holder.remove(),this.blocks[e].call(ue.REMOVED));const r=n?1:0;if(this.blocks.splice(e,r,t),e>0){const i=this.blocks[e-1];this.insertToDOM(t,"afterend",i)}else{const i=this.blocks[e+1];i?this.insertToDOM(t,"beforebegin",i):this.insertToDOM(t)}}replace(e,t){if(this.blocks[e]===void 0)throw Error("Incorrect index");this.blocks[e].holder.replaceWith(t.holder),this.blocks[e]=t}insertMany(e,t){const n=new DocumentFragment;for(const r of e)n.appendChild(r.holder);if(this.length>0){if(t>0){const r=Math.min(t-1,this.length-1);this.blocks[r].holder.after(n)}else t===0&&this.workingArea.prepend(n);this.blocks.splice(t,0,...e)}else this.blocks.push(...e),this.workingArea.appendChild(n);e.forEach(r=>r.call(ue.RENDERED))}remove(e){isNaN(e)&&(e=this.length-1),this.blocks[e].holder.remove(),this.blocks[e].call(ue.REMOVED),this.blocks.splice(e,1)}removeAll(){this.workingArea.innerHTML="",this.blocks.forEach(e=>e.call(ue.REMOVED)),this.blocks.length=0}insertAfter(e,t){const n=this.blocks.indexOf(e);this.insert(n+1,t)}get(e){return this.blocks[e]}indexOf(e){return this.blocks.indexOf(e)}insertToDOM(e,t,n){t?n.holder.insertAdjacentElement(t,e.holder):this.workingArea.appendChild(e.holder),e.call(ue.RENDERED)}composeBlockEvent(e,t){return new CustomEvent(e,{detail:t})}}const ar="block-removed",lr="block-added",mc="block-moved",cr="block-changed";class vc{constructor(){this.completed=Promise.resolve()}add(e){return new Promise((t,n)=>{this.completed=this.completed.then(e).then(t).catch(n)})}}class bc extends j{constructor(){super(...arguments),this._currentBlockIndex=-1,this._blocks=null}get currentBlockIndex(){return this._currentBlockIndex}set currentBlockIndex(e){this._currentBlockIndex=e}get firstBlock(){return this._blocks[0]}get lastBlock(){return this._blocks[this._blocks.length-1]}get currentBlock(){return this._blocks[this.currentBlockIndex]}set currentBlock(e){this.currentBlockIndex=this.getBlockIndex(e)}get nextBlock(){return this.currentBlockIndex===this._blocks.length-1?null:this._blocks[this.currentBlockIndex+1]}get nextContentfulBlock(){return this.blocks.slice(this.currentBlockIndex+1).find(e=>!!e.inputs.length)}get previousContentfulBlock(){return this.blocks.slice(0,this.currentBlockIndex).reverse().find(e=>!!e.inputs.length)}get previousBlock(){return this.currentBlockIndex===0?null:this._blocks[this.currentBlockIndex-1]}get blocks(){return this._blocks.array}get isEditorEmpty(){return this.blocks.every(e=>e.isEmpty)}prepare(){const e=new Yt(this.Editor.UI.nodes.redactor);this._blocks=new Proxy(e,{set:Yt.set,get:Yt.get}),this.listeners.on(document,"copy",t=>this.Editor.BlockEvents.handleCommandC(t))}toggleReadOnly(e){e?this.disableModuleBindings():this.enableModuleBindings()}composeBlock({tool:e,data:t={},id:n=void 0,tunes:r={}}){const i=this.Editor.ReadOnly.isEnabled,s=this.Editor.Tools.blockTools.get(e),a=new he({id:n,data:t,tool:s,api:this.Editor.API,readOnly:i,tunesData:r},this.eventsDispatcher);return i||window.requestIdleCallback(()=>{this.bindBlockEvents(a)},{timeout:2e3}),a}insert({id:e=void 0,tool:t=this.config.defaultBlock,data:n={},index:r,needToFocus:i=!0,replace:s=!1,tunes:a={}}={}){let c=r;c===void 0&&(c=this.currentBlockIndex+(s?0:1));const l=this.composeBlock({id:e,tool:t,data:n,tunes:a});return s&&this.blockDidMutated(ar,this.getBlockByIndex(c),{index:c}),this._blocks.insert(c,l,s),this.blockDidMutated(lr,l,{index:c}),i?this.currentBlockIndex=c:c<=this.currentBlockIndex&&this.currentBlockIndex++,l}insertMany(e,t=0){this._blocks.insertMany(e,t)}async update(e,t,n){if(!t&&!n)return e;const r=await e.data,i=this.composeBlock({id:e.id,tool:e.name,data:Object.assign({},r,t??{}),tunes:n??e.tunes}),s=this.getBlockIndex(e);return this._blocks.replace(s,i),this.blockDidMutated(cr,i,{index:s}),i}replace(e,t,n){const r=this.getBlockIndex(e);return this.insert({tool:t,data:n,index:r,replace:!0})}paste(e,t,n=!1){const r=this.insert({tool:e,replace:n});try{window.requestIdleCallback(()=>{r.call(ue.ON_PASTE,t)})}catch(i){D(`${e}: onPaste callback call is failed`,"error",i)}return r}insertDefaultBlockAtIndex(e,t=!1){const n=this.composeBlock({tool:this.config.defaultBlock});return this._blocks[e]=n,this.blockDidMutated(lr,n,{index:e}),t?this.currentBlockIndex=e:e<=this.currentBlockIndex&&this.currentBlockIndex++,n}insertAtEnd(){return this.currentBlockIndex=this.blocks.length-1,this.insert()}async mergeBlocks(e,t){let n;if(e.name===t.name&&e.mergeable){const r=await t.data;if(te(r)){console.error("Could not merge Block. Failed to extract original Block data.");return}const[i]=uo([r],e.tool.sanitizeConfig);n=i}else if(e.mergeable&&pt(t,"export")&&pt(e,"import")){const r=await t.exportDataAsString(),i=ae(r,e.tool.sanitizeConfig);n=tr(i,e.tool.conversionConfig)}n!==void 0&&(await e.mergeWith(n),this.removeBlock(t),this.currentBlockIndex=this._blocks.indexOf(e))}removeBlock(e,t=!0){return new Promise(n=>{const r=this._blocks.indexOf(e);if(!this.validateIndex(r))throw new Error("Can't find a Block to remove");e.destroy(),this._blocks.remove(r),this.blockDidMutated(ar,e,{index:r}),this.currentBlockIndex>=r&&this.currentBlockIndex--,this.blocks.length?r===0&&(this.currentBlockIndex=0):(this.unsetCurrentBlock(),t&&this.insert()),n()})}removeSelectedBlocks(){let e;for(let t=this.blocks.length-1;t>=0;t--)this.blocks[t].selected&&(this.removeBlock(this.blocks[t]),e=t);return e}removeAllBlocks(){for(let e=this.blocks.length-1;e>=0;e--)this._blocks.remove(e);this.unsetCurrentBlock(),this.insert(),this.currentBlock.firstInput.focus()}split(){const e=this.Editor.Caret.extractFragmentFromCaretPosition(),t=S.make("div");t.appendChild(e);const n={text:S.isEmpty(t)?"":t.innerHTML};return this.insert({data:n})}getBlockByIndex(e){return e===-1&&(e=this._blocks.length-1),this._blocks[e]}getBlockIndex(e){return this._blocks.indexOf(e)}getBlockById(e){return this._blocks.array.find(t=>t.id===e)}getBlock(e){S.isElement(e)||(e=e.parentNode);const t=this._blocks.nodes,n=e.closest(`.${he.CSS.wrapper}`),r=t.indexOf(n);if(r>=0)return this._blocks[r]}setCurrentBlockByChildNode(e){S.isElement(e)||(e=e.parentNode);const t=e.closest(`.${he.CSS.wrapper}`);if(!t)return;const n=t.closest(`.${this.Editor.UI.CSS.editorWrapper}`);if(n!=null&&n.isEqualNode(this.Editor.UI.nodes.wrapper))return this.currentBlockIndex=this._blocks.nodes.indexOf(t),this.currentBlock.updateCurrentInput(),this.currentBlock}getBlockByChildNode(e){if(!e||!(e instanceof Node))return;S.isElement(e)||(e=e.parentNode);const t=e.closest(`.${he.CSS.wrapper}`);return this.blocks.find(n=>n.holder===t)}swap(e,t){this._blocks.swap(e,t),this.currentBlockIndex=t}move(e,t=this.currentBlockIndex){if(isNaN(e)||isNaN(t)){D("Warning during 'move' call: incorrect indices provided.","warn");return}if(!this.validateIndex(e)||!this.validateIndex(t)){D("Warning during 'move' call: indices cannot be lower than 0 or greater than the amount of blocks.","warn");return}this._blocks.move(e,t),this.currentBlockIndex=e,this.blockDidMutated(mc,this.currentBlock,{fromIndex:t,toIndex:e})}async convert(e,t,n){if(!await e.save())throw new Error("Could not convert Block. Failed to extract original Block data.");const r=this.Editor.Tools.blockTools.get(t);if(!r)throw new Error(`Could not convert Block. Tool «${t}» not found.`);const i=await e.exportDataAsString(),s=ae(i,r.sanitizeConfig);let a=tr(s,r.conversionConfig,r.settings);return n&&(a=Object.assign(a,n)),this.replace(e,r.name,a)}unsetCurrentBlock(){this.currentBlockIndex=-1}async clear(e=!1){const t=new vc;this.blocks.forEach(n=>{t.add(async()=>{await this.removeBlock(n,!1)})}),await t.completed,this.unsetCurrentBlock(),e&&this.insert(),this.Editor.UI.checkEmptiness()}async destroy(){await Promise.all(this.blocks.map(e=>e.destroy()))}bindBlockEvents(e){const{BlockEvents:t}=this.Editor;this.readOnlyMutableListeners.on(e.holder,"keydown",n=>{t.keydown(n)}),this.readOnlyMutableListeners.on(e.holder,"keyup",n=>{t.keyup(n)}),this.readOnlyMutableListeners.on(e.holder,"dragover",n=>{t.dragOver(n)}),this.readOnlyMutableListeners.on(e.holder,"dragleave",n=>{t.dragLeave(n)}),e.on("didMutated",n=>this.blockDidMutated(cr,n,{index:this.getBlockIndex(n)}))}disableModuleBindings(){this.readOnlyMutableListeners.clearAll()}enableModuleBindings(){this.readOnlyMutableListeners.on(document,"cut",e=>this.Editor.BlockEvents.handleCommandX(e)),this.blocks.forEach(e=>{this.bindBlockEvents(e)})}validateIndex(e){return!(e<0||e>=this._blocks.length)}blockDidMutated(e,t,n){const r=new CustomEvent(e,{detail:{target:new de(t),...n}});return this.eventsDispatcher.emit(Tr,{event:r}),t}}class yc extends j{constructor(){super(...arguments),this.anyBlockSelectedCache=null,this.needToSelectAll=!1,this.nativeInputSelected=!1,this.readyToBlockSelection=!1}get sanitizerConfig(){return{p:{},h1:{},h2:{},h3:{},h4:{},h5:{},h6:{},ol:{},ul:{},li:{},br:!0,img:{src:!0,width:!0,height:!0},a:{href:!0},b:{},i:{},u:{}}}get allBlocksSelected(){const{BlockManager:e}=this.Editor;return e.blocks.every(t=>t.selected===!0)}set allBlocksSelected(e){const{BlockManager:t}=this.Editor;t.blocks.forEach(n=>{n.selected=e}),this.clearCache()}get anyBlockSelected(){const{BlockManager:e}=this.Editor;return this.anyBlockSelectedCache===null&&(this.anyBlockSelectedCache=e.blocks.some(t=>t.selected===!0)),this.anyBlockSelectedCache}get selectedBlocks(){return this.Editor.BlockManager.blocks.filter(e=>e.selected)}prepare(){this.selection=new P,je.add({name:"CMD+A",handler:e=>{const{BlockManager:t,ReadOnly:n}=this.Editor;if(n.isEnabled){e.preventDefault(),this.selectAllBlocks();return}t.currentBlock&&this.handleCommandA(e)},on:this.Editor.UI.nodes.redactor})}toggleReadOnly(){P.get().removeAllRanges(),this.allBlocksSelected=!1}unSelectBlockByIndex(e){const{BlockManager:t}=this.Editor;let n;isNaN(e)?n=t.currentBlock:n=t.getBlockByIndex(e),n.selected=!1,this.clearCache()}clearSelection(e,t=!1){const{BlockManager:n,Caret:r,RectangleSelection:i}=this.Editor;this.needToSelectAll=!1,this.nativeInputSelected=!1,this.readyToBlockSelection=!1;const s=e&&e instanceof KeyboardEvent,a=s&&br(e.keyCode);if(this.anyBlockSelected&&s&&a&&!P.isSelectionExists){const c=n.removeSelectedBlocks();n.insertDefaultBlockAtIndex(c,!0),r.setToBlock(n.currentBlock),ut(()=>{const l=e.key;r.insertContentAtCaretPosition(l.length>1?"":l)},20)()}if(this.Editor.CrossBlockSelection.clear(e),!this.anyBlockSelected||i.isRectActivated()){this.Editor.RectangleSelection.clearSelection();return}t&&this.selection.restore(),this.allBlocksSelected=!1}copySelectedBlocks(e){e.preventDefault();const t=S.make("div");this.selectedBlocks.forEach(i=>{const s=ae(i.holder.innerHTML,this.sanitizerConfig),a=S.make("p");a.innerHTML=s,t.appendChild(a)});const n=Array.from(t.childNodes).map(i=>i.textContent).join(`

`),r=t.innerHTML;return e.clipboardData.setData("text/plain",n),e.clipboardData.setData("text/html",r),Promise.all(this.selectedBlocks.map(i=>i.save())).then(i=>{try{e.clipboardData.setData(this.Editor.Paste.MIME_TYPE,JSON.stringify(i))}catch{}})}selectBlockByIndex(e){const{BlockManager:t}=this.Editor,n=t.getBlockByIndex(e);n!==void 0&&this.selectBlock(n)}selectBlock(e){this.selection.save(),P.get().removeAllRanges(),e.selected=!0,this.clearCache(),this.Editor.InlineToolbar.close()}unselectBlock(e){e.selected=!1,this.clearCache()}clearCache(){this.anyBlockSelectedCache=null}destroy(){je.remove(this.Editor.UI.nodes.redactor,"CMD+A")}handleCommandA(e){if(this.Editor.RectangleSelection.clearSelection(),S.isNativeInput(e.target)&&!this.readyToBlockSelection){this.readyToBlockSelection=!0;return}const t=this.Editor.BlockManager.getBlock(e.target),n=t.inputs;if(n.length>1&&!this.readyToBlockSelection){this.readyToBlockSelection=!0;return}if(n.length===1&&!this.needToSelectAll){this.needToSelectAll=!0;return}this.needToSelectAll?(e.preventDefault(),this.selectAllBlocks(),this.needToSelectAll=!1,this.readyToBlockSelection=!1):this.readyToBlockSelection&&(e.preventDefault(),this.selectBlock(t),this.needToSelectAll=!0)}selectAllBlocks(){this.selection.save(),P.get().removeAllRanges(),this.allBlocksSelected=!0,this.Editor.InlineToolbar.close()}}let kc=class ao extends j{get positions(){return{START:"start",END:"end",DEFAULT:"default"}}static get CSS(){return{shadowCaret:"cdx-shadow-caret"}}setToBlock(e,t=this.positions.DEFAULT,n=0){var r;const{BlockManager:i,BlockSelection:s}=this.Editor;if(s.clearSelection(),!e.focusable){(r=window.getSelection())==null||r.removeAllRanges(),s.selectBlock(e),i.currentBlock=e;return}let a;switch(t){case this.positions.START:a=e.firstInput;break;case this.positions.END:a=e.lastInput;break;default:a=e.currentInput}if(!a)return;const c=S.getDeepestNode(a,t===this.positions.END),l=S.getContentLength(c);switch(!0){case t===this.positions.START:n=0;break;case t===this.positions.END:case n>l:n=l;break}this.set(c,n),i.setCurrentBlockByChildNode(e.holder),i.currentBlock.currentInput=a}setToInput(e,t=this.positions.DEFAULT,n=0){const{currentBlock:r}=this.Editor.BlockManager,i=S.getDeepestNode(e);switch(t){case this.positions.START:this.set(i,0);break;case this.positions.END:this.set(i,S.getContentLength(i));break;default:n&&this.set(i,n)}r.currentInput=e}set(e,t=0){const{top:n,bottom:r}=P.setCursor(e,t),{innerHeight:i}=window;n<0?window.scrollBy(0,n-30):r>i&&window.scrollBy(0,r-i+30)}setToTheLastBlock(){const e=this.Editor.BlockManager.lastBlock;if(e)if(e.tool.isDefault&&e.isEmpty)this.setToBlock(e);else{const t=this.Editor.BlockManager.insertAtEnd();this.setToBlock(t)}}extractFragmentFromCaretPosition(){const e=P.get();if(e.rangeCount){const t=e.getRangeAt(0),n=this.Editor.BlockManager.currentBlock.currentInput;if(t.deleteContents(),n)if(S.isNativeInput(n)){const r=n,i=document.createDocumentFragment(),s=r.value.substring(0,r.selectionStart),a=r.value.substring(r.selectionStart);return i.textContent=a,r.value=s,i}else{const r=t.cloneRange();return r.selectNodeContents(n),r.setStart(t.endContainer,t.endOffset),r.extractContents()}}}navigateNext(e=!1){const{BlockManager:t}=this.Editor,{currentBlock:n,nextBlock:r}=t;if(n===void 0)return!1;const{nextInput:i,currentInput:s}=n,a=s!==void 0?it(s):void 0;let c=r;const l=e||a||!n.focusable;if(i&&l)return this.setToInput(i,this.positions.START),!0;if(c===null){if(n.tool.isDefault||!l)return!1;c=t.insertAtEnd()}return l?(this.setToBlock(c,this.positions.START),!0):!1}navigatePrevious(e=!1){const{currentBlock:t,previousBlock:n}=this.Editor.BlockManager;if(!t)return!1;const{previousInput:r,currentInput:i}=t,s=i!==void 0?rt(i):void 0,a=e||s||!t.focusable;return r&&a?(this.setToInput(r,this.positions.END),!0):n!==null&&a?(this.setToBlock(n,this.positions.END),!0):!1}createShadow(e){const t=document.createElement("span");t.classList.add(ao.CSS.shadowCaret),e.insertAdjacentElement("beforeend",t)}restoreCaret(e){const t=e.querySelector(`.${ao.CSS.shadowCaret}`);if(!t)return;new P().expandToTag(t);const n=document.createRange();n.selectNode(t),n.extractContents()}insertContentAtCaretPosition(e){const t=document.createDocumentFragment(),n=document.createElement("div"),r=P.get(),i=P.range;n.innerHTML=e,Array.from(n.childNodes).forEach(l=>t.appendChild(l)),t.childNodes.length===0&&t.appendChild(new Text);const s=t.lastChild;i.deleteContents(),i.insertNode(t);const a=document.createRange(),c=s.nodeType===Node.TEXT_NODE?s:s.firstChild;c!==null&&c.textContent!==null&&a.setStart(c,c.textContent.length),r.removeAllRanges(),r.addRange(a)}};class wc extends j{constructor(){super(...arguments),this.onMouseUp=()=>{this.listeners.off(document,"mouseover",this.onMouseOver),this.listeners.off(document,"mouseup",this.onMouseUp)},this.onMouseOver=e=>{const{BlockManager:t,BlockSelection:n}=this.Editor;if(e.relatedTarget===null&&e.target===null)return;const r=t.getBlockByChildNode(e.relatedTarget)||this.lastSelectedBlock,i=t.getBlockByChildNode(e.target);if(!(!r||!i)&&i!==r){if(r===this.firstSelectedBlock){P.get().removeAllRanges(),r.selected=!0,i.selected=!0,n.clearCache();return}if(i===this.firstSelectedBlock){r.selected=!1,i.selected=!1,n.clearCache();return}this.Editor.InlineToolbar.close(),this.toggleBlocksSelectedState(r,i),this.lastSelectedBlock=i}}}async prepare(){this.listeners.on(document,"mousedown",e=>{this.enableCrossBlockSelection(e)})}watchSelection(e){if(e.button!==ss.LEFT)return;const{BlockManager:t}=this.Editor;this.firstSelectedBlock=t.getBlock(e.target),this.lastSelectedBlock=this.firstSelectedBlock,this.listeners.on(document,"mouseover",this.onMouseOver),this.listeners.on(document,"mouseup",this.onMouseUp)}get isCrossBlockSelectionStarted(){return!!this.firstSelectedBlock&&!!this.lastSelectedBlock&&this.firstSelectedBlock!==this.lastSelectedBlock}toggleBlockSelectedState(e=!0){const{BlockManager:t,BlockSelection:n}=this.Editor;this.lastSelectedBlock||(this.lastSelectedBlock=this.firstSelectedBlock=t.currentBlock),this.firstSelectedBlock===this.lastSelectedBlock&&(this.firstSelectedBlock.selected=!0,n.clearCache(),P.get().removeAllRanges());const r=t.blocks.indexOf(this.lastSelectedBlock)+(e?1:-1),i=t.blocks[r];i&&(this.lastSelectedBlock.selected!==i.selected?(i.selected=!0,n.clearCache()):(this.lastSelectedBlock.selected=!1,n.clearCache()),this.lastSelectedBlock=i,this.Editor.InlineToolbar.close(),i.holder.scrollIntoView({block:"nearest"}))}clear(e){const{BlockManager:t,BlockSelection:n,Caret:r}=this.Editor,i=t.blocks.indexOf(this.firstSelectedBlock),s=t.blocks.indexOf(this.lastSelectedBlock);if(n.anyBlockSelected&&i>-1&&s>-1&&e&&e instanceof KeyboardEvent)switch(e.keyCode){case N.DOWN:case N.RIGHT:r.setToBlock(t.blocks[Math.max(i,s)],r.positions.END);break;case N.UP:case N.LEFT:r.setToBlock(t.blocks[Math.min(i,s)],r.positions.START);break;default:r.setToBlock(t.blocks[Math.max(i,s)],r.positions.END)}this.firstSelectedBlock=this.lastSelectedBlock=null}enableCrossBlockSelection(e){const{UI:t}=this.Editor;P.isCollapsed||this.Editor.BlockSelection.clearSelection(e),t.nodes.redactor.contains(e.target)?this.watchSelection(e):this.Editor.BlockSelection.clearSelection(e)}toggleBlocksSelectedState(e,t){const{BlockManager:n,BlockSelection:r}=this.Editor,i=n.blocks.indexOf(e),s=n.blocks.indexOf(t),a=e.selected!==t.selected;for(let c=Math.min(i,s);c<=Math.max(i,s);c++){const l=n.blocks[c];l!==this.firstSelectedBlock&&l!==(a?e:t)&&(n.blocks[c].selected=!n.blocks[c].selected,r.clearCache())}}}class xc extends j{constructor(){super(...arguments),this.isStartedAtEditor=!1}toggleReadOnly(e){e?this.disableModuleBindings():this.enableModuleBindings()}enableModuleBindings(){const{UI:e}=this.Editor;this.readOnlyMutableListeners.on(e.nodes.holder,"drop",async t=>{await this.processDrop(t)},!0),this.readOnlyMutableListeners.on(e.nodes.holder,"dragstart",()=>{this.processDragStart()}),this.readOnlyMutableListeners.on(e.nodes.holder,"dragover",t=>{this.processDragOver(t)},!0)}disableModuleBindings(){this.readOnlyMutableListeners.clearAll()}async processDrop(e){const{BlockManager:t,Paste:n,Caret:r}=this.Editor;e.preventDefault(),t.blocks.forEach(s=>{s.dropTarget=!1}),P.isAtEditor&&!P.isCollapsed&&this.isStartedAtEditor&&document.execCommand("delete"),this.isStartedAtEditor=!1;const i=t.setCurrentBlockByChildNode(e.target);if(i)this.Editor.Caret.setToBlock(i,r.positions.END);else{const s=t.setCurrentBlockByChildNode(t.lastBlock.holder);this.Editor.Caret.setToBlock(s,r.positions.END)}await n.processDataTransfer(e.dataTransfer,!0)}processDragStart(){P.isAtEditor&&!P.isCollapsed&&(this.isStartedAtEditor=!0),this.Editor.InlineToolbar.close()}processDragOver(e){e.preventDefault()}}const Ec=180,Cc=400;class Sc extends j{constructor({config:e,eventsDispatcher:t}){super({config:e,eventsDispatcher:t}),this.disabled=!1,this.batchingTimeout=null,this.batchingOnChangeQueue=new Map,this.batchTime=Cc,this.mutationObserver=new MutationObserver(n=>{this.redactorChanged(n)}),this.eventsDispatcher.on(Tr,n=>{this.particularBlockChanged(n.event)}),this.eventsDispatcher.on(Br,()=>{this.disable()}),this.eventsDispatcher.on(Or,()=>{this.enable()})}enable(){this.mutationObserver.observe(this.Editor.UI.nodes.redactor,{childList:!0,subtree:!0,characterData:!0,attributes:!0}),this.disabled=!1}disable(){this.mutationObserver.disconnect(),this.disabled=!0}particularBlockChanged(e){this.disabled||!U(this.config.onChange)||(this.batchingOnChangeQueue.set(`block:${e.detail.target.id}:event:${e.type}`,e),this.batchingTimeout&&clearTimeout(this.batchingTimeout),this.batchingTimeout=setTimeout(()=>{let t;this.batchingOnChangeQueue.size===1?t=this.batchingOnChangeQueue.values().next().value:t=Array.from(this.batchingOnChangeQueue.values()),this.config.onChange&&this.config.onChange(this.Editor.API.methods,t),this.batchingOnChangeQueue.clear()},this.batchTime))}redactorChanged(e){this.eventsDispatcher.emit(oo,{mutations:e})}}const wi=class xi extends j{constructor(){super(...arguments),this.MIME_TYPE="application/x-editor-js",this.toolsTags={},this.tagsByTool={},this.toolsPatterns=[],this.toolsFiles={},this.exceptionList=[],this.processTool=e=>{try{const t=e.create({},{},!1);if(e.pasteConfig===!1){this.exceptionList.push(e.name);return}if(!U(t.onPaste))return;this.getTagsConfig(e),this.getFilesConfig(e),this.getPatternsConfig(e)}catch(t){D(`Paste handling for «${e.name}» Tool hasn't been set up because of the error`,"warn",t)}},this.handlePasteEvent=async e=>{const{BlockManager:t,Toolbar:n}=this.Editor,r=t.setCurrentBlockByChildNode(e.target);!r||this.isNativeBehaviour(e.target)&&!e.clipboardData.types.includes("Files")||r&&this.exceptionList.includes(r.name)||(e.preventDefault(),this.processDataTransfer(e.clipboardData),n.close())}}async prepare(){this.processTools()}toggleReadOnly(e){e?this.unsetCallback():this.setCallback()}async processDataTransfer(e,t=!1){const{Tools:n}=this.Editor,r=e.types;if((r.includes?r.includes("Files"):r.contains("Files"))&&!te(this.toolsFiles)){await this.processFiles(e.files);return}const i=e.getData(this.MIME_TYPE),s=e.getData("text/plain");let a=e.getData("text/html");if(i)try{this.insertEditorJSData(JSON.parse(i));return}catch{}t&&s.trim()&&a.trim()&&(a="<p>"+(a.trim()?a:s)+"</p>");const c=Object.keys(this.toolsTags).reduce((u,p)=>(u[p.toLowerCase()]=this.toolsTags[p].sanitizationConfig??{},u),{}),l=Object.assign({},c,n.getAllInlineToolsSanitizeConfig(),{br:{}}),d=ae(a,l);!d.trim()||d.trim()===s||!S.isHTMLString(d)?await this.processText(s):await this.processText(d,!0)}async processText(e,t=!1){const{Caret:n,BlockManager:r}=this.Editor,i=t?this.processHTML(e):this.processPlain(e);if(!i.length)return;if(i.length===1){i[0].isBlock?this.processSingleBlock(i.pop()):this.processInlinePaste(i.pop());return}const s=r.currentBlock&&r.currentBlock.tool.isDefault&&r.currentBlock.isEmpty;i.map(async(a,c)=>this.insertBlock(a,c===0&&s)),r.currentBlock&&n.setToBlock(r.currentBlock,n.positions.END)}setCallback(){this.listeners.on(this.Editor.UI.nodes.holder,"paste",this.handlePasteEvent)}unsetCallback(){this.listeners.off(this.Editor.UI.nodes.holder,"paste",this.handlePasteEvent)}processTools(){const e=this.Editor.Tools.blockTools;Array.from(e.values()).forEach(this.processTool)}collectTagNames(e){return pe(e)?[e]:Y(e)?Object.keys(e):[]}getTagsConfig(e){if(e.pasteConfig===!1)return;const t=e.pasteConfig.tags||[],n=[];t.forEach(r=>{const i=this.collectTagNames(r);n.push(...i),i.forEach(s=>{if(Object.prototype.hasOwnProperty.call(this.toolsTags,s)){D(`Paste handler for «${e.name}» Tool on «${s}» tag is skipped because it is already used by «${this.toolsTags[s].tool.name}» Tool.`,"warn");return}const a=Y(r)?r[s]:null;this.toolsTags[s.toUpperCase()]={tool:e,sanitizationConfig:a}})}),this.tagsByTool[e.name]=n.map(r=>r.toUpperCase())}getFilesConfig(e){if(e.pasteConfig===!1)return;const{files:t={}}=e.pasteConfig;let{extensions:n,mimeTypes:r}=t;!n&&!r||(n&&!Array.isArray(n)&&(D(`«extensions» property of the onDrop config for «${e.name}» Tool should be an array`),n=[]),r&&!Array.isArray(r)&&(D(`«mimeTypes» property of the onDrop config for «${e.name}» Tool should be an array`),r=[]),r&&(r=r.filter(i=>us(i)?!0:(D(`MIME type value «${i}» for the «${e.name}» Tool is not a valid MIME type`,"warn"),!1))),this.toolsFiles[e.name]={extensions:n||[],mimeTypes:r||[]})}getPatternsConfig(e){e.pasteConfig===!1||!e.pasteConfig.patterns||te(e.pasteConfig.patterns)||Object.entries(e.pasteConfig.patterns).forEach(([t,n])=>{n instanceof RegExp||D(`Pattern ${n} for «${e.name}» Tool is skipped because it should be a Regexp instance.`,"warn"),this.toolsPatterns.push({key:t,pattern:n,tool:e})})}isNativeBehaviour(e){return S.isNativeInput(e)}async processFiles(e){const{BlockManager:t}=this.Editor;let n;n=await Promise.all(Array.from(e).map(i=>this.processFile(i))),n=n.filter(i=>!!i);const r=t.currentBlock.tool.isDefault&&t.currentBlock.isEmpty;n.forEach((i,s)=>{t.paste(i.type,i.event,s===0&&r)})}async processFile(e){const t=ds(e),n=Object.entries(this.toolsFiles).find(([i,{mimeTypes:s,extensions:a}])=>{const[c,l]=e.type.split("/"),d=a.find(p=>p.toLowerCase()===t.toLowerCase()),u=s.find(p=>{const[g,b]=p.split("/");return g===c&&(b===l||b==="*")});return!!d||!!u});if(!n)return;const[r]=n;return{event:this.composePasteEvent("file",{file:e}),type:r}}processHTML(e){const{Tools:t}=this.Editor,n=S.make("DIV");return n.innerHTML=e,this.getNodes(n).map(r=>{let i,s=t.defaultTool,a=!1;switch(r.nodeType){case Node.DOCUMENT_FRAGMENT_NODE:i=S.make("div"),i.appendChild(r);break;case Node.ELEMENT_NODE:i=r,a=!0,this.toolsTags[i.tagName]&&(s=this.toolsTags[i.tagName].tool);break}const{tags:c}=s.pasteConfig||{tags:[]},l=c.reduce((p,g)=>(this.collectTagNames(g).forEach(b=>{const f=Y(g)?g[b]:null;p[b.toLowerCase()]=f||{}}),p),{}),d=Object.assign({},l,s.baseSanitizeConfig);if(i.tagName.toLowerCase()==="table"){const p=ae(i.outerHTML,d);i=S.make("div",void 0,{innerHTML:p}).firstChild}else i.innerHTML=ae(i.innerHTML,d);const u=this.composePasteEvent("tag",{data:i});return{content:i,isBlock:a,tool:s.name,event:u}}).filter(r=>{const i=S.isEmpty(r.content),s=S.isSingleTag(r.content);return!i||s})}processPlain(e){const{defaultBlock:t}=this.config;if(!e)return[];const n=t;return e.split(/\r?\n/).filter(r=>r.trim()).map(r=>{const i=S.make("div");i.textContent=r;const s=this.composePasteEvent("tag",{data:i});return{content:i,tool:n,isBlock:!1,event:s}})}async processSingleBlock(e){const{Caret:t,BlockManager:n}=this.Editor,{currentBlock:r}=n;if(!r||e.tool!==r.name||!S.containsOnlyInlineElements(e.content.innerHTML)){this.insertBlock(e,(r==null?void 0:r.tool.isDefault)&&r.isEmpty);return}t.insertContentAtCaretPosition(e.content.innerHTML)}async processInlinePaste(e){const{BlockManager:t,Caret:n}=this.Editor,{content:r}=e;if(t.currentBlock&&t.currentBlock.tool.isDefault&&r.textContent.length<xi.PATTERN_PROCESSING_MAX_LENGTH){const i=await this.processPattern(r.textContent);if(i){const s=t.currentBlock&&t.currentBlock.tool.isDefault&&t.currentBlock.isEmpty,a=t.paste(i.tool,i.event,s);n.setToBlock(a,n.positions.END);return}}if(t.currentBlock&&t.currentBlock.currentInput){const i=t.currentBlock.tool.baseSanitizeConfig;document.execCommand("insertHTML",!1,ae(r.innerHTML,i))}else this.insertBlock(e)}async processPattern(e){const t=this.toolsPatterns.find(n=>{const r=n.pattern.exec(e);return r?e===r.shift():!1});return t?{event:this.composePasteEvent("pattern",{key:t.key,data:e}),tool:t.tool.name}:void 0}insertBlock(e,t=!1){const{BlockManager:n,Caret:r}=this.Editor,{currentBlock:i}=n;let s;if(t&&i&&i.isEmpty){s=n.paste(e.tool,e.event,!0),r.setToBlock(s,r.positions.END);return}s=n.paste(e.tool,e.event),r.setToBlock(s,r.positions.END)}insertEditorJSData(e){const{BlockManager:t,Caret:n,Tools:r}=this.Editor;uo(e,i=>r.blockTools.get(i).sanitizeConfig).forEach(({tool:i,data:s},a)=>{let c=!1;a===0&&(c=t.currentBlock&&t.currentBlock.tool.isDefault&&t.currentBlock.isEmpty);const l=t.insert({tool:i,data:s,replace:c});n.setToBlock(l,n.positions.END)})}processElementNode(e,t,n){const r=Object.keys(this.toolsTags),i=e,{tool:s}=this.toolsTags[i.tagName]||{},a=this.tagsByTool[s==null?void 0:s.name]||[],c=r.includes(i.tagName),l=S.blockElements.includes(i.tagName.toLowerCase()),d=Array.from(i.children).some(({tagName:p})=>r.includes(p)&&!a.includes(p)),u=Array.from(i.children).some(({tagName:p})=>S.blockElements.includes(p.toLowerCase()));if(!l&&!c&&!d)return n.appendChild(i),[...t,n];if(c&&!d||l&&!u&&!d)return[...t,n,i]}getNodes(e){const t=Array.from(e.childNodes);let n;const r=(i,s)=>{if(S.isEmpty(s)&&!S.isSingleTag(s))return i;const a=i[i.length-1];let c=new DocumentFragment;switch(a&&S.isFragment(a)&&(c=i.pop()),s.nodeType){case Node.ELEMENT_NODE:if(n=this.processElementNode(s,i,c),n)return n;break;case Node.TEXT_NODE:return c.appendChild(s),[...i,c];default:return[...i,c]}return[...i,...Array.from(s.childNodes).reduce(r,[])]};return t.reduce(r,[])}composePasteEvent(e,t){return new CustomEvent(e,{detail:t})}};wi.PATTERN_PROCESSING_MAX_LENGTH=450;let Tc=wi;class Bc extends j{constructor(){super(...arguments),this.toolsDontSupportReadOnly=[],this.readOnlyEnabled=!1}get isEnabled(){return this.readOnlyEnabled}async prepare(){const{Tools:e}=this.Editor,{blockTools:t}=e,n=[];Array.from(t.entries()).forEach(([r,i])=>{i.isReadOnlySupported||n.push(r)}),this.toolsDontSupportReadOnly=n,this.config.readOnly&&n.length>0&&this.throwCriticalError(),this.toggle(this.config.readOnly,!0)}async toggle(e=!this.readOnlyEnabled,t=!1){e&&this.toolsDontSupportReadOnly.length>0&&this.throwCriticalError();const n=this.readOnlyEnabled;this.readOnlyEnabled=e;for(const i in this.Editor)this.Editor[i].toggleReadOnly&&this.Editor[i].toggleReadOnly(e);if(n===e)return this.readOnlyEnabled;if(t)return this.readOnlyEnabled;this.Editor.ModificationsObserver.disable();const r=await this.Editor.Saver.save();return await this.Editor.BlockManager.clear(),await this.Editor.Renderer.render(r.blocks),this.Editor.ModificationsObserver.enable(),this.readOnlyEnabled}throwCriticalError(){throw new Cr(`To enable read-only mode all connected tools should support it. Tools ${this.toolsDontSupportReadOnly.join(", ")} don't support read-only mode.`)}}let Oc=class at extends j{constructor(){super(...arguments),this.isRectSelectionActivated=!1,this.SCROLL_SPEED=3,this.HEIGHT_OF_SCROLL_ZONE=40,this.BOTTOM_SCROLL_ZONE=1,this.TOP_SCROLL_ZONE=2,this.MAIN_MOUSE_BUTTON=0,this.mousedown=!1,this.isScrolling=!1,this.inScrollZone=null,this.startX=0,this.startY=0,this.mouseX=0,this.mouseY=0,this.stackOfSelected=[],this.listenerIds=[]}static get CSS(){return{overlay:"codex-editor-overlay",overlayContainer:"codex-editor-overlay__container",rect:"codex-editor-overlay__rectangle",topScrollZone:"codex-editor-overlay__scroll-zone--top",bottomScrollZone:"codex-editor-overlay__scroll-zone--bottom"}}prepare(){this.enableModuleBindings()}startSelection(e,t){const n=document.elementFromPoint(e-window.pageXOffset,t-window.pageYOffset);n.closest(`.${this.Editor.Toolbar.CSS.toolbar}`)||(this.Editor.BlockSelection.allBlocksSelected=!1,this.clearSelection(),this.stackOfSelected=[]);const r=[`.${he.CSS.content}`,`.${this.Editor.Toolbar.CSS.toolbar}`,`.${this.Editor.InlineToolbar.CSS.inlineToolbar}`],i=n.closest("."+this.Editor.UI.CSS.editorWrapper),s=r.some(a=>!!n.closest(a));!i||s||(this.mousedown=!0,this.startX=e,this.startY=t)}endSelection(){this.mousedown=!1,this.startX=0,this.startY=0,this.overlayRectangle.style.display="none"}isRectActivated(){return this.isRectSelectionActivated}clearSelection(){this.isRectSelectionActivated=!1}enableModuleBindings(){const{container:e}=this.genHTML();this.listeners.on(e,"mousedown",t=>{this.processMouseDown(t)},!1),this.listeners.on(document.body,"mousemove",Jt(t=>{this.processMouseMove(t)},10),{passive:!0}),this.listeners.on(document.body,"mouseleave",()=>{this.processMouseLeave()}),this.listeners.on(window,"scroll",Jt(t=>{this.processScroll(t)},10),{passive:!0}),this.listeners.on(document.body,"mouseup",()=>{this.processMouseUp()},!1)}processMouseDown(e){e.button===this.MAIN_MOUSE_BUTTON&&(e.target.closest(S.allInputsSelector)!==null||this.startSelection(e.pageX,e.pageY))}processMouseMove(e){this.changingRectangle(e),this.scrollByZones(e.clientY)}processMouseLeave(){this.clearSelection(),this.endSelection()}processScroll(e){this.changingRectangle(e)}processMouseUp(){this.clearSelection(),this.endSelection()}scrollByZones(e){if(this.inScrollZone=null,e<=this.HEIGHT_OF_SCROLL_ZONE&&(this.inScrollZone=this.TOP_SCROLL_ZONE),document.documentElement.clientHeight-e<=this.HEIGHT_OF_SCROLL_ZONE&&(this.inScrollZone=this.BOTTOM_SCROLL_ZONE),!this.inScrollZone){this.isScrolling=!1;return}this.isScrolling||(this.scrollVertical(this.inScrollZone===this.TOP_SCROLL_ZONE?-this.SCROLL_SPEED:this.SCROLL_SPEED),this.isScrolling=!0)}genHTML(){const{UI:e}=this.Editor,t=e.nodes.holder.querySelector("."+e.CSS.editorWrapper),n=S.make("div",at.CSS.overlay,{}),r=S.make("div",at.CSS.overlayContainer,{}),i=S.make("div",at.CSS.rect,{});return r.appendChild(i),n.appendChild(r),t.appendChild(n),this.overlayRectangle=i,{container:t,overlay:n}}scrollVertical(e){if(!(this.inScrollZone&&this.mousedown))return;const t=window.pageYOffset;window.scrollBy(0,e),this.mouseY+=window.pageYOffset-t,setTimeout(()=>{this.scrollVertical(e)},0)}changingRectangle(e){if(!this.mousedown)return;e.pageY!==void 0&&(this.mouseX=e.pageX,this.mouseY=e.pageY);const{rightPos:t,leftPos:n,index:r}=this.genInfoForMouseSelection(),i=this.startX>t&&this.mouseX>t,s=this.startX<n&&this.mouseX<n;this.rectCrossesBlocks=!(i||s),this.isRectSelectionActivated||(this.rectCrossesBlocks=!1,this.isRectSelectionActivated=!0,this.shrinkRectangleToPoint(),this.overlayRectangle.style.display="block"),this.updateRectangleSize(),this.Editor.Toolbar.close(),r!==void 0&&(this.trySelectNextBlock(r),this.inverseSelection(),P.get().removeAllRanges())}shrinkRectangleToPoint(){this.overlayRectangle.style.left=`${this.startX-window.pageXOffset}px`,this.overlayRectangle.style.top=`${this.startY-window.pageYOffset}px`,this.overlayRectangle.style.bottom=`calc(100% - ${this.startY-window.pageYOffset}px`,this.overlayRectangle.style.right=`calc(100% - ${this.startX-window.pageXOffset}px`}inverseSelection(){const e=this.Editor.BlockManager.getBlockByIndex(this.stackOfSelected[0]).selected;if(this.rectCrossesBlocks&&!e)for(const t of this.stackOfSelected)this.Editor.BlockSelection.selectBlockByIndex(t);if(!this.rectCrossesBlocks&&e)for(const t of this.stackOfSelected)this.Editor.BlockSelection.unSelectBlockByIndex(t)}updateRectangleSize(){this.mouseY>=this.startY?(this.overlayRectangle.style.top=`${this.startY-window.pageYOffset}px`,this.overlayRectangle.style.bottom=`calc(100% - ${this.mouseY-window.pageYOffset}px`):(this.overlayRectangle.style.bottom=`calc(100% - ${this.startY-window.pageYOffset}px`,this.overlayRectangle.style.top=`${this.mouseY-window.pageYOffset}px`),this.mouseX>=this.startX?(this.overlayRectangle.style.left=`${this.startX-window.pageXOffset}px`,this.overlayRectangle.style.right=`calc(100% - ${this.mouseX-window.pageXOffset}px`):(this.overlayRectangle.style.right=`calc(100% - ${this.startX-window.pageXOffset}px`,this.overlayRectangle.style.left=`${this.mouseX-window.pageXOffset}px`)}genInfoForMouseSelection(){const e=document.body.offsetWidth/2,t=this.mouseY-window.pageYOffset,n=document.elementFromPoint(e,t),r=this.Editor.BlockManager.getBlockByChildNode(n);let i;r!==void 0&&(i=this.Editor.BlockManager.blocks.findIndex(d=>d.holder===r.holder));const s=this.Editor.BlockManager.lastBlock.holder.querySelector("."+he.CSS.content),a=Number.parseInt(window.getComputedStyle(s).width,10)/2,c=e-a,l=e+a;return{index:i,leftPos:c,rightPos:l}}addBlockInSelection(e){this.rectCrossesBlocks&&this.Editor.BlockSelection.selectBlockByIndex(e),this.stackOfSelected.push(e)}trySelectNextBlock(e){const t=this.stackOfSelected[this.stackOfSelected.length-1]===e,n=this.stackOfSelected.length,r=1,i=-1,s=0;if(t)return;const a=this.stackOfSelected[n-1]-this.stackOfSelected[n-2]>0;let c=s;n>1&&(c=a?r:i);const l=e>this.stackOfSelected[n-1]&&c===r,d=e<this.stackOfSelected[n-1]&&c===i,u=!(l||d||c===s);if(!u&&(e>this.stackOfSelected[n-1]||this.stackOfSelected[n-1]===void 0)){let b=this.stackOfSelected[n-1]+1||e;for(b;b<=e;b++)this.addBlockInSelection(b);return}if(!u&&e<this.stackOfSelected[n-1]){for(let b=this.stackOfSelected[n-1]-1;b>=e;b--)this.addBlockInSelection(b);return}if(!u)return;let p=n-1,g;for(e>this.stackOfSelected[n-1]?g=()=>e>this.stackOfSelected[p]:g=()=>e<this.stackOfSelected[p];g();)this.rectCrossesBlocks&&this.Editor.BlockSelection.unSelectBlockByIndex(this.stackOfSelected[p]),this.stackOfSelected.pop(),p--}};class _c extends j{async render(e){return new Promise(t=>{const{Tools:n,BlockManager:r}=this.Editor;if(e.length===0)r.insert();else{const i=e.map(({type:s,data:a,tunes:c,id:l})=>{n.available.has(s)===!1&&(ee(`Tool «${s}» is not found. Check 'tools' property at the Editor.js config.`,"warn"),a=this.composeStubDataForTool(s,a,l),s=n.stubTool);let d;try{d=r.composeBlock({id:l,tool:s,data:a,tunes:c})}catch(u){D(`Block «${s}» skipped because of plugins error`,"error",{data:a,error:u}),a=this.composeStubDataForTool(s,a,l),s=n.stubTool,d=r.composeBlock({id:l,tool:s,data:a,tunes:c})}return d});r.insertMany(i)}window.requestIdleCallback(()=>{t()},{timeout:2e3})})}composeStubDataForTool(e,t,n){const{Tools:r}=this.Editor;let i=e;if(r.unavailable.has(e)){const s=r.unavailable.get(e).toolbox;s!==void 0&&s[0].title!==void 0&&(i=s[0].title)}return{savedData:{id:n,type:e,data:t},title:i}}}class Ic extends j{async save(){const{BlockManager:e,Tools:t}=this.Editor,n=e.blocks,r=[];try{n.forEach(a=>{r.push(this.getSavedData(a))});const i=await Promise.all(r),s=await uo(i,a=>t.blockTools.get(a).sanitizeConfig);return this.makeOutput(s)}catch(i){ee("Saving failed due to the Error %o","error",i)}}async getSavedData(e){const t=await e.save(),n=t&&await e.validate(t.data);return{...t,isValid:n}}makeOutput(e){const t=[];return e.forEach(({id:n,tool:r,data:i,tunes:s,isValid:a})=>{if(!a){D(`Block «${r}» skipped because saved data is invalid`);return}if(r===this.Editor.Tools.stubTool){t.push(i);return}const c={id:n,type:r,data:i,...!te(s)&&{tunes:s}};t.push(c)}),{time:+new Date,blocks:t,version:"2.31.0-rc.7"}}}(function(){try{if(typeof document<"u"){var o=document.createElement("style");o.appendChild(document.createTextNode(".ce-paragraph{line-height:1.6em;outline:none}.ce-block:only-of-type .ce-paragraph[data-placeholder-active]:empty:before,.ce-block:only-of-type .ce-paragraph[data-placeholder-active][data-empty=true]:before{content:attr(data-placeholder-active)}.ce-paragraph p:first-of-type{margin-top:0}.ce-paragraph p:last-of-type{margin-bottom:0}")),document.head.appendChild(o)}}catch(e){console.error("vite-plugin-css-injected-by-js",e)}})();const Mc='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M8 9V7.2C8 7.08954 8.08954 7 8.2 7L12 7M16 9V7.2C16 7.08954 15.9105 7 15.8 7L12 7M12 7L12 17M12 17H10M12 17H14"/></svg>';function Lc(o){const e=document.createElement("div");e.innerHTML=o.trim();const t=document.createDocumentFragment();return t.append(...Array.from(e.childNodes)),t}/**
 * Base Paragraph Block for the Editor.js.
 * Represents a regular text block
 *
 * <AUTHOR> (<EMAIL>)
 * @copyright CodeX 2018
 * @license The MIT License (MIT)
 */class rn{static get DEFAULT_PLACEHOLDER(){return""}constructor({data:e,config:t,api:n,readOnly:r}){this.api=n,this.readOnly=r,this._CSS={block:this.api.styles.block,wrapper:"ce-paragraph"},this.readOnly||(this.onKeyUp=this.onKeyUp.bind(this)),this._placeholder=t.placeholder?t.placeholder:rn.DEFAULT_PLACEHOLDER,this._data=e??{},this._element=null,this._preserveBlank=t.preserveBlank??!1}onKeyUp(e){if(e.code!=="Backspace"&&e.code!=="Delete"||!this._element)return;const{textContent:t}=this._element;t===""&&(this._element.innerHTML="")}drawView(){const e=document.createElement("DIV");return e.classList.add(this._CSS.wrapper,this._CSS.block),e.contentEditable="false",e.dataset.placeholderActive=this.api.i18n.t(this._placeholder),this._data.text&&(e.innerHTML=this._data.text),this.readOnly||(e.contentEditable="true",e.addEventListener("keyup",this.onKeyUp)),e}render(){return this._element=this.drawView(),this._element}merge(e){if(!this._element)return;this._data.text+=e.text;const t=Lc(e.text);this._element.appendChild(t),this._element.normalize()}validate(e){return!(e.text.trim()===""&&!this._preserveBlank)}save(e){return{text:e.innerHTML}}onPaste(e){const t={text:e.detail.data.innerHTML};this._data=t,window.requestAnimationFrame(()=>{this._element&&(this._element.innerHTML=this._data.text||"")})}static get conversionConfig(){return{export:"text",import:"text"}}static get sanitize(){return{text:{br:!0}}}static get isReadOnlySupported(){return!0}static get pasteConfig(){return{tags:["P"]}}static get toolbox(){return{icon:Mc,title:"Text"}}}class sn{constructor(){this.commandName="bold"}static get sanitize(){return{b:{}}}render(){return{icon:ra,name:"bold",onActivate:()=>{document.execCommand(this.commandName)},isActive:()=>document.queryCommandState(this.commandName)}}get shortcut(){return"CMD+B"}}sn.isInline=!0;sn.title="Bold";class an{constructor(){this.commandName="italic",this.CSS={button:"ce-inline-tool",buttonActive:"ce-inline-tool--active",buttonModifier:"ce-inline-tool--italic"},this.nodes={button:null}}static get sanitize(){return{i:{}}}render(){return this.nodes.button=document.createElement("button"),this.nodes.button.type="button",this.nodes.button.classList.add(this.CSS.button,this.CSS.buttonModifier),this.nodes.button.innerHTML=ua,this.nodes.button}surround(){document.execCommand(this.commandName)}checkState(){const e=document.queryCommandState(this.commandName);return this.nodes.button.classList.toggle(this.CSS.buttonActive,e),e}get shortcut(){return"CMD+I"}}an.isInline=!0;an.title="Italic";class ln{constructor({api:e}){this.commandLink="createLink",this.commandUnlink="unlink",this.ENTER_KEY=13,this.CSS={button:"ce-inline-tool",buttonActive:"ce-inline-tool--active",buttonModifier:"ce-inline-tool--link",buttonUnlink:"ce-inline-tool--unlink",input:"ce-inline-tool-input",inputShowed:"ce-inline-tool-input--showed"},this.nodes={button:null,input:null},this.inputOpened=!1,this.toolbar=e.toolbar,this.inlineToolbar=e.inlineToolbar,this.notifier=e.notifier,this.i18n=e.i18n,this.selection=new P}static get sanitize(){return{a:{href:!0,target:"_blank",rel:"nofollow"}}}render(){return this.nodes.button=document.createElement("button"),this.nodes.button.type="button",this.nodes.button.classList.add(this.CSS.button,this.CSS.buttonModifier),this.nodes.button.innerHTML=or,this.nodes.button}renderActions(){return this.nodes.input=document.createElement("input"),this.nodes.input.placeholder=this.i18n.t("Add a link"),this.nodes.input.enterKeyHint="done",this.nodes.input.classList.add(this.CSS.input),this.nodes.input.addEventListener("keydown",e=>{e.keyCode===this.ENTER_KEY&&this.enterPressed(e)}),this.nodes.input}surround(e){if(e){this.inputOpened?(this.selection.restore(),this.selection.removeFakeBackground()):(this.selection.setFakeBackground(),this.selection.save());const t=this.selection.findParentTag("A");if(t){this.selection.expandToTag(t),this.unlink(),this.closeActions(),this.checkState(),this.toolbar.close();return}}this.toggleActions()}checkState(){const e=this.selection.findParentTag("A");if(e){this.nodes.button.innerHTML=ga,this.nodes.button.classList.add(this.CSS.buttonUnlink),this.nodes.button.classList.add(this.CSS.buttonActive),this.openActions();const t=e.getAttribute("href");this.nodes.input.value=t!=="null"?t:"",this.selection.save()}else this.nodes.button.innerHTML=or,this.nodes.button.classList.remove(this.CSS.buttonUnlink),this.nodes.button.classList.remove(this.CSS.buttonActive);return!!e}clear(){this.closeActions()}get shortcut(){return"CMD+K"}toggleActions(){this.inputOpened?this.closeActions(!1):this.openActions(!0)}openActions(e=!1){this.nodes.input.classList.add(this.CSS.inputShowed),e&&this.nodes.input.focus(),this.inputOpened=!0}closeActions(e=!0){if(this.selection.isFakeBackgroundEnabled){const t=new P;t.save(),this.selection.restore(),this.selection.removeFakeBackground(),t.restore()}this.nodes.input.classList.remove(this.CSS.inputShowed),this.nodes.input.value="",e&&this.selection.clearSaved(),this.inputOpened=!1}enterPressed(e){let t=this.nodes.input.value||"";if(!t.trim()){this.selection.restore(),this.unlink(),e.preventDefault(),this.closeActions();return}if(!this.validateURL(t)){this.notifier.show({message:"Pasted link is not valid.",style:"error"}),D("Incorrect Link pasted","warn",t);return}t=this.prepareLink(t),this.selection.restore(),this.selection.removeFakeBackground(),this.insertLink(t),e.preventDefault(),e.stopPropagation(),e.stopImmediatePropagation(),this.selection.collapseToEnd(),this.inlineToolbar.close()}validateURL(e){return!/\s/.test(e)}prepareLink(e){return e=e.trim(),e=this.addProtocol(e),e}addProtocol(e){if(/^(\w+):(\/\/)?/.test(e))return e;const t=/^\/[^/\s]/.test(e),n=e.substring(0,1)==="#",r=/^\/\/[^/\s]/.test(e);return!t&&!n&&!r&&(e="http://"+e),e}insertLink(e){const t=this.selection.findParentTag("A");t&&this.selection.expandToTag(t),document.execCommand(this.commandLink,!1,e)}unlink(){document.execCommand(this.commandUnlink)}}ln.isInline=!0;ln.title="Link";let Ei=class{constructor({api:e}){this.i18nAPI=e.i18n,this.blocksAPI=e.blocks,this.selectionAPI=e.selection,this.toolsAPI=e.tools,this.caretAPI=e.caret}async render(){const e=P.get(),t=this.blocksAPI.getBlockByElement(e.anchorNode);if(t===void 0)return[];const n=this.toolsAPI.getBlockTools(),r=await Ir(t,n);if(r.length===0)return[];const i=r.reduce((l,d)=>{var u;return(u=d.toolbox)==null||u.forEach(p=>{l.push({icon:p.icon,title:G.t(Q.toolNames,p.title),name:d.name,closeOnActivate:!0,onActivate:async()=>{const g=await this.blocksAPI.convert(t.id,d.name,p.data);this.caretAPI.setToBlock(g,"end")}})}),l},[]),s=await t.getActiveToolboxEntry(),a=s!==void 0?s.icon:Rr,c=!De();return{icon:a,name:"convert-to",hint:{title:this.i18nAPI.t("Convert to")},children:{searchable:c,items:i,onOpen:()=>{c&&(this.selectionAPI.setFakeBackground(),this.selectionAPI.save())},onClose:()=>{c&&(this.selectionAPI.restore(),this.selectionAPI.removeFakeBackground())}}}}};Ei.isInline=!0;let Ci=class{constructor({data:e,api:t}){this.CSS={wrapper:"ce-stub",info:"ce-stub__info",title:"ce-stub__title",subtitle:"ce-stub__subtitle"},this.api=t,this.title=e.title||this.api.i18n.t("Error"),this.subtitle=this.api.i18n.t("The block can not be displayed correctly."),this.savedData=e.savedData,this.wrapper=this.make()}render(){return this.wrapper}save(){return this.savedData}make(){const e=S.make("div",this.CSS.wrapper),t=ma,n=S.make("div",this.CSS.info),r=S.make("div",this.CSS.title,{textContent:this.title}),i=S.make("div",this.CSS.subtitle,{textContent:this.subtitle});return e.innerHTML=t,n.appendChild(r),n.appendChild(i),e.appendChild(n),e}};Ci.isReadOnlySupported=!0;class Pc extends vo{constructor(){super(...arguments),this.type=xe.Inline}get title(){return this.constructable[bt.Title]}create(){return new this.constructable({api:this.api,config:this.settings})}get isReadOnlySupported(){return this.constructable[bt.IsReadOnlySupported]??!1}}class Ac extends vo{constructor(){super(...arguments),this.type=xe.Tune}create(e,t){return new this.constructable({api:this.api,config:this.settings,block:t,data:e})}}let re=class Pe extends Map{get blockTools(){const e=Array.from(this.entries()).filter(([,t])=>t.isBlock());return new Pe(e)}get inlineTools(){const e=Array.from(this.entries()).filter(([,t])=>t.isInline());return new Pe(e)}get blockTunes(){const e=Array.from(this.entries()).filter(([,t])=>t.isTune());return new Pe(e)}get internalTools(){const e=Array.from(this.entries()).filter(([,t])=>t.isInternal);return new Pe(e)}get externalTools(){const e=Array.from(this.entries()).filter(([,t])=>!t.isInternal);return new Pe(e)}};var Nc=Object.defineProperty,jc=Object.getOwnPropertyDescriptor,Si=(o,e,t,n)=>{for(var r=jc(e,t),i=o.length-1,s;i>=0;i--)(s=o[i])&&(r=s(e,t,r)||r);return r&&Nc(e,t,r),r};class cn extends vo{constructor(){super(...arguments),this.type=xe.Block,this.inlineTools=new re,this.tunes=new re}create(e,t,n){return new this.constructable({data:e,block:t,readOnly:n,api:this.api,config:this.settings})}get isReadOnlySupported(){return this.constructable[Le.IsReadOnlySupported]===!0}get isLineBreaksEnabled(){return this.constructable[Le.IsEnabledLineBreaks]}get toolbox(){const e=this.constructable[Le.Toolbox],t=this.config[nt.Toolbox];if(!te(e)&&t!==!1)return t?Array.isArray(e)?Array.isArray(t)?t.map((n,r)=>{const i=e[r];return i?{...i,...n}:n}):[t]:Array.isArray(t)?t:[{...e,...t}]:Array.isArray(e)?e:[e]}get conversionConfig(){return this.constructable[Le.ConversionConfig]}get enabledInlineTools(){return this.config[nt.EnabledInlineTools]||!1}get enabledBlockTunes(){return this.config[nt.EnabledBlockTunes]}get pasteConfig(){return this.constructable[Le.PasteConfig]??{}}get sanitizeConfig(){const e=super.sanitizeConfig,t=this.baseSanitizeConfig;if(te(e))return t;const n={};for(const r in e)if(Object.prototype.hasOwnProperty.call(e,r)){const i=e[r];Y(i)?n[r]=Object.assign({},t,i):n[r]=i}return n}get baseSanitizeConfig(){const e={};return Array.from(this.inlineTools.values()).forEach(t=>Object.assign(e,t.sanitizeConfig)),Array.from(this.tunes.values()).forEach(t=>Object.assign(e,t.sanitizeConfig)),e}}Si([Re],cn.prototype,"sanitizeConfig");Si([Re],cn.prototype,"baseSanitizeConfig");class Rc{constructor(e,t,n){this.api=n,this.config=e,this.editorConfig=t}get(e){const{class:t,isInternal:n=!1,...r}=this.config[e],i=this.getConstructor(t),s=t[io.IsTune];return new i({name:e,constructable:t,config:r,api:this.api.getMethodsForTool(e,s),isDefault:e===this.editorConfig.defaultBlock,defaultPlaceholder:this.editorConfig.placeholder,isInternal:n})}getConstructor(e){switch(!0){case e[bt.IsInline]:return Pc;case e[io.IsTune]:return Ac;default:return cn}}}let Ti=class{constructor({api:e}){this.CSS={animation:"wobble"},this.api=e}render(){return{icon:ia,title:this.api.i18n.t("Move down"),onActivate:()=>this.handleClick(),name:"move-down"}}handleClick(){const e=this.api.blocks.getCurrentBlockIndex(),t=this.api.blocks.getBlockByIndex(e+1);if(!t)throw new Error("Unable to move Block down since it is already the last");const n=t.holder,r=n.getBoundingClientRect();let i=Math.abs(window.innerHeight-n.offsetHeight);r.top<window.innerHeight&&(i=window.scrollY+n.offsetHeight),window.scrollTo(0,i),this.api.blocks.move(e+1),this.api.toolbar.toggleBlockSettings(!0)}};Ti.isTune=!0;let Bi=class{constructor({api:e}){this.api=e}render(){return{icon:ca,title:this.api.i18n.t("Delete"),name:"delete",confirmation:{title:this.api.i18n.t("Click to delete"),onActivate:()=>this.handleClick()}}}handleClick(){this.api.blocks.delete()}};Bi.isTune=!0;let Oi=class{constructor({api:e}){this.CSS={animation:"wobble"},this.api=e}render(){return{icon:la,title:this.api.i18n.t("Move up"),onActivate:()=>this.handleClick(),name:"move-up"}}handleClick(){const e=this.api.blocks.getCurrentBlockIndex(),t=this.api.blocks.getBlockByIndex(e),n=this.api.blocks.getBlockByIndex(e-1);if(e===0||!t||!n)throw new Error("Unable to move Block up since it is already the first");const r=t.holder,i=n.holder,s=r.getBoundingClientRect(),a=i.getBoundingClientRect();let c;a.top>0?c=Math.abs(s.top)-Math.abs(a.top):c=Math.abs(s.top)+a.height,window.scrollBy(0,-1*c),this.api.blocks.move(e-1),this.api.toolbar.toggleBlockSettings(!0)}};Oi.isTune=!0;var Dc=Object.defineProperty,Fc=Object.getOwnPropertyDescriptor,Hc=(o,e,t,n)=>{for(var r=Fc(e,t),i=o.length-1,s;i>=0;i--)(s=o[i])&&(r=s(e,t,r)||r);return r&&Dc(e,t,r),r};let _i=class extends j{constructor(){super(...arguments),this.stubTool="stub",this.toolsAvailable=new re,this.toolsUnavailable=new re}get available(){return this.toolsAvailable}get unavailable(){return this.toolsUnavailable}get inlineTools(){return this.available.inlineTools}get blockTools(){return this.available.blockTools}get blockTunes(){return this.available.blockTunes}get defaultTool(){return this.blockTools.get(this.config.defaultBlock)}get internal(){return this.available.internalTools}async prepare(){if(this.validateTools(),this.config.tools=Qt({},this.internalTools,this.config.tools),!Object.prototype.hasOwnProperty.call(this.config,"tools")||Object.keys(this.config.tools).length===0)throw Error("Can't start without tools");const e=this.prepareConfig();this.factory=new Rc(e,this.config,this.Editor.API);const t=this.getListOfPrepareFunctions(e);if(t.length===0)return Promise.resolve();await cs(t,n=>{this.toolPrepareMethodSuccess(n)},n=>{this.toolPrepareMethodFallback(n)}),this.prepareBlockTools()}getAllInlineToolsSanitizeConfig(){const e={};return Array.from(this.inlineTools.values()).forEach(t=>{Object.assign(e,t.sanitizeConfig)}),e}destroy(){Object.values(this.available).forEach(async e=>{U(e.reset)&&await e.reset()})}get internalTools(){return{convertTo:{class:Ei,isInternal:!0},link:{class:ln,isInternal:!0},bold:{class:sn,isInternal:!0},italic:{class:an,isInternal:!0},paragraph:{class:rn,inlineToolbar:!0,isInternal:!0},stub:{class:Ci,isInternal:!0},moveUp:{class:Oi,isInternal:!0},delete:{class:Bi,isInternal:!0},moveDown:{class:Ti,isInternal:!0}}}toolPrepareMethodSuccess(e){const t=this.factory.get(e.toolName);if(t.isInline()){const n=["render"].filter(r=>!t.create()[r]);if(n.length){D(`Incorrect Inline Tool: ${t.name}. Some of required methods is not implemented %o`,"warn",n),this.toolsUnavailable.set(t.name,t);return}}this.toolsAvailable.set(t.name,t)}toolPrepareMethodFallback(e){this.toolsUnavailable.set(e.toolName,this.factory.get(e.toolName))}getListOfPrepareFunctions(e){const t=[];return Object.entries(e).forEach(([n,r])=>{t.push({function:U(r.class.prepare)?r.class.prepare:()=>{},data:{toolName:n,config:r.config}})}),t}prepareBlockTools(){Array.from(this.blockTools.values()).forEach(e=>{this.assignInlineToolsToBlockTool(e),this.assignBlockTunesToBlockTool(e)})}assignInlineToolsToBlockTool(e){if(this.config.inlineToolbar!==!1){if(e.enabledInlineTools===!0){e.inlineTools=new re(Array.isArray(this.config.inlineToolbar)?this.config.inlineToolbar.map(t=>[t,this.inlineTools.get(t)]):Array.from(this.inlineTools.entries()));return}Array.isArray(e.enabledInlineTools)&&(e.inlineTools=new re(["convertTo",...e.enabledInlineTools].map(t=>[t,this.inlineTools.get(t)])))}}assignBlockTunesToBlockTool(e){if(e.enabledBlockTunes!==!1){if(Array.isArray(e.enabledBlockTunes)){const t=new re(e.enabledBlockTunes.map(n=>[n,this.blockTunes.get(n)]));e.tunes=new re([...t,...this.blockTunes.internalTools]);return}if(Array.isArray(this.config.tunes)){const t=new re(this.config.tunes.map(n=>[n,this.blockTunes.get(n)]));e.tunes=new re([...t,...this.blockTunes.internalTools]);return}e.tunes=this.blockTunes.internalTools}}validateTools(){for(const e in this.config.tools)if(Object.prototype.hasOwnProperty.call(this.config.tools,e)){if(e in this.internalTools)return;const t=this.config.tools[e];if(!U(t)&&!U(t.class))throw Error(`Tool «${e}» must be a constructor function or an object with function in the «class» property`)}}prepareConfig(){const e={};for(const t in this.config.tools)Y(this.config.tools[t])?e[t]=this.config.tools[t]:e[t]={class:this.config.tools[t]};return e}};Hc([Re],_i.prototype,"getAllInlineToolsSanitizeConfig");const $c=`:root{--selectionColor: #e1f2ff;--inlineSelectionColor: #d4ecff;--bg-light: #eff2f5;--grayText: #707684;--color-dark: #1D202B;--color-active-icon: #388AE5;--color-gray-border: rgba(201, 201, 204, .48);--content-width: 650px;--narrow-mode-right-padding: 50px;--toolbox-buttons-size: 26px;--toolbox-buttons-size--mobile: 36px;--icon-size: 20px;--icon-size--mobile: 28px;--block-padding-vertical: .4em;--color-line-gray: #EFF0F1 }.codex-editor{position:relative;-webkit-box-sizing:border-box;box-sizing:border-box;z-index:1}.codex-editor .hide{display:none}.codex-editor__redactor [contenteditable]:empty:after{content:"\\feff"}@media (min-width: 651px){.codex-editor--narrow .codex-editor__redactor{margin-right:50px}}@media (min-width: 651px){.codex-editor--narrow.codex-editor--rtl .codex-editor__redactor{margin-left:50px;margin-right:0}}@media (min-width: 651px){.codex-editor--narrow .ce-toolbar__actions{right:-5px}}.codex-editor-copyable{position:absolute;height:1px;width:1px;top:-400%;opacity:.001}.codex-editor-overlay{position:fixed;top:0;left:0;right:0;bottom:0;z-index:999;pointer-events:none;overflow:hidden}.codex-editor-overlay__container{position:relative;pointer-events:auto;z-index:0}.codex-editor-overlay__rectangle{position:absolute;pointer-events:none;background-color:#2eaadc33;border:1px solid transparent}.codex-editor svg{max-height:100%}.codex-editor path{stroke:currentColor}.codex-editor ::-moz-selection{background-color:#d4ecff}.codex-editor ::selection{background-color:#d4ecff}.codex-editor--toolbox-opened [contentEditable=true][data-placeholder]:focus:before{opacity:0!important}.ce-scroll-locked{overflow:hidden}.ce-scroll-locked--hard{overflow:hidden;top:calc(-1 * var(--window-scroll-offset));position:fixed;width:100%}.ce-toolbar{position:absolute;left:0;right:0;top:0;-webkit-transition:opacity .1s ease;transition:opacity .1s ease;will-change:opacity,top;display:none}.ce-toolbar--opened{display:block}.ce-toolbar__content{max-width:650px;margin:0 auto;position:relative}.ce-toolbar__plus{color:#1d202b;cursor:pointer;width:26px;height:26px;border-radius:7px;display:-webkit-inline-box;display:-ms-inline-flexbox;display:inline-flex;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-ms-flex-negative:0;flex-shrink:0}@media (max-width: 650px){.ce-toolbar__plus{width:36px;height:36px}}@media (hover: hover){.ce-toolbar__plus:hover{background-color:#eff2f5}}.ce-toolbar__plus--active{background-color:#eff2f5;-webkit-animation:bounceIn .75s 1;animation:bounceIn .75s 1;-webkit-animation-fill-mode:forwards;animation-fill-mode:forwards}.ce-toolbar__plus-shortcut{opacity:.6;word-spacing:-2px;margin-top:5px}@media (max-width: 650px){.ce-toolbar__plus{position:absolute;background-color:#fff;border:1px solid #E8E8EB;-webkit-box-shadow:0 3px 15px -3px rgba(13,20,33,.13);box-shadow:0 3px 15px -3px #0d142121;border-radius:6px;z-index:2;position:static}.ce-toolbar__plus--left-oriented:before{left:15px;margin-left:0}.ce-toolbar__plus--right-oriented:before{left:auto;right:15px;margin-left:0}}.ce-toolbar__actions{position:absolute;right:100%;opacity:0;display:-webkit-box;display:-ms-flexbox;display:flex;padding-right:5px}.ce-toolbar__actions--opened{opacity:1}@media (max-width: 650px){.ce-toolbar__actions{right:auto}}.ce-toolbar__settings-btn{color:#1d202b;width:26px;height:26px;border-radius:7px;display:-webkit-inline-box;display:-ms-inline-flexbox;display:inline-flex;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;margin-left:3px;cursor:pointer;user-select:none}@media (max-width: 650px){.ce-toolbar__settings-btn{width:36px;height:36px}}@media (hover: hover){.ce-toolbar__settings-btn:hover{background-color:#eff2f5}}.ce-toolbar__settings-btn--active{background-color:#eff2f5;-webkit-animation:bounceIn .75s 1;animation:bounceIn .75s 1;-webkit-animation-fill-mode:forwards;animation-fill-mode:forwards}@media (min-width: 651px){.ce-toolbar__settings-btn{width:24px}}.ce-toolbar__settings-btn--hidden{display:none}@media (max-width: 650px){.ce-toolbar__settings-btn{position:absolute;background-color:#fff;border:1px solid #E8E8EB;-webkit-box-shadow:0 3px 15px -3px rgba(13,20,33,.13);box-shadow:0 3px 15px -3px #0d142121;border-radius:6px;z-index:2;position:static}.ce-toolbar__settings-btn--left-oriented:before{left:15px;margin-left:0}.ce-toolbar__settings-btn--right-oriented:before{left:auto;right:15px;margin-left:0}}.ce-toolbar__plus svg,.ce-toolbar__settings-btn svg{width:24px;height:24px}@media (min-width: 651px){.codex-editor--narrow .ce-toolbar__plus{left:5px}}@media (min-width: 651px){.codex-editor--narrow .ce-toolbox .ce-popover{right:0;left:auto;left:initial}}.ce-inline-toolbar{--y-offset: 8px;--color-background-icon-active: rgba(56, 138, 229, .1);--color-text-icon-active: #388AE5;--color-text-primary: black;position:absolute;visibility:hidden;-webkit-transition:opacity .25s ease;transition:opacity .25s ease;will-change:opacity,left,top;top:0;left:0;z-index:3;opacity:1;visibility:visible}.ce-inline-toolbar [hidden]{display:none!important}.ce-inline-toolbar__toggler-and-button-wrapper{display:-webkit-box;display:-ms-flexbox;display:flex;width:100%;padding:0 6px}.ce-inline-toolbar__buttons{display:-webkit-box;display:-ms-flexbox;display:flex}.ce-inline-toolbar__dropdown{display:-webkit-box;display:-ms-flexbox;display:flex;padding:6px;margin:0 6px 0 -6px;-webkit-box-align:center;-ms-flex-align:center;align-items:center;cursor:pointer;border-right:1px solid rgba(201,201,204,.48);-webkit-box-sizing:border-box;box-sizing:border-box}@media (hover: hover){.ce-inline-toolbar__dropdown:hover{background:#eff2f5}}.ce-inline-toolbar__dropdown--hidden{display:none}.ce-inline-toolbar__dropdown-content,.ce-inline-toolbar__dropdown-arrow{display:-webkit-box;display:-ms-flexbox;display:flex}.ce-inline-toolbar__dropdown-content svg,.ce-inline-toolbar__dropdown-arrow svg{width:20px;height:20px}.ce-inline-toolbar__shortcut{opacity:.6;word-spacing:-3px;margin-top:3px}.ce-inline-tool{color:var(--color-text-primary);display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;border:0;border-radius:4px;line-height:normal;height:100%;padding:0;width:28px;background-color:transparent;cursor:pointer}@media (max-width: 650px){.ce-inline-tool{width:36px;height:36px}}@media (hover: hover){.ce-inline-tool:hover{background-color:#f8f8f8}}.ce-inline-tool svg{display:block;width:20px;height:20px}@media (max-width: 650px){.ce-inline-tool svg{width:28px;height:28px}}.ce-inline-tool--link .icon--unlink,.ce-inline-tool--unlink .icon--link{display:none}.ce-inline-tool--unlink .icon--unlink{display:inline-block;margin-bottom:-1px}.ce-inline-tool-input{background:#F8F8F8;border:1px solid rgba(226,226,229,.2);border-radius:6px;padding:4px 8px;font-size:14px;line-height:22px;outline:none;margin:0;width:100%;-webkit-box-sizing:border-box;box-sizing:border-box;display:none;font-weight:500;-webkit-appearance:none;font-family:inherit}@media (max-width: 650px){.ce-inline-tool-input{font-size:15px;font-weight:500}}.ce-inline-tool-input::-webkit-input-placeholder{color:#707684}.ce-inline-tool-input::-moz-placeholder{color:#707684}.ce-inline-tool-input:-ms-input-placeholder{color:#707684}.ce-inline-tool-input::-ms-input-placeholder{color:#707684}.ce-inline-tool-input::placeholder{color:#707684}.ce-inline-tool-input--showed{display:block}.ce-inline-tool--active{background:var(--color-background-icon-active);color:var(--color-text-icon-active)}@-webkit-keyframes fade-in{0%{opacity:0}to{opacity:1}}@keyframes fade-in{0%{opacity:0}to{opacity:1}}.ce-block{-webkit-animation:fade-in .3s ease;animation:fade-in .3s ease;-webkit-animation-fill-mode:none;animation-fill-mode:none;-webkit-animation-fill-mode:initial;animation-fill-mode:initial}.ce-block:first-of-type{margin-top:0}.ce-block--selected .ce-block__content{background:#e1f2ff}.ce-block--selected .ce-block__content [contenteditable]{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.ce-block--selected .ce-block__content img,.ce-block--selected .ce-block__content .ce-stub{opacity:.55}.ce-block--stretched .ce-block__content{max-width:none}.ce-block__content{position:relative;max-width:650px;margin:0 auto;-webkit-transition:background-color .15s ease;transition:background-color .15s ease}.ce-block--drop-target .ce-block__content:before{content:"";position:absolute;top:100%;left:-20px;margin-top:-1px;height:8px;width:8px;border:solid #388AE5;border-width:1px 1px 0 0;-webkit-transform-origin:right;transform-origin:right;-webkit-transform:rotate(45deg);transform:rotate(45deg)}.ce-block--drop-target .ce-block__content:after{content:"";position:absolute;top:100%;height:1px;width:100%;color:#388ae5;background:repeating-linear-gradient(90deg,#388AE5,#388AE5 1px,#fff 1px,#fff 6px)}.ce-block a{cursor:pointer;-webkit-text-decoration:underline;text-decoration:underline}.ce-block b{font-weight:700}.ce-block i{font-style:italic}@-webkit-keyframes bounceIn{0%,20%,40%,60%,80%,to{-webkit-animation-timing-function:cubic-bezier(.215,.61,.355,1);animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{-webkit-transform:scale3d(.9,.9,.9);transform:scale3d(.9,.9,.9)}20%{-webkit-transform:scale3d(1.03,1.03,1.03);transform:scale3d(1.03,1.03,1.03)}60%{-webkit-transform:scale3d(1,1,1);transform:scaleZ(1)}}@keyframes bounceIn{0%,20%,40%,60%,80%,to{-webkit-animation-timing-function:cubic-bezier(.215,.61,.355,1);animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{-webkit-transform:scale3d(.9,.9,.9);transform:scale3d(.9,.9,.9)}20%{-webkit-transform:scale3d(1.03,1.03,1.03);transform:scale3d(1.03,1.03,1.03)}60%{-webkit-transform:scale3d(1,1,1);transform:scaleZ(1)}}@-webkit-keyframes selectionBounce{0%,20%,40%,60%,80%,to{-webkit-animation-timing-function:cubic-bezier(.215,.61,.355,1);animation-timing-function:cubic-bezier(.215,.61,.355,1)}50%{-webkit-transform:scale3d(1.01,1.01,1.01);transform:scale3d(1.01,1.01,1.01)}70%{-webkit-transform:scale3d(1,1,1);transform:scaleZ(1)}}@keyframes selectionBounce{0%,20%,40%,60%,80%,to{-webkit-animation-timing-function:cubic-bezier(.215,.61,.355,1);animation-timing-function:cubic-bezier(.215,.61,.355,1)}50%{-webkit-transform:scale3d(1.01,1.01,1.01);transform:scale3d(1.01,1.01,1.01)}70%{-webkit-transform:scale3d(1,1,1);transform:scaleZ(1)}}@-webkit-keyframes buttonClicked{0%,20%,40%,60%,80%,to{-webkit-animation-timing-function:cubic-bezier(.215,.61,.355,1);animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{-webkit-transform:scale3d(.95,.95,.95);transform:scale3d(.95,.95,.95)}60%{-webkit-transform:scale3d(1.02,1.02,1.02);transform:scale3d(1.02,1.02,1.02)}80%{-webkit-transform:scale3d(1,1,1);transform:scaleZ(1)}}@keyframes buttonClicked{0%,20%,40%,60%,80%,to{-webkit-animation-timing-function:cubic-bezier(.215,.61,.355,1);animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{-webkit-transform:scale3d(.95,.95,.95);transform:scale3d(.95,.95,.95)}60%{-webkit-transform:scale3d(1.02,1.02,1.02);transform:scale3d(1.02,1.02,1.02)}80%{-webkit-transform:scale3d(1,1,1);transform:scaleZ(1)}}.cdx-block{padding:.4em 0}.cdx-block::-webkit-input-placeholder{line-height:normal!important}.cdx-input{border:1px solid rgba(201,201,204,.48);-webkit-box-shadow:inset 0 1px 2px 0 rgba(35,44,72,.06);box-shadow:inset 0 1px 2px #232c480f;border-radius:3px;padding:10px 12px;outline:none;width:100%;-webkit-box-sizing:border-box;box-sizing:border-box}.cdx-input[data-placeholder]:before{position:static!important}.cdx-input[data-placeholder]:before{display:inline-block;width:0;white-space:nowrap;pointer-events:none}.cdx-settings-button{display:-webkit-inline-box;display:-ms-inline-flexbox;display:inline-flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center;border-radius:3px;cursor:pointer;border:0;outline:none;background-color:transparent;vertical-align:bottom;color:inherit;margin:0;min-width:26px;min-height:26px}.cdx-settings-button--focused{background:rgba(34,186,255,.08)!important}.cdx-settings-button--focused{-webkit-box-shadow:inset 0 0 0px 1px rgba(7,161,227,.08);box-shadow:inset 0 0 0 1px #07a1e314}.cdx-settings-button--focused-animated{-webkit-animation-name:buttonClicked;animation-name:buttonClicked;-webkit-animation-duration:.25s;animation-duration:.25s}.cdx-settings-button--active{color:#388ae5}.cdx-settings-button svg{width:auto;height:auto}@media (max-width: 650px){.cdx-settings-button svg{width:28px;height:28px}}@media (max-width: 650px){.cdx-settings-button{width:36px;height:36px;border-radius:8px}}@media (hover: hover){.cdx-settings-button:hover{background-color:#eff2f5}}.cdx-loader{position:relative;border:1px solid rgba(201,201,204,.48)}.cdx-loader:before{content:"";position:absolute;left:50%;top:50%;width:18px;height:18px;margin:-11px 0 0 -11px;border:2px solid rgba(201,201,204,.48);border-left-color:#388ae5;border-radius:50%;-webkit-animation:cdxRotation 1.2s infinite linear;animation:cdxRotation 1.2s infinite linear}@-webkit-keyframes cdxRotation{0%{-webkit-transform:rotate(0deg);transform:rotate(0)}to{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes cdxRotation{0%{-webkit-transform:rotate(0deg);transform:rotate(0)}to{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}.cdx-button{padding:13px;border-radius:3px;border:1px solid rgba(201,201,204,.48);font-size:14.9px;background:#fff;-webkit-box-shadow:0 2px 2px 0 rgba(18,30,57,.04);box-shadow:0 2px 2px #121e390a;color:#707684;text-align:center;cursor:pointer}@media (hover: hover){.cdx-button:hover{background:#FBFCFE;-webkit-box-shadow:0 1px 3px 0 rgba(18,30,57,.08);box-shadow:0 1px 3px #121e3914}}.cdx-button svg{height:20px;margin-right:.2em;margin-top:-2px}.ce-stub{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center;padding:12px 18px;margin:10px 0;border-radius:10px;background:#eff2f5;border:1px solid #EFF0F1;color:#707684;font-size:14px}.ce-stub svg{width:20px;height:20px}.ce-stub__info{margin-left:14px}.ce-stub__title{font-weight:500;text-transform:capitalize}.codex-editor.codex-editor--rtl{direction:rtl}.codex-editor.codex-editor--rtl .cdx-list{padding-left:0;padding-right:40px}.codex-editor.codex-editor--rtl .ce-toolbar__plus{right:-26px;left:auto}.codex-editor.codex-editor--rtl .ce-toolbar__actions{right:auto;left:-26px}@media (max-width: 650px){.codex-editor.codex-editor--rtl .ce-toolbar__actions{margin-left:0;margin-right:auto;padding-right:0;padding-left:10px}}.codex-editor.codex-editor--rtl .ce-settings{left:5px;right:auto}.codex-editor.codex-editor--rtl .ce-settings:before{right:auto;left:25px}.codex-editor.codex-editor--rtl .ce-settings__button:not(:nth-child(3n+3)){margin-left:3px;margin-right:0}.codex-editor.codex-editor--rtl .ce-conversion-tool__icon{margin-right:0;margin-left:10px}.codex-editor.codex-editor--rtl .ce-inline-toolbar__dropdown{border-right:0px solid transparent;border-left:1px solid rgba(201,201,204,.48);margin:0 -6px 0 6px}.codex-editor.codex-editor--rtl .ce-inline-toolbar__dropdown .icon--toggler-down{margin-left:0;margin-right:4px}@media (min-width: 651px){.codex-editor--narrow.codex-editor--rtl .ce-toolbar__plus{left:0;right:5px}}@media (min-width: 651px){.codex-editor--narrow.codex-editor--rtl .ce-toolbar__actions{left:-5px}}.cdx-search-field{--icon-margin-right: 10px;background:#F8F8F8;border:1px solid rgba(226,226,229,.2);border-radius:6px;padding:2px;display:grid;grid-template-columns:auto auto 1fr;grid-template-rows:auto}.cdx-search-field__icon{width:26px;height:26px;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center;margin-right:var(--icon-margin-right)}.cdx-search-field__icon svg{width:20px;height:20px;color:#707684}.cdx-search-field__input{font-size:14px;outline:none;font-weight:500;font-family:inherit;border:0;background:transparent;margin:0;padding:0;line-height:22px;min-width:calc(100% - 26px - var(--icon-margin-right))}.cdx-search-field__input::-webkit-input-placeholder{color:#707684;font-weight:500}.cdx-search-field__input::-moz-placeholder{color:#707684;font-weight:500}.cdx-search-field__input:-ms-input-placeholder{color:#707684;font-weight:500}.cdx-search-field__input::-ms-input-placeholder{color:#707684;font-weight:500}.cdx-search-field__input::placeholder{color:#707684;font-weight:500}.ce-popover{--border-radius: 6px;--width: 200px;--max-height: 270px;--padding: 6px;--offset-from-target: 8px;--color-border: #EFF0F1;--color-shadow: rgba(13, 20, 33, .1);--color-background: white;--color-text-primary: black;--color-text-secondary: #707684;--color-border-icon: rgba(201, 201, 204, .48);--color-border-icon-disabled: #EFF0F1;--color-text-icon-active: #388AE5;--color-background-icon-active: rgba(56, 138, 229, .1);--color-background-item-focus: rgba(34, 186, 255, .08);--color-shadow-item-focus: rgba(7, 161, 227, .08);--color-background-item-hover: #F8F8F8;--color-background-item-confirm: #E24A4A;--color-background-item-confirm-hover: #CE4343;--popover-top: calc(100% + var(--offset-from-target));--popover-left: 0;--nested-popover-overlap: 4px;--icon-size: 20px;--item-padding: 3px;--item-height: calc(var(--icon-size) + 2 * var(--item-padding))}.ce-popover__container{min-width:var(--width);width:var(--width);max-height:var(--max-height);border-radius:var(--border-radius);overflow:hidden;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-box-shadow:0px 3px 15px -3px var(--color-shadow);box-shadow:0 3px 15px -3px var(--color-shadow);position:absolute;left:var(--popover-left);top:var(--popover-top);background:var(--color-background);display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-direction:column;flex-direction:column;z-index:4;opacity:0;max-height:0;pointer-events:none;padding:0;border:none}.ce-popover--opened>.ce-popover__container{opacity:1;padding:var(--padding);max-height:var(--max-height);pointer-events:auto;-webkit-animation:panelShowing .1s ease;animation:panelShowing .1s ease;border:1px solid var(--color-border)}@media (max-width: 650px){.ce-popover--opened>.ce-popover__container{-webkit-animation:panelShowingMobile .25s ease;animation:panelShowingMobile .25s ease}}.ce-popover--open-top .ce-popover__container{--popover-top: calc(-1 * (var(--offset-from-target) + var(--popover-height)))}.ce-popover--open-left .ce-popover__container{--popover-left: calc(-1 * var(--width) + 100%)}.ce-popover__items{overflow-y:auto;-ms-scroll-chaining:none;overscroll-behavior:contain}@media (max-width: 650px){.ce-popover__overlay{position:fixed;top:0;bottom:0;left:0;right:0;background:#1D202B;z-index:3;opacity:.5;-webkit-transition:opacity .12s ease-in;transition:opacity .12s ease-in;will-change:opacity;visibility:visible}}.ce-popover__overlay--hidden{display:none}@media (max-width: 650px){.ce-popover .ce-popover__container{--offset: 5px;position:fixed;max-width:none;min-width:calc(100% - var(--offset) * 2);left:var(--offset);right:var(--offset);bottom:calc(var(--offset) + env(safe-area-inset-bottom));top:auto;border-radius:10px}}.ce-popover__search{margin-bottom:5px}.ce-popover__nothing-found-message{color:#707684;display:none;cursor:default;padding:3px;font-size:14px;line-height:20px;font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.ce-popover__nothing-found-message--displayed{display:block}.ce-popover--nested .ce-popover__container{--popover-left: calc(var(--nesting-level) * (var(--width) - var(--nested-popover-overlap)));top:calc(var(--trigger-item-top) - var(--nested-popover-overlap));position:absolute}.ce-popover--open-top.ce-popover--nested .ce-popover__container{top:calc(var(--trigger-item-top) - var(--popover-height) + var(--item-height) + var(--offset-from-target) + var(--nested-popover-overlap))}.ce-popover--open-left .ce-popover--nested .ce-popover__container{--popover-left: calc(-1 * (var(--nesting-level) + 1) * var(--width) + 100%)}.ce-popover-item-separator{padding:4px 3px}.ce-popover-item-separator--hidden{display:none}.ce-popover-item-separator__line{height:1px;background:var(--color-border);width:100%}.ce-popover-item-html--hidden{display:none}.ce-popover-item{--border-radius: 6px;border-radius:var(--border-radius);display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center;padding:var(--item-padding);color:var(--color-text-primary);-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;border:none;background:transparent}@media (max-width: 650px){.ce-popover-item{padding:4px}}.ce-popover-item:not(:last-of-type){margin-bottom:1px}.ce-popover-item__icon{width:26px;height:26px;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center}.ce-popover-item__icon svg{width:20px;height:20px}@media (max-width: 650px){.ce-popover-item__icon{width:36px;height:36px;border-radius:8px}.ce-popover-item__icon svg{width:28px;height:28px}}.ce-popover-item__icon--tool{margin-right:4px}.ce-popover-item__title{font-size:14px;line-height:20px;font-weight:500;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;margin-right:auto}@media (max-width: 650px){.ce-popover-item__title{font-size:16px}}.ce-popover-item__secondary-title{color:var(--color-text-secondary);font-size:12px;white-space:nowrap;letter-spacing:-.1em;padding-right:5px;opacity:.6}@media (max-width: 650px){.ce-popover-item__secondary-title{display:none}}.ce-popover-item--active{background:var(--color-background-icon-active);color:var(--color-text-icon-active)}.ce-popover-item--disabled{color:var(--color-text-secondary);cursor:default;pointer-events:none}.ce-popover-item--focused:not(.ce-popover-item--no-focus){background:var(--color-background-item-focus)!important}.ce-popover-item--hidden{display:none}@media (hover: hover){.ce-popover-item:hover{cursor:pointer}.ce-popover-item:hover:not(.ce-popover-item--no-hover){background-color:var(--color-background-item-hover)}}.ce-popover-item--confirmation{background:var(--color-background-item-confirm)}.ce-popover-item--confirmation .ce-popover-item__title,.ce-popover-item--confirmation .ce-popover-item__icon{color:#fff}@media (hover: hover){.ce-popover-item--confirmation:not(.ce-popover-item--no-hover):hover{background:var(--color-background-item-confirm-hover)}}.ce-popover-item--confirmation:not(.ce-popover-item--no-focus).ce-popover-item--focused{background:var(--color-background-item-confirm-hover)!important}@-webkit-keyframes panelShowing{0%{opacity:0;-webkit-transform:translateY(-8px) scale(.9);transform:translateY(-8px) scale(.9)}70%{opacity:1;-webkit-transform:translateY(2px);transform:translateY(2px)}to{-webkit-transform:translateY(0);transform:translateY(0)}}@keyframes panelShowing{0%{opacity:0;-webkit-transform:translateY(-8px) scale(.9);transform:translateY(-8px) scale(.9)}70%{opacity:1;-webkit-transform:translateY(2px);transform:translateY(2px)}to{-webkit-transform:translateY(0);transform:translateY(0)}}@-webkit-keyframes panelShowingMobile{0%{opacity:0;-webkit-transform:translateY(14px) scale(.98);transform:translateY(14px) scale(.98)}70%{opacity:1;-webkit-transform:translateY(-4px);transform:translateY(-4px)}to{-webkit-transform:translateY(0);transform:translateY(0)}}@keyframes panelShowingMobile{0%{opacity:0;-webkit-transform:translateY(14px) scale(.98);transform:translateY(14px) scale(.98)}70%{opacity:1;-webkit-transform:translateY(-4px);transform:translateY(-4px)}to{-webkit-transform:translateY(0);transform:translateY(0)}}.wobble{-webkit-animation-name:wobble;animation-name:wobble;-webkit-animation-duration:.4s;animation-duration:.4s}@-webkit-keyframes wobble{0%{-webkit-transform:translate3d(0,0,0);transform:translateZ(0)}15%{-webkit-transform:translate3d(-9%,0,0);transform:translate3d(-9%,0,0)}30%{-webkit-transform:translate3d(9%,0,0);transform:translate3d(9%,0,0)}45%{-webkit-transform:translate3d(-4%,0,0);transform:translate3d(-4%,0,0)}60%{-webkit-transform:translate3d(4%,0,0);transform:translate3d(4%,0,0)}75%{-webkit-transform:translate3d(-1%,0,0);transform:translate3d(-1%,0,0)}to{-webkit-transform:translate3d(0,0,0);transform:translateZ(0)}}@keyframes wobble{0%{-webkit-transform:translate3d(0,0,0);transform:translateZ(0)}15%{-webkit-transform:translate3d(-9%,0,0);transform:translate3d(-9%,0,0)}30%{-webkit-transform:translate3d(9%,0,0);transform:translate3d(9%,0,0)}45%{-webkit-transform:translate3d(-4%,0,0);transform:translate3d(-4%,0,0)}60%{-webkit-transform:translate3d(4%,0,0);transform:translate3d(4%,0,0)}75%{-webkit-transform:translate3d(-1%,0,0);transform:translate3d(-1%,0,0)}to{-webkit-transform:translate3d(0,0,0);transform:translateZ(0)}}.ce-popover-header{margin-bottom:8px;margin-top:4px;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center}.ce-popover-header__text{font-size:18px;font-weight:600}.ce-popover-header__back-button{border:0;background:transparent;width:36px;height:36px;color:var(--color-text-primary)}.ce-popover-header__back-button svg{display:block;width:28px;height:28px}.ce-popover--inline{--height: 38px;--height-mobile: 46px;--container-padding: 4px;position:relative}.ce-popover--inline .ce-popover__custom-content{margin-bottom:0}.ce-popover--inline .ce-popover__items{display:-webkit-box;display:-ms-flexbox;display:flex}.ce-popover--inline .ce-popover__container{-webkit-box-orient:horizontal;-webkit-box-direction:normal;-ms-flex-direction:row;flex-direction:row;padding:var(--container-padding);height:var(--height);top:0;min-width:-webkit-max-content;min-width:-moz-max-content;min-width:max-content;width:-webkit-max-content;width:-moz-max-content;width:max-content;-webkit-animation:none;animation:none}@media (max-width: 650px){.ce-popover--inline .ce-popover__container{height:var(--height-mobile);position:absolute}}.ce-popover--inline .ce-popover-item-separator{padding:0 4px}.ce-popover--inline .ce-popover-item-separator__line{height:100%;width:1px}.ce-popover--inline .ce-popover-item{border-radius:4px;padding:4px}.ce-popover--inline .ce-popover-item__icon--tool{-webkit-box-shadow:none;box-shadow:none;background:transparent;margin-right:0}.ce-popover--inline .ce-popover-item__icon{width:auto;width:initial;height:auto;height:initial}.ce-popover--inline .ce-popover-item__icon svg{width:20px;height:20px}@media (max-width: 650px){.ce-popover--inline .ce-popover-item__icon svg{width:28px;height:28px}}.ce-popover--inline .ce-popover-item:not(:last-of-type){margin-bottom:0;margin-bottom:initial}.ce-popover--inline .ce-popover-item-html{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center}.ce-popover--inline .ce-popover-item__icon--chevron-right{-webkit-transform:rotate(90deg);transform:rotate(90deg)}.ce-popover--inline .ce-popover--nested-level-1 .ce-popover__container{--offset: 3px;left:0;top:calc(var(--height) + var(--offset))}@media (max-width: 650px){.ce-popover--inline .ce-popover--nested-level-1 .ce-popover__container{top:calc(var(--height-mobile) + var(--offset))}}.ce-popover--inline .ce-popover--nested .ce-popover__container{min-width:var(--width);width:var(--width);height:-webkit-fit-content;height:-moz-fit-content;height:fit-content;padding:6px;-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-direction:column;flex-direction:column}.ce-popover--inline .ce-popover--nested .ce-popover__items{display:block;width:100%}.ce-popover--inline .ce-popover--nested .ce-popover-item{border-radius:6px;padding:3px}@media (max-width: 650px){.ce-popover--inline .ce-popover--nested .ce-popover-item{padding:4px}}.ce-popover--inline .ce-popover--nested .ce-popover-item__icon--tool{margin-right:4px}.ce-popover--inline .ce-popover--nested .ce-popover-item__icon{width:26px;height:26px}.ce-popover--inline .ce-popover--nested .ce-popover-item-separator{padding:4px 3px}.ce-popover--inline .ce-popover--nested .ce-popover-item-separator__line{width:100%;height:1px}.codex-editor [data-placeholder]:empty:before,.codex-editor [data-placeholder][data-empty=true]:before{pointer-events:none;color:#707684;cursor:text;content:attr(data-placeholder)}.codex-editor [data-placeholder-active]:empty:before,.codex-editor [data-placeholder-active][data-empty=true]:before{pointer-events:none;color:#707684;cursor:text}.codex-editor [data-placeholder-active]:empty:focus:before,.codex-editor [data-placeholder-active][data-empty=true]:focus:before{content:attr(data-placeholder-active)}
`;class Uc extends j{constructor(){super(...arguments),this.isMobile=!1,this.contentRectCache=null,this.resizeDebouncer=Qn(()=>{this.windowResize()},200),this.selectionChangeDebounced=Qn(()=>{this.selectionChanged()},Ec),this.documentTouchedListener=e=>{this.documentTouched(e)}}get CSS(){return{editorWrapper:"codex-editor",editorWrapperNarrow:"codex-editor--narrow",editorZone:"codex-editor__redactor",editorZoneHidden:"codex-editor__redactor--hidden",editorEmpty:"codex-editor--empty",editorRtlFix:"codex-editor--rtl"}}get contentRect(){if(this.contentRectCache!==null)return this.contentRectCache;const e=this.nodes.wrapper.querySelector(`.${he.CSS.content}`);return e?(this.contentRectCache=e.getBoundingClientRect(),this.contentRectCache):{width:650,left:0,right:0}}async prepare(){this.setIsMobile(),this.make(),this.loadStyles()}toggleReadOnly(e){e?this.unbindReadOnlySensitiveListeners():window.requestIdleCallback(()=>{this.bindReadOnlySensitiveListeners()},{timeout:2e3})}checkEmptiness(){const{BlockManager:e}=this.Editor;this.nodes.wrapper.classList.toggle(this.CSS.editorEmpty,e.isEditorEmpty)}get someToolbarOpened(){const{Toolbar:e,BlockSettings:t,InlineToolbar:n}=this.Editor;return!!(t.opened||n.opened||e.toolbox.opened)}get someFlipperButtonFocused(){return this.Editor.Toolbar.toolbox.hasFocus()?!0:Object.entries(this.Editor).filter(([e,t])=>t.flipper instanceof mt).some(([e,t])=>t.flipper.hasFocus())}destroy(){this.nodes.holder.innerHTML="",this.unbindReadOnlyInsensitiveListeners()}closeAllToolbars(){const{Toolbar:e,BlockSettings:t,InlineToolbar:n}=this.Editor;t.close(),n.close(),e.toolbox.close()}setIsMobile(){const e=window.innerWidth<kr;e!==this.isMobile&&this.eventsDispatcher.emit(Ke,{isEnabled:this.isMobile}),this.isMobile=e}make(){this.nodes.holder=S.getHolder(this.config.holder),this.nodes.wrapper=S.make("div",[this.CSS.editorWrapper,...this.isRtl?[this.CSS.editorRtlFix]:[]]),this.nodes.redactor=S.make("div",this.CSS.editorZone),this.nodes.holder.offsetWidth<this.contentRect.width&&this.nodes.wrapper.classList.add(this.CSS.editorWrapperNarrow),this.nodes.redactor.style.paddingBottom=this.config.minHeight+"px",this.nodes.wrapper.appendChild(this.nodes.redactor),this.nodes.holder.appendChild(this.nodes.wrapper),this.bindReadOnlyInsensitiveListeners()}loadStyles(){const e="editor-js-styles";if(S.get(e))return;const t=S.make("style",null,{id:e,textContent:$c.toString()});this.config.style&&!te(this.config.style)&&this.config.style.nonce&&t.setAttribute("nonce",this.config.style.nonce),S.prepend(document.head,t)}bindReadOnlyInsensitiveListeners(){this.listeners.on(document,"selectionchange",this.selectionChangeDebounced),this.listeners.on(window,"resize",this.resizeDebouncer,{passive:!0}),this.listeners.on(this.nodes.redactor,"mousedown",this.documentTouchedListener,{capture:!0,passive:!0}),this.listeners.on(this.nodes.redactor,"touchstart",this.documentTouchedListener,{capture:!0,passive:!0})}unbindReadOnlyInsensitiveListeners(){this.listeners.off(document,"selectionchange",this.selectionChangeDebounced),this.listeners.off(window,"resize",this.resizeDebouncer),this.listeners.off(this.nodes.redactor,"mousedown",this.documentTouchedListener),this.listeners.off(this.nodes.redactor,"touchstart",this.documentTouchedListener)}bindReadOnlySensitiveListeners(){this.readOnlyMutableListeners.on(this.nodes.redactor,"click",e=>{this.redactorClicked(e)},!1),this.readOnlyMutableListeners.on(document,"keydown",e=>{this.documentKeydown(e)},!0),this.readOnlyMutableListeners.on(document,"mousedown",e=>{this.documentClicked(e)},!0),this.watchBlockHoveredEvents(),this.enableInputsEmptyMark()}watchBlockHoveredEvents(){let e;this.readOnlyMutableListeners.on(this.nodes.redactor,"mousemove",Jt(t=>{const n=t.target.closest(".ce-block");this.Editor.BlockSelection.anyBlockSelected||n&&e!==n&&(e=n,this.eventsDispatcher.emit(Kr,{block:this.Editor.BlockManager.getBlockByChildNode(n)}))},20),{passive:!0})}unbindReadOnlySensitiveListeners(){this.readOnlyMutableListeners.clearAll()}windowResize(){this.contentRectCache=null,this.setIsMobile()}documentKeydown(e){switch(e.keyCode){case N.ENTER:this.enterPressed(e);break;case N.BACKSPACE:case N.DELETE:this.backspacePressed(e);break;case N.ESC:this.escapePressed(e);break;default:this.defaultBehaviour(e);break}}defaultBehaviour(e){const{currentBlock:t}=this.Editor.BlockManager,n=e.target.closest(`.${this.CSS.editorWrapper}`),r=e.altKey||e.ctrlKey||e.metaKey||e.shiftKey;if(t!==void 0&&n===null){this.Editor.BlockEvents.keydown(e);return}n||t&&r||(this.Editor.BlockManager.unsetCurrentBlock(),this.Editor.Toolbar.close())}backspacePressed(e){const{BlockManager:t,BlockSelection:n,Caret:r}=this.Editor;if(n.anyBlockSelected&&!P.isSelectionExists){const i=t.removeSelectedBlocks(),s=t.insertDefaultBlockAtIndex(i,!0);r.setToBlock(s,r.positions.START),n.clearSelection(e),e.preventDefault(),e.stopPropagation(),e.stopImmediatePropagation()}}escapePressed(e){this.Editor.BlockSelection.clearSelection(e),this.Editor.Toolbar.toolbox.opened?(this.Editor.Toolbar.toolbox.close(),this.Editor.Caret.setToBlock(this.Editor.BlockManager.currentBlock,this.Editor.Caret.positions.END)):this.Editor.BlockSettings.opened?this.Editor.BlockSettings.close():this.Editor.InlineToolbar.opened?this.Editor.InlineToolbar.close():this.Editor.Toolbar.close()}enterPressed(e){const{BlockManager:t,BlockSelection:n}=this.Editor;if(this.someToolbarOpened)return;const r=t.currentBlockIndex>=0;if(n.anyBlockSelected&&!P.isSelectionExists){n.clearSelection(e),e.preventDefault(),e.stopImmediatePropagation(),e.stopPropagation();return}if(!this.someToolbarOpened&&r&&e.target.tagName==="BODY"){const i=this.Editor.BlockManager.insert();e.preventDefault(),this.Editor.Caret.setToBlock(i),this.Editor.Toolbar.moveAndOpen(i)}this.Editor.BlockSelection.clearSelection(e)}documentClicked(e){var t,n;if(!e.isTrusted)return;const r=e.target;this.nodes.holder.contains(r)||P.isAtEditor||(this.Editor.BlockManager.unsetCurrentBlock(),this.Editor.Toolbar.close());const i=(t=this.Editor.BlockSettings.nodes.wrapper)==null?void 0:t.contains(r),s=(n=this.Editor.Toolbar.nodes.settingsToggler)==null?void 0:n.contains(r),a=i||s;if(this.Editor.BlockSettings.opened&&!a){this.Editor.BlockSettings.close();const c=this.Editor.BlockManager.getBlockByChildNode(r);this.Editor.Toolbar.moveAndOpen(c)}this.Editor.BlockSelection.clearSelection(e)}documentTouched(e){let t=e.target;if(t===this.nodes.redactor){const n=e instanceof MouseEvent?e.clientX:e.touches[0].clientX,r=e instanceof MouseEvent?e.clientY:e.touches[0].clientY;t=document.elementFromPoint(n,r)}try{this.Editor.BlockManager.setCurrentBlockByChildNode(t)}catch{this.Editor.RectangleSelection.isRectActivated()||this.Editor.Caret.setToTheLastBlock()}this.Editor.ReadOnly.isEnabled||this.Editor.Toolbar.moveAndOpen()}redactorClicked(e){if(!P.isCollapsed)return;const t=e.target,n=e.metaKey||e.ctrlKey;if(S.isAnchor(t)&&n){e.stopImmediatePropagation(),e.stopPropagation();const r=t.getAttribute("href"),i=ps(r);gs(i);return}this.processBottomZoneClick(e)}processBottomZoneClick(e){const t=this.Editor.BlockManager.getBlockByIndex(-1),n=S.offset(t.holder).bottom,r=e.pageY,{BlockSelection:i}=this.Editor;if(e.target instanceof Element&&e.target.isEqualNode(this.nodes.redactor)&&!i.anyBlockSelected&&n<r){e.stopImmediatePropagation(),e.stopPropagation();const{BlockManager:s,Caret:a,Toolbar:c}=this.Editor;(!s.lastBlock.tool.isDefault||!s.lastBlock.isEmpty)&&s.insertAtEnd(),a.setToTheLastBlock(),c.moveAndOpen(s.lastBlock)}}selectionChanged(){const{CrossBlockSelection:e,BlockSelection:t}=this.Editor,n=P.anchorElement;if(e.isCrossBlockSelectionStarted&&t.anyBlockSelected&&P.get().removeAllRanges(),!n){P.range||this.Editor.InlineToolbar.close();return}const r=n.closest(`.${he.CSS.content}`);(r===null||r.closest(`.${P.CSS.editorWrapper}`)!==this.nodes.wrapper)&&(this.Editor.InlineToolbar.containsNode(n)||this.Editor.InlineToolbar.close(),n.dataset.inlineToolbar!=="true")||(this.Editor.BlockManager.currentBlock||this.Editor.BlockManager.setCurrentBlockByChildNode(n),this.Editor.InlineToolbar.tryToShow(!0))}enableInputsEmptyMark(){function e(t){const n=t.target;wr(n)}this.readOnlyMutableListeners.on(this.nodes.wrapper,"input",e),this.readOnlyMutableListeners.on(this.nodes.wrapper,"focusin",e),this.readOnlyMutableListeners.on(this.nodes.wrapper,"focusout",e)}}const zc={BlocksAPI:Ts,CaretAPI:Os,EventsAPI:_s,I18nAPI:Is,API:Ms,InlineToolbarAPI:Ls,ListenersAPI:Ps,NotifierAPI:Rs,ReadOnlyAPI:Ds,SanitizerAPI:qs,SaverAPI:Ys,SelectionAPI:Ks,ToolsAPI:Vs,StylesAPI:Xs,ToolbarAPI:Gs,TooltipAPI:ta,UiAPI:oa,BlockSettings:Oa,Toolbar:ja,InlineToolbar:Ra,BlockEvents:gc,BlockManager:bc,BlockSelection:yc,Caret:kc,CrossBlockSelection:wc,DragNDrop:xc,ModificationsObserver:Sc,Paste:Tc,ReadOnly:Bc,RectangleSelection:Oc,Renderer:_c,Saver:Ic,Tools:_i,UI:Uc};class Wc{constructor(e){this.moduleInstances={},this.eventsDispatcher=new Ge;let t,n;this.isReady=new Promise((r,i)=>{t=r,n=i}),Promise.resolve().then(async()=>{this.configuration=e,this.validate(),this.init(),await this.start(),await this.render();const{BlockManager:r,Caret:i,UI:s,ModificationsObserver:a}=this.moduleInstances;s.checkEmptiness(),a.enable(),this.configuration.autofocus===!0&&this.configuration.readOnly!==!0&&i.setToBlock(r.blocks[0],i.positions.START),t()}).catch(r=>{D(`Editor.js is not ready because of ${r}`,"error"),n(r)})}set configuration(e){var t,n;Y(e)?this.config={...e}:this.config={holder:e},eo(!!this.config.holderId,"config.holderId","config.holder"),this.config.holderId&&!this.config.holder&&(this.config.holder=this.config.holderId,this.config.holderId=null),this.config.holder==null&&(this.config.holder="editorjs"),this.config.logLevel||(this.config.logLevel=vr.VERBOSE),as(this.config.logLevel),eo(!!this.config.initialBlock,"config.initialBlock","config.defaultBlock"),this.config.defaultBlock=this.config.defaultBlock||this.config.initialBlock||"paragraph",this.config.minHeight=this.config.minHeight!==void 0?this.config.minHeight:300;const r={type:this.config.defaultBlock,data:{}};this.config.placeholder=this.config.placeholder||!1,this.config.sanitizer=this.config.sanitizer||{p:!0,b:!0,a:!0},this.config.hideToolbar=this.config.hideToolbar?this.config.hideToolbar:!1,this.config.tools=this.config.tools||{},this.config.i18n=this.config.i18n||{},this.config.data=this.config.data||{blocks:[]},this.config.onReady=this.config.onReady||(()=>{}),this.config.onChange=this.config.onChange||(()=>{}),this.config.inlineToolbar=this.config.inlineToolbar!==void 0?this.config.inlineToolbar:!0,(te(this.config.data)||!this.config.data.blocks||this.config.data.blocks.length===0)&&(this.config.data={blocks:[r]}),this.config.readOnly=this.config.readOnly||!1,(t=this.config.i18n)!=null&&t.messages&&G.setDictionary(this.config.i18n.messages),this.config.i18n.direction=((n=this.config.i18n)==null?void 0:n.direction)||"ltr"}get configuration(){return this.config}validate(){const{holderId:e,holder:t}=this.config;if(e&&t)throw Error("«holderId» and «holder» param can't assign at the same time.");if(pe(t)&&!S.get(t))throw Error(`element with ID «${t}» is missing. Pass correct holder's ID.`);if(t&&Y(t)&&!S.isElement(t))throw Error("«holder» value must be an Element node")}init(){this.constructModules(),this.configureModules()}async start(){await["Tools","UI","BlockManager","Paste","BlockSelection","RectangleSelection","CrossBlockSelection","ReadOnly"].reduce((e,t)=>e.then(async()=>{try{await this.moduleInstances[t].prepare()}catch(n){if(n instanceof Cr)throw new Error(n.message);D(`Module ${t} was skipped because of %o`,"warn",n)}}),Promise.resolve())}render(){return this.moduleInstances.Renderer.render(this.config.data.blocks)}constructModules(){Object.entries(zc).forEach(([e,t])=>{try{this.moduleInstances[e]=new t({config:this.configuration,eventsDispatcher:this.eventsDispatcher})}catch(n){D("[constructModules]",`Module ${e} skipped because`,"error",n)}})}configureModules(){for(const e in this.moduleInstances)Object.prototype.hasOwnProperty.call(this.moduleInstances,e)&&(this.moduleInstances[e].state=this.getModulesDiff(e))}getModulesDiff(e){const t={};for(const n in this.moduleInstances)n!==e&&(t[n]=this.moduleInstances[n]);return t}}/**
 * Editor.js
 *
 * @license Apache-2.0
 * @see Editor.js <https://editorjs.io>
 * <AUTHOR> Team <https://codex.so>
 */class qc{static get version(){return"2.31.0-rc.7"}constructor(e){let t=()=>{};Y(e)&&U(e.onReady)&&(t=e.onReady);const n=new Wc(e);this.isReady=n.isReady.then(()=>{this.exportAPI(n),t()})}exportAPI(e){const t=["configuration"],n=()=>{Object.values(e.moduleInstances).forEach(r=>{U(r.destroy)&&r.destroy(),r.listeners.removeAll()}),ea(),e=null;for(const r in this)Object.prototype.hasOwnProperty.call(this,r)&&delete this[r];Object.setPrototypeOf(this,null)};t.forEach(r=>{this[r]=e[r]}),this.destroy=n,Object.setPrototypeOf(this,e.moduleInstances.API.methods),delete this.exportAPI,Object.entries({blocks:{clear:"clear",render:"render"},caret:{focus:"focus"},events:{on:"on",off:"off",emit:"emit"},saver:{save:"save"}}).forEach(([r,i])=>{Object.entries(i).forEach(([s,a])=>{this[a]=e.moduleInstances.API.methods[r][s]})})}}(function(){try{if(typeof document<"u"){var o=document.createElement("style");o.appendChild(document.createTextNode(".ce-header{padding:.6em 0 3px;margin:0;line-height:1.25em;outline:none}.ce-header p,.ce-header div{padding:0!important;margin:0!important}")),document.head.appendChild(o)}}catch(e){console.error("vite-plugin-css-injected-by-js",e)}})();const Yc='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M6 7L6 12M6 17L6 12M6 12L12 12M12 7V12M12 17L12 12"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M19 17V10.2135C19 10.1287 18.9011 10.0824 18.836 10.1367L16 12.5"/></svg>',Kc='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M6 7L6 12M6 17L6 12M6 12L12 12M12 7V12M12 17L12 12"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M16 11C16 10 19 9.5 19 12C19 13.9771 16.0684 13.9997 16.0012 16.8981C15.9999 16.9533 16.0448 17 16.1 17L19.3 17"/></svg>',Vc='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M6 7L6 12M6 17L6 12M6 12L12 12M12 7V12M12 17L12 12"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M16 11C16 10.5 16.8323 10 17.6 10C18.3677 10 19.5 10.311 19.5 11.5C19.5 12.5315 18.7474 12.9022 18.548 12.9823C18.5378 12.9864 18.5395 13.0047 18.5503 13.0063C18.8115 13.0456 20 13.3065 20 14.8C20 16 19.5 17 17.8 17C17.8 17 16 17 16 16.3"/></svg>',Xc='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M6 7L6 12M6 17L6 12M6 12L12 12M12 7V12M12 17L12 12"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M18 10L15.2834 14.8511C15.246 14.9178 15.294 15 15.3704 15C16.8489 15 18.7561 15 20.2 15M19 17C19 15.7187 19 14.8813 19 13.6"/></svg>',Gc='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M6 7L6 12M6 17L6 12M6 12L12 12M12 7V12M12 17L12 12"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M16 15.9C16 15.9 16.3768 17 17.8 17C19.5 17 20 15.6199 20 14.7C20 12.7323 17.6745 12.0486 16.1635 12.9894C16.094 13.0327 16 12.9846 16 12.9027V10.1C16 10.0448 16.0448 10 16.1 10H19.8"/></svg>',Zc='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M6 7L6 12M6 17L6 12M6 12L12 12M12 7V12M12 17L12 12"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M19.5 10C16.5 10.5 16 13.3285 16 15M16 15V15C16 16.1046 16.8954 17 18 17H18.3246C19.3251 17 20.3191 16.3492 20.2522 15.3509C20.0612 12.4958 16 12.6611 16 15Z"/></svg>',Jc='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M9 7L9 12M9 17V12M9 12L15 12M15 7V12M15 17L15 12"/></svg>';/**
 * Header block for the Editor.js.
 *
 * <AUTHOR> (<EMAIL>)
 * @copyright CodeX 2018
 * @license MIT
 * @version 2.0.0
 */let Qc=class{constructor({data:e,config:t,api:n,readOnly:r}){this.api=n,this.readOnly=r,this._settings=t,this._data=this.normalizeData(e),this._element=this.getTag()}get _CSS(){return{block:this.api.styles.block,wrapper:"ce-header"}}isHeaderData(e){return e.text!==void 0}normalizeData(e){const t={text:"",level:this.defaultLevel.number};return this.isHeaderData(e)&&(t.text=e.text||"",e.level!==void 0&&!isNaN(parseInt(e.level.toString()))&&(t.level=parseInt(e.level.toString()))),t}render(){return this._element}renderSettings(){return this.levels.map(e=>({icon:e.svg,label:this.api.i18n.t(`Heading ${e.number}`),onActivate:()=>this.setLevel(e.number),closeOnActivate:!0,isActive:this.currentLevel.number===e.number,render:()=>document.createElement("div")}))}setLevel(e){this.data={level:e,text:this.data.text}}merge(e){this._element.insertAdjacentHTML("beforeend",e.text)}validate(e){return e.text.trim()!==""}save(e){return{text:e.innerHTML,level:this.currentLevel.number}}static get conversionConfig(){return{export:"text",import:"text"}}static get sanitize(){return{level:!1,text:{}}}static get isReadOnlySupported(){return!0}get data(){return this._data.text=this._element.innerHTML,this._data.level=this.currentLevel.number,this._data}set data(e){if(this._data=this.normalizeData(e),e.level!==void 0&&this._element.parentNode){const t=this.getTag();t.innerHTML=this._element.innerHTML,this._element.parentNode.replaceChild(t,this._element),this._element=t}e.text!==void 0&&(this._element.innerHTML=this._data.text||"")}getTag(){const e=document.createElement(this.currentLevel.tag);return e.innerHTML=this._data.text||"",e.classList.add(this._CSS.wrapper),e.contentEditable=this.readOnly?"false":"true",e.dataset.placeholder=this.api.i18n.t(this._settings.placeholder||""),e}get currentLevel(){let e=this.levels.find(t=>t.number===this._data.level);return e||(e=this.defaultLevel),e}get defaultLevel(){if(this._settings.defaultLevel){const e=this.levels.find(t=>t.number===this._settings.defaultLevel);if(e)return e;console.warn("(ง'̀-'́)ง Heading Tool: the default level specified was not found in available levels")}return this.levels[1]}get levels(){const e=[{number:1,tag:"H1",svg:Yc},{number:2,tag:"H2",svg:Kc},{number:3,tag:"H3",svg:Vc},{number:4,tag:"H4",svg:Xc},{number:5,tag:"H5",svg:Gc},{number:6,tag:"H6",svg:Zc}];return this._settings.levels?e.filter(t=>this._settings.levels.includes(t.number)):e}onPaste(e){const t=e.detail;if("data"in t){const n=t.data;let r=this.defaultLevel.number;switch(n.tagName){case"H1":r=1;break;case"H2":r=2;break;case"H3":r=3;break;case"H4":r=4;break;case"H5":r=5;break;case"H6":r=6;break}this._settings.levels&&(r=this._settings.levels.reduce((i,s)=>Math.abs(s-r)<Math.abs(i-r)?s:i)),this.data={level:r,text:n.innerHTML}}}static get pasteConfig(){return{tags:["H1","H2","H3","H4","H5","H6"]}}static get toolbox(){return{icon:Jc,title:"Heading"}}};(function(){try{if(typeof document<"u"){var o=document.createElement("style");o.appendChild(document.createTextNode('.cdx-list{margin:0;padding:0;outline:none;display:grid;counter-reset:item;gap:var(--spacing-s);padding:var(--spacing-xs);--spacing-s: 8px;--spacing-xs: 6px;--list-counter-type: numeric;--radius-border: 5px;--checkbox-background: #fff;--color-border: #C9C9C9;--color-bg-checked: #369FFF;--line-height: 1.45em;--color-bg-checked-hover: #0059AB;--color-tick: #fff;--size-checkbox: 1.2em}.cdx-list__item{line-height:var(--line-height);display:grid;grid-template-columns:auto 1fr;grid-template-rows:auto auto;grid-template-areas:"checkbox content" ". child"}.cdx-list__item-children{display:grid;grid-area:child;gap:var(--spacing-s);padding-top:var(--spacing-s)}.cdx-list__item [contenteditable]{outline:none}.cdx-list__item-content{word-break:break-word;white-space:pre-wrap;grid-area:content;padding-left:var(--spacing-s)}.cdx-list__item:before{counter-increment:item;white-space:nowrap}.cdx-list-ordered .cdx-list__item:before{content:counters(item,".",var(--list-counter-type)) "."}.cdx-list-ordered{counter-reset:item}.cdx-list-unordered .cdx-list__item:before{content:"•"}.cdx-list-checklist .cdx-list__item:before{content:""}.cdx-list__settings .cdx-settings-button{width:50%}.cdx-list__checkbox{padding-top:calc((var(--line-height) - var(--size-checkbox)) / 2);grid-area:checkbox;width:var(--size-checkbox);height:var(--size-checkbox);display:flex;cursor:pointer}.cdx-list__checkbox svg{opacity:0;height:var(--size-checkbox);width:var(--size-checkbox);left:-1px;top:-1px;position:absolute}@media (hover: hover){.cdx-list__checkbox:not(.cdx-list__checkbox--no-hover):hover .cdx-list__checkbox-check svg{opacity:1}}.cdx-list__checkbox--checked{line-height:var(--line-height)}@media (hover: hover){.cdx-list__checkbox--checked:not(.cdx-list__checkbox--checked--no-hover):hover .cdx-checklist__checkbox-check{background:var(--color-bg-checked-hover);border-color:var(--color-bg-checked-hover)}}.cdx-list__checkbox--checked .cdx-list__checkbox-check{background:var(--color-bg-checked);border-color:var(--color-bg-checked)}.cdx-list__checkbox--checked .cdx-list__checkbox-check svg{opacity:1}.cdx-list__checkbox--checked .cdx-list__checkbox-check svg path{stroke:var(--color-tick)}.cdx-list__checkbox--checked .cdx-list__checkbox-check:before{opacity:0;visibility:visible;transform:scale(2.5)}.cdx-list__checkbox-check{cursor:pointer;display:inline-block;position:relative;margin:0 auto;width:var(--size-checkbox);height:var(--size-checkbox);box-sizing:border-box;border-radius:var(--radius-border);border:1px solid var(--color-border);background:var(--checkbox-background)}.cdx-list__checkbox-check:before{content:"";position:absolute;top:0;right:0;bottom:0;left:0;border-radius:100%;background-color:var(--color-bg-checked);visibility:hidden;pointer-events:none;transform:scale(1);transition:transform .4s ease-out,opacity .4s}.cdx-list-start-with-field{background:#F8F8F8;border:1px solid rgba(226,226,229,.2);border-radius:6px;padding:2px;display:grid;grid-template-columns:auto auto 1fr;grid-template-rows:auto}.cdx-list-start-with-field--invalid{background:#FFECED;border:1px solid #E13F3F}.cdx-list-start-with-field--invalid .cdx-list-start-with-field__input{color:#e13f3f}.cdx-list-start-with-field__input{font-size:14px;outline:none;font-weight:500;font-family:inherit;border:0;background:transparent;margin:0;padding:0;line-height:22px;min-width:calc(100% - var(--toolbox-buttons-size) - var(--icon-margin-right))}.cdx-list-start-with-field__input::placeholder{color:var(--grayText);font-weight:500}')),document.head.appendChild(o)}}catch(e){console.error("vite-plugin-css-injected-by-js",e)}})();const ed='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M7 12L10.4884 15.8372C10.5677 15.9245 10.705 15.9245 10.7844 15.8372L17 9"/></svg>',dr='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M9.2 12L11.0586 13.8586C11.1367 13.9367 11.2633 13.9367 11.3414 13.8586L14.7 10.5"/><rect width="14" height="14" x="5" y="5" stroke="currentColor" stroke-width="2" rx="4"/></svg>',ur='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><line x1="9" x2="19" y1="7" y2="7" stroke="currentColor" stroke-linecap="round" stroke-width="2"/><line x1="9" x2="19" y1="12" y2="12" stroke="currentColor" stroke-linecap="round" stroke-width="2"/><line x1="9" x2="19" y1="17" y2="17" stroke="currentColor" stroke-linecap="round" stroke-width="2"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M5.00001 17H4.99002"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M5.00001 12H4.99002"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M5.00001 7H4.99002"/></svg>',hr='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><line x1="12" x2="19" y1="7" y2="7" stroke="currentColor" stroke-linecap="round" stroke-width="2"/><line x1="12" x2="19" y1="12" y2="12" stroke="currentColor" stroke-linecap="round" stroke-width="2"/><line x1="12" x2="19" y1="17" y2="17" stroke="currentColor" stroke-linecap="round" stroke-width="2"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M7.79999 14L7.79999 7.2135C7.79999 7.12872 7.7011 7.0824 7.63597 7.13668L4.79999 9.5"/></svg>',td='<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M10 14.2L10 7.4135C10 7.32872 9.90111 7.28241 9.83598 7.33668L7 9.7" stroke="black" stroke-width="1.6" stroke-linecap="round"/><path d="M13.2087 14.2H13.2" stroke="black" stroke-width="1.6" stroke-linecap="round"/></svg>',od='<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M13.2087 14.2H13.2" stroke="black" stroke-width="1.6" stroke-linecap="round"/><path d="M10 14.2L10 9.5" stroke="black" stroke-width="1.6" stroke-linecap="round"/><path d="M10 7.01L10 7" stroke="black" stroke-width="1.8" stroke-linecap="round"/></svg>',nd='<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M13.2087 14.2H13.2" stroke="black" stroke-width="1.6" stroke-linecap="round"/><path d="M10 14.2L10 7.2" stroke="black" stroke-width="1.6" stroke-linecap="round"/></svg>',rd='<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M16.0087 14.2H16" stroke="black" stroke-width="1.6" stroke-linecap="round"/><path d="M7 14.2L7.78865 12M13 14.2L12.1377 12M7.78865 12C7.78865 12 9.68362 7 10 7C10.3065 7 12.1377 12 12.1377 12M7.78865 12L12.1377 12" stroke="black" stroke-width="1.6" stroke-linecap="round"/></svg>',id='<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M14.2087 14.2H14.2" stroke="black" stroke-width="1.6" stroke-linecap="round"/><path d="M11.5 14.5C11.5 14.5 11 13.281 11 12.5M7 9.5C7 9.5 7.5 8.5 9 8.5C10.5 8.5 11 9.5 11 10.5L11 11.5M11 11.5L11 12.5M11 11.5C11 11.5 7 11 7 13C7 15.3031 11 15 11 12.5" stroke="black" stroke-width="1.6" stroke-linecap="round"/></svg>',sd='<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M8 14.2L8 7.4135C8 7.32872 7.90111 7.28241 7.83598 7.33668L5 9.7" stroke="black" stroke-width="1.6" stroke-linecap="round"/><path d="M14 13L16.4167 10.7778M16.4167 10.7778L14 8.5M16.4167 10.7778H11.6562" stroke="black" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round"/></svg>';var yt=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function ad(o){if(o.__esModule)return o;var e=o.default;if(typeof e=="function"){var t=function n(){return this instanceof n?Reflect.construct(e,arguments,this.constructor):e.apply(this,arguments)};t.prototype=e.prototype}else t={};return Object.defineProperty(t,"__esModule",{value:!0}),Object.keys(o).forEach(function(n){var r=Object.getOwnPropertyDescriptor(o,n);Object.defineProperty(t,n,r.get?r:{enumerable:!0,get:function(){return o[n]}})}),t}var F={},dn={},un={};Object.defineProperty(un,"__esModule",{value:!0});un.allInputsSelector=ld;function ld(){var o=["text","password","email","number","search","tel","url"];return"[contenteditable=true], textarea, input:not([type]), "+o.map(function(e){return'input[type="'.concat(e,'"]')}).join(", ")}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.allInputsSelector=void 0;var e=un;Object.defineProperty(o,"allInputsSelector",{enumerable:!0,get:function(){return e.allInputsSelector}})})(dn);var Te={},hn={};Object.defineProperty(hn,"__esModule",{value:!0});hn.isNativeInput=cd;function cd(o){var e=["INPUT","TEXTAREA"];return o&&o.tagName?e.includes(o.tagName):!1}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.isNativeInput=void 0;var e=hn;Object.defineProperty(o,"isNativeInput",{enumerable:!0,get:function(){return e.isNativeInput}})})(Te);var Ii={},pn={};Object.defineProperty(pn,"__esModule",{value:!0});pn.append=dd;function dd(o,e){Array.isArray(e)?e.forEach(function(t){o.appendChild(t)}):o.appendChild(e)}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.append=void 0;var e=pn;Object.defineProperty(o,"append",{enumerable:!0,get:function(){return e.append}})})(Ii);var fn={},gn={};Object.defineProperty(gn,"__esModule",{value:!0});gn.blockElements=ud;function ud(){return["address","article","aside","blockquote","canvas","div","dl","dt","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","hr","li","main","nav","noscript","ol","output","p","pre","ruby","section","table","tbody","thead","tr","tfoot","ul","video"]}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.blockElements=void 0;var e=gn;Object.defineProperty(o,"blockElements",{enumerable:!0,get:function(){return e.blockElements}})})(fn);var Mi={},mn={};Object.defineProperty(mn,"__esModule",{value:!0});mn.calculateBaseline=hd;function hd(o){var e=window.getComputedStyle(o),t=parseFloat(e.fontSize),n=parseFloat(e.lineHeight)||t*1.2,r=parseFloat(e.paddingTop),i=parseFloat(e.borderTopWidth),s=parseFloat(e.marginTop),a=t*.8,c=(n-t)/2,l=s+i+r+c+a;return l}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.calculateBaseline=void 0;var e=mn;Object.defineProperty(o,"calculateBaseline",{enumerable:!0,get:function(){return e.calculateBaseline}})})(Mi);var Li={},vn={},bn={},yn={};Object.defineProperty(yn,"__esModule",{value:!0});yn.isContentEditable=pd;function pd(o){return o.contentEditable==="true"}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.isContentEditable=void 0;var e=yn;Object.defineProperty(o,"isContentEditable",{enumerable:!0,get:function(){return e.isContentEditable}})})(bn);Object.defineProperty(vn,"__esModule",{value:!0});vn.canSetCaret=md;var fd=Te,gd=bn;function md(o){var e=!0;if((0,fd.isNativeInput)(o))switch(o.type){case"file":case"checkbox":case"radio":case"hidden":case"submit":case"button":case"image":case"reset":e=!1;break}else e=(0,gd.isContentEditable)(o);return e}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.canSetCaret=void 0;var e=vn;Object.defineProperty(o,"canSetCaret",{enumerable:!0,get:function(){return e.canSetCaret}})})(Li);var Mt={},kn={};function vd(o,e,t){const n=t.value!==void 0?"value":"get",r=t[n],i=`#${e}Cache`;if(t[n]=function(...s){return this[i]===void 0&&(this[i]=r.apply(this,s)),this[i]},n==="get"&&t.set){const s=t.set;t.set=function(a){delete o[i],s.apply(this,a)}}return t}function Pi(){const o={win:!1,mac:!1,x11:!1,linux:!1},e=Object.keys(o).find(t=>window.navigator.appVersion.toLowerCase().indexOf(t)!==-1);return e!==void 0&&(o[e]=!0),o}function wn(o){return o!=null&&o!==""&&(typeof o!="object"||Object.keys(o).length>0)}function bd(o){return!wn(o)}const yd=()=>typeof window<"u"&&window.navigator!==null&&wn(window.navigator.platform)&&(/iP(ad|hone|od)/.test(window.navigator.platform)||window.navigator.platform==="MacIntel"&&window.navigator.maxTouchPoints>1);function kd(o){const e=Pi();return o=o.replace(/shift/gi,"⇧").replace(/backspace/gi,"⌫").replace(/enter/gi,"⏎").replace(/up/gi,"↑").replace(/left/gi,"→").replace(/down/gi,"↓").replace(/right/gi,"←").replace(/escape/gi,"⎋").replace(/insert/gi,"Ins").replace(/delete/gi,"␡").replace(/\+/gi,"+"),e.mac?o=o.replace(/ctrl|cmd/gi,"⌘").replace(/alt/gi,"⌥"):o=o.replace(/cmd/gi,"Ctrl").replace(/windows/gi,"WIN"),o}function wd(o){return o[0].toUpperCase()+o.slice(1)}function xd(o){const e=document.createElement("div");e.style.position="absolute",e.style.left="-999px",e.style.bottom="-999px",e.innerHTML=o,document.body.appendChild(e);const t=window.getSelection(),n=document.createRange();if(n.selectNode(e),t===null)throw new Error("Cannot copy text to clipboard");t.removeAllRanges(),t.addRange(n),document.execCommand("copy"),document.body.removeChild(e)}function Ed(o,e,t){let n;return(...r)=>{const i=this,s=()=>{n=void 0,t!==!0&&o.apply(i,r)},a=t===!0&&n!==void 0;window.clearTimeout(n),n=window.setTimeout(s,e),a&&o.apply(i,r)}}function be(o){return Object.prototype.toString.call(o).match(/\s([a-zA-Z]+)/)[1].toLowerCase()}function Cd(o){return be(o)==="boolean"}function Ai(o){return be(o)==="function"||be(o)==="asyncfunction"}function Sd(o){return Ai(o)&&/^\s*class\s+/.test(o.toString())}function Td(o){return be(o)==="number"}function lt(o){return be(o)==="object"}function Bd(o){return Promise.resolve(o)===o}function Od(o){return be(o)==="string"}function _d(o){return be(o)==="undefined"}function lo(o,...e){if(!e.length)return o;const t=e.shift();if(lt(o)&&lt(t))for(const n in t)lt(t[n])?(o[n]===void 0&&Object.assign(o,{[n]:{}}),lo(o[n],t[n])):Object.assign(o,{[n]:t[n]});return lo(o,...e)}function Id(o,e,t){const n=`«${e}» is deprecated and will be removed in the next major release. Please use the «${t}» instead.`;o&&console.warn(n)}function Md(o){try{return new URL(o).href}catch{}return o.substring(0,2)==="//"?window.location.protocol+o:window.location.origin+o}function Ld(o){return o>47&&o<58||o===32||o===13||o===229||o>64&&o<91||o>95&&o<112||o>185&&o<193||o>218&&o<223}const Pd={BACKSPACE:8,TAB:9,ENTER:13,SHIFT:16,CTRL:17,ALT:18,ESC:27,SPACE:32,LEFT:37,UP:38,DOWN:40,RIGHT:39,DELETE:46,META:91,SLASH:191},Ad={LEFT:0,WHEEL:1,RIGHT:2,BACKWARD:3,FORWARD:4};class Nd{constructor(){this.completed=Promise.resolve()}add(e){return new Promise((t,n)=>{this.completed=this.completed.then(e).then(t).catch(n)})}}function jd(o,e,t=void 0){let n,r,i,s=null,a=0;t||(t={});const c=function(){a=t.leading===!1?0:Date.now(),s=null,i=o.apply(n,r),s===null&&(n=r=null)};return function(){const l=Date.now();!a&&t.leading===!1&&(a=l);const d=e-(l-a);return n=this,r=arguments,d<=0||d>e?(s&&(clearTimeout(s),s=null),a=l,i=o.apply(n,r),s===null&&(n=r=null)):!s&&t.trailing!==!1&&(s=setTimeout(c,d)),i}}const Rd=Object.freeze(Object.defineProperty({__proto__:null,PromiseQueue:Nd,beautifyShortcut:kd,cacheable:vd,capitalize:wd,copyTextToClipboard:xd,debounce:Ed,deepMerge:lo,deprecationAssert:Id,getUserOS:Pi,getValidUrl:Md,isBoolean:Cd,isClass:Sd,isEmpty:bd,isFunction:Ai,isIosDevice:yd,isNumber:Td,isObject:lt,isPrintableKey:Ld,isPromise:Bd,isString:Od,isUndefined:_d,keyCodes:Pd,mouseButtons:Ad,notEmpty:wn,throttle:jd,typeOf:be},Symbol.toStringTag,{value:"Module"})),xn=ad(Rd);Object.defineProperty(kn,"__esModule",{value:!0});kn.containsOnlyInlineElements=Hd;var Dd=xn,Fd=fn;function Hd(o){var e;(0,Dd.isString)(o)?(e=document.createElement("div"),e.innerHTML=o):e=o;var t=function(n){return!(0,Fd.blockElements)().includes(n.tagName.toLowerCase())&&Array.from(n.children).every(t)};return Array.from(e.children).every(t)}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.containsOnlyInlineElements=void 0;var e=kn;Object.defineProperty(o,"containsOnlyInlineElements",{enumerable:!0,get:function(){return e.containsOnlyInlineElements}})})(Mt);var Ni={},En={},Lt={},Cn={};Object.defineProperty(Cn,"__esModule",{value:!0});Cn.make=$d;function $d(o,e,t){var n;e===void 0&&(e=null),t===void 0&&(t={});var r=document.createElement(o);if(Array.isArray(e)){var i=e.filter(function(a){return a!==void 0});(n=r.classList).add.apply(n,i)}else e!==null&&r.classList.add(e);for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(r[s]=t[s]);return r}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.make=void 0;var e=Cn;Object.defineProperty(o,"make",{enumerable:!0,get:function(){return e.make}})})(Lt);Object.defineProperty(En,"__esModule",{value:!0});En.fragmentToString=zd;var Ud=Lt;function zd(o){var e=(0,Ud.make)("div");return e.appendChild(o),e.innerHTML}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.fragmentToString=void 0;var e=En;Object.defineProperty(o,"fragmentToString",{enumerable:!0,get:function(){return e.fragmentToString}})})(Ni);var ji={},Sn={};Object.defineProperty(Sn,"__esModule",{value:!0});Sn.getContentLength=qd;var Wd=Te;function qd(o){var e,t;return(0,Wd.isNativeInput)(o)?o.value.length:o.nodeType===Node.TEXT_NODE?o.length:(t=(e=o.textContent)===null||e===void 0?void 0:e.length)!==null&&t!==void 0?t:0}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.getContentLength=void 0;var e=Sn;Object.defineProperty(o,"getContentLength",{enumerable:!0,get:function(){return e.getContentLength}})})(ji);var Tn={},Bn={},pr=yt&&yt.__spreadArray||function(o,e,t){if(t||arguments.length===2)for(var n=0,r=e.length,i;n<r;n++)(i||!(n in e))&&(i||(i=Array.prototype.slice.call(e,0,n)),i[n]=e[n]);return o.concat(i||Array.prototype.slice.call(e))};Object.defineProperty(Bn,"__esModule",{value:!0});Bn.getDeepestBlockElements=Ri;var Yd=Mt;function Ri(o){return(0,Yd.containsOnlyInlineElements)(o)?[o]:Array.from(o.children).reduce(function(e,t){return pr(pr([],e,!0),Ri(t),!0)},[])}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.getDeepestBlockElements=void 0;var e=Bn;Object.defineProperty(o,"getDeepestBlockElements",{enumerable:!0,get:function(){return e.getDeepestBlockElements}})})(Tn);var Di={},On={},Pt={},_n={};Object.defineProperty(_n,"__esModule",{value:!0});_n.isLineBreakTag=Kd;function Kd(o){return["BR","WBR"].includes(o.tagName)}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.isLineBreakTag=void 0;var e=_n;Object.defineProperty(o,"isLineBreakTag",{enumerable:!0,get:function(){return e.isLineBreakTag}})})(Pt);var At={},In={};Object.defineProperty(In,"__esModule",{value:!0});In.isSingleTag=Vd;function Vd(o){return["AREA","BASE","BR","COL","COMMAND","EMBED","HR","IMG","INPUT","KEYGEN","LINK","META","PARAM","SOURCE","TRACK","WBR"].includes(o.tagName)}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.isSingleTag=void 0;var e=In;Object.defineProperty(o,"isSingleTag",{enumerable:!0,get:function(){return e.isSingleTag}})})(At);Object.defineProperty(On,"__esModule",{value:!0});On.getDeepestNode=Fi;var Xd=Te,Gd=Pt,Zd=At;function Fi(o,e){e===void 0&&(e=!1);var t=e?"lastChild":"firstChild",n=e?"previousSibling":"nextSibling";if(o.nodeType===Node.ELEMENT_NODE&&o[t]){var r=o[t];if((0,Zd.isSingleTag)(r)&&!(0,Xd.isNativeInput)(r)&&!(0,Gd.isLineBreakTag)(r))if(r[n])r=r[n];else if(r.parentNode!==null&&r.parentNode[n])r=r.parentNode[n];else return r.parentNode;return Fi(r,e)}return o}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.getDeepestNode=void 0;var e=On;Object.defineProperty(o,"getDeepestNode",{enumerable:!0,get:function(){return e.getDeepestNode}})})(Di);var Hi={},Mn={},et=yt&&yt.__spreadArray||function(o,e,t){if(t||arguments.length===2)for(var n=0,r=e.length,i;n<r;n++)(i||!(n in e))&&(i||(i=Array.prototype.slice.call(e,0,n)),i[n]=e[n]);return o.concat(i||Array.prototype.slice.call(e))};Object.defineProperty(Mn,"__esModule",{value:!0});Mn.findAllInputs=ou;var Jd=Mt,Qd=Tn,eu=dn,tu=Te;function ou(o){return Array.from(o.querySelectorAll((0,eu.allInputsSelector)())).reduce(function(e,t){return(0,tu.isNativeInput)(t)||(0,Jd.containsOnlyInlineElements)(t)?et(et([],e,!0),[t],!1):et(et([],e,!0),(0,Qd.getDeepestBlockElements)(t),!0)},[])}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.findAllInputs=void 0;var e=Mn;Object.defineProperty(o,"findAllInputs",{enumerable:!0,get:function(){return e.findAllInputs}})})(Hi);var $i={},Ln={};Object.defineProperty(Ln,"__esModule",{value:!0});Ln.isCollapsedWhitespaces=nu;function nu(o){return!/[^\t\n\r ]/.test(o)}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.isCollapsedWhitespaces=void 0;var e=Ln;Object.defineProperty(o,"isCollapsedWhitespaces",{enumerable:!0,get:function(){return e.isCollapsedWhitespaces}})})($i);var Pn={},An={};Object.defineProperty(An,"__esModule",{value:!0});An.isElement=iu;var ru=xn;function iu(o){return(0,ru.isNumber)(o)?!1:!!o&&!!o.nodeType&&o.nodeType===Node.ELEMENT_NODE}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.isElement=void 0;var e=An;Object.defineProperty(o,"isElement",{enumerable:!0,get:function(){return e.isElement}})})(Pn);var Ui={},Nn={},jn={},Rn={};Object.defineProperty(Rn,"__esModule",{value:!0});Rn.isLeaf=su;function su(o){return o===null?!1:o.childNodes.length===0}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.isLeaf=void 0;var e=Rn;Object.defineProperty(o,"isLeaf",{enumerable:!0,get:function(){return e.isLeaf}})})(jn);var Dn={},Fn={};Object.defineProperty(Fn,"__esModule",{value:!0});Fn.isNodeEmpty=uu;var au=Pt,lu=Pn,cu=Te,du=At;function uu(o,e){var t="";return(0,du.isSingleTag)(o)&&!(0,au.isLineBreakTag)(o)?!1:((0,lu.isElement)(o)&&(0,cu.isNativeInput)(o)?t=o.value:o.textContent!==null&&(t=o.textContent.replace("​","")),e!==void 0&&(t=t.replace(new RegExp(e,"g"),"")),t.trim().length===0)}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.isNodeEmpty=void 0;var e=Fn;Object.defineProperty(o,"isNodeEmpty",{enumerable:!0,get:function(){return e.isNodeEmpty}})})(Dn);Object.defineProperty(Nn,"__esModule",{value:!0});Nn.isEmpty=fu;var hu=jn,pu=Dn;function fu(o,e){o.normalize();for(var t=[o];t.length>0;){var n=t.shift();if(n){if(o=n,(0,hu.isLeaf)(o)&&!(0,pu.isNodeEmpty)(o,e))return!1;t.push.apply(t,Array.from(o.childNodes))}}return!0}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.isEmpty=void 0;var e=Nn;Object.defineProperty(o,"isEmpty",{enumerable:!0,get:function(){return e.isEmpty}})})(Ui);var zi={},Hn={};Object.defineProperty(Hn,"__esModule",{value:!0});Hn.isFragment=mu;var gu=xn;function mu(o){return(0,gu.isNumber)(o)?!1:!!o&&!!o.nodeType&&o.nodeType===Node.DOCUMENT_FRAGMENT_NODE}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.isFragment=void 0;var e=Hn;Object.defineProperty(o,"isFragment",{enumerable:!0,get:function(){return e.isFragment}})})(zi);var Wi={},$n={};Object.defineProperty($n,"__esModule",{value:!0});$n.isHTMLString=bu;var vu=Lt;function bu(o){var e=(0,vu.make)("div");return e.innerHTML=o,e.childElementCount>0}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.isHTMLString=void 0;var e=$n;Object.defineProperty(o,"isHTMLString",{enumerable:!0,get:function(){return e.isHTMLString}})})(Wi);var qi={},Un={};Object.defineProperty(Un,"__esModule",{value:!0});Un.offset=yu;function yu(o){var e=o.getBoundingClientRect(),t=window.pageXOffset||document.documentElement.scrollLeft,n=window.pageYOffset||document.documentElement.scrollTop,r=e.top+n,i=e.left+t;return{top:r,left:i,bottom:r+e.height,right:i+e.width}}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.offset=void 0;var e=Un;Object.defineProperty(o,"offset",{enumerable:!0,get:function(){return e.offset}})})(qi);var Yi={},zn={};Object.defineProperty(zn,"__esModule",{value:!0});zn.prepend=ku;function ku(o,e){Array.isArray(e)?(e=e.reverse(),e.forEach(function(t){return o.prepend(t)})):o.prepend(e)}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.prepend=void 0;var e=zn;Object.defineProperty(o,"prepend",{enumerable:!0,get:function(){return e.prepend}})})(Yi);(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.prepend=o.offset=o.make=o.isLineBreakTag=o.isSingleTag=o.isNodeEmpty=o.isLeaf=o.isHTMLString=o.isFragment=o.isEmpty=o.isElement=o.isContentEditable=o.isCollapsedWhitespaces=o.findAllInputs=o.isNativeInput=o.allInputsSelector=o.getDeepestNode=o.getDeepestBlockElements=o.getContentLength=o.fragmentToString=o.containsOnlyInlineElements=o.canSetCaret=o.calculateBaseline=o.blockElements=o.append=void 0;var e=dn;Object.defineProperty(o,"allInputsSelector",{enumerable:!0,get:function(){return e.allInputsSelector}});var t=Te;Object.defineProperty(o,"isNativeInput",{enumerable:!0,get:function(){return t.isNativeInput}});var n=Ii;Object.defineProperty(o,"append",{enumerable:!0,get:function(){return n.append}});var r=fn;Object.defineProperty(o,"blockElements",{enumerable:!0,get:function(){return r.blockElements}});var i=Mi;Object.defineProperty(o,"calculateBaseline",{enumerable:!0,get:function(){return i.calculateBaseline}});var s=Li;Object.defineProperty(o,"canSetCaret",{enumerable:!0,get:function(){return s.canSetCaret}});var a=Mt;Object.defineProperty(o,"containsOnlyInlineElements",{enumerable:!0,get:function(){return a.containsOnlyInlineElements}});var c=Ni;Object.defineProperty(o,"fragmentToString",{enumerable:!0,get:function(){return c.fragmentToString}});var l=ji;Object.defineProperty(o,"getContentLength",{enumerable:!0,get:function(){return l.getContentLength}});var d=Tn;Object.defineProperty(o,"getDeepestBlockElements",{enumerable:!0,get:function(){return d.getDeepestBlockElements}});var u=Di;Object.defineProperty(o,"getDeepestNode",{enumerable:!0,get:function(){return u.getDeepestNode}});var p=Hi;Object.defineProperty(o,"findAllInputs",{enumerable:!0,get:function(){return p.findAllInputs}});var g=$i;Object.defineProperty(o,"isCollapsedWhitespaces",{enumerable:!0,get:function(){return g.isCollapsedWhitespaces}});var b=bn;Object.defineProperty(o,"isContentEditable",{enumerable:!0,get:function(){return b.isContentEditable}});var f=Pn;Object.defineProperty(o,"isElement",{enumerable:!0,get:function(){return f.isElement}});var m=Ui;Object.defineProperty(o,"isEmpty",{enumerable:!0,get:function(){return m.isEmpty}});var w=zi;Object.defineProperty(o,"isFragment",{enumerable:!0,get:function(){return w.isFragment}});var h=Wi;Object.defineProperty(o,"isHTMLString",{enumerable:!0,get:function(){return h.isHTMLString}});var v=jn;Object.defineProperty(o,"isLeaf",{enumerable:!0,get:function(){return v.isLeaf}});var y=Dn;Object.defineProperty(o,"isNodeEmpty",{enumerable:!0,get:function(){return y.isNodeEmpty}});var k=Pt;Object.defineProperty(o,"isLineBreakTag",{enumerable:!0,get:function(){return k.isLineBreakTag}});var T=At;Object.defineProperty(o,"isSingleTag",{enumerable:!0,get:function(){return T.isSingleTag}});var O=Lt;Object.defineProperty(o,"make",{enumerable:!0,get:function(){return O.make}});var x=qi;Object.defineProperty(o,"offset",{enumerable:!0,get:function(){return x.offset}});var C=Yi;Object.defineProperty(o,"prepend",{enumerable:!0,get:function(){return C.prepend}})})(F);const Z="cdx-list",K={wrapper:Z,item:`${Z}__item`,itemContent:`${Z}__item-content`,itemChildren:`${Z}__item-children`};class ie{static get CSS(){return{...K,orderedList:`${Z}-ordered`}}constructor(e,t){this.config=t,this.readOnly=e}renderWrapper(e){let t;return e===!0?t=F.make("ol",[ie.CSS.wrapper,ie.CSS.orderedList]):t=F.make("ol",[ie.CSS.orderedList,ie.CSS.itemChildren]),t}renderItem(e,t){const n=F.make("li",ie.CSS.item),r=F.make("div",ie.CSS.itemContent,{innerHTML:e,contentEditable:(!this.readOnly).toString()});return n.appendChild(r),n}getItemContent(e){const t=e.querySelector(`.${ie.CSS.itemContent}`);return!t||F.isEmpty(t)?"":t.innerHTML}getItemMeta(){return{}}composeDefaultMeta(){return{}}}class se{static get CSS(){return{...K,unorderedList:`${Z}-unordered`}}constructor(e,t){this.config=t,this.readOnly=e}renderWrapper(e){let t;return e===!0?t=F.make("ul",[se.CSS.wrapper,se.CSS.unorderedList]):t=F.make("ul",[se.CSS.unorderedList,se.CSS.itemChildren]),t}renderItem(e,t){const n=F.make("li",se.CSS.item),r=F.make("div",se.CSS.itemContent,{innerHTML:e,contentEditable:(!this.readOnly).toString()});return n.appendChild(r),n}getItemContent(e){const t=e.querySelector(`.${se.CSS.itemContent}`);return!t||F.isEmpty(t)?"":t.innerHTML}getItemMeta(){return{}}composeDefaultMeta(){return{}}}function ke(o){return o.nodeType===Node.ELEMENT_NODE}var qe={},Wn={},Nt={},jt={};Object.defineProperty(jt,"__esModule",{value:!0});jt.getContenteditableSlice=xu;var wu=F;function xu(o,e,t,n,r){var i;r===void 0&&(r=!1);var s=document.createRange();if(n==="left"?(s.setStart(o,0),s.setEnd(e,t)):(s.setStart(e,t),s.setEnd(o,o.childNodes.length)),r===!0){var a=s.extractContents();return(0,wu.fragmentToString)(a)}var c=s.cloneContents(),l=document.createElement("div");l.appendChild(c);var d=(i=l.textContent)!==null&&i!==void 0?i:"";return d}Object.defineProperty(Nt,"__esModule",{value:!0});Nt.checkContenteditableSliceForEmptiness=Su;var Eu=F,Cu=jt;function Su(o,e,t,n){var r=(0,Cu.getContenteditableSlice)(o,e,t,n);return(0,Eu.isCollapsedWhitespaces)(r)}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.checkContenteditableSliceForEmptiness=void 0;var e=Nt;Object.defineProperty(o,"checkContenteditableSliceForEmptiness",{enumerable:!0,get:function(){return e.checkContenteditableSliceForEmptiness}})})(Wn);var Ki={};(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.getContenteditableSlice=void 0;var e=jt;Object.defineProperty(o,"getContenteditableSlice",{enumerable:!0,get:function(){return e.getContenteditableSlice}})})(Ki);var Vi={},qn={};Object.defineProperty(qn,"__esModule",{value:!0});qn.focus=Bu;var Tu=F;function Bu(o,e){var t,n;if(e===void 0&&(e=!0),(0,Tu.isNativeInput)(o)){o.focus();var r=e?0:o.value.length;o.setSelectionRange(r,r)}else{var i=document.createRange(),s=window.getSelection();if(!s)return;var a=function(p,g){g===void 0&&(g=!1);var b=document.createTextNode("");g?p.insertBefore(b,p.firstChild):p.appendChild(b),i.setStart(b,0),i.setEnd(b,0)},c=function(p){return p!=null},l=o.childNodes,d=e?l[0]:l[l.length-1];if(c(d)){for(;c(d)&&d.nodeType!==Node.TEXT_NODE;)d=e?d.firstChild:d.lastChild;if(c(d)&&d.nodeType===Node.TEXT_NODE){var u=(n=(t=d.textContent)===null||t===void 0?void 0:t.length)!==null&&n!==void 0?n:0,r=e?0:u;i.setStart(d,r),i.setEnd(d,r)}else a(o,e)}else a(o);s.removeAllRanges(),s.addRange(i)}}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.focus=void 0;var e=qn;Object.defineProperty(o,"focus",{enumerable:!0,get:function(){return e.focus}})})(Vi);var Yn={},Rt={};Object.defineProperty(Rt,"__esModule",{value:!0});Rt.getCaretNodeAndOffset=Ou;function Ou(){var o=window.getSelection();if(o===null)return[null,0];var e=o.focusNode,t=o.focusOffset;return e===null?[null,0]:(e.nodeType!==Node.TEXT_NODE&&e.childNodes.length>0&&(e.childNodes[t]!==void 0?(e=e.childNodes[t],t=0):(e=e.childNodes[t-1],e.textContent!==null&&(t=e.textContent.length))),[e,t])}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.getCaretNodeAndOffset=void 0;var e=Rt;Object.defineProperty(o,"getCaretNodeAndOffset",{enumerable:!0,get:function(){return e.getCaretNodeAndOffset}})})(Yn);var Xi={},Dt={};Object.defineProperty(Dt,"__esModule",{value:!0});Dt.getRange=_u;function _u(){var o=window.getSelection();return o&&o.rangeCount?o.getRangeAt(0):null}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.getRange=void 0;var e=Dt;Object.defineProperty(o,"getRange",{enumerable:!0,get:function(){return e.getRange}})})(Xi);var Gi={},Kn={};Object.defineProperty(Kn,"__esModule",{value:!0});Kn.isCaretAtEndOfInput=Lu;var fr=F,Iu=Yn,Mu=Wn;function Lu(o){var e=(0,fr.getDeepestNode)(o,!0);if(e===null)return!0;if((0,fr.isNativeInput)(e))return e.selectionEnd===e.value.length;var t=(0,Iu.getCaretNodeAndOffset)(),n=t[0],r=t[1];return n===null?!1:(0,Mu.checkContenteditableSliceForEmptiness)(o,n,r,"right")}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.isCaretAtEndOfInput=void 0;var e=Kn;Object.defineProperty(o,"isCaretAtEndOfInput",{enumerable:!0,get:function(){return e.isCaretAtEndOfInput}})})(Gi);var Zi={},Vn={};Object.defineProperty(Vn,"__esModule",{value:!0});Vn.isCaretAtStartOfInput=Nu;var tt=F,Pu=Rt,Au=Nt;function Nu(o){var e=(0,tt.getDeepestNode)(o);if(e===null||(0,tt.isEmpty)(o))return!0;if((0,tt.isNativeInput)(e))return e.selectionEnd===0;if((0,tt.isEmpty)(o))return!0;var t=(0,Pu.getCaretNodeAndOffset)(),n=t[0],r=t[1];return n===null?!1:(0,Au.checkContenteditableSliceForEmptiness)(o,n,r,"left")}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.isCaretAtStartOfInput=void 0;var e=Vn;Object.defineProperty(o,"isCaretAtStartOfInput",{enumerable:!0,get:function(){return e.isCaretAtStartOfInput}})})(Zi);var Ji={},Xn={};Object.defineProperty(Xn,"__esModule",{value:!0});Xn.save=Du;var ju=F,Ru=Dt;function Du(){var o=(0,Ru.getRange)(),e=(0,ju.make)("span");if(e.id="cursor",e.hidden=!0,!!o)return o.insertNode(e),function(){var t=window.getSelection();t&&(o.setStartAfter(e),o.setEndAfter(e),t.removeAllRanges(),t.addRange(o),setTimeout(function(){e.remove()},150))}}(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.save=void 0;var e=Xn;Object.defineProperty(o,"save",{enumerable:!0,get:function(){return e.save}})})(Ji);(function(o){Object.defineProperty(o,"__esModule",{value:!0}),o.save=o.isCaretAtStartOfInput=o.isCaretAtEndOfInput=o.getRange=o.getCaretNodeAndOffset=o.focus=o.getContenteditableSlice=o.checkContenteditableSliceForEmptiness=void 0;var e=Wn;Object.defineProperty(o,"checkContenteditableSliceForEmptiness",{enumerable:!0,get:function(){return e.checkContenteditableSliceForEmptiness}});var t=Ki;Object.defineProperty(o,"getContenteditableSlice",{enumerable:!0,get:function(){return t.getContenteditableSlice}});var n=Vi;Object.defineProperty(o,"focus",{enumerable:!0,get:function(){return n.focus}});var r=Yn;Object.defineProperty(o,"getCaretNodeAndOffset",{enumerable:!0,get:function(){return r.getCaretNodeAndOffset}});var i=Xi;Object.defineProperty(o,"getRange",{enumerable:!0,get:function(){return i.getRange}});var s=Gi;Object.defineProperty(o,"isCaretAtEndOfInput",{enumerable:!0,get:function(){return s.isCaretAtEndOfInput}});var a=Zi;Object.defineProperty(o,"isCaretAtStartOfInput",{enumerable:!0,get:function(){return a.isCaretAtStartOfInput}});var c=Ji;Object.defineProperty(o,"save",{enumerable:!0,get:function(){return c.save}})})(qe);class q{static get CSS(){return{...K,checklist:`${Z}-checklist`,itemChecked:`${Z}__checkbox--checked`,noHover:`${Z}__checkbox--no-hover`,checkbox:`${Z}__checkbox-check`,checkboxContainer:`${Z}__checkbox`}}constructor(e,t){this.config=t,this.readOnly=e}renderWrapper(e){let t;return e===!0?(t=F.make("ul",[q.CSS.wrapper,q.CSS.checklist]),t.addEventListener("click",n=>{const r=n.target;if(r){const i=r.closest(`.${q.CSS.checkboxContainer}`);i&&i.contains(r)&&this.toggleCheckbox(i)}})):t=F.make("ul",[q.CSS.checklist,q.CSS.itemChildren]),t}renderItem(e,t){const n=F.make("li",[q.CSS.item,q.CSS.item]),r=F.make("div",q.CSS.itemContent,{innerHTML:e,contentEditable:(!this.readOnly).toString()}),i=F.make("span",q.CSS.checkbox),s=F.make("div",q.CSS.checkboxContainer);return t.checked===!0&&s.classList.add(q.CSS.itemChecked),i.innerHTML=ed,s.appendChild(i),n.appendChild(s),n.appendChild(r),n}getItemContent(e){const t=e.querySelector(`.${q.CSS.itemContent}`);return!t||F.isEmpty(t)?"":t.innerHTML}getItemMeta(e){const t=e.querySelector(`.${q.CSS.checkboxContainer}`);return{checked:t?t.classList.contains(q.CSS.itemChecked):!1}}composeDefaultMeta(){return{checked:!1}}toggleCheckbox(e){e.classList.toggle(q.CSS.itemChecked),e.classList.add(q.CSS.noHover),e.addEventListener("mouseleave",()=>this.removeSpecialHoverBehavior(e),{once:!0})}removeSpecialHoverBehavior(e){e.classList.remove(q.CSS.noHover)}}function Kt(o,e="after"){const t=[];let n;function r(i){switch(e){case"after":return i.nextElementSibling;case"before":return i.previousElementSibling}}for(n=r(o);n!==null;)t.push(n),n=r(n);return t.length!==0?t:null}function ce(o,e=!0){let t=o;return o.classList.contains(K.item)&&(t=o.querySelector(`.${K.itemChildren}`)),t===null?[]:e?Array.from(t.querySelectorAll(`:scope > .${K.item}`)):Array.from(t.querySelectorAll(`.${K.item}`))}function Fu(o){return o.nextElementSibling===null}function Hu(o){return o.querySelector(`.${K.itemChildren}`)!==null}function me(o){return o.querySelector(`.${K.itemChildren}`)}function Vt(o){let e=o;o.classList.contains(K.item)&&(e=me(o)),e!==null&&ce(e).length===0&&e.remove()}function ct(o){return o.querySelector(`.${K.itemContent}`)}function Be(o,e=!0){const t=ct(o);t&&qe.focus(t,e)}class Xt{get currentItem(){const e=window.getSelection();if(!e)return null;let t=e.anchorNode;return!t||(ke(t)||(t=t.parentNode),!t)||!ke(t)?null:t.closest(`.${K.item}`)}get currentItemLevel(){const e=this.currentItem;if(e===null)return null;let t=e.parentNode,n=0;for(;t!==null&&t!==this.listWrapper;)ke(t)&&t.classList.contains(K.item)&&(n+=1),t=t.parentNode;return n+1}constructor({data:e,config:t,api:n,readOnly:r,block:i},s){this.config=t,this.data=e,this.readOnly=r,this.api=n,this.block=i,this.renderer=s}render(){return this.listWrapper=this.renderer.renderWrapper(!0),this.data.items.length?this.appendItems(this.data.items,this.listWrapper):this.appendItems([{content:"",meta:{},items:[]}],this.listWrapper),this.readOnly||this.listWrapper.addEventListener("keydown",e=>{switch(e.key){case"Enter":this.enterPressed(e);break;case"Backspace":this.backspace(e);break;case"Tab":e.shiftKey?this.shiftTab(e):this.addTab(e);break}},!1),"start"in this.data.meta&&this.data.meta.start!==void 0&&this.changeStartWith(this.data.meta.start),"counterType"in this.data.meta&&this.data.meta.counterType!==void 0&&this.changeCounters(this.data.meta.counterType),this.listWrapper}save(e){const t=e??this.listWrapper,n=s=>ce(s).map(a=>{const c=me(a),l=this.renderer.getItemContent(a),d=this.renderer.getItemMeta(a),u=c?n(c):[];return{content:l,meta:d,items:u}}),r=t?n(t):[];let i={style:this.data.style,meta:{},items:r};return this.data.style==="ordered"&&(i.meta={start:this.data.meta.start,counterType:this.data.meta.counterType}),i}static get pasteConfig(){return{tags:["OL","UL","LI"]}}merge(e){const t=this.block.holder.querySelectorAll(`.${K.item}`),n=t[t.length-1],r=ct(n);if(n===null||r===null||(r.insertAdjacentHTML("beforeend",e.items[0].content),this.listWrapper===void 0))return;const i=ce(this.listWrapper);if(i.length===0)return;const s=i[i.length-1];let a=me(s);const c=e.items.shift();c!==void 0&&(c.items.length!==0&&(a===null&&(a=this.renderer.renderWrapper(!1)),this.appendItems(c.items,a)),e.items.length>0&&this.appendItems(e.items,this.listWrapper))}onPaste(e){const t=e.detail.data;this.data=this.pasteHandler(t);const n=this.listWrapper;n&&n.parentNode&&n.parentNode.replaceChild(this.render(),n)}pasteHandler(e){const{tagName:t}=e;let n="unordered",r;switch(t){case"OL":n="ordered",r="ol";break;case"UL":case"LI":n="unordered",r="ul"}const i={style:n,meta:{},items:[]};n==="ordered"&&(this.data.meta.counterType="numeric",this.data.meta.start=1);const s=a=>Array.from(a.querySelectorAll(":scope > li")).map(c=>{const l=c.querySelector(`:scope > ${r}`),d=l?s(l):[];return{content:c.innerHTML??"",meta:{},items:d}});return i.items=s(e),i}changeStartWith(e){this.listWrapper.style.setProperty("counter-reset",`item ${e-1}`),this.data.meta.start=e}changeCounters(e){this.listWrapper.style.setProperty("--list-counter-type",e),this.data.meta.counterType=e}enterPressed(e){var t;const n=this.currentItem;if(e.stopPropagation(),e.preventDefault(),e.isComposing||n===null)return;const r=((t=this.renderer)==null?void 0:t.getItemContent(n).trim().length)===0,i=n.parentNode===this.listWrapper,s=n.previousElementSibling===null,a=this.api.blocks.getCurrentBlockIndex();if(i&&r)if(Fu(n)&&!Hu(n)){s?this.convertItemToDefaultBlock(a,!0):this.convertItemToDefaultBlock();return}else{this.splitList(n);return}else if(r){this.unshiftItem(n);return}else this.splitItem(n)}backspace(e){var t;const n=this.currentItem;if(n!==null&&qe.isCaretAtStartOfInput(n)&&((t=window.getSelection())==null?void 0:t.isCollapsed)!==!1){if(e.stopPropagation(),n.parentNode===this.listWrapper&&n.previousElementSibling===null){this.convertFirstItemToDefaultBlock();return}e.preventDefault(),this.mergeItemWithPrevious(n)}}shiftTab(e){e.stopPropagation(),e.preventDefault(),this.currentItem!==null&&this.unshiftItem(this.currentItem)}unshiftItem(e){if(!e.parentNode||!ke(e.parentNode))return;const t=e.parentNode.closest(`.${K.item}`);if(!t)return;let n=me(e);if(e.parentElement===null)return;const r=Kt(e);r!==null&&(n===null&&(n=this.renderer.renderWrapper(!1)),r.forEach(i=>{n.appendChild(i)}),e.appendChild(n)),t.after(e),Be(e,!1),Vt(t)}splitList(e){const t=ce(e),n=this.block,r=this.api.blocks.getCurrentBlockIndex();if(t.length!==0){const c=t[0];this.unshiftItem(c),Be(e,!1)}if(e.previousElementSibling===null&&e.parentNode===this.listWrapper){this.convertItemToDefaultBlock(r);return}const i=Kt(e);if(i===null)return;const s=this.renderer.renderWrapper(!0);i.forEach(c=>{s.appendChild(c)});const a=this.save(s);a.meta.start=this.data.style=="ordered"?1:void 0,this.api.blocks.insert(n==null?void 0:n.name,a,this.config,r+1),this.convertItemToDefaultBlock(r+1),s.remove()}splitItem(e){const[t,n]=qe.getCaretNodeAndOffset();if(t===null)return;const r=ct(e);let i;r===null?i="":i=qe.getContenteditableSlice(r,t,n,"right",!0);const s=me(e),a=this.renderItem(i);e==null||e.after(a),s&&a.appendChild(s),Be(a)}mergeItemWithPrevious(e){const t=e.previousElementSibling,n=e.parentNode;if(n===null||!ke(n))return;const r=n.closest(`.${K.item}`);if(!t&&!r||t&&!ke(t))return;let i;if(t){const u=ce(t,!1);u.length!==0&&u.length!==0?i=u[u.length-1]:i=t}else i=r;const s=this.renderer.getItemContent(e);if(!i)return;Be(i,!1);const a=ct(i);if(a===null)return;a.insertAdjacentHTML("beforeend",s);const c=ce(e);if(c.length===0){e.remove(),Vt(i);return}const l=t||r,d=me(l)??this.renderer.renderWrapper(!1);t?c.forEach(u=>{d.appendChild(u)}):c.forEach(u=>{d.prepend(u)}),me(l)===null&&i.appendChild(d),e.remove()}addTab(e){var t;e.stopPropagation(),e.preventDefault();const n=this.currentItem;if(!n)return;if(((t=this.config)==null?void 0:t.maxLevel)!==void 0){const s=this.currentItemLevel;if(s!==null&&s===this.config.maxLevel)return}const r=n.previousSibling;if(r===null||!ke(r))return;const i=me(r);if(i)i.appendChild(n),ce(n).forEach(s=>{i.appendChild(s)});else{const s=this.renderer.renderWrapper(!1);s.appendChild(n),ce(n).forEach(a=>{s.appendChild(a)}),r.appendChild(s)}Vt(n),Be(n,!1)}convertItemToDefaultBlock(e,t){let n;const r=this.currentItem,i=r!==null?this.renderer.getItemContent(r):"";t===!0&&this.api.blocks.delete(),e!==void 0?n=this.api.blocks.insert(void 0,{text:i},void 0,e):n=this.api.blocks.insert(),r==null||r.remove(),this.api.caret.setToBlock(n,"start")}convertFirstItemToDefaultBlock(){const e=this.currentItem;if(e===null)return;const t=ce(e);if(t.length!==0){const s=t[0];this.unshiftItem(s),Be(e)}const n=Kt(e),r=this.api.blocks.getCurrentBlockIndex(),i=n===null;this.convertItemToDefaultBlock(r,i)}renderItem(e,t){const n=t??this.renderer.composeDefaultMeta();switch(!0){case this.renderer instanceof ie:return this.renderer.renderItem(e,n);case this.renderer instanceof se:return this.renderer.renderItem(e,n);default:return this.renderer.renderItem(e,n)}}appendItems(e,t){e.forEach(n=>{var r;const i=this.renderItem(n.content,n.meta);if(t.appendChild(i),n.items.length){const s=(r=this.renderer)==null?void 0:r.renderWrapper(!1);this.appendItems(n.items,s),i.appendChild(s)}})}}const Oe={wrapper:`${Z}-start-with-field`,input:`${Z}-start-with-field__input`,startWithElementWrapperInvalid:`${Z}-start-with-field--invalid`};function $u(o,{value:e,placeholder:t,attributes:n,sanitize:r}){const i=F.make("div",Oe.wrapper),s=F.make("input",Oe.input,{placeholder:t,tabIndex:-1,value:e});for(const a in n)s.setAttribute(a,n[a]);return i.appendChild(s),s.addEventListener("input",()=>{r!==void 0&&(s.value=r(s.value));const a=s.checkValidity();!a&&!i.classList.contains(Oe.startWithElementWrapperInvalid)&&i.classList.add(Oe.startWithElementWrapperInvalid),a&&i.classList.contains(Oe.startWithElementWrapperInvalid)&&i.classList.remove(Oe.startWithElementWrapperInvalid),a&&o(s.value)}),i}const Ue=new Map([["Numeric","numeric"],["Lower Roman","lower-roman"],["Upper Roman","upper-roman"],["Lower Alpha","lower-alpha"],["Upper Alpha","upper-alpha"]]),gr=new Map([["numeric",td],["lower-roman",od],["upper-roman",nd],["lower-alpha",id],["upper-alpha",rd]]);function Uu(o){return o.replace(/\D+/g,"")}function zu(o){return typeof o.items[0]=="string"}function Wu(o){return!("meta"in o)}function qu(o){return typeof o.items[0]!="string"&&"text"in o.items[0]&&"checked"in o.items[0]&&typeof o.items[0].text=="string"&&typeof o.items[0].checked=="boolean"}function Yu(o){const e=[];return zu(o)?(o.items.forEach(t=>{e.push({content:t,meta:{},items:[]})}),{style:o.style,meta:{},items:e}):qu(o)?(o.items.forEach(t=>{e.push({content:t.text,meta:{checked:t.checked},items:[]})}),{style:"checklist",meta:{},items:e}):Wu(o)?{style:o.style,meta:{},items:o.items}:o}class kt{static get isReadOnlySupported(){return!0}static get enableLineBreaks(){return!0}static get toolbox(){return[{icon:ur,title:"Unordered List",data:{style:"unordered"}},{icon:hr,title:"Ordered List",data:{style:"ordered"}},{icon:dr,title:"Checklist",data:{style:"checklist"}}]}static get pasteConfig(){return{tags:["OL","UL","LI"]}}static get conversionConfig(){return{export:e=>kt.joinRecursive(e),import:(e,t)=>({meta:{},items:[{content:e,meta:{},items:[]}],style:(t==null?void 0:t.defaultStyle)!==void 0?t.defaultStyle:"unordered"})}}get listStyle(){return this.data.style||this.defaultListStyle}set listStyle(e){var t;this.data.style=e,this.changeTabulatorByStyle();const n=this.list.render();(t=this.listElement)==null||t.replaceWith(n),this.listElement=n}constructor({data:e,config:t,api:n,readOnly:r,block:i}){var s;this.api=n,this.readOnly=r,this.config=t,this.block=i,this.defaultListStyle=((s=this.config)==null?void 0:s.defaultStyle)||"unordered",this.defaultCounterTypes=this.config.counterTypes||Array.from(Ue.values());const a={style:this.defaultListStyle,meta:{},items:[]};this.data=Object.keys(e).length?Yu(e):a,this.listStyle==="ordered"&&this.data.meta.counterType===void 0&&(this.data.meta.counterType="numeric"),this.changeTabulatorByStyle()}static joinRecursive(e){return e.items.map(t=>`${t.content} ${kt.joinRecursive(t)}`).join("")}render(){return this.listElement=this.list.render(),this.listElement}save(){return this.data=this.list.save(),this.data}merge(e){this.list.merge(e)}renderSettings(){const e=[{label:this.api.i18n.t("Unordered"),icon:ur,closeOnActivate:!0,isActive:this.listStyle=="unordered",onActivate:()=>{this.listStyle="unordered"}},{label:this.api.i18n.t("Ordered"),icon:hr,closeOnActivate:!0,isActive:this.listStyle=="ordered",onActivate:()=>{this.listStyle="ordered"}},{label:this.api.i18n.t("Checklist"),icon:dr,closeOnActivate:!0,isActive:this.listStyle=="checklist",onActivate:()=>{this.listStyle="checklist"}}];if(this.listStyle==="ordered"){const t=$u(i=>this.changeStartWith(Number(i)),{value:String(this.data.meta.start??1),placeholder:"",attributes:{required:"true"},sanitize:i=>Uu(i)}),n=[{label:this.api.i18n.t("Start with"),icon:sd,children:{items:[{element:t,type:"html"}]}}],r={label:this.api.i18n.t("Counter type"),icon:gr.get(this.data.meta.counterType),children:{items:[]}};Ue.forEach((i,s)=>{const a=Ue.get(s);this.defaultCounterTypes.includes(a)&&r.children.items.push({title:this.api.i18n.t(s),icon:gr.get(a),isActive:this.data.meta.counterType===Ue.get(s),closeOnActivate:!0,onActivate:()=>{this.changeCounters(Ue.get(s))}})}),r.children.items.length>1&&n.push(r),e.push({type:"separator"},...n)}return e}onPaste(e){const{tagName:t}=e.detail.data;switch(t){case"OL":this.listStyle="ordered";break;case"UL":case"LI":this.listStyle="unordered"}this.list.onPaste(e)}pasteHandler(e){return this.list.pasteHandler(e)}changeCounters(e){var t;(t=this.list)==null||t.changeCounters(e),this.data.meta.counterType=e}changeStartWith(e){var t;(t=this.list)==null||t.changeStartWith(e),this.data.meta.start=e}changeTabulatorByStyle(){switch(this.listStyle){case"ordered":this.list=new Xt({data:this.data,readOnly:this.readOnly,api:this.api,config:this.config,block:this.block},new ie(this.readOnly,this.config));break;case"unordered":this.list=new Xt({data:this.data,readOnly:this.readOnly,api:this.api,config:this.config,block:this.block},new se(this.readOnly,this.config));break;case"checklist":this.list=new Xt({data:this.data,readOnly:this.readOnly,api:this.api,config:this.config,block:this.block},new q(this.readOnly,this.config));break}}}(function(){try{if(typeof document<"u"){var o=document.createElement("style");o.appendChild(document.createTextNode('.image-tool{--bg-color: #cdd1e0;--front-color: #388ae5;--border-color: #e8e8eb}.image-tool__image{border-radius:3px;overflow:hidden;margin-bottom:10px;padding-bottom:0}.image-tool__image-picture{max-width:100%;vertical-align:bottom;display:block}.image-tool__image-preloader{width:50px;height:50px;border-radius:50%;background-size:cover;margin:auto;position:relative;background-color:var(--bg-color);background-position:center center}.image-tool__image-preloader:after{content:"";position:absolute;z-index:3;width:60px;height:60px;border-radius:50%;border:2px solid var(--bg-color);border-top-color:var(--front-color);left:50%;top:50%;margin-top:-30px;margin-left:-30px;animation:image-preloader-spin 2s infinite linear;box-sizing:border-box}.image-tool__caption{visibility:hidden;position:absolute;bottom:0;left:0;margin-bottom:10px}.image-tool__caption[contentEditable=true][data-placeholder]:before{position:absolute!important;content:attr(data-placeholder);color:#707684;font-weight:400;display:none}.image-tool__caption[contentEditable=true][data-placeholder]:empty:before{display:block}.image-tool__caption[contentEditable=true][data-placeholder]:empty:focus:before{display:none}.image-tool--empty .image-tool__image,.image-tool--empty .image-tool__image-preloader{display:none}.image-tool--empty .image-tool__caption,.image-tool--uploading .image-tool__caption{visibility:hidden!important}.image-tool .cdx-button{display:flex;align-items:center;justify-content:center}.image-tool .cdx-button svg{height:auto;margin:0 6px 0 0}.image-tool--filled .cdx-button,.image-tool--filled .image-tool__image-preloader{display:none}.image-tool--uploading .image-tool__image{min-height:200px;display:flex;border:1px solid var(--border-color);background-color:#fff}.image-tool--uploading .image-tool__image-picture,.image-tool--uploading .cdx-button{display:none}.image-tool--withBorder .image-tool__image{border:1px solid var(--border-color)}.image-tool--withBackground .image-tool__image{padding:15px;background:var(--bg-color)}.image-tool--withBackground .image-tool__image-picture{max-width:60%;margin:0 auto}.image-tool--stretched .image-tool__image-picture{width:100%}.image-tool--caption .image-tool__caption{visibility:visible}.image-tool--caption{padding-bottom:50px}@keyframes image-preloader-spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}')),document.head.appendChild(o)}}catch(e){console.error("vite-plugin-css-injected-by-js",e)}})();const Ku='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 19V19C9.13623 19 8.20435 19 7.46927 18.6955C6.48915 18.2895 5.71046 17.5108 5.30448 16.5307C5 15.7956 5 14.8638 5 13V12C5 9.19108 5 7.78661 5.67412 6.77772C5.96596 6.34096 6.34096 5.96596 6.77772 5.67412C7.78661 5 9.19108 5 12 5H13.5C14.8956 5 15.5933 5 16.1611 5.17224C17.4395 5.56004 18.44 6.56046 18.8278 7.83886C19 8.40666 19 9.10444 19 10.5V10.5"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M16 13V16M16 19V16M19 16H16M16 16H13"/><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6.5 17.5L17.5 6.5"/><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.9919 10.5H19.0015"/><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.9919 19H11.0015"/><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13L13 5"/></svg>',Vu='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.9919 9.5H19.0015"/><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.5 5H14.5096"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M14.625 5H15C17.2091 5 19 6.79086 19 9V9.375"/><path stroke="currentColor" stroke-width="2" d="M9.375 5L9 5C6.79086 5 5 6.79086 5 9V9.375"/><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.3725 5H9.38207"/><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 9.5H5.00957"/><path stroke="currentColor" stroke-width="2" d="M9.375 19H9C6.79086 19 5 17.2091 5 15V14.625"/><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.3725 19H9.38207"/><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 14.55H5.00957"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M16 13V16M16 19V16M19 16H16M16 16H13"/></svg>',Qi='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><rect width="14" height="14" x="5" y="5" stroke="currentColor" stroke-width="2" rx="4"/><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.13968 15.32L8.69058 11.5661C9.02934 11.2036 9.48873 11 9.96774 11C10.4467 11 10.9061 11.2036 11.2449 11.5661L15.3871 16M13.5806 14.0664L15.0132 12.533C15.3519 12.1705 15.8113 11.9668 16.2903 11.9668C16.7693 11.9668 17.2287 12.1705 17.5675 12.533L18.841 13.9634"/><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.7778 9.33331H13.7867"/></svg>',Xu='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9L20 12L17 15"/><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 12H20"/><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 9L4 12L7 15"/><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 12H10"/></svg>',Gu='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M8 9V7.2C8 7.08954 8.08954 7 8.2 7L12 7M16 9V7.2C16 7.08954 15.9105 7 15.8 7L12 7M12 7L12 17M12 17H10M12 17H14"/></svg>';function _e(o,e=null,t={}){const n=document.createElement(o);Array.isArray(e)?n.classList.add(...e):e!==null&&n.classList.add(e);for(const r in t)t.hasOwnProperty(r)&&(n[r]=t[r]);return n}var dt=(o=>(o.Empty="empty",o.Uploading="uploading",o.Filled="filled",o))(dt||{});class Zu{constructor({api:e,config:t,onSelectFile:n,readOnly:r}){this.api=e,this.config=t,this.onSelectFile=n,this.readOnly=r,this.nodes={wrapper:_e("div",[this.CSS.baseClass,this.CSS.wrapper]),imageContainer:_e("div",[this.CSS.imageContainer]),fileButton:this.createFileButton(),imageEl:void 0,imagePreloader:_e("div",this.CSS.imagePreloader),caption:_e("div",[this.CSS.input,this.CSS.caption],{contentEditable:!this.readOnly})},this.nodes.caption.dataset.placeholder=this.config.captionPlaceholder,this.nodes.imageContainer.appendChild(this.nodes.imagePreloader),this.nodes.wrapper.appendChild(this.nodes.imageContainer),this.nodes.wrapper.appendChild(this.nodes.caption),this.nodes.wrapper.appendChild(this.nodes.fileButton)}applyTune(e,t){this.nodes.wrapper.classList.toggle(`${this.CSS.wrapper}--${e}`,t)}render(){return this.toggleStatus("empty"),this.nodes.wrapper}showPreloader(e){this.nodes.imagePreloader.style.backgroundImage=`url(${e})`,this.toggleStatus("uploading")}hidePreloader(){this.nodes.imagePreloader.style.backgroundImage="",this.toggleStatus("empty")}fillImage(e){const t=/\.mp4$/.test(e)?"VIDEO":"IMG",n={src:e};let r="load";t==="VIDEO"&&(n.autoplay=!0,n.loop=!0,n.muted=!0,n.playsinline=!0,r="loadeddata"),this.nodes.imageEl=_e(t,this.CSS.imageEl,n),this.nodes.imageEl.addEventListener(r,()=>{this.toggleStatus("filled"),this.nodes.imagePreloader!==void 0&&(this.nodes.imagePreloader.style.backgroundImage="")}),this.nodes.imageContainer.appendChild(this.nodes.imageEl)}fillCaption(e){this.nodes.caption!==void 0&&(this.nodes.caption.innerHTML=e)}toggleStatus(e){for(const t in dt)if(Object.prototype.hasOwnProperty.call(dt,t)){const n=dt[t];this.nodes.wrapper.classList.toggle(`${this.CSS.wrapper}--${n}`,n===e)}}get CSS(){return{baseClass:this.api.styles.block,loading:this.api.styles.loader,input:this.api.styles.input,button:this.api.styles.button,wrapper:"image-tool",imageContainer:"image-tool__image",imagePreloader:"image-tool__image-preloader",imageEl:"image-tool__image-picture",caption:"image-tool__caption"}}createFileButton(){const e=_e("div",[this.CSS.button]);return e.innerHTML=this.config.buttonContent??`${Qi} ${this.api.i18n.t("Select an Image")}`,e.addEventListener("click",()=>{this.onSelectFile()}),e}}function Ju(o){return o&&o.__esModule&&Object.prototype.hasOwnProperty.call(o,"default")?o.default:o}var es={exports:{}};(function(o,e){(function(t,n){o.exports=n()})(window,function(){return function(t){var n={};function r(i){if(n[i])return n[i].exports;var s=n[i]={i,l:!1,exports:{}};return t[i].call(s.exports,s,s.exports,r),s.l=!0,s.exports}return r.m=t,r.c=n,r.d=function(i,s,a){r.o(i,s)||Object.defineProperty(i,s,{enumerable:!0,get:a})},r.r=function(i){typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(i,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(i,"__esModule",{value:!0})},r.t=function(i,s){if(1&s&&(i=r(i)),8&s||4&s&&typeof i=="object"&&i&&i.__esModule)return i;var a=Object.create(null);if(r.r(a),Object.defineProperty(a,"default",{enumerable:!0,value:i}),2&s&&typeof i!="string")for(var c in i)r.d(a,c,(function(l){return i[l]}).bind(null,c));return a},r.n=function(i){var s=i&&i.__esModule?function(){return i.default}:function(){return i};return r.d(s,"a",s),s},r.o=function(i,s){return Object.prototype.hasOwnProperty.call(i,s)},r.p="",r(r.s=3)}([function(t,n){var r;r=function(){return this}();try{r=r||new Function("return this")()}catch{typeof window=="object"&&(r=window)}t.exports=r},function(t,n,r){(function(i){var s=r(2),a=setTimeout;function c(){}function l(m){if(!(this instanceof l))throw new TypeError("Promises must be constructed via new");if(typeof m!="function")throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=void 0,this._deferreds=[],f(m,this)}function d(m,w){for(;m._state===3;)m=m._value;m._state!==0?(m._handled=!0,l._immediateFn(function(){var h=m._state===1?w.onFulfilled:w.onRejected;if(h!==null){var v;try{v=h(m._value)}catch(y){return void p(w.promise,y)}u(w.promise,v)}else(m._state===1?u:p)(w.promise,m._value)})):m._deferreds.push(w)}function u(m,w){try{if(w===m)throw new TypeError("A promise cannot be resolved with itself.");if(w&&(typeof w=="object"||typeof w=="function")){var h=w.then;if(w instanceof l)return m._state=3,m._value=w,void g(m);if(typeof h=="function")return void f((v=h,y=w,function(){v.apply(y,arguments)}),m)}m._state=1,m._value=w,g(m)}catch(k){p(m,k)}var v,y}function p(m,w){m._state=2,m._value=w,g(m)}function g(m){m._state===2&&m._deferreds.length===0&&l._immediateFn(function(){m._handled||l._unhandledRejectionFn(m._value)});for(var w=0,h=m._deferreds.length;w<h;w++)d(m,m._deferreds[w]);m._deferreds=null}function b(m,w,h){this.onFulfilled=typeof m=="function"?m:null,this.onRejected=typeof w=="function"?w:null,this.promise=h}function f(m,w){var h=!1;try{m(function(v){h||(h=!0,u(w,v))},function(v){h||(h=!0,p(w,v))})}catch(v){if(h)return;h=!0,p(w,v)}}l.prototype.catch=function(m){return this.then(null,m)},l.prototype.then=function(m,w){var h=new this.constructor(c);return d(this,new b(m,w,h)),h},l.prototype.finally=s.a,l.all=function(m){return new l(function(w,h){if(!m||m.length===void 0)throw new TypeError("Promise.all accepts an array");var v=Array.prototype.slice.call(m);if(v.length===0)return w([]);var y=v.length;function k(O,x){try{if(x&&(typeof x=="object"||typeof x=="function")){var C=x.then;if(typeof C=="function")return void C.call(x,function(I){k(O,I)},h)}v[O]=x,--y==0&&w(v)}catch(I){h(I)}}for(var T=0;T<v.length;T++)k(T,v[T])})},l.resolve=function(m){return m&&typeof m=="object"&&m.constructor===l?m:new l(function(w){w(m)})},l.reject=function(m){return new l(function(w,h){h(m)})},l.race=function(m){return new l(function(w,h){for(var v=0,y=m.length;v<y;v++)m[v].then(w,h)})},l._immediateFn=typeof i=="function"&&function(m){i(m)}||function(m){a(m,0)},l._unhandledRejectionFn=function(m){typeof console<"u"&&console&&console.warn("Possible Unhandled Promise Rejection:",m)},n.a=l}).call(this,r(5).setImmediate)},function(t,n,r){n.a=function(i){var s=this.constructor;return this.then(function(a){return s.resolve(i()).then(function(){return a})},function(a){return s.resolve(i()).then(function(){return s.reject(a)})})}},function(t,n,r){function i(f){return(i=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(m){return typeof m}:function(m){return m&&typeof Symbol=="function"&&m.constructor===Symbol&&m!==Symbol.prototype?"symbol":typeof m})(f)}r(4);var s,a,c,l,d,u,p,g=r(8),b=(a=function(f){return new Promise(function(m,w){f=l(f),(f=d(f)).beforeSend&&f.beforeSend();var h=window.XMLHttpRequest?new window.XMLHttpRequest:new window.ActiveXObject("Microsoft.XMLHTTP");h.open(f.method,f.url),h.setRequestHeader("X-Requested-With","XMLHttpRequest"),Object.keys(f.headers).forEach(function(y){var k=f.headers[y];h.setRequestHeader(y,k)});var v=f.ratio;h.upload.addEventListener("progress",function(y){var k=Math.round(y.loaded/y.total*100),T=Math.ceil(k*v/100);f.progress(Math.min(T,100))},!1),h.addEventListener("progress",function(y){var k=Math.round(y.loaded/y.total*100),T=Math.ceil(k*(100-v)/100)+v;f.progress(Math.min(T,100))},!1),h.onreadystatechange=function(){if(h.readyState===4){var y=h.response;try{y=JSON.parse(y)}catch{}var k=g.parseHeaders(h.getAllResponseHeaders()),T={body:y,code:h.status,headers:k};p(h.status)?m(T):w(T)}},h.send(f.data)})},c=function(f){return f.method="POST",a(f)},l=function(){var f=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(f.url&&typeof f.url!="string")throw new Error("Url must be a string");if(f.url=f.url||"",f.method&&typeof f.method!="string")throw new Error("`method` must be a string or null");if(f.method=f.method?f.method.toUpperCase():"GET",f.headers&&i(f.headers)!=="object")throw new Error("`headers` must be an object or null");if(f.headers=f.headers||{},f.type&&(typeof f.type!="string"||!Object.values(s).includes(f.type)))throw new Error("`type` must be taken from module's «contentType» library");if(f.progress&&typeof f.progress!="function")throw new Error("`progress` must be a function or null");if(f.progress=f.progress||function(m){},f.beforeSend=f.beforeSend||function(m){},f.ratio&&typeof f.ratio!="number")throw new Error("`ratio` must be a number");if(f.ratio<0||f.ratio>100)throw new Error("`ratio` must be in a 0-100 interval");if(f.ratio=f.ratio||90,f.accept&&typeof f.accept!="string")throw new Error("`accept` must be a string with a list of allowed mime-types");if(f.accept=f.accept||"*/*",f.multiple&&typeof f.multiple!="boolean")throw new Error("`multiple` must be a true or false");if(f.multiple=f.multiple||!1,f.fieldName&&typeof f.fieldName!="string")throw new Error("`fieldName` must be a string");return f.fieldName=f.fieldName||"files",f},d=function(f){switch(f.method){case"GET":var m=u(f.data,s.URLENCODED);delete f.data,f.url=/\?/.test(f.url)?f.url+"&"+m:f.url+"?"+m;break;case"POST":case"PUT":case"DELETE":case"UPDATE":var w=function(){return(arguments.length>0&&arguments[0]!==void 0?arguments[0]:{}).type||s.JSON}(f);(g.isFormData(f.data)||g.isFormElement(f.data))&&(w=s.FORM),f.data=u(f.data,w),w!==b.contentType.FORM&&(f.headers["content-type"]=w)}return f},u=function(){var f=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};switch(arguments.length>1?arguments[1]:void 0){case s.URLENCODED:return g.urlEncode(f);case s.JSON:return g.jsonEncode(f);case s.FORM:return g.formEncode(f);default:return f}},p=function(f){return f>=200&&f<300},{contentType:s={URLENCODED:"application/x-www-form-urlencoded; charset=utf-8",FORM:"multipart/form-data",JSON:"application/json; charset=utf-8"},request:a,get:function(f){return f.method="GET",a(f)},post:c,transport:function(f){return f=l(f),g.selectFiles(f).then(function(m){for(var w=new FormData,h=0;h<m.length;h++)w.append(f.fieldName,m[h],m[h].name);g.isObject(f.data)&&Object.keys(f.data).forEach(function(y){var k=f.data[y];w.append(y,k)});var v=f.beforeSend;return f.beforeSend=function(){return v(m)},f.data=w,c(f)})},selectFiles:function(f){return delete(f=l(f)).beforeSend,g.selectFiles(f)}});t.exports=b},function(t,n,r){r.r(n);var i=r(1);window.Promise=window.Promise||i.a},function(t,n,r){(function(i){var s=i!==void 0&&i||typeof self<"u"&&self||window,a=Function.prototype.apply;function c(l,d){this._id=l,this._clearFn=d}n.setTimeout=function(){return new c(a.call(setTimeout,s,arguments),clearTimeout)},n.setInterval=function(){return new c(a.call(setInterval,s,arguments),clearInterval)},n.clearTimeout=n.clearInterval=function(l){l&&l.close()},c.prototype.unref=c.prototype.ref=function(){},c.prototype.close=function(){this._clearFn.call(s,this._id)},n.enroll=function(l,d){clearTimeout(l._idleTimeoutId),l._idleTimeout=d},n.unenroll=function(l){clearTimeout(l._idleTimeoutId),l._idleTimeout=-1},n._unrefActive=n.active=function(l){clearTimeout(l._idleTimeoutId);var d=l._idleTimeout;d>=0&&(l._idleTimeoutId=setTimeout(function(){l._onTimeout&&l._onTimeout()},d))},r(6),n.setImmediate=typeof self<"u"&&self.setImmediate||i!==void 0&&i.setImmediate||this&&this.setImmediate,n.clearImmediate=typeof self<"u"&&self.clearImmediate||i!==void 0&&i.clearImmediate||this&&this.clearImmediate}).call(this,r(0))},function(t,n,r){(function(i,s){(function(a,c){if(!a.setImmediate){var l,d,u,p,g,b=1,f={},m=!1,w=a.document,h=Object.getPrototypeOf&&Object.getPrototypeOf(a);h=h&&h.setTimeout?h:a,{}.toString.call(a.process)==="[object process]"?l=function(k){s.nextTick(function(){y(k)})}:function(){if(a.postMessage&&!a.importScripts){var k=!0,T=a.onmessage;return a.onmessage=function(){k=!1},a.postMessage("","*"),a.onmessage=T,k}}()?(p="setImmediate$"+Math.random()+"$",g=function(k){k.source===a&&typeof k.data=="string"&&k.data.indexOf(p)===0&&y(+k.data.slice(p.length))},a.addEventListener?a.addEventListener("message",g,!1):a.attachEvent("onmessage",g),l=function(k){a.postMessage(p+k,"*")}):a.MessageChannel?((u=new MessageChannel).port1.onmessage=function(k){y(k.data)},l=function(k){u.port2.postMessage(k)}):w&&"onreadystatechange"in w.createElement("script")?(d=w.documentElement,l=function(k){var T=w.createElement("script");T.onreadystatechange=function(){y(k),T.onreadystatechange=null,d.removeChild(T),T=null},d.appendChild(T)}):l=function(k){setTimeout(y,0,k)},h.setImmediate=function(k){typeof k!="function"&&(k=new Function(""+k));for(var T=new Array(arguments.length-1),O=0;O<T.length;O++)T[O]=arguments[O+1];var x={callback:k,args:T};return f[b]=x,l(b),b++},h.clearImmediate=v}function v(k){delete f[k]}function y(k){if(m)setTimeout(y,0,k);else{var T=f[k];if(T){m=!0;try{(function(O){var x=O.callback,C=O.args;switch(C.length){case 0:x();break;case 1:x(C[0]);break;case 2:x(C[0],C[1]);break;case 3:x(C[0],C[1],C[2]);break;default:x.apply(c,C)}})(T)}finally{v(k),m=!1}}}}})(typeof self>"u"?i===void 0?this:i:self)}).call(this,r(0),r(7))},function(t,n){var r,i,s=t.exports={};function a(){throw new Error("setTimeout has not been defined")}function c(){throw new Error("clearTimeout has not been defined")}function l(h){if(r===setTimeout)return setTimeout(h,0);if((r===a||!r)&&setTimeout)return r=setTimeout,setTimeout(h,0);try{return r(h,0)}catch{try{return r.call(null,h,0)}catch{return r.call(this,h,0)}}}(function(){try{r=typeof setTimeout=="function"?setTimeout:a}catch{r=a}try{i=typeof clearTimeout=="function"?clearTimeout:c}catch{i=c}})();var d,u=[],p=!1,g=-1;function b(){p&&d&&(p=!1,d.length?u=d.concat(u):g=-1,u.length&&f())}function f(){if(!p){var h=l(b);p=!0;for(var v=u.length;v;){for(d=u,u=[];++g<v;)d&&d[g].run();g=-1,v=u.length}d=null,p=!1,function(y){if(i===clearTimeout)return clearTimeout(y);if((i===c||!i)&&clearTimeout)return i=clearTimeout,clearTimeout(y);try{i(y)}catch{try{return i.call(null,y)}catch{return i.call(this,y)}}}(h)}}function m(h,v){this.fun=h,this.array=v}function w(){}s.nextTick=function(h){var v=new Array(arguments.length-1);if(arguments.length>1)for(var y=1;y<arguments.length;y++)v[y-1]=arguments[y];u.push(new m(h,v)),u.length!==1||p||l(f)},m.prototype.run=function(){this.fun.apply(null,this.array)},s.title="browser",s.browser=!0,s.env={},s.argv=[],s.version="",s.versions={},s.on=w,s.addListener=w,s.once=w,s.off=w,s.removeListener=w,s.removeAllListeners=w,s.emit=w,s.prependListener=w,s.prependOnceListener=w,s.listeners=function(h){return[]},s.binding=function(h){throw new Error("process.binding is not supported")},s.cwd=function(){return"/"},s.chdir=function(h){throw new Error("process.chdir is not supported")},s.umask=function(){return 0}},function(t,n,r){function i(a,c){for(var l=0;l<c.length;l++){var d=c[l];d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,d.key,d)}}var s=r(9);t.exports=function(){function a(){(function(u,p){if(!(u instanceof p))throw new TypeError("Cannot call a class as a function")})(this,a)}var c,l,d;return c=a,d=[{key:"urlEncode",value:function(u){return s(u)}},{key:"jsonEncode",value:function(u){return JSON.stringify(u)}},{key:"formEncode",value:function(u){if(this.isFormData(u))return u;if(this.isFormElement(u))return new FormData(u);if(this.isObject(u)){var p=new FormData;return Object.keys(u).forEach(function(g){var b=u[g];p.append(g,b)}),p}throw new Error("`data` must be an instance of Object, FormData or <FORM> HTMLElement")}},{key:"isObject",value:function(u){return Object.prototype.toString.call(u)==="[object Object]"}},{key:"isFormData",value:function(u){return u instanceof FormData}},{key:"isFormElement",value:function(u){return u instanceof HTMLFormElement}},{key:"selectFiles",value:function(){var u=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};return new Promise(function(p,g){var b=document.createElement("INPUT");b.type="file",u.multiple&&b.setAttribute("multiple","multiple"),u.accept&&b.setAttribute("accept",u.accept),b.style.display="none",document.body.appendChild(b),b.addEventListener("change",function(f){var m=f.target.files;p(m),document.body.removeChild(b)},!1),b.click()})}},{key:"parseHeaders",value:function(u){var p=u.trim().split(/[\r\n]+/),g={};return p.forEach(function(b){var f=b.split(": "),m=f.shift(),w=f.join(": ");m&&(g[m]=w)}),g}}],(l=null)&&i(c.prototype,l),d&&i(c,d),a}()},function(t,n){var r=function(s){return encodeURIComponent(s).replace(/[!'()*]/g,escape).replace(/%20/g,"+")},i=function(s,a,c,l){return a=a||null,c=c||"&",l=l||null,s?function(d){for(var u=new Array,p=0;p<d.length;p++)d[p]&&u.push(d[p]);return u}(Object.keys(s).map(function(d){var u,p,g=d;if(l&&(g=l+"["+g+"]"),typeof s[d]=="object"&&s[d]!==null)u=i(s[d],null,c,g);else{a&&(p=g,g=!isNaN(parseFloat(p))&&isFinite(p)?a+Number(g):g);var b=s[d];b=(b=(b=(b=b===!0?"1":b)===!1?"0":b)===0?"0":b)||"",u=r(g)+"="+r(b)}return u})).join(c).replace(/[!'()*]/g,""):""};t.exports=i}])})})(es);var Qu=es.exports;const Ie=Ju(Qu);function Gt(o){return o!==void 0&&typeof o.then=="function"}class eh{constructor({config:e,onUpload:t,onError:n}){this.config=e,this.onUpload=t,this.onError=n}uploadSelectedFile({onPreview:e}){const t=function(r){const i=new FileReader;i.readAsDataURL(r),i.onload=s=>{e(s.target.result)}};let n;if(this.config.uploader&&typeof this.config.uploader.uploadByFile=="function"){const r=this.config.uploader.uploadByFile;n=Ie.selectFiles({accept:this.config.types??"image/*"}).then(i=>{t(i[0]);const s=r(i[0]);return Gt(s)||console.warn("Custom uploader method uploadByFile should return a Promise"),s})}else n=Ie.transport({url:this.config.endpoints.byFile,data:this.config.additionalRequestData,accept:this.config.types??"image/*",headers:this.config.additionalRequestHeaders,beforeSend:r=>{t(r[0])},fieldName:this.config.field??"image"}).then(r=>r.body);n.then(r=>{this.onUpload(r)}).catch(r=>{this.onError(r)})}uploadByUrl(e){let t;this.config.uploader&&typeof this.config.uploader.uploadByUrl=="function"?(t=this.config.uploader.uploadByUrl(e),Gt(t)||console.warn("Custom uploader method uploadByUrl should return a Promise")):t=Ie.post({url:this.config.endpoints.byUrl,data:Object.assign({url:e},this.config.additionalRequestData),type:Ie.contentType.JSON,headers:this.config.additionalRequestHeaders}).then(n=>n.body),t.then(n=>{this.onUpload(n)}).catch(n=>{this.onError(n)})}uploadByFile(e,{onPreview:t}){const n=new FileReader;n.readAsDataURL(e),n.onload=i=>{t(i.target.result)};let r;if(this.config.uploader&&typeof this.config.uploader.uploadByFile=="function")r=this.config.uploader.uploadByFile(e),Gt(r)||console.warn("Custom uploader method uploadByFile should return a Promise");else{const i=new FormData;i.append(this.config.field??"image",e),this.config.additionalRequestData&&Object.keys(this.config.additionalRequestData).length&&Object.entries(this.config.additionalRequestData).forEach(([s,a])=>{i.append(s,a)}),r=Ie.post({url:this.config.endpoints.byFile,data:i,type:Ie.contentType.JSON,headers:this.config.additionalRequestHeaders}).then(s=>s.body)}r.then(i=>{this.onUpload(i)}).catch(i=>{this.onError(i)})}}/**
 * Image Tool for the Editor.js
 * <AUTHOR> <<EMAIL>>
 * @license MIT
 * @see {@link https://github.com/editor-js/image}
 *
 * To developers.
 * To simplify Tool structure, we split it to 4 parts:
 *  1) index.ts — main Tool's interface, public API and methods for working with data
 *  2) uploader.ts — module that has methods for sending files via AJAX: from device, by URL or File pasting
 *  3) ui.ts — module for UI manipulations: render, showing preloader, etc
 *
 * For debug purposes there is a testing server
 * that can save uploaded files and return a Response {@link UploadResponseFormat}
 *
 *       $ node dev/server.js
 *
 * It will expose 8008 port, so you can pass http://localhost:8008 with the Tools config:
 *
 * image: {
 *   class: ImageTool,
 *   config: {
 *     endpoints: {
 *       byFile: 'http://localhost:8008/uploadFile',
 *       byUrl: 'http://localhost:8008/fetchUrl',
 *     }
 *   },
 * },
 */class wt{constructor({data:e,config:t,api:n,readOnly:r,block:i}){this.isCaptionEnabled=null,this.api=n,this.block=i,this.config={endpoints:t.endpoints,additionalRequestData:t.additionalRequestData,additionalRequestHeaders:t.additionalRequestHeaders,field:t.field,types:t.types,captionPlaceholder:this.api.i18n.t(t.captionPlaceholder??"Caption"),buttonContent:t.buttonContent,uploader:t.uploader,actions:t.actions,features:t.features||{}},this.uploader=new eh({config:this.config,onUpload:s=>this.onUpload(s),onError:s=>this.uploadingFailed(s)}),this.ui=new Zu({api:n,config:this.config,onSelectFile:()=>{this.uploader.uploadSelectedFile({onPreview:s=>{this.ui.showPreloader(s)}})},readOnly:r}),this._data={caption:"",withBorder:!1,withBackground:!1,stretched:!1,file:{url:""}},this.data=e}static get isReadOnlySupported(){return!0}static get toolbox(){return{icon:Qi,title:"Image"}}static get tunes(){return[{name:"withBorder",icon:Vu,title:"With border",toggle:!0},{name:"stretched",icon:Xu,title:"Stretch image",toggle:!0},{name:"withBackground",icon:Ku,title:"With background",toggle:!0}]}render(){var e,t,n;return(((e=this.config.features)==null?void 0:e.caption)===!0||((t=this.config.features)==null?void 0:t.caption)===void 0||((n=this.config.features)==null?void 0:n.caption)==="optional"&&this.data.caption)&&(this.isCaptionEnabled=!0),this.ui.render()}validate(e){return!!e.file.url}save(){const e=this.ui.nodes.caption;return this._data.caption=e.innerHTML,this.data}renderSettings(){var e;const t=wt.tunes.concat(this.config.actions||[]),n={border:"withBorder",background:"withBackground",stretch:"stretched",caption:"caption"};((e=this.config.features)==null?void 0:e.caption)==="optional"&&t.push({name:"caption",icon:Gu,title:"With caption",toggle:!0});const r=t.filter(s=>{var a,c;const l=Object.keys(n).find(d=>n[d]===s.name);return l==="caption"?((a=this.config.features)==null?void 0:a.caption)!==!1:l==null||((c=this.config.features)==null?void 0:c[l])!==!1}),i=s=>{let a=this.data[s.name];return s.name==="caption"&&(a=this.isCaptionEnabled??a),a};return r.map(s=>({icon:s.icon,label:this.api.i18n.t(s.title),name:s.name,toggle:s.toggle,isActive:i(s),onActivate:()=>{if(typeof s.action=="function"){s.action(s.name);return}let a=!i(s);s.name==="caption"&&(this.isCaptionEnabled=!(this.isCaptionEnabled??!1),a=this.isCaptionEnabled),this.tuneToggled(s.name,a)}}))}appendCallback(){this.ui.nodes.fileButton.click()}static get pasteConfig(){return{tags:[{img:{src:!0}}],patterns:{image:/https?:\/\/\S+\.(gif|jpe?g|tiff|png|svg|webp)(\?[a-z0-9=]*)?$/i},files:{mimeTypes:["image/*"]}}}async onPaste(e){switch(e.type){case"tag":{const t=e.detail.data;if(/^blob:/.test(t.src)){const n=await(await fetch(t.src)).blob();this.uploadFile(n);break}this.uploadUrl(t.src);break}case"pattern":{const t=e.detail.data;this.uploadUrl(t);break}case"file":{const t=e.detail.file;this.uploadFile(t);break}}}set data(e){this.image=e.file,this._data.caption=e.caption||"",this.ui.fillCaption(this._data.caption),wt.tunes.forEach(({name:t})=>{const n=typeof e[t]<"u"?e[t]===!0||e[t]==="true":!1;this.setTune(t,n)}),e.caption&&this.setTune("caption",!0)}get data(){return this._data}set image(e){this._data.file=e||{url:""},e&&e.url&&this.ui.fillImage(e.url)}onUpload(e){e.success&&e.file?this.image=e.file:this.uploadingFailed("incorrect response: "+JSON.stringify(e))}uploadingFailed(e){console.log("Image Tool: uploading failed because of",e),this.api.notifier.show({message:this.api.i18n.t("Couldn’t upload image. Please try another."),style:"error"}),this.ui.hidePreloader()}tuneToggled(e,t){e==="caption"?(this.ui.applyTune(e,t),t==!1&&(this._data.caption="",this.ui.fillCaption(""))):this.setTune(e,t)}setTune(e,t){this._data[e]=t,this.ui.applyTune(e,t),e==="stretched"&&Promise.resolve().then(()=>{this.block.stretched=t}).catch(n=>{console.error(n)})}uploadFile(e){this.uploader.uploadByFile(e,{onPreview:t=>{this.ui.showPreloader(t)}})}uploadUrl(e){this.ui.showPreloader(e),this.uploader.uploadByUrl(e)}}var Zt={exports:{}},mr;function th(){return mr||(mr=1,function(o,e){(function(t,n){o.exports=n()})(window,function(){return function(t){var n={};function r(i){if(n[i])return n[i].exports;var s=n[i]={i,l:!1,exports:{}};return t[i].call(s.exports,s,s.exports,r),s.l=!0,s.exports}return r.m=t,r.c=n,r.d=function(i,s,a){r.o(i,s)||Object.defineProperty(i,s,{enumerable:!0,get:a})},r.r=function(i){typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(i,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(i,"__esModule",{value:!0})},r.t=function(i,s){if(1&s&&(i=r(i)),8&s||4&s&&typeof i=="object"&&i&&i.__esModule)return i;var a=Object.create(null);if(r.r(a),Object.defineProperty(a,"default",{enumerable:!0,value:i}),2&s&&typeof i!="string")for(var c in i)r.d(a,c,(function(l){return i[l]}).bind(null,c));return a},r.n=function(i){var s=i&&i.__esModule?function(){return i.default}:function(){return i};return r.d(s,"a",s),s},r.o=function(i,s){return Object.prototype.hasOwnProperty.call(i,s)},r.p="/",r(r.s=6)}([function(t,n,r){t.exports=function(i){var s={};function a(c){if(s[c])return s[c].exports;var l=s[c]={i:c,l:!1,exports:{}};return i[c].call(l.exports,l,l.exports,a),l.l=!0,l.exports}return a.m=i,a.c=s,a.d=function(c,l,d){a.o(c,l)||Object.defineProperty(c,l,{enumerable:!0,get:d})},a.r=function(c){typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(c,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(c,"__esModule",{value:!0})},a.t=function(c,l){if(1&l&&(c=a(c)),8&l||4&l&&typeof c=="object"&&c&&c.__esModule)return c;var d=Object.create(null);if(a.r(d),Object.defineProperty(d,"default",{enumerable:!0,value:c}),2&l&&typeof c!="string")for(var u in c)a.d(d,u,(function(p){return c[p]}).bind(null,u));return d},a.n=function(c){var l=c&&c.__esModule?function(){return c.default}:function(){return c};return a.d(l,"a",l),l},a.o=function(c,l){return Object.prototype.hasOwnProperty.call(c,l)},a.p="",a(a.s=3)}([function(i,s){var a;a=function(){return this}();try{a=a||new Function("return this")()}catch{typeof window=="object"&&(a=window)}i.exports=a},function(i,s,a){(function(c){var l=a(2),d=setTimeout;function u(){}function p(v){if(!(this instanceof p))throw new TypeError("Promises must be constructed via new");if(typeof v!="function")throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=void 0,this._deferreds=[],h(v,this)}function g(v,y){for(;v._state===3;)v=v._value;v._state!==0?(v._handled=!0,p._immediateFn(function(){var k=v._state===1?y.onFulfilled:y.onRejected;if(k!==null){var T;try{T=k(v._value)}catch(O){return void f(y.promise,O)}b(y.promise,T)}else(v._state===1?b:f)(y.promise,v._value)})):v._deferreds.push(y)}function b(v,y){try{if(y===v)throw new TypeError("A promise cannot be resolved with itself.");if(y&&(typeof y=="object"||typeof y=="function")){var k=y.then;if(y instanceof p)return v._state=3,v._value=y,void m(v);if(typeof k=="function")return void h((T=k,O=y,function(){T.apply(O,arguments)}),v)}v._state=1,v._value=y,m(v)}catch(x){f(v,x)}var T,O}function f(v,y){v._state=2,v._value=y,m(v)}function m(v){v._state===2&&v._deferreds.length===0&&p._immediateFn(function(){v._handled||p._unhandledRejectionFn(v._value)});for(var y=0,k=v._deferreds.length;y<k;y++)g(v,v._deferreds[y]);v._deferreds=null}function w(v,y,k){this.onFulfilled=typeof v=="function"?v:null,this.onRejected=typeof y=="function"?y:null,this.promise=k}function h(v,y){var k=!1;try{v(function(T){k||(k=!0,b(y,T))},function(T){k||(k=!0,f(y,T))})}catch(T){if(k)return;k=!0,f(y,T)}}p.prototype.catch=function(v){return this.then(null,v)},p.prototype.then=function(v,y){var k=new this.constructor(u);return g(this,new w(v,y,k)),k},p.prototype.finally=l.a,p.all=function(v){return new p(function(y,k){if(!v||v.length===void 0)throw new TypeError("Promise.all accepts an array");var T=Array.prototype.slice.call(v);if(T.length===0)return y([]);var O=T.length;function x(I,E){try{if(E&&(typeof E=="object"||typeof E=="function")){var B=E.then;if(typeof B=="function")return void B.call(E,function(_){x(I,_)},k)}T[I]=E,--O==0&&y(T)}catch(_){k(_)}}for(var C=0;C<T.length;C++)x(C,T[C])})},p.resolve=function(v){return v&&typeof v=="object"&&v.constructor===p?v:new p(function(y){y(v)})},p.reject=function(v){return new p(function(y,k){k(v)})},p.race=function(v){return new p(function(y,k){for(var T=0,O=v.length;T<O;T++)v[T].then(y,k)})},p._immediateFn=typeof c=="function"&&function(v){c(v)}||function(v){d(v,0)},p._unhandledRejectionFn=function(v){typeof console<"u"&&console&&console.warn("Possible Unhandled Promise Rejection:",v)},s.a=p}).call(this,a(5).setImmediate)},function(i,s,a){s.a=function(c){var l=this.constructor;return this.then(function(d){return l.resolve(c()).then(function(){return d})},function(d){return l.resolve(c()).then(function(){return l.reject(d)})})}},function(i,s,a){function c(h){return(c=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(v){return typeof v}:function(v){return v&&typeof Symbol=="function"&&v.constructor===Symbol&&v!==Symbol.prototype?"symbol":typeof v})(h)}a(4);var l,d,u,p,g,b,f,m=a(8),w=(d=function(h){return new Promise(function(v,y){h=p(h),(h=g(h)).beforeSend&&h.beforeSend();var k=window.XMLHttpRequest?new window.XMLHttpRequest:new window.ActiveXObject("Microsoft.XMLHTTP");k.open(h.method,h.url),k.setRequestHeader("X-Requested-With","XMLHttpRequest"),Object.keys(h.headers).forEach(function(O){var x=h.headers[O];k.setRequestHeader(O,x)});var T=h.ratio;k.upload.addEventListener("progress",function(O){var x=Math.round(O.loaded/O.total*100),C=Math.ceil(x*T/100);h.progress(Math.min(C,100))},!1),k.addEventListener("progress",function(O){var x=Math.round(O.loaded/O.total*100),C=Math.ceil(x*(100-T)/100)+T;h.progress(Math.min(C,100))},!1),k.onreadystatechange=function(){if(k.readyState===4){var O=k.response;try{O=JSON.parse(O)}catch{}var x=m.parseHeaders(k.getAllResponseHeaders()),C={body:O,code:k.status,headers:x};f(k.status)?v(C):y(C)}},k.send(h.data)})},u=function(h){return h.method="POST",d(h)},p=function(){var h=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(h.url&&typeof h.url!="string")throw new Error("Url must be a string");if(h.url=h.url||"",h.method&&typeof h.method!="string")throw new Error("`method` must be a string or null");if(h.method=h.method?h.method.toUpperCase():"GET",h.headers&&c(h.headers)!=="object")throw new Error("`headers` must be an object or null");if(h.headers=h.headers||{},h.type&&(typeof h.type!="string"||!Object.values(l).includes(h.type)))throw new Error("`type` must be taken from module's «contentType» library");if(h.progress&&typeof h.progress!="function")throw new Error("`progress` must be a function or null");if(h.progress=h.progress||function(v){},h.beforeSend=h.beforeSend||function(v){},h.ratio&&typeof h.ratio!="number")throw new Error("`ratio` must be a number");if(h.ratio<0||h.ratio>100)throw new Error("`ratio` must be in a 0-100 interval");if(h.ratio=h.ratio||90,h.accept&&typeof h.accept!="string")throw new Error("`accept` must be a string with a list of allowed mime-types");if(h.accept=h.accept||"*/*",h.multiple&&typeof h.multiple!="boolean")throw new Error("`multiple` must be a true or false");if(h.multiple=h.multiple||!1,h.fieldName&&typeof h.fieldName!="string")throw new Error("`fieldName` must be a string");return h.fieldName=h.fieldName||"files",h},g=function(h){switch(h.method){case"GET":var v=b(h.data,l.URLENCODED);delete h.data,h.url=/\?/.test(h.url)?h.url+"&"+v:h.url+"?"+v;break;case"POST":case"PUT":case"DELETE":case"UPDATE":var y=function(){return(arguments.length>0&&arguments[0]!==void 0?arguments[0]:{}).type||l.JSON}(h);(m.isFormData(h.data)||m.isFormElement(h.data))&&(y=l.FORM),h.data=b(h.data,y),y!==w.contentType.FORM&&(h.headers["content-type"]=y)}return h},b=function(){var h=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};switch(arguments.length>1?arguments[1]:void 0){case l.URLENCODED:return m.urlEncode(h);case l.JSON:return m.jsonEncode(h);case l.FORM:return m.formEncode(h);default:return h}},f=function(h){return h>=200&&h<300},{contentType:l={URLENCODED:"application/x-www-form-urlencoded; charset=utf-8",FORM:"multipart/form-data",JSON:"application/json; charset=utf-8"},request:d,get:function(h){return h.method="GET",d(h)},post:u,transport:function(h){return h=p(h),m.selectFiles(h).then(function(v){for(var y=new FormData,k=0;k<v.length;k++)y.append(h.fieldName,v[k],v[k].name);m.isObject(h.data)&&Object.keys(h.data).forEach(function(O){var x=h.data[O];y.append(O,x)});var T=h.beforeSend;return h.beforeSend=function(){return T(v)},h.data=y,u(h)})},selectFiles:function(h){return delete(h=p(h)).beforeSend,m.selectFiles(h)}});i.exports=w},function(i,s,a){a.r(s);var c=a(1);window.Promise=window.Promise||c.a},function(i,s,a){(function(c){var l=c!==void 0&&c||typeof self<"u"&&self||window,d=Function.prototype.apply;function u(p,g){this._id=p,this._clearFn=g}s.setTimeout=function(){return new u(d.call(setTimeout,l,arguments),clearTimeout)},s.setInterval=function(){return new u(d.call(setInterval,l,arguments),clearInterval)},s.clearTimeout=s.clearInterval=function(p){p&&p.close()},u.prototype.unref=u.prototype.ref=function(){},u.prototype.close=function(){this._clearFn.call(l,this._id)},s.enroll=function(p,g){clearTimeout(p._idleTimeoutId),p._idleTimeout=g},s.unenroll=function(p){clearTimeout(p._idleTimeoutId),p._idleTimeout=-1},s._unrefActive=s.active=function(p){clearTimeout(p._idleTimeoutId);var g=p._idleTimeout;g>=0&&(p._idleTimeoutId=setTimeout(function(){p._onTimeout&&p._onTimeout()},g))},a(6),s.setImmediate=typeof self<"u"&&self.setImmediate||c!==void 0&&c.setImmediate||this&&this.setImmediate,s.clearImmediate=typeof self<"u"&&self.clearImmediate||c!==void 0&&c.clearImmediate||this&&this.clearImmediate}).call(this,a(0))},function(i,s,a){(function(c,l){(function(d,u){if(!d.setImmediate){var p,g,b,f,m,w=1,h={},v=!1,y=d.document,k=Object.getPrototypeOf&&Object.getPrototypeOf(d);k=k&&k.setTimeout?k:d,{}.toString.call(d.process)==="[object process]"?p=function(x){l.nextTick(function(){O(x)})}:function(){if(d.postMessage&&!d.importScripts){var x=!0,C=d.onmessage;return d.onmessage=function(){x=!1},d.postMessage("","*"),d.onmessage=C,x}}()?(f="setImmediate$"+Math.random()+"$",m=function(x){x.source===d&&typeof x.data=="string"&&x.data.indexOf(f)===0&&O(+x.data.slice(f.length))},d.addEventListener?d.addEventListener("message",m,!1):d.attachEvent("onmessage",m),p=function(x){d.postMessage(f+x,"*")}):d.MessageChannel?((b=new MessageChannel).port1.onmessage=function(x){O(x.data)},p=function(x){b.port2.postMessage(x)}):y&&"onreadystatechange"in y.createElement("script")?(g=y.documentElement,p=function(x){var C=y.createElement("script");C.onreadystatechange=function(){O(x),C.onreadystatechange=null,g.removeChild(C),C=null},g.appendChild(C)}):p=function(x){setTimeout(O,0,x)},k.setImmediate=function(x){typeof x!="function"&&(x=new Function(""+x));for(var C=new Array(arguments.length-1),I=0;I<C.length;I++)C[I]=arguments[I+1];var E={callback:x,args:C};return h[w]=E,p(w),w++},k.clearImmediate=T}function T(x){delete h[x]}function O(x){if(v)setTimeout(O,0,x);else{var C=h[x];if(C){v=!0;try{(function(I){var E=I.callback,B=I.args;switch(B.length){case 0:E();break;case 1:E(B[0]);break;case 2:E(B[0],B[1]);break;case 3:E(B[0],B[1],B[2]);break;default:E.apply(void 0,B)}})(C)}finally{T(x),v=!1}}}}})(typeof self>"u"?c===void 0?this:c:self)}).call(this,a(0),a(7))},function(i,s){var a,c,l=i.exports={};function d(){throw new Error("setTimeout has not been defined")}function u(){throw new Error("clearTimeout has not been defined")}function p(k){if(a===setTimeout)return setTimeout(k,0);if((a===d||!a)&&setTimeout)return a=setTimeout,setTimeout(k,0);try{return a(k,0)}catch{try{return a.call(null,k,0)}catch{return a.call(this,k,0)}}}(function(){try{a=typeof setTimeout=="function"?setTimeout:d}catch{a=d}try{c=typeof clearTimeout=="function"?clearTimeout:u}catch{c=u}})();var g,b=[],f=!1,m=-1;function w(){f&&g&&(f=!1,g.length?b=g.concat(b):m=-1,b.length&&h())}function h(){if(!f){var k=p(w);f=!0;for(var T=b.length;T;){for(g=b,b=[];++m<T;)g&&g[m].run();m=-1,T=b.length}g=null,f=!1,function(O){if(c===clearTimeout)return clearTimeout(O);if((c===u||!c)&&clearTimeout)return c=clearTimeout,clearTimeout(O);try{c(O)}catch{try{return c.call(null,O)}catch{return c.call(this,O)}}}(k)}}function v(k,T){this.fun=k,this.array=T}function y(){}l.nextTick=function(k){var T=new Array(arguments.length-1);if(arguments.length>1)for(var O=1;O<arguments.length;O++)T[O-1]=arguments[O];b.push(new v(k,T)),b.length!==1||f||p(h)},v.prototype.run=function(){this.fun.apply(null,this.array)},l.title="browser",l.browser=!0,l.env={},l.argv=[],l.version="",l.versions={},l.on=y,l.addListener=y,l.once=y,l.off=y,l.removeListener=y,l.removeAllListeners=y,l.emit=y,l.prependListener=y,l.prependOnceListener=y,l.listeners=function(k){return[]},l.binding=function(k){throw new Error("process.binding is not supported")},l.cwd=function(){return"/"},l.chdir=function(k){throw new Error("process.chdir is not supported")},l.umask=function(){return 0}},function(i,s,a){function c(d,u){for(var p=0;p<u.length;p++){var g=u[p];g.enumerable=g.enumerable||!1,g.configurable=!0,"value"in g&&(g.writable=!0),Object.defineProperty(d,g.key,g)}}var l=a(9);i.exports=function(){function d(){(function(g,b){if(!(g instanceof b))throw new TypeError("Cannot call a class as a function")})(this,d)}var u,p;return u=d,(p=[{key:"urlEncode",value:function(g){return l(g)}},{key:"jsonEncode",value:function(g){return JSON.stringify(g)}},{key:"formEncode",value:function(g){if(this.isFormData(g))return g;if(this.isFormElement(g))return new FormData(g);if(this.isObject(g)){var b=new FormData;return Object.keys(g).forEach(function(f){var m=g[f];b.append(f,m)}),b}throw new Error("`data` must be an instance of Object, FormData or <FORM> HTMLElement")}},{key:"isObject",value:function(g){return Object.prototype.toString.call(g)==="[object Object]"}},{key:"isFormData",value:function(g){return g instanceof FormData}},{key:"isFormElement",value:function(g){return g instanceof HTMLFormElement}},{key:"selectFiles",value:function(){var g=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};return new Promise(function(b,f){var m=document.createElement("INPUT");m.type="file",g.multiple&&m.setAttribute("multiple","multiple"),g.accept&&m.setAttribute("accept",g.accept),m.style.display="none",document.body.appendChild(m),m.addEventListener("change",function(w){var h=w.target.files;b(h),document.body.removeChild(m)},!1),m.click()})}},{key:"parseHeaders",value:function(g){var b=g.trim().split(/[\r\n]+/),f={};return b.forEach(function(m){var w=m.split(": "),h=w.shift(),v=w.join(": ");h&&(f[h]=v)}),f}}])&&c(u,p),d}()},function(i,s){var a=function(l){return encodeURIComponent(l).replace(/[!'()*]/g,escape).replace(/%20/g,"+")},c=function(l,d,u,p){return d=d||null,u=u||"&",p=p||null,l?function(g){for(var b=new Array,f=0;f<g.length;f++)g[f]&&b.push(g[f]);return b}(Object.keys(l).map(function(g){var b,f,m=g;if(p&&(m=p+"["+m+"]"),typeof l[g]=="object"&&l[g]!==null)b=c(l[g],null,u,m);else{d&&(f=m,m=!isNaN(parseFloat(f))&&isFinite(f)?d+Number(m):m);var w=l[g];w=(w=(w=(w=w===!0?"1":w)===!1?"0":w)===0?"0":w)||"",b=a(m)+"="+a(w)}return b})).join(u).replace(/[!'()*]/g,""):""};i.exports=c}])},function(t,n,r){var i=r(2);typeof i=="string"&&(i=[[t.i,i,""]]);var s={hmr:!0,transform:void 0,insertInto:void 0};r(4)(i,s),i.locals&&(t.exports=i.locals)},function(t,n,r){(t.exports=r(3)(!1)).push([t.i,`.cdx-attaches {
  --color-line: #EFF0F1;
  --color-bg: #fff;
  --color-bg-secondary: #F8F8F8;
  --color-bg-secondary--hover: #f2f2f2;
  --color-text-secondary: #707684;
}

  .cdx-attaches--with-file {
    display: flex;
    align-items: center;
    padding: 10px 12px;
    border: 1px solid var(--color-line);
    border-radius: 7px;
    background: var(--color-bg);
  }

  .cdx-attaches--with-file .cdx-attaches__file-info {
      display: grid;
      grid-gap: 4px;
      max-width: calc(100% - 80px);
      margin: auto 0;
      flex-grow: 2;
    }

  .cdx-attaches--with-file .cdx-attaches__download-button {
      display: flex;
      align-items: center;
      background: var(--color-bg-secondary);
      padding: 6px;
      border-radius: 6px;
      margin: auto 0 auto auto;
    }

  .cdx-attaches--with-file .cdx-attaches__download-button:hover {
        background: var(--color-bg-secondary--hover);
      }

  .cdx-attaches--with-file .cdx-attaches__download-button svg {
        width: 20px;
        height: 20px;
        fill: none;
      }

  .cdx-attaches--with-file .cdx-attaches__file-icon {
      position: relative;
    }

  .cdx-attaches--with-file .cdx-attaches__file-icon-background {
        background-color: #333;

        width: 27px;
        height: 30px;
        margin-right: 12px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

  @supports(-webkit-mask-box-image: url('')){

  .cdx-attaches--with-file .cdx-attaches__file-icon-background {
          border-radius: 0;
          -webkit-mask-box-image: url("data:image/svg+xml,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0 10.3872C0 1.83334 1.83334 0 10.3872 0H13.6128C22.1667 0 24 1.83334 24 10.3872V13.6128C24 22.1667 22.1667 24 13.6128 24H10.3872C1.83334 24 0 22.1667 0 13.6128V10.3872Z' fill='black'/%3E%3C/svg%3E%0A") 48% 41% 37.9% 53.3%
      };
        }

  .cdx-attaches--with-file .cdx-attaches__file-icon-label {
        position: absolute;
        left: 3px;
        top: 11px;
        background: inherit;
        text-transform: uppercase;
        line-height: 1em;
        color: #fff;
        padding: 1px 2px;
        border-radius: 3px;
        font-size: 10px;
        font-weight: bold;
        /* box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.22); */
        font-family: ui-monospace,SFMono-Regular,SF Mono,Menlo,Consolas,Liberation Mono,monospace;
        letter-spacing: 0.02em;
      }

  .cdx-attaches--with-file .cdx-attaches__file-icon svg {
        width: 20px;
        height: 20px;
      }

  .cdx-attaches--with-file .cdx-attaches__file-icon path {
        stroke: #fff;
      }

  .cdx-attaches--with-file .cdx-attaches__size {
      color: var(--color-text-secondary);
      font-size: 12px;
      line-height: 1em;
    }

  .cdx-attaches--with-file .cdx-attaches__size::after {
        content: attr(data-size);
        margin-left: 0.2em;
      }

  .cdx-attaches--with-file .cdx-attaches__title {
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      outline: none;
      max-width: 90%;
      font-size: 14px;
      font-weight: 500;
      line-height: 1em;
    }

  .cdx-attaches--with-file .cdx-attaches__title:empty::before {
      content: attr(data-placeholder);
      color: #7b7e89;
    }

  .cdx-attaches--loading .cdx-attaches__title,
    .cdx-attaches--loading .cdx-attaches__file-icon,
    .cdx-attaches--loading .cdx-attaches__size,
    .cdx-attaches--loading .cdx-attaches__download-button,
    .cdx-attaches--loading .cdx-attaches__button {
      opacity: 0;
      font-size: 0;
    }

  .cdx-attaches__button {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #000;
    border-radius: 7px;
    font-weight: 500;
  }

  .cdx-attaches__button svg {
      margin-top: 0;
    }
`,""])},function(t,n){t.exports=function(r){var i=[];return i.toString=function(){return this.map(function(s){var a=function(c,l){var d=c[1]||"",u=c[3];if(!u)return d;if(l&&typeof btoa=="function"){var p=(b=u,"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(b))))+" */"),g=u.sources.map(function(f){return"/*# sourceURL="+u.sourceRoot+f+" */"});return[d].concat(g).concat([p]).join(`
`)}var b;return[d].join(`
`)}(s,r);return s[2]?"@media "+s[2]+"{"+a+"}":a}).join("")},i.i=function(s,a){typeof s=="string"&&(s=[[null,s,""]]);for(var c={},l=0;l<this.length;l++){var d=this[l][0];typeof d=="number"&&(c[d]=!0)}for(l=0;l<s.length;l++){var u=s[l];typeof u[0]=="number"&&c[u[0]]||(a&&!u[2]?u[2]=a:a&&(u[2]="("+u[2]+") and ("+a+")"),i.push(u))}},i}},function(t,n,r){var i,s,a={},c=(i=function(){return window&&document&&document.all&&!window.atob},function(){return s===void 0&&(s=i.apply(this,arguments)),s}),l=function(E){return document.querySelector(E)},d=function(E){var B={};return function(_){if(typeof _=="function")return _();if(B[_]===void 0){var L=l.call(this,_);if(window.HTMLIFrameElement&&L instanceof window.HTMLIFrameElement)try{L=L.contentDocument.head}catch{L=null}B[_]=L}return B[_]}}(),u=null,p=0,g=[],b=r(5);function f(E,B){for(var _=0;_<E.length;_++){var L=E[_],M=a[L.id];if(M){M.refs++;for(var A=0;A<M.parts.length;A++)M.parts[A](L.parts[A]);for(;A<L.parts.length;A++)M.parts.push(k(L.parts[A],B))}else{var H=[];for(A=0;A<L.parts.length;A++)H.push(k(L.parts[A],B));a[L.id]={id:L.id,refs:1,parts:H}}}}function m(E,B){for(var _=[],L={},M=0;M<E.length;M++){var A=E[M],H=B.base?A[0]+B.base:A[0],R={css:A[1],media:A[2],sourceMap:A[3]};L[H]?L[H].parts.push(R):_.push(L[H]={id:H,parts:[R]})}return _}function w(E,B){var _=d(E.insertInto);if(!_)throw new Error("Couldn't find a style target. This probably means that the value for the 'insertInto' parameter is invalid.");var L=g[g.length-1];if(E.insertAt==="top")L?L.nextSibling?_.insertBefore(B,L.nextSibling):_.appendChild(B):_.insertBefore(B,_.firstChild),g.push(B);else if(E.insertAt==="bottom")_.appendChild(B);else{if(typeof E.insertAt!="object"||!E.insertAt.before)throw new Error(`[Style Loader]

 Invalid value for parameter 'insertAt' ('options.insertAt') found.
 Must be 'top', 'bottom', or Object.
 (https://github.com/webpack-contrib/style-loader#insertat)
`);var M=d(E.insertInto+" "+E.insertAt.before);_.insertBefore(B,M)}}function h(E){if(E.parentNode===null)return!1;E.parentNode.removeChild(E);var B=g.indexOf(E);B>=0&&g.splice(B,1)}function v(E){var B=document.createElement("style");return E.attrs.type===void 0&&(E.attrs.type="text/css"),y(B,E.attrs),w(E,B),B}function y(E,B){Object.keys(B).forEach(function(_){E.setAttribute(_,B[_])})}function k(E,B){var _,L,M,A;if(B.transform&&E.css){if(!(A=B.transform(E.css)))return function(){};E.css=A}if(B.singleton){var H=p++;_=u||(u=v(B)),L=x.bind(null,_,H,!1),M=x.bind(null,_,H,!0)}else E.sourceMap&&typeof URL=="function"&&typeof URL.createObjectURL=="function"&&typeof URL.revokeObjectURL=="function"&&typeof Blob=="function"&&typeof btoa=="function"?(_=function(R){var oe=document.createElement("link");return R.attrs.type===void 0&&(R.attrs.type="text/css"),R.attrs.rel="stylesheet",y(oe,R.attrs),w(R,oe),oe}(B),L=I.bind(null,_,B),M=function(){h(_),_.href&&URL.revokeObjectURL(_.href)}):(_=v(B),L=C.bind(null,_),M=function(){h(_)});return L(E),function(R){if(R){if(R.css===E.css&&R.media===E.media&&R.sourceMap===E.sourceMap)return;L(E=R)}else M()}}t.exports=function(E,B){if(typeof DEBUG<"u"&&DEBUG&&typeof document!="object")throw new Error("The style-loader cannot be used in a non-browser environment");(B=B||{}).attrs=typeof B.attrs=="object"?B.attrs:{},B.singleton||typeof B.singleton=="boolean"||(B.singleton=c()),B.insertInto||(B.insertInto="head"),B.insertAt||(B.insertAt="bottom");var _=m(E,B);return f(_,B),function(L){for(var M=[],A=0;A<_.length;A++){var H=_[A];(R=a[H.id]).refs--,M.push(R)}for(L&&f(m(L,B),B),A=0;A<M.length;A++){var R;if((R=M[A]).refs===0){for(var oe=0;oe<R.parts.length;oe++)R.parts[oe]();delete a[R.id]}}}};var T,O=(T=[],function(E,B){return T[E]=B,T.filter(Boolean).join(`
`)});function x(E,B,_,L){var M=_?"":L.css;if(E.styleSheet)E.styleSheet.cssText=O(B,M);else{var A=document.createTextNode(M),H=E.childNodes;H[B]&&E.removeChild(H[B]),H.length?E.insertBefore(A,H[B]):E.appendChild(A)}}function C(E,B){var _=B.css,L=B.media;if(L&&E.setAttribute("media",L),E.styleSheet)E.styleSheet.cssText=_;else{for(;E.firstChild;)E.removeChild(E.firstChild);E.appendChild(document.createTextNode(_))}}function I(E,B,_){var L=_.css,M=_.sourceMap,A=B.convertToAbsoluteUrls===void 0&&M;(B.convertToAbsoluteUrls||A)&&(L=b(L)),M&&(L+=`
/*# sourceMappingURL=data:application/json;base64,`+btoa(unescape(encodeURIComponent(JSON.stringify(M))))+" */");var H=new Blob([L],{type:"text/css"}),R=E.href;E.href=URL.createObjectURL(H),R&&URL.revokeObjectURL(R)}},function(t,n){t.exports=function(r){var i=typeof window<"u"&&window.location;if(!i)throw new Error("fixUrls requires window.location");if(!r||typeof r!="string")return r;var s=i.protocol+"//"+i.host,a=s+i.pathname.replace(/\/[^\/]*$/,"/");return r.replace(/url\s*\(((?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)\)/gi,function(c,l){var d,u=l.trim().replace(/^"(.*)"$/,function(p,g){return g}).replace(/^'(.*)'$/,function(p,g){return g});return/^(#|data:|http:\/\/|https:\/\/|file:\/\/\/|\s*$)/i.test(u)?c:(d=u.indexOf("//")===0?u:u.indexOf("/")===0?s+u:a+u.replace(/^\.\//,""),"url("+JSON.stringify(d)+")")})}},function(t,n,r){r.r(n),r.d(n,"default",function(){return f}),r(1);var i=r(0),s=r.n(i);function a(m,w){for(var h=0;h<w.length;h++){var v=w[h];v.enumerable=v.enumerable||!1,v.configurable=!0,"value"in v&&(v.writable=!0),Object.defineProperty(m,v.key,v)}}var c=function(){function m(v){var y=v.config,k=v.onUpload,T=v.onError;(function(O,x){if(!(O instanceof x))throw new TypeError("Cannot call a class as a function")})(this,m),this.config=y,this.onUpload=k,this.onError=T}var w,h;return w=m,(h=[{key:"uploadSelectedFile",value:function(v){var y=this,k=v.onPreview;(this.config.uploader&&typeof this.config.uploader.uploadByFile=="function"?s.a.selectFiles({accept:this.config.types}).then(function(T){k();var O,x=y.config.uploader.uploadByFile(T[0]);return(O=x)&&typeof O.then=="function"||console.warn("Custom uploader method uploadByFile should return a Promise"),x}):s.a.transport({url:this.config.endpoint,accept:this.config.types,beforeSend:function(){return k()},fieldName:this.config.field,headers:this.config.additionalRequestHeaders||{}}).then(function(T){return T.body})).then(function(T){y.onUpload(T)}).catch(function(T){var O=T.body,x=O&&O.message?O.message:y.config.errorMessage;y.onError(x)})}}])&&a(w.prototype,h),m}();function l(m){return function(w){if(Array.isArray(w))return d(w)}(m)||function(w){if(typeof Symbol<"u"&&w[Symbol.iterator]!=null||w["@@iterator"]!=null)return Array.from(w)}(m)||function(w,h){if(w){if(typeof w=="string")return d(w,h);var v=Object.prototype.toString.call(w).slice(8,-1);if(v==="Object"&&w.constructor&&(v=w.constructor.name),v==="Map"||v==="Set")return Array.from(w);if(v==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(v))return d(w,h)}}(m)||function(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}()}function d(m,w){(w==null||w>m.length)&&(w=m.length);for(var h=0,v=new Array(w);h<w;h++)v[h]=m[h];return v}function u(m){var w,h=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null,v=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},y=document.createElement(m);Array.isArray(h)?(w=y.classList).add.apply(w,l(h)):h&&y.classList.add(h);for(var k in v)y[k]=v[k];return y}function p(m){return Object.keys(m).length===0}const g='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.3236 8.43554L9.49533 12.1908C9.13119 12.5505 8.93118 13.043 8.9393 13.5598C8.94741 14.0767 9.163 14.5757 9.53862 14.947C9.91424 15.3182 10.4191 15.5314 10.9422 15.5397C11.4653 15.5479 11.9637 15.3504 12.3279 14.9908L16.1562 11.2355C16.8845 10.5161 17.2845 9.53123 17.2682 8.4975C17.252 7.46376 16.8208 6.46583 16.0696 5.72324C15.3184 4.98066 14.3086 4.55425 13.2624 4.53782C12.2162 4.52138 11.2193 4.91627 10.4911 5.63562L6.66277 9.39093C5.57035 10.4699 4.97032 11.9473 4.99467 13.4979C5.01903 15.0485 5.66578 16.5454 6.79264 17.6592C7.9195 18.7731 9.43417 19.4127 11.0034 19.4374C12.5727 19.462 14.068 18.8697 15.1604 17.7907L18.9887 14.0354"/></svg>';function b(m,w){for(var h=0;h<w.length;h++){var v=w[h];v.enumerable=v.enumerable||!1,v.configurable=!0,"value"in v&&(v.writable=!0),Object.defineProperty(m,v.key,v)}}var f=function(){function m(y){var k=this,T=y.data,O=y.config,x=y.api,C=y.readOnly;(function(I,E){if(!(I instanceof E))throw new TypeError("Cannot call a class as a function")})(this,m),this.api=x,this.readOnly=C,this.nodes={wrapper:null,button:null,title:null},this._data={file:{},title:""},this.config={endpoint:O.endpoint||"",field:O.field||"file",types:O.types||"*",buttonText:O.buttonText||"Select file to upload",errorMessage:O.errorMessage||"File upload failed",uploader:O.uploader||void 0,additionalRequestHeaders:O.additionalRequestHeaders||{}},T===void 0||p(T)||(this.data=T),this.uploader=new c({config:this.config,onUpload:function(I){return k.onUpload(I)},onError:function(I){return k.uploadingFailed(I)}}),this.enableFileUpload=this.enableFileUpload.bind(this)}var w,h,v;return w=m,v=[{key:"toolbox",get:function(){return{icon:g,title:"Attachment"}}},{key:"isReadOnlySupported",get:function(){return!0}}],(h=[{key:"CSS",get:function(){return{baseClass:this.api.styles.block,apiButton:this.api.styles.button,loader:this.api.styles.loader,wrapper:"cdx-attaches",wrapperWithFile:"cdx-attaches--with-file",wrapperLoading:"cdx-attaches--loading",button:"cdx-attaches__button",title:"cdx-attaches__title",size:"cdx-attaches__size",downloadButton:"cdx-attaches__download-button",fileInfo:"cdx-attaches__file-info",fileIcon:"cdx-attaches__file-icon",fileIconBackground:"cdx-attaches__file-icon-background",fileIconLabel:"cdx-attaches__file-icon-label"}}},{key:"EXTENSIONS",get:function(){return{doc:"#1483E9",docx:"#1483E9",odt:"#1483E9",pdf:"#DB2F2F",rtf:"#744FDC",tex:"#5a5a5b",txt:"#5a5a5b",pptx:"#E35200",ppt:"#E35200",mp3:"#eab456",mp4:"#f676a6",xls:"#11AE3D",html:"#2988f0",htm:"#2988f0",png:"#AA2284",jpg:"#D13359",jpeg:"#D13359",gif:"#f6af76",zip:"#4f566f",rar:"#4f566f",exe:"#e26f6f",svg:"#bf5252",key:"#00B2FF",sketch:"#FFC700",ai:"#FB601D",psd:"#388ae5",dmg:"#e26f6f",json:"#2988f0",csv:"#11AE3D"}}},{key:"validate",value:function(y){return!p(y.file)}},{key:"save",value:function(y){if(this.pluginHasData()){var k=y.querySelector(".".concat(this.CSS.title));k&&Object.assign(this.data,{title:k.innerHTML})}return this.data}},{key:"render",value:function(){var y=u("div",this.CSS.baseClass);return this.nodes.wrapper=u("div",this.CSS.wrapper),this.pluginHasData()?this.showFileData():this.prepareUploadButton(),y.appendChild(this.nodes.wrapper),y}},{key:"prepareUploadButton",value:function(){this.nodes.button=u("div",[this.CSS.apiButton,this.CSS.button]),this.nodes.button.innerHTML="".concat(g," ").concat(this.config.buttonText),this.readOnly||this.nodes.button.addEventListener("click",this.enableFileUpload),this.nodes.wrapper.appendChild(this.nodes.button)}},{key:"appendCallback",value:function(){this.nodes.button.click()}},{key:"pluginHasData",value:function(){return this.data.title!==""||Object.values(this.data.file).some(function(y){return y!==void 0})}},{key:"enableFileUpload",value:function(){var y=this;this.uploader.uploadSelectedFile({onPreview:function(){y.nodes.wrapper.classList.add(y.CSS.wrapperLoading,y.CSS.loader)}})}},{key:"onUpload",value:function(y){var k,T,O,x=y;try{x.success&&x.file!==void 0&&!p(x.file)?(this.data={file:x.file,title:x.file.title||""},this.nodes.button.remove(),this.showFileData(),k=this.nodes.title,T=document.createRange(),O=window.getSelection(),T.selectNodeContents(k),T.collapse(!1),O.removeAllRanges(),O.addRange(T),this.removeLoader()):this.uploadingFailed(this.config.errorMessage)}catch(C){console.error("Attaches tool error:",C),this.uploadingFailed(this.config.errorMessage)}this.api.blocks.getBlockByIndex(this.api.blocks.getCurrentBlockIndex()).dispatchChange()}},{key:"appendFileIcon",value:function(y){var k,T=y.extension||((k=y.name)===void 0?"":k.split(".").pop()),O=this.EXTENSIONS[T],x=u("div",this.CSS.fileIcon),C=u("div",this.CSS.fileIconBackground);if(O&&(C.style.backgroundColor=O),x.appendChild(C),T){var I=T;T.length>4&&(I=T.substring(0,4)+"…");var E=u("div",this.CSS.fileIconLabel,{textContent:I,title:T});O&&(E.style.backgroundColor=O),x.appendChild(E)}else C.innerHTML=g;this.nodes.wrapper.appendChild(x)}},{key:"removeLoader",value:function(){var y=this;setTimeout(function(){return y.nodes.wrapper.classList.remove(y.CSS.wrapperLoading,y.CSS.loader)},500)}},{key:"showFileData",value:function(){this.nodes.wrapper.classList.add(this.CSS.wrapperWithFile);var y=this.data,k=y.file,T=y.title;this.appendFileIcon(k);var O=u("div",this.CSS.fileInfo);if(this.nodes.title=u("div",this.CSS.title,{contentEditable:this.readOnly===!1}),this.nodes.title.dataset.placeholder=this.api.i18n.t("File title"),this.nodes.title.textContent=T||"",O.appendChild(this.nodes.title),k.size){var x,C,I=u("div",this.CSS.size);Math.log10(+k.size)>=6?(x="MiB",C=k.size/Math.pow(2,20)):(x="KiB",C=k.size/Math.pow(2,10)),I.textContent=C.toFixed(1),I.setAttribute("data-size",x),O.appendChild(I)}if(this.nodes.wrapper.appendChild(O),k.url!==void 0){var E=u("a",this.CSS.downloadButton,{innerHTML:'<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M7 10L11.8586 14.8586C11.9367 14.9367 12.0633 14.9367 12.1414 14.8586L17 10"/></svg>',href:k.url,target:"_blank",rel:"nofollow noindex noreferrer"});this.nodes.wrapper.appendChild(E)}}},{key:"uploadingFailed",value:function(y){this.api.notifier.show({message:y,style:"error"}),this.removeLoader()}},{key:"data",get:function(){return this._data},set:function(y){var k=y.file,T=y.title;this._data={file:k,title:T}}}])&&b(w.prototype,h),v&&b(w,v),m}()}]).default})}(Zt)),Zt.exports}var oh=th();const nh=ns(oh);(function(){try{if(typeof document<"u"){var o=document.createElement("style");o.appendChild(document.createTextNode(".cdx-marker{background:rgba(245,235,111,.29);padding:3px 0}")),document.head.appendChild(o)}}catch(e){console.error("vite-plugin-css-injected-by-js",e)}})();const rh='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-width="2" d="M11.3536 9.31802L12.7678 7.90381C13.5488 7.12276 14.8151 7.12276 15.5962 7.90381C16.3772 8.68486 16.3772 9.95119 15.5962 10.7322L14.182 12.1464M11.3536 9.31802L7.96729 12.7043C7.40889 13.2627 7.02827 13.9739 6.8734 14.7482L6.69798 15.6253C6.55804 16.325 7.17496 16.942 7.87468 16.802L8.75176 16.6266C9.52612 16.4717 10.2373 16.0911 10.7957 15.5327L14.182 12.1464M11.3536 9.31802L14.182 12.1464"/><line x1="15" x2="19" y1="17" y2="17" stroke="currentColor" stroke-linecap="round" stroke-width="2"/></svg>';class Ae{static get CSS(){return"cdx-marker"}constructor({api:e}){this.api=e,this.button=null,this.tag="MARK",this.iconClasses={base:this.api.styles.inlineToolButton,active:this.api.styles.inlineToolButtonActive}}static get isInline(){return!0}render(){return this.button=document.createElement("button"),this.button.type="button",this.button.classList.add(this.iconClasses.base),this.button.innerHTML=this.toolboxIcon,this.button}surround(e){if(!e)return;let t=this.api.selection.findParentTag(this.tag,Ae.CSS);t?this.unwrap(t):this.wrap(e)}wrap(e){let t=document.createElement(this.tag);t.classList.add(Ae.CSS),t.appendChild(e.extractContents()),e.insertNode(t),this.api.selection.expandToTag(t)}unwrap(e){this.api.selection.expandToTag(e);let t=window.getSelection(),n=t.getRangeAt(0),r=n.extractContents();e.parentNode.removeChild(e),n.insertNode(r),t.removeAllRanges(),t.addRange(n)}checkState(){const e=this.api.selection.findParentTag(this.tag,Ae.CSS);this.button.classList.toggle(this.iconClasses.active,!!e)}get toolboxIcon(){return rh}static get sanitize(){return{mark:{class:Ae.CSS}}}}new Quill("#editor",{theme:"snow"});const ih={time:1742315418218,blocks:[{id:"mhTl6ghSkV",type:"paragraph",data:{text:"Hey. Meet the new Editor. On this picture you can see it in action. Then, try a demo 🤓"}},{id:"l98dyx3yjb",type:"header",data:{text:"Key features",level:3}},{id:"os_YI4eub4",type:"list",data:{style:"unordered",items:["It is a block-style editor","It returns clean data output in JSON","Designed to be extendable and pluggable with a <a href='https://editorjs.io/creating-a-block-tool'>simple API</a>"]}},{id:"1yKeXKxN7-",type:"header",data:{text:"What does it mean «block-styled editor»",level:3}},{id:"TcUNySG15P",type:"paragraph",data:{text:"Workspace in classic editors is made of a single contenteditable element, used to create different HTML markups. Editor.js workspace consists of separate Blocks: paragraphs, headings, images, lists, quotes, etc. Each of them is an independent <sup data-tune='footnotes'>1</sup> contenteditable element (or more complex structure) provided by Plugin and united by Editor's Core."},tunes:{footnotes:["It works more stable then in other WYSIWYG editors. Same time it has smooth and well-known arrow navigation behavior like classic editors."]}},{id:"M3UXyblhAo",type:"header",data:{text:"What does it mean clean data output?",level:3}},{id:"KOcIofZ3Z1",type:"paragraph",data:{text:"There are dozens of ready-to-use Blocks and a simple API <sup data-tune='footnotes'>2</sup> for creating any Block you need. For example, you can implement Blocks for Tweets, Instagram posts, surveys and polls, CTA buttons, and even games."},tunes:{footnotes:["Just take a look at our Creating Block Tool guide. You'll be surprised."]}},{id:"ksCokKAhQw",type:"paragraph",data:{text:"Classic WYSIWYG editors produce raw HTML-markup with both content data and content appearance. On the contrary, <mark class='cdx-marker'>Editor.js outputs JSON object</mark> with data of each Block."}},{id:"XKNT99-qqS",type:"attaches",data:{file:{url:"https://drive.google.com/user/catalog/my-file.pdf",size:12902,name:"file.pdf",extension:"pdf"},title:"My file"}},{id:"7RosVX2kcH",type:"paragraph",data:{text:"Given data can be used as you want: render with HTML for Web clients, render natively for mobile apps, create the markup for Facebook Instant Articles or Google AMP, generate an audio version, and so on."}},{id:"eq06PsNsab",type:"paragraph",data:{text:"Clean data is useful to sanitize, validate and process on the backend."}}]},sh=new qc({holder:"editorjs",tools:{header:{class:Qc,inlineToolbar:!0},list:{class:kt,inlineToolbar:!0},image:{class:wt,config:{endpoints:{byFile:"https://example.com/upload-image"}}},attaches:{class:nh},marker:{class:Ae}},data:ih});document.getElementById("save-button").addEventListener("click",()=>{sh.save().then(o=>{console.log("Saved data: ",o)}).catch(o=>{console.log("Saving failed: ",o)})});
