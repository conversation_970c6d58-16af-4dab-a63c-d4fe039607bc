import"../../admin.bundle-DI9_jvUJ.js";import{u as c}from"../../apexcharts.esm-BofaT7g3.js";import"../../main-Cyta4iCA.js";function p(s){const e=getComputedStyle(document.documentElement).getPropertyValue(s).trim();return/^\d{1,3},\s*\d{1,3},\s*\d{1,3}$/.test(e)?`rgb(${e})`:e}var l=[];const u=s=>{const e=JSON.parse(JSON.stringify(s)),r=t=>{for(const o in t)typeof t[o]=="string"&&t[o].startsWith("--dx-")?t[o]=p(t[o]):typeof t[o]=="object"&&t[o]!==null&&r(t[o])};return r(e),e};function d(s=""){s&&document.documentElement.setAttribute("data-colors",s),l.forEach(e=>{const r=JSON.parse(JSON.stringify(e[0].data)),t=u(structuredClone(r));e[0].chart&&e[0].chart.destroy();var o=new c(document.querySelector("#"+e[0].id),t);o.render(),e[0].chart=o})}document.querySelectorAll('input[name="data-colors"]').forEach(s=>{s.addEventListener("change",function(){i(this.value)})});document.querySelectorAll('input[name="data-bs-theme"]').forEach(s=>{s.addEventListener("change",function(){i(this.value)})});var n;(n=document.getElementById("darkModeButton"))==null||n.addEventListener("click",function(){i(this.value)});function i(s){setTimeout(()=>{d(s)},0)}var a={series:[{name:"Earnings",data:[67,48,85,51,93,109,116]}],chart:{defaultLocale:"en",height:115,type:"area",sparkline:{enabled:!0},zoom:{enabled:!1}},dataLabels:{enabled:!1},stroke:{width:3,curve:"smooth",dashArray:2},legend:{tooltipHoverFormatter:function(s,e){return s+" - <strong>"+e.w.globals.series[e.seriesIndex][e.dataPointIndex]+"</strong>"}},markers:{size:0,hover:{sizeOffset:6}},labels:["Mar","Apr","May","Jun","Jul","Aug","Sep"],yaxis:{title:{text:"Growth"},labels:{formatter:function(s){return"$"+s.toFixed(0)+"k"}}},grid:{padding:{top:-20,right:0,bottom:0}},colors:["--dx-primary"]};l.push([{id:"projectStatusChart",data:a}]);var a={series:[55,33,46],chart:{height:210,type:"donut",dropShadow:{enabled:!0,color:"#111",top:-1,left:3,blur:3,opacity:.2}},stroke:{width:0},plotOptions:{pie:{donut:{labels:{show:!1}}}},labels:["Afternoon","Evening","Morning"],dataLabels:{enabled:!1},fill:{type:"pattern",pattern:{style:"squares"}},states:{hover:{filter:"none"}},theme:{palette:"palette2"},legend:{show:!1},colors:["--dx-primary","--dx-secondary","--dx-success"]};l.push([{id:"dailyWorkingReportsChart",data:a}]);var a={series:[32],chart:{height:60,width:50,type:"radialBar",sparkline:{enabled:!0}},plotOptions:{radialBar:{hollow:{size:"70%"}}},plotOptions:{radialBar:{dataLabels:{show:!1},hollow:{size:"20%"}}},labels:["Progress"],colors:["--dx-primary"]};l.push([{id:"myTask1Chart",data:a}]);var a={series:[45],chart:{height:60,width:50,type:"radialBar",sparkline:{enabled:!0}},plotOptions:{radialBar:{hollow:{size:"70%"}}},plotOptions:{radialBar:{dataLabels:{show:!1},hollow:{size:"20%"}}},labels:["Progress"],colors:["--dx-success"]};l.push([{id:"myTask2Chart",data:a}]);var a={series:[79],chart:{height:60,width:50,type:"radialBar",sparkline:{enabled:!0}},plotOptions:{radialBar:{hollow:{size:"70%"}}},plotOptions:{radialBar:{dataLabels:{show:!1},hollow:{size:"20%"}}},labels:["Progress"],colors:["--dx-danger"]};l.push([{id:"myTask3Chart",data:a}]);var a={series:[100],chart:{height:60,width:50,type:"radialBar",sparkline:{enabled:!0}},plotOptions:{radialBar:{hollow:{size:"70%"}}},plotOptions:{radialBar:{dataLabels:{show:!1},hollow:{size:"20%"}}},labels:["Progress"],colors:["--dx-warning"]};l.push([{id:"myTask4Chart",data:a}]);var a={series:[87],chart:{height:60,width:50,type:"radialBar",sparkline:{enabled:!0}},plotOptions:{radialBar:{hollow:{size:"70%"}}},plotOptions:{radialBar:{dataLabels:{show:!1},hollow:{size:"20%"}}},labels:["Progress"],colors:["--dx-secondary"]};l.push([{id:"myTask5Chart",data:a}]);var a={series:[26],chart:{height:60,width:50,type:"radialBar",sparkline:{enabled:!0}},plotOptions:{radialBar:{hollow:{size:"70%"}}},plotOptions:{radialBar:{dataLabels:{show:!1},hollow:{size:"20%"}}},labels:["Progress"],colors:["--dx-orange"]};l.push([{id:"myTask6Chart",data:a}]);var a={series:[49],chart:{height:60,width:50,type:"radialBar",sparkline:{enabled:!0}},plotOptions:{radialBar:{hollow:{size:"70%"}}},plotOptions:{radialBar:{dataLabels:{show:!1},hollow:{size:"20%"}}},labels:["Progress"],colors:["--dx-info"]};l.push([{id:"myTask7Chart",data:a}]);var a={series:[73],chart:{height:60,width:50,type:"radialBar",sparkline:{enabled:!0}},plotOptions:{radialBar:{hollow:{size:"70%"}}},plotOptions:{radialBar:{dataLabels:{show:!1},hollow:{size:"20%"}}},labels:["Progress"],colors:["--dx-pink"]};l.push([{id:"myTask8Chart",data:a}]);d();class k{constructor(e,r=[]){this.container=document.querySelector(e),this.defaultSelectedIndexes=r,this.tasks=[],this.init()}init(){this.extractTasksFromDOM(),this.setDefaultSelectedTasks(),this.addEventListeners()}extractTasksFromDOM(){this.container.querySelectorAll(".task-list-item").forEach((r,t)=>{const o=r.querySelector(".form-check-input"),h=r.querySelector(".form-check-label");this.tasks.push({id:o.id,text:h.textContent.trim(),completed:o.checked,index:t})})}setDefaultSelectedTasks(){this.defaultSelectedIndexes.forEach(e=>{if(e<this.tasks.length){const r=this.tasks[e].id,t=document.getElementById(r);t&&(t.checked=!0,this.tasks[e].completed=!0)}})}addEventListeners(){this.tasks.forEach(e=>{const r=document.getElementById(e.id);r&&r.addEventListener("change",t=>{this.updateTaskStatus(e.id,t.target.checked)})})}updateTaskStatus(e,r){const t=this.tasks.findIndex(o=>o.id===e);t!==-1&&(this.tasks[t].completed=r,console.log(`Task "${this.tasks[t].text}" is now ${r?"completed":"pending"}`))}getAllTasks(){return this.tasks}getCompletedTasks(){return this.tasks.filter(e=>e.completed)}getPendingTasks(){return this.tasks.filter(e=>!e.completed)}}document.addEventListener("DOMContentLoaded",()=>{new k(".task-list",[2,6])});
