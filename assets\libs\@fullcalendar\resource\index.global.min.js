/*!
FullCalendar Resource Plugin v6.1.17
Docs & License: https://fullcalendar.io/docs/premium
(c) 2024 Adam Shaw
*/
FullCalendar.Resource=function(e,t,r,s,n){"use strict";function o(e){return e&&e.__esModule?e:{default:e}}var i=o(r);function u(e,t){let{resourceEditable:r}=e;if(null==r){let s=e.sourceId&&t.getCurrentData().eventSources[e.sourceId];s&&(r=s.extendedProps.resourceEditable),null==r&&(r=t.options.eventResourceEditable,null==r&&(r=t.options.editable))}return r}function a(e,t,r,n){if(t){let t=function(e,t){let r={};for(let s in e){let n=e[s];for(let e of t[n.defId].resourceIds)r[e]=!0}return r}(function(e,t){return s.filterHash(e,e=>s.rangesIntersect(e.range,t))}(r.instances,n),r.defs);return Object.assign(t,function(e,t){let r={};for(let s in e){let e;for(;(e=t[s])&&(s=e.parentId,s);)r[s]=!0}return r}(t,e)),s.filterHash(e,(e,r)=>t[r])}return e}function c(e){return s.mapHash(e,e=>e.ui)}function l(e,t,r){return s.mapHash(e,(e,n)=>n?function(e,t,r){let n=[];for(let e of t.resourceIds)r[e]&&n.unshift(r[e]);return n.unshift(e),s.combineEventUis(n)}(e,t[n],r):e)}let d=[];function p(e){d.push(e)}function f(e){return d[e]}function g(){return d}const h={id:String,resources:s.identity,url:String,method:String,startParam:String,endParam:String,timeZoneParam:String,extraParams:s.identity};function m(e){let t;if("string"==typeof e?t={url:e}:"function"==typeof e||Array.isArray(e)?t={resources:e}:"object"==typeof e&&e&&(t=e),t){let{refined:r,extra:n}=s.refineProps(t,h);!function(e){for(let t in e)console.warn(`Unknown resource prop '${t}'`)}(n);let o=function(e){let t=g();for(let r=t.length-1;r>=0;r-=1){let s=t[r].parseMeta(e);if(s)return{meta:s,sourceDefId:r}}return null}(r);if(o)return{_raw:e,sourceId:s.guid(),sourceDefId:o.sourceDefId,meta:o.meta,publicId:r.id||"",isFetching:!1,latestFetchId:"",fetchRange:null}}return null}function R(e,t,r){let{options:n,dateProfile:o}=r;if(!e||!t)return E(n.initialResources||n.resources,o.activeRange,n.refetchResourcesOnNavigate,r);switch(t.type){case"RESET_RESOURCE_SOURCE":return E(t.resourceSourceInput,o.activeRange,n.refetchResourcesOnNavigate,r);case"PREV":case"NEXT":case"CHANGE_DATE":case"CHANGE_VIEW_TYPE":return function(e,t,r,n){if(r&&!function(e){return Boolean(f(e.sourceDefId).ignoreRange)}(e)&&(!e.fetchRange||!s.rangesEqual(e.fetchRange,t)))return S(e,t,n);return e}(e,o.activeRange,n.refetchResourcesOnNavigate,r);case"RECEIVE_RESOURCES":case"RECEIVE_RESOURCE_ERROR":return function(e,t,r){if(t===e.latestFetchId)return Object.assign(Object.assign({},e),{isFetching:!1,fetchRange:r});return e}(e,t.fetchId,t.fetchRange);case"REFETCH_RESOURCES":return S(e,o.activeRange,r);default:return e}}function E(e,t,r,s){if(e){let n=m(e);return n=S(n,r?t:null,s),n}return null}function S(e,t,r){let n=f(e.sourceDefId),o=s.guid();return n.fetch({resourceSource:e,range:t,context:r},e=>{r.dispatch({type:"RECEIVE_RESOURCES",fetchId:o,fetchRange:t,rawResources:e.rawResources})},e=>{r.dispatch({type:"RECEIVE_RESOURCE_ERROR",fetchId:o,fetchRange:t,error:e})}),Object.assign(Object.assign({},e),{isFetching:!0,latestFetchId:o})}const C={id:String,parentId:String,children:s.identity,title:String,businessHours:s.identity,extendedProps:s.identity,eventEditable:Boolean,eventStartEditable:Boolean,eventDurationEditable:Boolean,eventConstraint:s.identity,eventOverlap:Boolean,eventAllow:s.identity,eventClassNames:s.parseClassNames,eventBackgroundColor:String,eventBorderColor:String,eventTextColor:String,eventColor:String};function b(e,t="",r,n){let{refined:o,extra:i}=s.refineProps(e,C),u={id:o.id||"_fc:"+s.guid(),parentId:o.parentId||t,title:o.title||"",businessHours:o.businessHours?s.parseBusinessHours(o.businessHours,n):null,ui:s.createEventUi({editable:o.eventEditable,startEditable:o.eventStartEditable,durationEditable:o.eventDurationEditable,constraint:o.eventConstraint,overlap:o.eventOverlap,allow:o.eventAllow,classNames:o.eventClassNames,backgroundColor:o.eventBackgroundColor,borderColor:o.eventBorderColor,textColor:o.eventTextColor,color:o.eventColor},n),extendedProps:Object.assign(Object.assign({},i),o.extendedProps)};if(Object.freeze(u.ui.classNames),Object.freeze(u.extendedProps),r[u.id]);else if(r[u.id]=u,o.children)for(let e of o.children)b(e,u.id,r,n);return u}function v(e){return 0===e.indexOf("_fc:")?"":e}function y(e,t,r,s){if(!e||!t)return{};switch(t.type){case"RECEIVE_RESOURCES":return function(e,t,r,s,n){if(s.latestFetchId===r){let e={};for(let r of t)b(r,"",e,n);return e}return e}(e,t.rawResources,t.fetchId,r,s);case"ADD_RESOURCE":return n=e,o=t.resourceHash,Object.assign(Object.assign({},n),o);case"REMOVE_RESOURCE":return function(e,t){let r=Object.assign({},e);delete r[t];for(let e in r)r[e].parentId===t&&(r[e]=Object.assign(Object.assign({},r[e]),{parentId:""}));return r}(e,t.resourceId);case"SET_RESOURCE_PROP":return function(e,t,r,s){let n=e[t];if(n)return Object.assign(Object.assign({},e),{[t]:Object.assign(Object.assign({},n),{[r]:s})});return e}(e,t.resourceId,t.propName,t.propValue);case"SET_RESOURCE_EXTENDED_PROP":return function(e,t,r,s){let n=e[t];if(n)return Object.assign(Object.assign({},e),{[t]:Object.assign(Object.assign({},n),{extendedProps:Object.assign(Object.assign({},n.extendedProps),{[r]:s})})});return e}(e,t.resourceId,t.propName,t.propValue);default:return e}var n,o}const I={resourceId:String,resourceIds:s.identity,resourceEditable:Boolean};class O{constructor(e,t){this._context=e,this._resource=t}setProp(e,t){let r=this._resource;this._context.dispatch({type:"SET_RESOURCE_PROP",resourceId:r.id,propName:e,propValue:t}),this.sync(r)}setExtendedProp(e,t){let r=this._resource;this._context.dispatch({type:"SET_RESOURCE_EXTENDED_PROP",resourceId:r.id,propName:e,propValue:t}),this.sync(r)}sync(e){let t=this._context,r=e.id;this._resource=t.getCurrentData().resourceStore[r],t.emitter.trigger("resourceChange",{oldResource:new O(t,e),resource:this,revert(){t.dispatch({type:"ADD_RESOURCE",resourceHash:{[r]:e}})}})}remove(){let e=this._context,t=this._resource,r=t.id;e.dispatch({type:"REMOVE_RESOURCE",resourceId:r}),e.emitter.trigger("resourceRemove",{resource:this,revert(){e.dispatch({type:"ADD_RESOURCE",resourceHash:{[r]:t}})}})}getParent(){let e=this._context,t=this._resource.parentId;return t?new O(e,e.getCurrentData().resourceStore[t]):null}getChildren(){let e=this._resource.id,t=this._context,{resourceStore:r}=t.getCurrentData(),s=[];for(let n in r)r[n].parentId===e&&s.push(new O(t,r[n]));return s}getEvents(){let e=this._resource.id,t=this._context,{defs:r,instances:n}=t.getCurrentData().eventStore,o=[];for(let i in n){let u=n[i],a=r[u.defId];-1!==a.resourceIds.indexOf(e)&&o.push(new s.EventImpl(t,a,u))}return o}get id(){return v(this._resource.id)}get title(){return this._resource.title}get eventConstraint(){return this._resource.ui.constraints[0]||null}get eventOverlap(){return this._resource.ui.overlap}get eventAllow(){return this._resource.ui.allows[0]||null}get eventBackgroundColor(){return this._resource.ui.backgroundColor}get eventBorderColor(){return this._resource.ui.borderColor}get eventTextColor(){return this._resource.ui.textColor}get eventClassNames(){return this._resource.ui.classNames}get extendedProps(){return this._resource.extendedProps}toPlainObject(e={}){let t=this._resource,{ui:r}=t,s=this.id,n={};return s&&(n.id=s),t.title&&(n.title=t.title),e.collapseEventColor&&r.backgroundColor&&r.backgroundColor===r.borderColor?n.eventColor=r.backgroundColor:(r.backgroundColor&&(n.eventBackgroundColor=r.backgroundColor),r.borderColor&&(n.eventBorderColor=r.borderColor)),r.textColor&&(n.eventTextColor=r.textColor),r.classNames.length&&(n.eventClassNames=r.classNames),Object.keys(t.extendedProps).length&&(e.collapseExtendedProps?Object.assign(n,t.extendedProps):n.extendedProps=t.extendedProps),n}toJSON(){return this.toPlainObject()}}s.CalendarImpl.prototype.addResource=function(e,t=!0){let r,s,n=this.getCurrentData();e instanceof O?(s=e._resource,r={[s.id]:s}):(r={},s=b(e,"",r,n)),this.dispatch({type:"ADD_RESOURCE",resourceHash:r}),t&&this.trigger("_scrollRequest",{resourceId:s.id});let o=new O(n,s);return n.emitter.trigger("resourceAdd",{resource:o,revert:()=>{this.dispatch({type:"REMOVE_RESOURCE",resourceId:s.id})}}),o},s.CalendarImpl.prototype.getResourceById=function(e){e=String(e);let t=this.getCurrentData();if(t.resourceStore){let r=t.resourceStore[e];if(r)return new O(t,r)}return null},s.CalendarImpl.prototype.getResources=function(){let e=this.getCurrentData(),{resourceStore:t}=e,r=[];if(t)for(let s in t)r.push(new O(e,t[s]));return r},s.CalendarImpl.prototype.getTopLevelResources=function(){let e=this.getCurrentData(),{resourceStore:t}=e,r=[];if(t)for(let s in t)t[s].parentId||r.push(new O(e,t[s]));return r},s.CalendarImpl.prototype.refetchResources=function(){this.dispatch({type:"REFETCH_RESOURCES"})};class D extends s.Splitter{getKeyInfo(e){return Object.assign({"":{}},e.resourceStore)}getKeysForDateSpan(e){return[e.resourceId||""]}getKeysForEventDef(e){let t=e.resourceIds;return t.length?t:[""]}}function x(e,t){return Object.assign(Object.assign({},t),{constraints:_(e,t.constraints)})}function _(e,t){return t.map(t=>{let r=t.defs;if(r)for(let t in r){let s=r[t].resourceIds;if(s.length&&-1===s.indexOf(e))return!1}return t})}const P={resources:function(e,t){t.getCurrentData().resourceSource._raw!==e&&t.dispatch({type:"RESET_RESOURCE_SOURCE",resourceSourceInput:e})}};const j=s.parseFieldSpecs("id,title");const w={initialResources:s.identity,resources:s.identity,eventResourceEditable:Boolean,refetchResourcesOnNavigate:Boolean,resourceOrder:s.parseFieldSpecs,filterResourcesWithEvents:Boolean,resourceGroupField:String,resourceAreaWidth:s.identity,resourceAreaColumns:s.identity,resourcesInitiallyExpanded:Boolean,datesAboveResources:Boolean,needsResourceData:Boolean,resourceAreaHeaderClassNames:s.identity,resourceAreaHeaderContent:s.identity,resourceAreaHeaderDidMount:s.identity,resourceAreaHeaderWillUnmount:s.identity,resourceGroupLabelClassNames:s.identity,resourceGroupLabelContent:s.identity,resourceGroupLabelDidMount:s.identity,resourceGroupLabelWillUnmount:s.identity,resourceLabelClassNames:s.identity,resourceLabelContent:s.identity,resourceLabelDidMount:s.identity,resourceLabelWillUnmount:s.identity,resourceLaneClassNames:s.identity,resourceLaneContent:s.identity,resourceLaneDidMount:s.identity,resourceLaneWillUnmount:s.identity,resourceGroupLaneClassNames:s.identity,resourceGroupLaneContent:s.identity,resourceGroupLaneDidMount:s.identity,resourceGroupLaneWillUnmount:s.identity},T={resourcesSet:s.identity,resourceAdd:s.identity,resourceChange:s.identity,resourceRemove:s.identity};s.EventImpl.prototype.getResources=function(){let{calendarApi:e}=this._context;return this._def.resourceIds.map(t=>e.getResourceById(t))},s.EventImpl.prototype.setResources=function(e){let t=[];for(let r of e){let e=null;"string"==typeof r?e=r:"number"==typeof r?e=String(r):r instanceof O?e=r.id:console.warn("unknown resource type: "+r),e&&t.push(e)}this.mutate({standardProps:{resourceIds:t}})},p({ignoreRange:!0,parseMeta:e=>Array.isArray(e.resources)?e.resources:null,fetch(e,t){t({rawResources:e.resourceSource.meta})}}),p({parseMeta:e=>"function"==typeof e.resources?e.resources:null,fetch(e,t,r){const n=e.context.dateEnv,o=e.resourceSource.meta,i=e.range?{start:n.toDate(e.range.start),end:n.toDate(e.range.end),startStr:n.formatIso(e.range.start),endStr:n.formatIso(e.range.end),timeZone:n.timeZone}:{};s.unpromisify(o.bind(null,i),e=>t({rawResources:e}),r)}}),p({parseMeta:e=>e.url?{url:e.url,method:(e.method||"GET").toUpperCase(),extraParams:e.extraParams}:null,fetch(e,t,r){const n=e.resourceSource.meta,o=function(e,t,r){let s,n,o,i,{dateEnv:u,options:a}=r,c={};t&&(s=e.startParam,null==s&&(s=a.startParam),n=e.endParam,null==n&&(n=a.endParam),o=e.timeZoneParam,null==o&&(o=a.timeZoneParam),c[s]=u.formatIso(t.start),c[n]=u.formatIso(t.end),"local"!==u.timeZone&&(c[o]=u.timeZone));i="function"==typeof e.extraParams?e.extraParams():e.extraParams||{};return Object.assign(c,i),c}(n,e.range,e.context);s.requestJson(n.method,n.url,o).then(([e,r])=>{t({rawResources:e,response:r})},r)}});var U=t.createPlugin({name:"@fullcalendar/resource",premiumReleaseDate:"2025-04-02",deps:[i.default],reducers:[function(e,t,r){let s=R(e&&e.resourceSource,t,r);return{resourceSource:s,resourceStore:y(e&&e.resourceStore,t,s,r),resourceEntityExpansions:function(e,t){if(!e||!t)return{};switch(t.type){case"SET_RESOURCE_ENTITY_EXPANDED":return Object.assign(Object.assign({},e),{[t.id]:t.isExpanded});default:return e}}(e&&e.resourceEntityExpansions,t)}}],isLoadingFuncs:[e=>e.resourceSource&&e.resourceSource.isFetching],eventRefiners:I,eventDefMemberAdders:[function(e){return{resourceIds:(t=e.resourceIds,(t||[]).map(e=>String(e))).concat(e.resourceId?[e.resourceId]:[]),resourceEditable:e.resourceEditable};var t}],isDraggableTransformers:[function(e,t,r,s){if(!e){let e=s.getCurrentData();if(e.viewSpecs[e.currentViewType].optionDefaults.needsResourceData&&u(t,s))return!0}return e}],eventDragMutationMassagers:[function(e,t,r){let s=t.dateSpan.resourceId,n=r.dateSpan.resourceId;s&&n&&s!==n&&(e.resourceMutation={matchResourceId:s,setResourceId:n})}],eventDefMutationAppliers:[function(e,t,r){let s=t.resourceMutation;if(s&&u(e,r)){let t=e.resourceIds.indexOf(s.matchResourceId);if(-1!==t){let r=e.resourceIds.slice();r.splice(t,1),-1===r.indexOf(s.setResourceId)&&r.push(s.setResourceId),e.resourceIds=r}}}],dateSelectionTransformers:[function(e,t){let r=e.dateSpan.resourceId,s=t.dateSpan.resourceId;return r&&s?{resourceId:r}:null}],datePointTransforms:[function(e,t){return e.resourceId?{resource:t.calendarApi.getResourceById(e.resourceId)}:{}}],dateSpanTransforms:[function(e,t){return e.resourceId?{resource:t.calendarApi.getResourceById(e.resourceId)}:{}}],viewPropsTransformers:[class{constructor(){this.filterResources=s.memoize(a)}transform(e,t){return t.viewSpec.optionDefaults.needsResourceData?{resourceStore:this.filterResources(t.resourceStore,t.options.filterResourcesWithEvents,t.eventStore,t.dateProfile.activeRange),resourceEntityExpansions:t.resourceEntityExpansions}:null}},class{constructor(){this.buildResourceEventUis=s.memoize(c,s.isPropsEqual),this.injectResourceEventUis=s.memoize(l)}transform(e,t){return t.viewSpec.optionDefaults.needsResourceData?null:{eventUiBases:this.injectResourceEventUis(e.eventUiBases,e.eventStore.defs,this.buildResourceEventUis(t.resourceStore))}}}],isPropsValid:function(e,t){let r=(new D).splitProps(Object.assign(Object.assign({},e),{resourceStore:t.getCurrentData().resourceStore}));for(let e in r){let n=r[e];if(e&&r[""]&&(n=Object.assign(Object.assign({},n),{eventStore:s.mergeEventStores(r[""].eventStore,n.eventStore),eventUiBases:Object.assign(Object.assign({},r[""].eventUiBases),n.eventUiBases)})),!s.isPropsValid(n,t,{resourceId:e},x.bind(null,e)))return!1}return!0},externalDefTransforms:[function(e){return e.resourceId?{resourceId:e.resourceId}:{}}],eventDropTransformers:[function(e,t){let{resourceMutation:r}=e;if(r){let{calendarApi:e}=t;return{oldResource:e.getResourceById(r.matchResourceId),newResource:e.getResourceById(r.setResourceId)}}return{oldResource:null,newResource:null}}],optionChangeHandlers:P,optionRefiners:w,listenerRefiners:T,propSetHandlers:{resourceStore:function(e,t){let{emitter:r}=t;r.hasHandlers("resourcesSet")&&r.trigger("resourcesSet",function(e,t){let r=[];for(let s in e)r.push(new O(t,e[s]));return r}(e,t))}}});class A extends s.BaseComponent{constructor(){super(...arguments),this.refineRenderProps=s.memoizeObjArg(F)}render(){const{props:e}=this;return n.createElement(s.ViewContextType.Consumer,null,t=>{let{options:r}=t,o=this.refineRenderProps({resource:e.resource,date:e.date,context:t});return n.createElement(s.ContentContainer,Object.assign({},e,{elAttrs:Object.assign(Object.assign({},e.elAttrs),{"data-resource-id":e.resource.id,"data-date":e.date?s.formatDayString(e.date):void 0}),renderProps:o,generatorName:"resourceLabelContent",customGenerator:r.resourceLabelContent,defaultGenerator:B,classNameGenerator:r.resourceLabelClassNames,didMount:r.resourceLabelDidMount,willUnmount:r.resourceLabelWillUnmount}))})}}function B(e){return e.resource.title||e.resource.id}function F(e){return{resource:new O(e.context,e.resource),date:e.date?e.context.dateEnv.toDate(e.date):null,view:e.context.viewApi}}class N extends s.BaseComponent{render(){let{props:e}=this;return n.createElement(A,{elTag:"th",elClasses:["fc-col-header-cell","fc-resource"],elAttrs:{role:"columnheader",colSpan:e.colSpan},resource:e.resource,date:e.date},t=>n.createElement("div",{className:"fc-scrollgrid-sync-inner"},n.createElement(t,{elTag:"span",elClasses:["fc-col-header-cell-cushion",e.isSticky&&"fc-sticky"]})))}}class H extends s.BaseComponent{constructor(){super(...arguments),this.buildDateFormat=s.memoize(k)}render(){let{props:e,context:t}=this,r=this.buildDateFormat(t.options.dayHeaderFormat,e.datesRepDistinctDays,e.dates.length);return n.createElement(s.NowTimer,{unit:"day"},(s,n)=>1===e.dates.length?this.renderResourceRow(e.resources,e.dates[0]):t.options.datesAboveResources?this.renderDayAndResourceRows(e.dates,r,n,e.resources):this.renderResourceAndDayRows(e.resources,e.dates,r,n))}renderResourceRow(e,t){let r=e.map(e=>n.createElement(N,{key:e.id,resource:e,colSpan:1,date:t}));return this.buildTr(r,"resources")}renderDayAndResourceRows(e,t,r,s){let o=[],i=[];for(let u of e){o.push(this.renderDateCell(u,t,r,s.length,null,!0));for(let e of s)i.push(n.createElement(N,{key:e.id+":"+u.toISOString(),resource:e,colSpan:1,date:u}))}return n.createElement(n.Fragment,null,this.buildTr(o,"day"),this.buildTr(i,"resources"))}renderResourceAndDayRows(e,t,r,s){let o=[],i=[];for(let u of e){o.push(n.createElement(N,{key:u.id,resource:u,colSpan:t.length,isSticky:!0}));for(let e of t)i.push(this.renderDateCell(e,r,s,1,u))}return n.createElement(n.Fragment,null,this.buildTr(o,"resources"),this.buildTr(i,"day"))}renderDateCell(e,t,r,o,i,u){let{props:a}=this,c=i?":"+i.id:"",l=i?{resource:new O(this.context,i)}:{},d=i?{"data-resource-id":i.id}:{};return a.datesRepDistinctDays?n.createElement(s.TableDateCell,{key:e.toISOString()+c,date:e,dateProfile:a.dateProfile,todayRange:r,colCnt:a.dates.length*a.resources.length,dayHeaderFormat:t,colSpan:o,isSticky:u,extraRenderProps:l,extraDataAttrs:d}):n.createElement(s.TableDowCell,{key:e.getUTCDay()+c,dow:e.getUTCDay(),dayHeaderFormat:t,colSpan:o,isSticky:u,extraRenderProps:l,extraDataAttrs:d})}buildTr(e,t){let{renderIntro:r}=this.props;return e.length||(e=[n.createElement("td",{key:0}," ")]),n.createElement("tr",{key:t,role:"row"},r&&r(t),e)}}function k(e,t,r){return e||s.computeFallbackHeaderFormat(t,r)}class M{constructor(e){let t={},r=[];for(let s=0;s<e.length;s+=1){let n=e[s].id;r.push(n),t[n]=s}this.ids=r,this.indicesById=t,this.length=e.length}}class L{constructor(e,t,r){this.dayTableModel=e,this.resources=t,this.context=r,this.resourceIndex=new M(t),this.rowCnt=e.rowCnt,this.colCnt=e.colCnt*t.length,this.cells=this.buildCells()}buildCells(){let{rowCnt:e,dayTableModel:t,resources:r}=this,s=[];for(let n=0;n<e;n+=1){let e=[];for(let s=0;s<t.colCnt;s+=1)for(let o=0;o<r.length;o+=1){let i=r[o],u={resource:new O(this.context,i)},a={"data-resource-id":i.id},c=["fc-resource"],l={resourceId:i.id},d=t.cells[n][s].date;e[this.computeCol(s,o)]={key:i.id+":"+d.toISOString(),date:d,extraRenderProps:u,extraDataAttrs:a,extraClassNames:c,extraDateSpan:l}}s.push(e)}return s}}const V=[];class z extends s.Splitter{getKeyInfo(e){let{resourceDayTableModel:t}=e,r=s.mapHash(t.resourceIndex.indicesById,e=>t.resources[e]);return r[""]={},r}getKeysForDateSpan(e){return[e.resourceId||""]}getKeysForEventDef(e){let t=e.resourceIds;return t.length?t:[""]}}function G(e,t,r,s,n,o){let i=[];return function e(t,r,s,n,o,i,u){for(let a=0;a<t.length;a+=1){let c=t[a],l=c.group;if(l)if(s){let t=r.length,a=n.length;if(e(c.children,r,s,n.concat(0),o,i,u),t<r.length){let e=r[t];(e.rowSpans=e.rowSpans.slice())[a]=r.length-t}}else{let t=l.spec.field+":"+l.value,a=null!=i[t]?i[t]:u;r.push({id:t,group:l,isExpanded:a}),a&&e(c.children,r,s,n,o+1,i,u)}else if(c.resource){let t=c.resource.id,a=null!=i[t]?i[t]:u;r.push({id:t,rowSpans:n,depth:o,isExpanded:a,hasChildren:Boolean(c.children.length),resource:c.resource,resourceFields:c.resourceFields}),a&&e(c.children,r,s,n,o+1,i,u)}}}(function(e,t,r,s){let n=function(e,t){let r={};for(let t in e){let s=e[t];r[t]={resource:s,resourceFields:K(s),children:[]}}for(let s in e){let n=e[s];if(n.parentId){let e=r[n.parentId];e&&Z(r[s],e.children,t)}}return r}(e,s),o=[];for(let e in n){let i=n[e];i.resource.parentId||W(i,o,r,0,t,s)}return o}(e,s?-1:1,t,r),i,s,[],0,n,o),i}function W(e,t,r,n,o,i){if(r.length&&(-1===o||n<=o)){W(e,function(e,t,r){let n,o,i=e.resourceFields[r.field];if(r.order)for(o=0;o<t.length;o+=1){let e=t[o];if(e.group){let t=s.flexibleCompare(i,e.group.value)*r.order;if(0===t){n=e;break}if(t<0)break}}else for(o=0;o<t.length;o+=1){let e=t[o];if(e.group&&i===e.group.value){n=e;break}}n||(n={group:{value:i,spec:r},children:[]},t.splice(o,0,n));return n}(e,t,r[0]).children,r.slice(1),n+1,o,i)}else Z(e,t,i)}function Z(e,t,r){let n;for(n=0;n<t.length;n+=1){if(s.compareByFieldSpecs(t[n].resourceFields,e.resourceFields,r)>0)break}t.splice(n,0,e)}function K(e){let t=Object.assign(Object.assign(Object.assign({},e.extendedProps),e.ui),e);return delete t.ui,delete t.extendedProps,t}var q={__proto__:null,refineRenderProps:function(e){return{resource:new O(e.context,e.resource)}},DEFAULT_RESOURCE_ORDER:j,ResourceDayHeader:H,AbstractResourceDayTableModel:L,ResourceDayTableModel:class extends L{computeCol(e,t){return t*this.dayTableModel.colCnt+e}computeColRanges(e,t,r){return[{firstCol:this.computeCol(e,r),lastCol:this.computeCol(t,r),isStart:!0,isEnd:!0}]}},DayResourceTableModel:class extends L{computeCol(e,t){return e*this.resources.length+t}computeColRanges(e,t,r){let s=[];for(let n=e;n<=t;n+=1){let o=this.computeCol(n,r);s.push({firstCol:o,lastCol:o,isStart:n===e,isEnd:n===t})}return s}},VResourceJoiner:class{constructor(){this.joinDateSelection=s.memoize(this.joinSegs),this.joinBusinessHours=s.memoize(this.joinSegs),this.joinFgEvents=s.memoize(this.joinSegs),this.joinBgEvents=s.memoize(this.joinSegs),this.joinEventDrags=s.memoize(this.joinInteractions),this.joinEventResizes=s.memoize(this.joinInteractions)}joinProps(e,t){let r=[],s=[],n=[],o=[],i=[],u=[],a="",c=t.resourceIndex.ids.concat([""]);for(let t of c){let c=e[t];r.push(c.dateSelectionSegs),s.push(t?c.businessHourSegs:V),n.push(t?c.fgEventSegs:V),o.push(c.bgEventSegs),i.push(c.eventDrag),u.push(c.eventResize),a=a||c.eventSelection}return{dateSelectionSegs:this.joinDateSelection(t,...r),businessHourSegs:this.joinBusinessHours(t,...s),fgEventSegs:this.joinFgEvents(t,...n),bgEventSegs:this.joinBgEvents(t,...o),eventDrag:this.joinEventDrags(t,...i),eventResize:this.joinEventResizes(t,...u),eventSelection:a}}joinSegs(e,...t){let r=e.resources.length,s=[];for(let n=0;n<r;n+=1){for(let r of t[n])s.push(...this.transformSeg(r,e,n));for(let o of t[r])s.push(...this.transformSeg(o,e,n))}return s}expandSegs(e,t){let r=e.resources.length,s=[];for(let n=0;n<r;n+=1)for(let r of t)s.push(...this.transformSeg(r,e,n));return s}joinInteractions(e,...t){let r=e.resources.length,s={},n=[],o=!1,i=!1;for(let u=0;u<r;u+=1){let a=t[u];if(a){o=!0;for(let t of a.segs)n.push(...this.transformSeg(t,e,u));Object.assign(s,a.affectedInstances),i=i||a.isEvent}if(t[r])for(let s of t[r].segs)n.push(...this.transformSeg(s,e,u))}return o?{affectedInstances:s,segs:n,isEvent:i}:null}},VResourceSplitter:z,getPublicId:v,flattenResources:function(e,t){return G(e,[],t,!1,{},!0).map(e=>e.resource)},isGroupsEqual:function(e,t){return e.spec===t.spec&&e.value===t.value},buildRowNodes:G,buildResourceFields:K,ResourceSplitter:D,ResourceLabelContainer:A};return t.globalPlugins.push(U),e.Internal=q,e.ResourceApi=O,e.default=U,Object.defineProperty(e,"__esModule",{value:!0}),e}({},FullCalendar,FullCalendar.PremiumCommon,FullCalendar.Internal,FullCalendar.Preact);