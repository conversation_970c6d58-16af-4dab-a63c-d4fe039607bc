import{c as D,i as C}from"../../admin.bundle-DI9_jvUJ.js";/* empty css                               */import"../../main-Cyta4iCA.js";VirtualSelect.init({ele:"#countrySelect",options:[{label:"Afghanistan",value:"1"},{label:"Åland Islands",value:"2"},{label:"Albania",value:"3"},{label:"Algeria",value:"3"},{label:"Zambia",value:"3"},{label:"Zimbabwe",value:"3"}],selectedValue:0});function L(a){document.querySelectorAll('[id^="setDefault"]').forEach(p=>{p.querySelector("span").textContent="Set as Default"});const m=document.getElementById(`setDefault${a}`);m.querySelector("span").textContent="Default Set"}document.getElementById("setDefault1").addEventListener("click",()=>L(1));document.getElementById("setDefault2").addEventListener("click",()=>L(2));document.getElementById("setDefault3").addEventListener("click",()=>L(3));document.getElementById("cardNumberInput").addEventListener("input",function(a){let n=a.target.value.replace(/[^0-9]/g,"").slice(0,16);n.length>=4&&(n=n.slice(0,4)+" "+n.slice(4)),n.length>=9&&(n=n.slice(0,9)+" "+n.slice(9)),n.length>=14&&(n=n.slice(0,14)+" "+n.slice(14)),a.target.value=n});document.getElementById("cvvInput").addEventListener("input",function(a){a.target.value=a.target.value.replace(/[^0-9]/g,"").slice(0,3)});document.getElementById("expiryDateInput").addEventListener("input",function(a){let n=a.target.value.replace(/[^0-9]/g,"");n.length>=2&&(n=n.slice(0,2)+"/"+n.slice(2)),n=n.slice(0,7),a.target.value=n});document.addEventListener("DOMContentLoaded",function(){const a=document.querySelector("#exampleModal .btn-primary")||document.querySelector('#exampleModal button[type="submit"]');a?a.addEventListener("click",function(){var I,y,x,E,f,v;const n=((I=document.getElementById("namePersonalInput"))==null?void 0:I.value)||"",m=((y=document.getElementById("addressInput"))==null?void 0:y.value)||"",p=((x=document.getElementById("cityInput"))==null?void 0:x.value)||"",u=((E=document.getElementById("stateInput"))==null?void 0:E.value)||"",g=((f=document.getElementById("zipCodeInput"))==null?void 0:f.value)||"",h=((v=document.getElementById("phoneNumberInput"))==null?void 0:v.value)||"";document.getElementById("displayName")&&(document.getElementById("displayName").textContent=n),document.getElementById("displayAddress")&&(document.getElementById("displayAddress").textContent=`${m}, ${p}, ${u},`),document.getElementById("displayCountryZip")&&(document.getElementById("displayCountryZip").textContent=`United States - ${g}.`),document.getElementById("displayPhone")&&(document.getElementById("displayPhone").textContent=h)}):console.error("Save button not found in modal")});let b=null;document.addEventListener("DOMContentLoaded",function(){const a=document.getElementById("paymentModal"),n=a.querySelector("form"),m=document.getElementById("paymentModalLabel"),p=document.querySelectorAll('[id^="setDefault"]'),u={1:{number:"1547",expiry:"01/2030",name:"John Doe",type:"visa"},2:{number:"8749",expiry:"24/2030",name:"Jane Smith",type:"american"},3:{number:"3641",expiry:"13/2028",name:"Alex Johnson",type:"mastercard"}},g=new Set(Object.keys(u).map(Number));let h=Math.max(...g)+1;window.setCard=function(e){b=e,m.textContent="Edit Card";const t=u[e];t&&(document.getElementById("cardNumberInput").value="xxxx xxxx xxxx "+t.number,document.getElementById("expiryDateInput").value=t.expiry,document.getElementById("nameOnTheCardInput").value=t.name,document.getElementById("cvvInput").value="")};const I=document.querySelectorAll('[data-bs-target="#paymentModal"]'),y=Array.from(I).find(e=>e.classList.contains("card")||e.querySelector(".card-body.avatar"));y&&y.addEventListener("click",function(){b=null,m.textContent="Add Card",document.getElementById("cardNumberInput").value="",document.getElementById("expiryDateInput").value="",document.getElementById("nameOnTheCardInput").value="",document.getElementById("cvvInput").value="",document.getElementById("defaultCheck1").checked=!1}),n.addEventListener("submit",function(e){e.preventDefault();const t=document.getElementById("cardNumberInput").value,l=document.getElementById("expiryDateInput").value,d=document.getElementById("nameOnTheCardInput").value,i=document.getElementById("cvvInput").value,s=document.getElementById("defaultCheck1").checked;if(!t||!l||!d||!i)return;const c=t.replace(/\D/g,"").slice(-4);b?E(b,c,l,d,s):x(c,l,d,s),window.bootstrap.Modal.getInstance(a).hide()}),p.forEach(e=>{e.addEventListener("click",function(t){t.preventDefault(),p.forEach(l=>{l.innerHTML="<span>Set as Default</span>",l.classList.remove("active")}),this.innerHTML='<i data-lucide="check-circle"></i> <span>Default</span>',this.classList.add("active"),D({icons:C})})});function x(e,t,l,d){let i;const s=document.getElementById("cardNumberInput").value.replace(/\D/g,"")[0];s==="4"?i="visa":s==="5"?i="mastercard":s==="3"?i="american":i="visa";const c=h++;u[c]={number:e,expiry:t,name:l,type:i},g.add(c);const o=document.querySelector(".card-body > .payment-cards");if(!o)return;const r=document.createElement("div");r.className="col-12 col-sm-6 col-xl-3",r.innerHTML=`
      <div class="card mb-0">
        <div class="card-body payment-gradient">
          <img src="assets/images/payment/${i}.png" loading="lazy" alt="${i}" class="h-10">
        </div>
        <div class="card-body pt-0">
          <div>
            <h6 class="mb-1">xxxx xxxx xxxx ${e}</h6>
            <p class="text-muted">Expiry on ${t}</p>
          </div>
          <div class="d-flex justify-content-between align-items-center mt-5">
            <a href="#!" class="link link-custom-success" id="setDefault${c}">
              <span>Set as Default</span>
            </a>
            <a href="#!" class="link link-custom-primary" data-bs-toggle="modal" data-bs-target="#paymentModal" onclick="setCard(${c})">
              <i data-lucide="pencil" class="size-4"></i> Edit
            </a>
          </div>
        </div>
      </div>
    `;const S=o.querySelector(".col-12.col-sm-6.col-xl-3:last-child");S?o.insertBefore(r,S):o.appendChild(r),D({icons:C});const B=document.getElementById(`setDefault${c}`);B&&B.addEventListener("click",function(N){N.preventDefault(),document.querySelectorAll('[id^="setDefault"]').forEach(M=>{M.innerHTML="<span>Set as Default</span>",M.classList.remove("active")}),this.innerHTML='<i data-lucide="check-circle"></i> <span>Default</span>',this.classList.add("active"),D({icons:C})}),d&&B&&setTimeout(()=>{B.click()},100)}function E(e,t,l,d,i){u[e]&&(u[e].number=t,u[e].expiry=l,u[e].name=d);const s=document.querySelector(`[onclick="setCard(${e})"]`);if(s){const c=s.closest(".card");if(c){const o=c.querySelector("h6"),r=c.querySelector("p.text-muted");o&&(o.textContent=`xxxx xxxx xxxx ${t}`),r&&(r.textContent=`Expiry on ${l}`)}}if(i){const c=document.getElementById(`setDefault${e}`);c&&c.click()}}const f=document.getElementById("cardNumberInput");f&&f.addEventListener("input",function(e){let t=e.target.value.replace(/\D/g,"");t.length>16&&(t=t.slice(0,16));let l="";for(let d=0;d<t.length;d++)d>0&&d%4===0&&(l+=" "),l+=t[d];e.target.value=l});const v=document.getElementById("cvvInput");v&&v.addEventListener("input",function(e){e.target.value=e.target.value.replace(/\D/g,"").slice(0,3)});const k=document.getElementById("expiryDateInput");k&&k.addEventListener("input",function(e){let t=e.target.value.replace(/\D/g,"");t.length>6&&(t=t.slice(0,6)),t.length>2?e.target.value=t.slice(0,2)+"/"+t.slice(2):e.target.value=t})});
