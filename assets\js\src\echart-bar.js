import"../../admin.bundle-DI9_jvUJ.js";import{i as u}from"../../index-u3wYLQ4o.js";import"../../main-Cyta4iCA.js";function p(e){const t=getComputedStyle(document.documentElement).getPropertyValue(e).trim();return/^\d{1,3},\s*\d{1,3},\s*\d{1,3}$/.test(t)?`rgb(${t})`:t}var i=[];const b=e=>{const t=JSON.parse(JSON.stringify(e)),l=r=>{for(const a in r)typeof r[a]=="string"&&r[a].startsWith("--dx-")?r[a]=p(r[a]):typeof r[a]=="object"&&r[a]!==null&&l(r[a])};return l(t),t};function d(e=""){e&&document.documentElement.setAttribute("data-colors",e),i.forEach(t=>{var c;const l=JSON.parse(JSON.stringify(t[0].data)),r=b(structuredClone(l));t[0].chart&&((c=t[0].chart)==null||c.dispose());var a=document.getElementById(t[0].id),s=u(a);t[0].chart=s,r&&typeof r=="object"&&s.setOption(r)})}document.querySelectorAll('input[name="data-colors"]').forEach(e=>{e.addEventListener("change",function(){n(this.value)})});document.querySelectorAll('input[name="data-bs-theme"]').forEach(e=>{e.addEventListener("change",function(){n(this.value)})});var y;(y=document.getElementById("darkModeButton"))==null||y.addEventListener("click",function(){n(this.value)});function n(e){setTimeout(()=>{d(e)},0)}var x;(x=document.getElementById("darkModeButton"))==null||x.addEventListener("click",function(){setTimeout(()=>{d()},0)});var o;o={series:[{data:[120,200,150,80,70,110,130],type:"bar"}],xAxis:{type:"category",data:["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],axisLabel:{color:"--dx-secondary-color"}},legend:{textStyle:{color:"--dx-body-color"}},yAxis:{type:"value",splitLine:{lineStyle:{color:["--dx-border-color"]}},axisLabel:{color:"--dx-secondary-color"}},axisLine:{lineStyle:{color:["--dx-border-color"]}},color:"--dx-primary",grid:{top:"5%",left:"6%",right:"0%",bottom:"8%"}};i.push([{id:"basicBarChart",data:o}]);var o;o={series:[{name:"Direct",type:"bar",barWidth:"60%",data:[10,52,200,334,390,330,220]}],tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},xAxis:{type:"category",data:["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],axisTick:{alignWithLabel:!0},axisLabel:{color:"--dx-secondary-color"}},legend:{show:!1},yAxis:{type:"value",splitLine:{lineStyle:{color:["--dx-border-color"]}},axisLabel:{color:"--dx-secondary-color"}},axisLine:{lineStyle:{color:["--dx-border-color"]}},color:"--dx-secondary",grid:{top:"5%",left:"6%",right:"0%",bottom:"8%"}};i.push([{id:"axisAlignBarChart",data:o}]);var o;o={series:[{data:[120,200,150,80,70,110,130],type:"bar",showBackground:!0,backgroundStyle:{color:"--dx-success-bg-subtle"}}],xAxis:{type:"category",data:["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],axisLabel:{color:"--dx-secondary-color"}},tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},legend:{textStyle:{color:"--dx-body-color"}},yAxis:{type:"value",splitLine:{lineStyle:{color:["--dx-border-color"]}},axisLabel:{color:"--dx-secondary-color"}},axisLine:{lineStyle:{color:["--dx-border-color"]}},color:"--dx-success",grid:{top:"5%",left:"6%",right:"0%",bottom:"8%"}};i.push([{id:"backgroundBarChart",data:o}]);var o;o={series:[{data:[120,{value:200,itemStyle:{color:"--dx-secondary"}},150,80,70,110,130],type:"bar"}],xAxis:{type:"category",data:["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],axisLabel:{color:"--dx-secondary-color"}},tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},legend:{textStyle:{color:"--dx-body-color"}},yAxis:{type:"value",splitLine:{lineStyle:{color:["--dx-border-color"]}},axisLabel:{color:"--dx-secondary-color"}},axisLine:{lineStyle:{color:["--dx-border-color"]}},color:"--dx-primary",grid:{top:"5%",left:"6%",right:"0%",bottom:"8%"}};i.push([{id:"singleBarChart",data:o}]);var o;o={series:[{name:"2024",type:"bar",data:[18203,23489,29034,104970,131744,630230]},{name:"2025",type:"bar",data:[19325,23438,31e3,121594,134141,681807]}],title:{text:"World Population",textStyle:{color:"--dx-body-color"}},tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},xAxis:{type:"value",boundaryGap:[0,.01],splitLine:{lineStyle:{color:["--dx-border-color"]}},axisLabel:{color:"--dx-secondary-color"}},yAxis:{type:"category",data:["Brazil","Indonesia","USA","India","China","World"],axisLabel:{color:"--dx-secondary-color"}},axisLine:{lineStyle:{color:["--dx-border-color"]}},color:"--dx-primary",grid:{top:"12%",left:"6%",right:"0%",bottom:"8%"}};i.push([{id:"worldPopulationBarChart",data:o}]);window.addEventListener("DOMContentLoaded",()=>{d()});window.addEventListener("resize",()=>{setTimeout(()=>{d()},0)});
