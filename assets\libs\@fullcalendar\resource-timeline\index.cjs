'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var index_cjs = require('@fullcalendar/core/index.cjs');
var premiumCommonPlugin = require('@fullcalendar/premium-common/index.cjs');
var timelinePlugin = require('@fullcalendar/timeline/index.cjs');
var resourcePlugin = require('@fullcalendar/resource/index.cjs');
var internalCommon = require('./internal.cjs');
require('@fullcalendar/core/internal.cjs');
require('@fullcalendar/core/preact.cjs');
require('@fullcalendar/timeline/internal.cjs');
require('@fullcalendar/resource/internal.cjs');
require('@fullcalendar/scrollgrid/internal.cjs');

function _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }

var premiumCommonPlugin__default = /*#__PURE__*/_interopDefaultLegacy(premiumCommonPlugin);
var timelinePlugin__default = /*#__PURE__*/_interopDefaultLegacy(timelinePlugin);
var resourcePlugin__default = /*#__PURE__*/_interopDefaultLegacy(resourcePlugin);

var index = index_cjs.createPlugin({
    name: '@fullcalendar/resource-timeline',
    premiumReleaseDate: '2025-04-02',
    deps: [
        premiumCommonPlugin__default["default"],
        resourcePlugin__default["default"],
        timelinePlugin__default["default"],
    ],
    initialView: 'resourceTimelineDay',
    views: {
        resourceTimeline: {
            type: 'timeline',
            component: internalCommon.ResourceTimelineView,
            needsResourceData: true,
            resourceAreaWidth: '30%',
            resourcesInitiallyExpanded: true,
            eventResizableFromStart: true, // TODO: not DRY with this same setting in the main timeline config
        },
        resourceTimelineDay: {
            type: 'resourceTimeline',
            duration: { days: 1 },
        },
        resourceTimelineWeek: {
            type: 'resourceTimeline',
            duration: { weeks: 1 },
        },
        resourceTimelineMonth: {
            type: 'resourceTimeline',
            duration: { months: 1 },
        },
        resourceTimelineYear: {
            type: 'resourceTimeline',
            duration: { years: 1 },
        },
    },
});

exports["default"] = index;
