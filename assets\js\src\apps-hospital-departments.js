import{c as b,i as y}from"../../admin.bundle-DI9_jvUJ.js";/* empty css                               */import{u as E}from"../../apexcharts.esm-BofaT7g3.js";import"../../main-Cyta4iCA.js";function I(l){const t=getComputedStyle(document.documentElement).getPropertyValue(l).trim();return/^\d{1,3},\s*\d{1,3},\s*\d{1,3}$/.test(t)?`rgb(${t})`:t}var p=[];const v=l=>{const t=JSON.parse(JSON.stringify(l)),a=e=>{for(const i in e)typeof e[i]=="string"&&e[i].startsWith("--dx-")?e[i]=I(e[i]):typeof e[i]=="object"&&e[i]!==null&&a(e[i])};return a(t),t};function f(l=""){l&&document.documentElement.setAttribute("data-colors",l),p.forEach(t=>{const a=JSON.parse(JSON.stringify(t[0].data)),e=v(structuredClone(a));t[0].chart&&t[0].chart.destroy();var i=new E(document.querySelector("#"+t[0].id),e);i.render(),t[0].chart=i})}document.querySelectorAll('input[name="data-colors"]').forEach(l=>{l.addEventListener("change",function(){u(this.value)})});document.querySelectorAll('input[name="data-bs-theme"]').forEach(l=>{l.addEventListener("change",function(){u(this.value)})});var g;(g=document.getElementById("darkModeButton"))==null||g.addEventListener("click",function(){u(this.value)});function u(l){setTimeout(()=>{f(l)},0)}var P={series:[{name:"Employee",data:[21,22,19,10,10,28,16]}],chart:{height:300,type:"bar"},plotOptions:{bar:{columnWidth:"25%",distributed:!0}},fill:{type:"gradient",gradient:{shade:"dark",type:"horizontal",shadeIntensity:.2,inverseColors:!0,opacityFrom:1,opacityTo:1,stops:[0,50,30],colorStops:[]}},states:{normal:{filter:{type:"none",value:0}},hover:{filter:{type:"none",value:0}},active:{filter:{type:"none",value:0}}},dataLabels:{enabled:!1},legend:{show:!1},xaxis:{categories:[["Radiology"],["Orthopedics"],["Neurology"],["Cardiology"],["Pediatrics"],["Nurse"],["Others"]]},colors:["--dx-primary","--dx-pink","--dx-info","--dx-success","--dx-warning","--dx-orange","--dx-purple"],grid:{padding:{top:-20,right:0,bottom:0}}};p.push([{id:"employeeDepartmentChart",data:P}]);f();VirtualSelect.init({ele:"#statusSelect",options:[{label:"Active",value:"1"},{label:"Un Active",value:"2"}],selectedValue:1});class D{constructor(t){this.tableId=t.tableId,this.tableElement=document.getElementById(this.tableId),this.data=t.data||[],this.columns=t.columns,this.itemsPerPage=t.itemsPerPage||10,this.currentPage=1,this.totalPages=Math.ceil(this.data.length/this.itemsPerPage),this.sorting={column:null,direction:"asc"},this.paginationId=t.paginationId,this.paginationElement=document.getElementById(this.paginationId),this.addFormId=t.addFormId,this.addFormElement=document.getElementById(this.addFormId),this.addModalId=t.addModalId,this.deleteModalId=t.deleteModalId,this.init()}init(){this.renderTable(),this.renderPagination(),this.setupEventListeners(),this.setupImagePreview(),this.setupModalEvents()}setupImagePreview(){var i;const t=document.getElementById("imageInput"),a=document.getElementById("LogoPreview"),e=(i=t==null?void 0:t.parentElement)==null?void 0:i.querySelector(".ri-upload-line");t&&a&&t.addEventListener("change",function(){if(this.files&&this.files[0]){const n=new FileReader;n.onload=function(s){a.src=s.target.result,a.classList.remove("d-none"),e&&(e.style.display="none");const r=document.getElementById("imageError");r&&(r.textContent="")},n.readAsDataURL(this.files[0])}})}setupModalEvents(){const t=document.getElementById(this.addModalId);t&&t.addEventListener("show.bs.modal",a=>{const e=a.relatedTarget;(!e||!e.classList.contains("edit-btn"))&&this.resetForm()})}resetForm(){var t;if(this.addFormElement){this.addFormElement.reset(),this.addFormElement.removeAttribute("data-edit-id");const a=document.getElementById("LogoPreview");a&&(a.src="",a.classList.add("d-none"));const e=this.addFormElement.querySelector("#imageInput"),i=(t=e==null?void 0:e.parentElement)==null?void 0:t.querySelector(".ri-upload-line");i&&(i.style.display="block");const n=this.addFormElement.querySelector('button[type="submit"]');n&&(n.textContent="Add Department");const s=document.getElementById("imageError");s&&(s.textContent="")}}renderTable(){if(!this.tableElement)return;const t=this.tableElement.querySelector("tbody");t.innerHTML="";const a=(this.currentPage-1)*this.itemsPerPage,e=Math.min(a+this.itemsPerPage,this.data.length),i=this.getSortedData();for(let s=a;s<e;s++){const r=i[s];if(!r)continue;const o=document.createElement("tr");if(o.dataset.id=r.id,this.columns.forEach(d=>{const c=document.createElement("td");d.render?c.innerHTML=d.render(r[d.key],r):c.textContent=r[d.key],o.appendChild(c)}),this.columns.some(d=>d.key==="action")){const d=o.querySelector("td:last-child");d.innerHTML=`
            <div class="d-flex align-items-center gap-2">
              <button class="btn btn-sub-primary size-8 btn-icon edit-btn" data-id="${r.id}" data-bs-toggle="modal" data-bs-target="#${this.addModalId}">
                <i class="ri-pencil-line"></i>
              </button>
              <button class="btn btn-sub-danger size-8 btn-icon delete-btn" data-id="${r.id}" data-bs-toggle="modal" data-bs-target="#${this.deleteModalId}">
                <i class="ri-delete-bin-line"></i>
              </button>
            </div>
          `}t.appendChild(o)}this.tableElement.querySelectorAll("thead th").forEach((s,r)=>{const o=this.columns[r];o&&o.sortable&&(s.classList.add("sortable"),s.dataset.column=o.key,s.classList.remove("sort-asc","sort-desc"),this.sorting.column===o.key&&s.classList.add(this.sorting.direction==="asc"?"sort-asc":"sort-desc"))})}renderPagination(){if(!this.paginationElement)return;this.totalPages=Math.ceil(this.data.length/this.itemsPerPage);const t=this.paginationElement.querySelector("ul");if(!t)return;t.innerHTML="";const a=document.createElement("li");a.className=`page-item ${this.currentPage===1?"disabled":""}`,a.innerHTML='<a class="page-link" href="#!"><i data-lucide="chevron-left" class="size-4"></i> Previous</a>',t.appendChild(a);const e=Math.min(5,this.totalPages);let i=Math.max(1,this.currentPage-2),n=Math.min(i+e-1,this.totalPages);n-i+1<e&&(i=Math.max(1,n-e+1));for(let o=i;o<=n;o++){const d=document.createElement("li");d.className=`page-item ${o===this.currentPage?"active":""}`,d.innerHTML=`<a class="page-link" href="#!" data-page="${o}">${o}</a>`,t.appendChild(d)}const s=document.createElement("li");s.className=`page-item ${this.currentPage===this.totalPages?"disabled":""}`,s.innerHTML='<a class="page-link" href="#!">Next <i data-lucide="chevron-right" class="size-4"></i></a>',t.appendChild(s);const r=this.paginationElement.querySelector("#paginationInfo");if(r){const o=(this.currentPage-1)*this.itemsPerPage+1,d=Math.min(o+this.itemsPerPage-1,this.data.length);r.innerHTML=`Showing <b class="me-1">${o}-${d}</b>of<b class="ms-1">${this.data.length}</b> Results`}b({icons:y})}setupEventListeners(){if(this.paginationElement&&this.paginationElement.addEventListener("click",a=>{a.preventDefault();const e=a.target.closest(".page-link");e&&(e.textContent.includes("Previous")&&this.currentPage>1?this.currentPage--:e.textContent.includes("Next")&&this.currentPage<this.totalPages?this.currentPage++:e.dataset.page&&(this.currentPage=parseInt(e.dataset.page)),this.renderTable(),this.renderPagination())}),this.tableElement){const a=this.tableElement.querySelector("thead");a&&a.addEventListener("click",e=>{const i=e.target.closest("th.sortable");if(!i)return;const n=i.dataset.column;this.sorting.column===n?this.sorting.direction=this.sorting.direction==="asc"?"desc":"asc":(this.sorting.column=n,this.sorting.direction="asc"),this.renderTable()})}const t=document.getElementById(this.deleteModalId);if(t){let a=null;document.addEventListener("click",e=>{const i=e.target.closest(".delete-btn");i&&(a=i.dataset.id)}),t.querySelector(".btn-danger").addEventListener("click",()=>{a&&(this.deleteItem(a),a=null)})}this.addFormElement&&this.addFormElement.addEventListener("submit",a=>{a.preventDefault();const e=new FormData(a.target),i={},n=a.target.dataset.editId;let s=null;n&&(s=this.data.find(m=>m.id===n));for(const[m,h]of e.entries())m==="image"&&h.size===0||(i[m]=h);const r=a.target.querySelector("#imageInput"),o=document.getElementById("LogoPreview");r&&r.files&&r.files[0]?i.image=URL.createObjectURL(r.files[0]):o&&o.src&&o.style.display!=="none"&&(s&&s.image&&!r.files.length?i.image=s.image:i.image=o.src);const d=a.target.querySelector("#doctorInitials");if(d&&d.value)i.doctorInitials=d.value;else if(i.doctor)if(s&&s.doctorInitials&&!d)i.doctorInitials=s.doctorInitials;else{const m=i.doctor.split(" ");m.length>=2?i.doctorInitials=(m[0][0]+m[1][0]).toUpperCase():i.doctorInitials=m[0].substring(0,2).toUpperCase()}delete i.imageFile,n?this.updateItem(n,i):this.addItem(i),this.resetForm();const c=window.bootstrap.Modal.getInstance(document.getElementById(this.addModalId));c&&c.hide()}),document.addEventListener("click",a=>{const e=a.target.closest(".edit-btn");if(e&&this.addFormElement){const i=e.dataset.id,n=this.data.find(s=>s.id===i);if(n){this.addFormElement.reset();for(const d in n){const c=this.addFormElement.querySelector(`[name="${d}"]`);c&&c.type!=="file"&&(c.value=n[d])}const s=document.getElementById("LogoPreview"),r=document.querySelector(".ri-upload-line");s&&n.image?(s.src=n.image,s.classList.remove("d-none"),r&&(r.style.display="none")):(s&&(s.src="",s.classList.add("d-none")),r&&(r.style.display="block")),this.addFormElement.dataset.editId=i;const o=this.addFormElement.querySelector('button[type="submit"]');o&&(o.textContent="Update Department")}}}),document.addEventListener("click",a=>{const e=a.target.closest('[data-bs-target="#'+this.addModalId+'"]');e&&!e.classList.contains("edit-btn")&&this.resetForm()})}getSortedData(){return this.sorting.column?[...this.data].sort((t,a)=>{const e=t[this.sorting.column],i=a[this.sorting.column];return typeof e=="string"&&typeof i=="string"?this.sorting.direction==="asc"?e.localeCompare(i):i.localeCompare(e):this.sorting.direction==="asc"?e-i:i-e}):[...this.data]}addItem(t){if(!t.id){const a=this.data.length>0?this.data[0].id.split("-")[0]:"PED",e=Math.max(...this.data.map(i=>{const n=i.id.split("-");return n.length>1?parseInt(n[1]):0}),0);t.id=`${a}-${e+1}`}this.data.unshift(t),this.renderTable(),this.renderPagination()}updateItem(t,a){const e=this.data.findIndex(i=>i.id===t);if(e!==-1){const n={...this.data[e],...a};n.id=t,this.data[e]=n,this.renderTable()}}deleteItem(t){this.data=this.data.filter(a=>a.id!==t),this.data.length<=(this.currentPage-1)*this.itemsPerPage&&this.currentPage>1&&this.currentPage--,this.renderTable(),this.renderPagination()}exportJSON(){return JSON.stringify(this.data)}importJSON(t){try{return this.data=JSON.parse(t),this.currentPage=1,this.renderTable(),this.renderPagination(),!0}catch(a){return console.error("Invalid JSON data",a),!1}}validateImage(t,a=2){const e=document.getElementById("imageError");if(!e||(e.textContent="",!t))return!0;const i=a*1024*1024;return t.size>i?(e.textContent=`Image size exceeds ${a}MB limit`,!1):["image/jpeg","image/png","image/gif","image/webp"].includes(t.type)?!0:(e.textContent="Invalid file type. Please use JPG, PNG, GIF or WebP",!1)}}document.addEventListener("DOMContentLoaded",function(){const l=[{id:"PED-1",departmentName:"Cardiology",doctor:"Dr. Mark Thompson",doctorInitials:"MT",totalEmployee:8,headOfDept:"Dr. Sarah Patel",status:"Active"},{id:"PED-2",departmentName:"Dermatology",doctor:"Dr. Emily Chen",image:"assets/images/avatar/user-22.png",totalEmployee:7,headOfDept:"Dr. Benjamin Davis",status:"Active"},{id:"PED-3",departmentName:"Pediatrics",doctor:"Dr. Jennifer Ramirez",doctorInitials:"JR",totalEmployee:12,headOfDept:"Dr. Daniel Smith",status:"Active"},{id:"PED-4",departmentName:"Neurology",doctor:"Dr. Rebecca Evans",doctorInitials:"RE",totalEmployee:7,headOfDept:"Dr. Andrew Clark",status:"Active"},{id:"PED-5",departmentName:"Ophthalmology",doctor:"Dr. Sophia Lee",image:"assets/images/avatar/user-23.png",totalEmployee:6,headOfDept:"Dr. David Wong",status:"Unactive"}],t=[{key:"id",label:"ID",sortable:!0},{key:"departmentName",label:"Department Name",sortable:!0},{key:"doctor",label:"Doctor",sortable:!0,render:(a,e)=>e.image?`
              <div class="d-flex align-items-center gap-3">
                <img src="${e.image}" loading="lazy" alt="" class="size-10 rounded-circle">
                <div>
                  <h6 class="mb-1"><a href="#!" class="text-body">${a}</a></h6>
                </div>
              </div>
            `:`
              <div class="d-flex align-items-center gap-3">
                <span class="size-10 rounded-circle bg-light-subtle avatar text-muted fw-semibold fs-sm">${e.doctorInitials}</span>
                <div>
                  <h6 class="mb-1"><a href="#!" class="text-body">${a}</a></h6>
                </div>
              </div>
            `},{key:"totalEmployee",label:"Total Employee",sortable:!0},{key:"headOfDept",label:"Head of Dept.",render:a=>`<a href="#!" class="link link-custom-primary">${a}</a>`},{key:"status",label:"Status",sortable:!0,render:a=>{const e=a==="Active"?"success":"danger";return`<span class="badge bg-${e}-subtle text-${e} border border-${e}-subtle">${a}</span>`}},{key:"action",label:"Action"}];new D({tableId:"departmentTable",paginationId:"paginationContainer",addFormId:"addDepartmentForm",addModalId:"addDepartmentModal",deleteModalId:"deleteModal",data:l,columns:t,itemsPerPage:5})});
