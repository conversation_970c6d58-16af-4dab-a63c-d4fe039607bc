import{c as p,i as g}from"../../admin.bundle-DI9_jvUJ.js";import"../../main-Cyta4iCA.js";const y=[{name:"<PERSON>",email:"<EMAIL>",phone:"************",department:"Radiology",salary:"$13,000",status:"Failed"},{name:"<PERSON>",email:"<EMAIL>",phone:"************",department:"Ophthalmology",salary:"$13,500",status:"Pending"},{name:"<PERSON>",email:"<EMAIL>",phone:"************",department:"Psychiatry",salary:"$14,800",status:"Failed"},{name:"<PERSON>",email:"<EMAIL>",phone:"************",department:"Urology",salary:"$14,500",status:"Successful"},{name:"<PERSON>",email:"<EMAIL>",phone:"************",department:"Rheumatology",salary:"$15,400",status:"Pending"},{name:"<PERSON>",email:"<EMAIL>",phone:"************",department:"Neurology",salary:"$16,000",status:"Pending"},{name:"Michael <PERSON>",email:"<EMAIL>",phone:"************",department:"Pediatrics",salary:"$12,000",status:"Failed"},{name:"Emma Taylor",email:"<EMAIL>",phone:"************",department:"Dermatology",salary:"$13,000",status:"Pending"},{name:"Liam Robinson",email:"<EMAIL>",phone:"************",department:"Hematology",salary:"$13,700",status:"Successful"},{name:"Rachel Green",email:"<EMAIL>",phone:"************",department:"Gastroenterology",salary:"$14,000",status:"Failed"},{name:"Olivia Parker",email:"<EMAIL>",phone:"************",department:"Cardiology",salary:"$15,200",status:"Pending"},{name:"Noah Johnson",email:"<EMAIL>",phone:"************",department:"Neurology",salary:"$16,500",status:"Successful"},{name:"Ava Martinez",email:"<EMAIL>",phone:"************",department:"Oncology",salary:"$14,900",status:"Pending"},{name:"William Lee",email:"<EMAIL>",phone:"************",department:"Radiology",salary:"$13,300",status:"Failed"},{name:"Sophia Adams",email:"<EMAIL>",phone:"************",department:"Orthopedics",salary:"$12,800",status:"Successful"},{name:"James Walker",email:"<EMAIL>",phone:"************",department:"Urology",salary:"$15,100",status:"Pending"},{name:"Mia Thompson",email:"<EMAIL>",phone:"************",department:"Nephrology",salary:"$13,600",status:"Failed"},{name:"Benjamin Scott",email:"<EMAIL>",phone:"************",department:"Endocrinology",salary:"$14,500",status:"Failed"},{name:"Isabella Lewis",email:"<EMAIL>",phone:"************",department:"Pulmonology",salary:"$13,200",status:"Successful"},{name:"Elijah White",email:"<EMAIL>",phone:"************",department:"Pathology",salary:"$12,600",status:"Pending"}],c=10;let o=1,i=[...y],d=null,s={key:null,direction:"asc"};const u=document.querySelector("tbody"),b=document.querySelector(".pagination"),h=document.querySelectorAll("thead th");function f(e,n="asc"){return function(a,t){if(e==="salary"){const l=parseFloat(a[e].replace("$","").replace(",","")),r=parseFloat(t[e].replace("$","").replace(",",""));return n==="asc"?l-r:r-l}return a[e]<t[e]?n==="asc"?-1:1:a[e]>t[e]?n==="asc"?1:-1:0}}function x(){s.key&&i.sort(f(s.key,s.direction))}function $(){const e=["name","email","phone","department","salary","status"];h.forEach((n,a)=>{if(a<e.length){const t=e[a];n.classList.add("sortable"),n.style.cursor="pointer";const l=n.textContent;n.innerHTML=`${l} <span class="sort-icon"></span>`,n.addEventListener("click",()=>{s.key===t?s.direction=s.direction==="asc"?"desc":"asc":(s.key=t,s.direction="asc"),v(),x(),o=1,m()})}})}function v(){h.forEach((e,n)=>{const a=e.querySelector(".sort-icon");if(a&&(a.innerHTML="",n===["name","email","phone","department","salary","status"].indexOf(s.key))){const t=document.createElement("i");t.setAttribute("data-lucide",s.direction==="asc"?"arrow-up":"arrow-down"),t.classList.add("size-4","ml-1","inline-block"),a.appendChild(t),p({icons:g})}})}function m(){u.innerHTML="";const e=(o-1)*c,n=e+c;i.slice(e,n).forEach((t,l)=>{const r=document.createElement("tr");r.innerHTML=`
        <td>${t.name}</td>
        <td>${t.email}</td>
        <td>${t.phone}</td>
        <td>${t.department}</td>
        <td>${t.salary}</td>
        <td>
          <span class="badge ${t.status==="Successful"?"bg-success-subtle text-success border border-success-subtle":t.status==="Pending"?"bg-warning-subtle text-warning border border-warning-subtle":"bg-danger-subtle text-danger border border-danger-subtle"}">${t.status}</span>
        </td>
        <td>
          <div class="d-flex align-items-center gap-2">
            <button class="btn btn-sub-primary size-8 btn-icon"><i class="ri-pencil-line"></i></button>
            <button class="btn btn-sub-danger size-8 btn-icon" data-bs-toggle="modal" data-bs-target="#deleteModal" data-index="${e+l}">
              <i class="ri-delete-bin-line"></i>
            </button>
          </div>
        </td>`,u.appendChild(r)}),document.getElementById("range-start").textContent=i.length===0?0:e+1,document.getElementById("range-end").textContent=Math.min(n,i.length),document.getElementById("range-total").textContent=i.length,P()}function P(){const e=Math.ceil(i.length/c),n=[];for(let a=1;a<=e;a++)n.push(`<li class="page-item ${a===o?"active":""}">
        <a class="page-link" href="#">${a}</a></li>`);b.innerHTML=`
      <li class="page-item ${o===1?"disabled":""}">
        <a class="page-link" href="#" data-nav="prev"><i data-lucide="chevron-left" class="size-4"></i> Previous</a>
      </li>
      ${n.join("")}
      <li class="page-item ${o===e?"disabled":""}">
        <a class="page-link" href="#" data-nav="next">Next <i data-lucide="chevron-right" class="size-4"></i></a>
      </li>
    `,document.querySelectorAll(".page-link").forEach(a=>{a.addEventListener("click",t=>{t.preventDefault(),a.dataset.nav==="prev"&&o>1?o--:a.dataset.nav==="next"&&o<e?o++:isNaN(+a.textContent)||(o=+a.textContent),m()})}),p({icons:g})}document.getElementById("deleteModal").addEventListener("show.bs.modal",function(e){d=+e.relatedTarget.getAttribute("data-index")});document.querySelector("#deleteModal .btn-danger").addEventListener("click",function(){if(d!==null){i.splice(d,1);const e=Math.ceil(i.length/c);o>e&&(o=e),m(),d=null}});$();m();
