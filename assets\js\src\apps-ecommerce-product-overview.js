import{c as m,i as u}from"../../admin.bundle-DI9_jvUJ.js";import"../../form-input-spin.init-BLcZ9-IP.js";import{A as h}from"../../air-datepicker-DlUcrly3.js";import{S as d}from"../../swiper-bundle-BRj3juaA.js";import"../../main-Cyta4iCA.js";new d(".productSlider",{loop:!0,pagination:{el:".swiper-pagination",clickable:!0},autoplay:{delay:2500,disableOnInteraction:!1}});new d(".previewImages",{loop:!0,pagination:{el:".swiper-pagination",type:"fraction"},autoplay:{delay:2500,disableOnInteraction:!1},navigation:{nextEl:".swiper-button-next",prevEl:".swiper-button-prev"}});document.addEventListener("DOMContentLoaded",function(){const o=document.querySelectorAll(".product-size a");o.forEach(e=>{e.addEventListener("click",function(a){a.preventDefault(),o.forEach(i=>{i.classList.remove("active","text-success"),i.classList.add("text-muted")}),this.classList.add("active","text-success"),this.classList.remove("text-muted")})})});document.addEventListener("DOMContentLoaded",function(){const o=[document.getElementById("rating-1"),document.getElementById("rating-2"),document.getElementById("rating-3"),document.getElementById("rating-4"),document.getElementById("rating-5")],e=[document.getElementById("emoji-1"),document.getElementById("emoji-2"),document.getElementById("emoji-3"),document.getElementById("emoji-4"),document.getElementById("emoji-5")],a=document.getElementById("rating-input");function i(t){t=Math.max(0,Math.min(4,t)),o.forEach((n,s)=>{s<=t?(n.classList.remove("bg-body-tertiary"),n.classList.add("bg-warning")):(n.classList.remove("bg-warning"),n.classList.add("bg-body-tertiary"))}),e.forEach(n=>{n.classList.add("d-none")}),e[t].classList.remove("d-none"),a.value=t+1}o.forEach((t,n)=>{t.addEventListener("click",()=>{i(n)})}),a.addEventListener("change",function(){let t=parseInt(this.value);isNaN(t)&&(t=1),t=Math.max(1,Math.min(5,t)),this.value=t,i(t-1)}),a.addEventListener("input",function(){let t=parseInt(this.value)||0;t=Math.max(0,Math.min(5,t)),t>=1&&t<=5&&i(t-1)}),i(0)});const p=[{name:"John Doe",date:"24 May, 2024",location:"New York",image:"assets/images/avatar/user-1.png",rating:4.5,title:"Code Quality",message:"Was a really good move to add this as part of our project, we had great comments from the business regarding design and user experience. Install with no dependency issues or deprecated packages and was really good overall."},{name:"Jane Smith",date:"22 May, 2024",location:"Los Angeles",image:"assets/images/avatar/user-2.png",rating:5,title:"Documentation Quality",message:"Excellent product! Exceeded all my expectations. Highly recommend to anyone looking for quality."},{name:"Michael Brown",date:"20 May, 2024",location:"Chicago",image:"assets/images/avatar/user-3.png",rating:3.5,title:"Design Quality",message:"The product is decent but has some room for improvement. The installation was smooth, but some features were a bit underwhelming."},{name:"Emily Davis",date:"18 May, 2024",location:"Houston",image:"assets/images/avatar/user-4.png",rating:2,title:"Flexibility",message:"Not very satisfied with the product. It did not meet my expectations and had several issues during installation."},{name:"Chris Wilson",date:"15 May, 2024",location:"Phoenix",image:"assets/images/avatar/user-5.png",rating:4,title:"Code Quality",message:"Good product overall, but there are a few bugs that need to be addressed. Customer support was helpful in resolving some issues."},{name:"Sarah Lee",date:"12 May, 2024",location:"San Francisco",image:"assets/images/avatar/user-6.png",rating:4.5,title:"Customizability",message:"Great product with excellent features. The user interface is very intuitive and easy to navigate."},{name:"David Johnson",date:"10 May, 2024",location:"Miami",image:"assets/images/avatar/user-7.png",rating:3.5,title:"Code Quality",message:"The product is average. It works as expected but lacks some advanced features that competitors offer."},{name:"Nancy Adams",date:"8 May, 2024",location:"Seattle",image:"assets/images/avatar/user-8.png",rating:4.5,title:"Design Quality",message:"Had some issues with the product initially, but customer support was able to help resolve them. Still, it's not as seamless as I hoped."},{name:"Paul White",date:"5 May, 2024",location:"Boston",image:"assets/images/avatar/user-9.png",rating:3.5,title:"Feature Availability",message:"The product did not meet my expectations at all. It was difficult to install and had many bugs."},{name:"Lisa Green",date:"3 May, 2024",location:"Denver",image:"assets/images/avatar/user-10.png",rating:5,title:"Design Quality",message:"Absolutely fantastic product! It has all the features I need and works flawlessly."},{name:"James Clark",date:"1 May, 2024",location:"Atlanta",image:"assets/images/avatar/user-11.png",rating:5,title:"Feature Availability",message:"Overall, I'm happy with the product. It performs well and has a good range of features."},{name:"Patricia Martinez",date:"28 April, 2024",location:"Dallas",image:"assets/images/avatar/user-12.png",rating:4.5,title:"Flexibility",message:"The product is good but could use some improvements. The user interface could be more user-friendly."},{name:"Charles Brown",date:"25 April, 2024",location:"Orlando",image:"assets/images/avatar/user-13.png",rating:1,title:"Code Quality",message:"I had high hopes for this product, but it didn't deliver as expected. There were several issues that made it difficult to use."},{name:"Mary Johnson",date:"22 April, 2024",location:"Philadelphia",image:"assets/images/avatar/user-14.png",rating:4,title:"Feature Availability",message:"Overall, a good product. It has most of the features I need and works well."},{name:"Richard Wilson",date:"20 April, 2024",location:"San Diego",image:"assets/images/avatar/user-15.png",rating:3.5,title:"Design Quality",message:"Decent product, but the design could be more modern. It's a bit outdated."},{name:"Karen Taylor",date:"18 April, 2024",location:"Las Vegas",image:"assets/images/avatar/user-16.png",rating:2.5,title:"Flexibility",message:"Not very flexible. It's difficult to customize according to our needs."},{name:"Daniel Thomas",date:"15 April, 2024",location:"Austin",image:"assets/images/avatar/user-17.png",rating:4,title:"Documentation Quality",message:"Good documentation. It helped us understand the product better."},{name:"Barbara Hernandez",date:"12 April, 2024",location:"San Antonio",image:"assets/images/avatar/user-18.png",rating:3,title:"Feature Availability",message:"Lacking some features we expected. It needs more customization options."},{name:"Matthew Martinez",date:"10 April, 2024",location:"Charlotte",image:"assets/images/avatar/user-19.png",rating:4.5,title:"Flexibility",message:"Very flexible product. We were able to customize it according to our needs."},{name:"Amanda Young",date:"8 April, 2024",location:"San Jose",image:"assets/images/avatar/user-20.png",rating:5,title:"Code Quality",message:"Excellent code quality. It's well-structured and easy to maintain."},{name:"Robert Lopez",date:"5 April, 2024",location:"Indianapolis",image:"assets/images/avatar/user-21.png",rating:3,title:"Documentation Quality",message:"Documentation could be improved. It lacks some details and examples."},{name:"Dorothy Gonzalez",date:"3 April, 2024",location:"Jacksonville",image:"assets/images/avatar/user-22.png",rating:4.5,title:"Design Quality",message:"Great design! It's visually appealing and easy to navigate."},{name:"Joseph Perez",date:"1 April, 2024",location:"San Francisco",image:"assets/images/avatar/user-23.png",rating:4,title:"Flexibility",message:"Flexible enough for our needs. We were able to customize it to fit our workflow."},{name:"Donna Flores",date:"29 March, 2024",location:"Columbus",image:"assets/images/avatar/user-24.png",rating:2,title:"Feature Availability",message:"Lacks some important features we were looking for. Disappointing."},{name:"Kenneth Scott",date:"27 March, 2024",location:"Fort Worth",image:"assets/images/avatar/user-25.png",rating:4.5,title:"Design Quality",message:"The design is top-notch! It's clean, modern, and intuitive."},{name:"Jennifer King",date:"24 March, 2024",location:"Memphis",image:"assets/images/avatar/user-26.png",rating:3,title:"Feature Availability",message:"Average feature set. It meets our basic needs but lacks advanced functionality."},{name:"Gerald Hernandez",date:"22 March, 2024",location:"Baltimore",image:"assets/images/avatar/user-27.png",rating:4,title:"Documentation Quality",message:"Good documentation. It helped us get started with the product quickly."},{name:"Megan Sanchez",date:"20 March, 2024",location:"Washington",image:"assets/images/avatar/user-28.png",rating:4.5,title:"Code Quality",message:"High-quality codebase. It's well-written and easy to understand."}];class v{constructor(e={}){this.config={perPage:e.perPage||8,reviewsData:e.reviewsData||[],reviewTableBody:e.reviewTableBody||"reviewTableBody",paginationSummary:e.paginationSummary||"paginationSummary",paginationControls:e.paginationControls||"paginationControls",addReviewModal:e.addReviewModal||"addReviewModal",addReviewBtn:e.addReviewBtn||"addReviewBtn",confirmDeleteBtn:e.confirmDeleteBtn||"confirmDeleteBtn",deleteModal:e.deleteModal||"deleteModal"},this.state={currentPage:1,deleteItemUsername:null,editingReviewIndex:null,totalPages:Math.ceil(this.config.reviewsData.length/this.config.perPage)},this.init()}init(){this.renderTable(),this.renderPagination(),this.attachEventListeners()}renderStars(e){let a="";for(let i=1;i<=5;i++)e>=i?a+='<i class="ri-star-fill text-warning"></i>':e>=i-.5?a+='<i class="ri-star-half-fill text-warning"></i>':a+='<i class="ri-star-line text-warning"></i>';return a}renderTable(){const e=(this.state.currentPage-1)*this.config.perPage,a=Math.min(e+this.config.perPage,this.config.reviewsData.length),i=this.config.reviewsData.slice(e,a),t=document.getElementById(this.config.reviewTableBody);if(!t)return;t.innerHTML=i.map((s,r)=>{const l=e+r;return`
          <tr class="gap-2">
            <td class="align-top text-nowrap">
              <div class="d-flex align-items-center gap-3 flex-nowrap">
                <img src="${s.image}" alt="" class="rounded-2 flex-shrink-0 size-16" loading="lazy">
                <div class="overflow-hidden flex-grow-1">
                  <h6 class="mb-1"><a href="#!" class="link link-custom">${s.name}</a></h6>
                  <p class="mb-1 fs-sm text-truncate">${s.date}</p>
                  <p class="fs-sm text-muted">Location: <span>${s.location}</span></p>
                </div>
              </div>
            </td>
            <td class="whitespace-nowrap">
              <div class="w-350px">
                <div class="d-flex align-items-center gap-2 mb-3">
                  <div class="text-warning d-flex align-items-center">
                    ${this.renderStars(s.rating)}
                  </div>
                  <h6 class="mb-0">(${s.rating})</h6>
                </div>
                <h6 class="mb-1 lh-base">${s.title}</h6>
                <p class="text-muted">${s.message}</p>
              </div>
            </td>
            <td class="align-top text-nowrap">
              <div class="d-flex align-items-center justify-content-end gap-3">
                <button class="btn btn-light flex-shrink-0">Direct Message</button>
                <div class="dropdown">
                  <button class="btn btn-primary btn-icon" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="ri-more-2-fill"></i>
                  </button>
                  <ul class="dropdown-menu dropdown-menu-end">
                    <li><a href="#${this.config.addReviewModal}" class="dropdown-item edit-review-btn" data-bs-toggle="modal" data-global-index="${l}"><i class="align-middle me-2 ri-pencil-line"></i> Edit</a></li>
                    <li><a href="#${this.config.deleteModal}" class="dropdown-item delete-review-btn" data-bs-toggle="modal" data-username="${s.name}"><i class="align-middle me-2 ri-delete-bin-line"></i> Delete</a></li>
                  </ul>
                </div>
              </div>
            </td>
          </tr>
        `}).join("");const n=document.getElementById(this.config.paginationSummary);n&&(n.innerHTML=`Showing <b>${e+1}</b>-<b>${a}</b> of <b>${this.config.reviewsData.length}</b> Results`)}renderPagination(){this.state.totalPages=Math.ceil(this.config.reviewsData.length/this.config.perPage);const e=document.getElementById(this.config.paginationControls);if(!e)return;e.innerHTML="";const a=document.createElement("li");a.className=`page-item ${this.state.currentPage===1?"disabled":""}`,a.innerHTML='<a class="page-link" href="#!"><i data-lucide="chevron-left" class="size-4"></i>Previous</a>',a.addEventListener("click",t=>{t.preventDefault(),this.changePage(this.state.currentPage-1)}),e.appendChild(a);for(let t=1;t<=this.state.totalPages;t++){const n=document.createElement("li");n.className=`page-item ${t===this.state.currentPage?"active":""}`,n.innerHTML=`<a class="page-link" href="#!">${t}</a>`,n.addEventListener("click",s=>{s.preventDefault(),this.changePage(t)}),e.appendChild(n)}const i=document.createElement("li");i.className=`page-item ${this.state.currentPage===this.state.totalPages?"disabled":""}`,i.innerHTML='<a class="page-link" href="#!">Next <i data-lucide="chevron-right" class="size-4"></i></a>',i.addEventListener("click",t=>{t.preventDefault(),this.changePage(this.state.currentPage+1)}),e.appendChild(i),m({icons:u})}changePage(e){e<1||e>this.state.totalPages||(this.state.currentPage=e,this.renderTable(),this.renderPagination())}addOrUpdateReview(){const e=document.getElementById("userNameInput").value.trim(),a=document.getElementById("createDateInput").value.trim(),i=document.getElementById("locationInput").value.trim(),t=document.getElementById("titleInput").value.trim(),n=document.getElementById("writeReviewInput").value.trim(),s=parseFloat(document.getElementById("rating-input").value),r=c=>{const g=document.querySelector("form #reviewDiv");g.innerHTML=`
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <span>${c}</span>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                `};if(!e||!a||!i||!t||!n||isNaN(s))return r("Please fill all fields properly!"),!1;const l={name:e,date:a,location:i,title:t,message:n,rating:s};return this.state.editingReviewIndex!==null?(l.image=this.config.reviewsData[this.state.editingReviewIndex].image,this.config.reviewsData[this.state.editingReviewIndex]=l):(l.image="assets/images/avatar/user-45.png",this.config.reviewsData.unshift(l)),this.state.editingReviewIndex===null&&(this.state.currentPage=1),this.renderTable(),this.renderPagination(),this.resetForm(),!0}resetForm(){const e=document.querySelector("form");e&&e.reset(),this.state.editingReviewIndex=null;const a=document.querySelector(`#${this.config.addReviewModal} h6`);a&&(a.textContent="Add Review");const i=document.getElementById(this.config.addReviewBtn);i&&(i.textContent="Add Review")}deleteReview(){if(!this.state.deleteItemUsername)return!1;const e=this.config.reviewsData.findIndex(a=>a.name===this.state.deleteItemUsername);if(e!==-1){this.config.reviewsData.splice(e,1),this.state.deleteItemUsername=null;const a=Math.ceil(this.config.reviewsData.length/this.config.perPage);return this.state.currentPage>a&&(this.state.currentPage=Math.max(1,a)),this.renderTable(),this.renderPagination(),!0}return!1}attachEventListeners(){const e=document.getElementById(this.config.addReviewBtn);e&&e.addEventListener("click",()=>{if(this.addOrUpdateReview()){const n=document.getElementById(this.config.addReviewModal),s=window.bootstrap.Modal.getInstance(n);s&&s.hide()}});const a=document.getElementById(this.config.confirmDeleteBtn);a&&a.addEventListener("click",()=>{if(this.deleteReview()){const n=document.getElementById(this.config.deleteModal),s=window.bootstrap.Modal.getInstance(n);s&&s.hide()}}),document.addEventListener("click",t=>{const n=t.target.closest(".edit-review-btn");if(n){const r=parseInt(n.getAttribute("data-global-index"));!isNaN(r)&&this.config.reviewsData[r]&&this.editReview(r)}const s=t.target.closest(".delete-review-btn");s&&(this.state.deleteItemUsername=s.getAttribute("data-username"))});const i=document.getElementById(this.config.addReviewModal);i&&i.addEventListener("hidden.bs.modal",()=>{this.resetForm()})}editReview(e){const a=this.config.reviewsData[e];if(!a)return;this.state.editingReviewIndex=e,document.getElementById("userNameInput").value=a.name,document.getElementById("createDateInput").value=a.date,document.getElementById("locationInput").value=a.location,document.getElementById("titleInput").value=a.title,document.getElementById("writeReviewInput").value=a.message,document.getElementById("rating-input").value=a.rating,typeof updateRating=="function"&&updateRating(Math.floor(a.rating)-1);const i=document.querySelector(`#${this.config.addReviewModal} h6`);i&&(i.textContent="Edit Review");const t=document.getElementById(this.config.addReviewBtn);t&&(t.textContent="Save Changes")}}document.addEventListener("DOMContentLoaded",function(){new h("#createDateInput",{autoClose:!0,dateFormat:"dd-MM-yyyy"});const o=new v({reviewsData:p,perPage:8});window.productOverview=o});
