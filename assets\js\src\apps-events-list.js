import{c as d,i as c}from"../../admin.bundle-DI9_jvUJ.js";/* empty css                               */import{A as u}from"../../air-datepicker-DlUcrly3.js";import"../../main-Cyta4iCA.js";VirtualSelect.init({ele:"#StatusSelect",options:[{label:"Published",value:"Published"},{label:"Coming Soon",value:"Coming Soon"},{label:"Expired",value:"Expired"}]});VirtualSelect.init({ele:"#eventTypeSelect",options:[{label:"Offline",value:"Offline"},{label:"Online",value:"Online"}]});class m{constructor(){this.data=[],this.filteredData=[],this.currentPage=1,this.itemsPerPage=10,this.sortColumn="none",this.sortDirection="asc",this.editingId=null,this.modal=null,this.loadSampleData(),this.initEventListeners(),this.modal=new window.bootstrap.Modal(document.getElementById("leadCreateModal")),this.render()}loadSampleData(){this.data=[{id:1,logo:"assets/images/brands/img-01.png",name:"Tech Innovations Summit",date:"2024-06-15",duration:"09:00",people:500,location:"New York, NY",type:"Offline",price:899,status:"Published"},{id:2,logo:"assets/images/brands/img-02.png",name:"Marketing Strategies 2024",date:"2024-07-20",duration:"10:00",people:300,location:"San Francisco, CA",type:"Offline",price:499,status:"Published"},{id:3,logo:"assets/images/brands/img-03.png",name:"AI & Machine Learning Expo",date:"2024-08-10",duration:"11:00",people:800,location:"Chicago, IL",type:"Online",price:599,status:"Coming Soon"},{id:4,logo:"assets/images/brands/img-04.png",name:"Blockchain World Conference",date:"2024-09-05",duration:"09:30",people:1e3,location:"Los Angeles, CA",type:"Offline",price:1099,status:"Published"},{id:5,logo:"assets/images/brands/img-05.png",name:"Cybersecurity Summit",date:"2024-10-18",duration:"10:30",people:600,location:"Boston, MA",type:"Offline",price:799,status:"Published"},{id:6,logo:"assets/images/brands/img-06.png",name:"Healthcare Innovations Forum",date:"2024-11-15",duration:"12:00",people:400,location:"Seattle, WA",type:"Online",price:399,status:"Coming Soon"},{id:7,logo:"assets/images/brands/img-07.png",name:"E-commerce Trends 2024",date:"2024-12-05",duration:"13:00",people:700,location:"Austin, TX",type:"Offline",price:699,status:"Published"},{id:8,logo:"assets/images/brands/img-08.png",name:"Fintech Conference",date:"2024-06-30",duration:"09:00",people:200,location:"Miami, FL",type:"Online",price:499,status:"Expired"},{id:9,logo:"assets/images/brands/img-09.png",name:"Digital Transformation Summit",date:"2024-07-25",duration:"09:00",people:900,location:"Dallas, TX",type:"Offline",price:899,status:"Published"},{id:10,logo:"assets/images/brands/img-10.png",name:"Green Technology Expo",date:"2024-08-15",duration:"10:00",people:500,location:"Denver, CO",type:"Online",price:599,status:"Coming Soon"},{id:11,logo:"assets/images/brands/img-11.png",name:"Smart Cities Conference",date:"2024-09-10",duration:"11:00",people:600,location:"Las Vegas, NV",type:"Offline",price:999,status:"Published"},{id:12,logo:"assets/images/brands/img-12.png",name:"Virtual Reality Expo",date:"2024-10-22",duration:"12:00",people:300,location:"Orlando, FL",type:"Online",price:399,status:"Coming Soon"},{id:13,logo:"assets/images/brands/img-13.png",name:"Renewable Energy Summit",date:"2024-11-05",duration:"09:00",people:1e3,location:"San Diego, CA",type:"Offline",price:1099,status:"Published"},{id:14,logo:"assets/images/brands/img-14.png",name:"IoT World Forum",date:"2024-12-01",duration:"10:00",people:700,location:"Phoenix, AZ",type:"Online",price:499,status:"Published"},{id:15,logo:"assets/images/brands/img-15.png",name:"EdTech Conference",date:"2024-08-25",duration:"11:00",people:400,location:"Houston, TX",type:"Offline",price:699,status:"Coming Soon"},{id:16,logo:"assets/images/brands/img-16.png",name:"Cloud Computing Summit",date:"2024-09-18",duration:"12:00",people:800,location:"Salt Lake City, UT",type:"Offline",price:899,status:"Published"}],this.filteredData=[...this.data]}initEventListeners(){this.addSortableHeaders(),document.getElementById("event-form").addEventListener("submit",a=>{a.preventDefault(),this.saveEvent()}),document.getElementById("event-logo").addEventListener("change",a=>{this.handleImageUpload(a)}),document.querySelector('[data-bs-target="#leadCreateModal"]').addEventListener("click",()=>{this.resetForm()}),document.getElementById("event-table-body").addEventListener("click",a=>{const e=a.target.closest(".dropdown-item");if(!e)return;const t=a.target.closest("tr"),n=parseInt(t.dataset.id);e.innerText.includes("Edit")?this.editEvent(n):e.innerText.includes("Delete")&&this.deleteEvent(n)})}addSortableHeaders(){const a=[{column:"name",headerIndex:0,label:"Event Name"},{column:"date",headerIndex:1,label:"Event Date"},{column:"people",headerIndex:2,label:"People Size"},{column:"location",headerIndex:3,label:"Location"},{column:"type",headerIndex:4,label:"Event Type"},{column:"price",headerIndex:5,label:"Price"},{column:"status",headerIndex:6,label:"Status"}],e=document.querySelector("thead tr");if(!e)return;const t=e.querySelectorAll("th");a.forEach(({column:n,headerIndex:i,label:o})=>{if(i<t.length){const s=t[i];s.innerHTML=`
                    <div class="d-flex align-items-center gap-2">
                        <span class="fw-medium text-muted">${o}</span>
                        <a href="#!" class="sort-btn text-muted" data-column="${n}">
                            <i class="ri-arrow-up-down-line sort-icon"></i>
                        </a>
                    </div>
                `,s.querySelector(".sort-btn").addEventListener("click",()=>this.toggleSort(n))}})}toggleSort(a){this.sortColumn===a?this.sortDirection=this.sortDirection==="asc"?"desc":"asc":(this.sortColumn=a,this.sortDirection="asc"),this.currentPage=1,this.sortData(),this.updateSortIcons(),this.render()}updateSortIcons(){document.querySelectorAll(".sort-icon").forEach(e=>{e.className="ri-arrow-up-down-line sort-icon"});const a=document.querySelector(`.sort-btn[data-column="${this.sortColumn}"]`);if(a){const e=a.querySelector(".sort-icon");e&&(e.className=this.sortDirection==="asc"?"ri-arrow-up-line sort-icon":"ri-arrow-down-line sort-icon")}}sortData(){if(this.sortColumn==="none"){this.filteredData=[...this.data];return}this.filteredData.sort((a,e)=>{let t=a[this.sortColumn],n=e[this.sortColumn];return this.sortColumn==="people"||this.sortColumn==="price"?(t=Number(t),n=Number(n),this.sortDirection==="asc"?t-n:n-t):this.sortColumn==="date"?(t=new Date(t),n=new Date(n),this.sortDirection==="asc"?t-n:n-t):(t=String(t).toLowerCase(),n=String(n).toLowerCase(),t<n?this.sortDirection==="asc"?-1:1:t>n?this.sortDirection==="asc"?1:-1:0)})}render(){this.renderTable(),this.renderPagination(),this.updateTotalCount()}renderTable(){const a=document.getElementById("event-table-body");a.innerHTML="";const e=(this.currentPage-1)*this.itemsPerPage,t=e+this.itemsPerPage;this.filteredData.slice(e,t).forEach(s=>{const r=document.createElement("tr");r.dataset.id=s.id;const l=s.status==="Published"?"bg-success-subtle text-success border border-success-subtle":s.status==="Coming Soon"?"bg-warning-subtle text-warning border border-warning-subtle":"bg-danger-subtle text-danger border border-danger-subtle";r.innerHTML=`
                <td>
                    <div class="d-flex align-items-center gap-2">
                        <div class="size-9 border avatar rounded">
                            <img src="${s.logo}" loading="lazy" alt="" class="size-7">
                        </div>
                        <h6 class="mb-0"><a href="#!" class="text-reset">${s.name}</a></h6>
                    </div>
                </td>
                <td>${this.formatDate(s.date)}</td>
                <td>${s.people}</td>
                <td>${s.location}</td>
                <td>${s.type}</td>
                <td>$${s.price}</td>
                <td>
                    <span class="badge ${l}">${s.status}</span>
                </td>
                <td>
                    <div class="dropdown">
                        <a href="#!" class="link link-custom-primary" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="ri-more-2-fill"></i>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#!"><i class="ri-eye-line me-2"></i><span>Overview</span></a></li>
                            <li><a class="dropdown-item" href="#!"><i class="ri-pencil-line me-2"></i>Edit</a></li>
                            <li><a class="dropdown-item" href="#!"><i class="ri-delete-bin-line me-2"></i><span>Delete</span></a></li>
                        </ul>
                    </div>
                </td>
            `,a.appendChild(r)});const i=this.filteredData.length>0?e+1:0,o=Math.min(t,this.filteredData.length);document.getElementById("pagination-info").innerHTML=`
            Showing <b class="me-1">${i}-${o}</b> of <b class="ms-1">${this.filteredData.length}</b> Results
        `}renderPagination(){const a=document.getElementById("pagination");a.innerHTML="";const e=Math.ceil(this.filteredData.length/this.itemsPerPage),t=document.createElement("li");t.className=`page-item ${this.currentPage===1?"disabled":""}`,t.innerHTML='<a class="page-link" href="#"> <i data-lucide="chevron-left" class="size-4"></i> Previous</a>',t.addEventListener("click",i=>{i.preventDefault(),this.currentPage>1&&(this.currentPage--,this.render())}),a.appendChild(t);for(let i=1;i<=e;i++){const o=document.createElement("li");o.className=`page-item ${this.currentPage===i?"active":""}`,o.innerHTML=`<a class="page-link" href="#">${i}</a>`,o.addEventListener("click",s=>{s.preventDefault(),this.currentPage=i,this.render()}),a.appendChild(o)}const n=document.createElement("li");n.className=`page-item ${this.currentPage===e?"disabled":""}`,n.innerHTML='<a class="page-link" href="#">Next <i data-lucide="chevron-right" class="size-4"></i></a>',n.addEventListener("click",i=>{i.preventDefault(),this.currentPage<e&&(this.currentPage++,this.render())}),a.appendChild(n),d({icons:c})}updateTotalCount(){document.getElementById("total-count").textContent=this.data.length}resetForm(){document.getElementById("modal-title").textContent="Add New Event",document.getElementById("submit-btn").textContent="Add Event",document.getElementById("event-id").value="",document.getElementById("event-form").reset(),this.editingId=null;const a=document.querySelector("#uploadLabel");a.innerHTML=`
        <i class="ri-upload-2-line"></i>
        <span class="block mt-3">Upload Your Event Logo</span>
        <input type="file" id="event-logo" class="d-none" />
    `,this.tempLogoDataUrl=null,document.getElementById("event-logo").addEventListener("change",e=>{this.handleImageUpload(e)}),document.querySelectorAll(".text-danger").forEach(e=>e.classList.add("d-none"))}handleImageUpload(a){const e=a.target.files[0];if(!e)return;if(!e.type.match("image.*")){alert("Please select an image file");return}const t=new FileReader;t.onload=n=>{const i=document.querySelector("#uploadLabel");i.innerHTML="";const o=document.createElement("img");o.src=n.target.result,o.className="img-fluid",o.style.maxHeight="100%",o.style.maxWidth="100%",o.style.objectFit="contain",i.appendChild(o),this.tempLogoDataUrl=n.target.result},t.readAsDataURL(e)}editEvent(a){const e=this.data.find(t=>t.id===a);if(e){if(this.editingId=a,document.getElementById("modal-title").textContent="Edit Event",document.getElementById("submit-btn").textContent="Update Event",document.getElementById("event-id").value=e.id,document.getElementById("event-name").value=e.name,document.getElementById("event-date").value=e.date,document.getElementById("event-duration").value=e.duration,document.getElementById("event-people").value=e.people,document.getElementById("event-price").value=e.price,document.getElementById("event-location").value=e.location,window.VsEventType&&e.type&&window.VsEventType.setValue(e.type),window.VsStatus&&e.status&&window.VsStatus.setValue(e.status),e.logo){const t=document.querySelector("#uploadLabel");t.innerHTML="";const n=document.createElement("img");n.src=e.logo,n.className="img-fluid",n.style.maxHeight="100%",n.style.maxWidth="100%",n.style.objectFit="contain";const i=document.createElement("input");i.type="file",i.id="event-logo",i.className="d-none",t.appendChild(n),t.appendChild(i),this.tempLogoDataUrl=e.logo,i.addEventListener("change",o=>{this.handleImageUpload(o)})}this.modal.show()}}deleteEvent(a){confirm("Are you sure you want to delete this event?")&&(this.data=this.data.filter(e=>e.id!==a),this.filteredData=this.filteredData.filter(e=>e.id!==a),this.render())}validateForm(){let a=!0;const e=[{id:"event-name",error:"name-error"},{id:"event-date",error:"date-error"},{id:"event-duration",error:"duration-error"},{id:"event-people",error:"people-error"},{id:"event-price",error:"price-error"},{id:"event-location",error:"location-error"},{id:"event-type",error:"type-error"},{id:"event-status",error:"status-error"}];return document.querySelectorAll(".text-danger").forEach(t=>t.classList.add("d-none")),e.forEach(t=>{const n=document.getElementById(t.id),i=document.getElementById(t.error);n.value.trim()||(i.classList.remove("d-none"),a=!1)}),a}saveEvent(){if(!this.validateForm())return;const a={name:document.getElementById("event-name").value,date:document.getElementById("event-date").value,duration:document.getElementById("event-duration").value,people:parseInt(document.getElementById("event-people").value),price:parseInt(document.getElementById("event-price").value),location:document.getElementById("event-location").value,type:document.getElementById("event-type").value,status:document.getElementById("event-status").value,logo:this.tempLogoDataUrl||"assets/images/brands/img-01.png"};if(this.editingId){const e=this.data.findIndex(t=>t.id===this.editingId);e!==-1&&(this.data[e]={...this.data[e],...a})}else{const e=Math.max(...this.data.map(t=>t.id),0)+1;this.data.unshift({id:e,...a})}this.resetForm(),this.modal.hide(),this.filteredData=[...this.data],this.sortData(),this.render()}formatDate(a){const e=new Date(a),t=e.getDate(),n=e.toLocaleString("default",{month:"short"}),i=e.getFullYear();return`${t} ${n} ${i}`}}new u("#event-date",{dateFormat:"dd MMM, yyyy",autoClose:!0,position:"bottom left",locale:{days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],daysShort:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],daysMin:["Su","Mo","Tu","We","Th","Fr","Sa"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],monthsShort:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],today:"Today",clear:"Clear",firstDay:0}});document.addEventListener("DOMContentLoaded",()=>{window.tableManager=new m});
