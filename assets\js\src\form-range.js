import"../../admin.bundle-DI9_jvUJ.js";/* empty css                           */import"../../main-Cyta4iCA.js";var i=document.getElementById("slider");noUiSlider.create(i,{start:[20,80],connect:!0,range:{min:0,max:100}});var f=document.getElementById("values-slider"),l=[1,2,3,4,5,6,7,8,10,12,14,16,20,24,28,32],t={to:function(e){return l[Math.round(e)]},from:function(e){return l.indexOf(Number(e))}};noUiSlider.create(f,{start:[8,24],range:{min:0,max:l.length-1},step:1,tooltips:!0,format:t,pips:{mode:"steps",format:t}});f.noUiSlider.set(["7","28"]);var g=document.getElementById("arbitrary-values-slider"),s=["128MB","256MB","1GB","8GB","16GB","32GB"],t={to:function(e){return s[Math.round(e)]},from:function(e){return s.indexOf(e)}};noUiSlider.create(g,{start:["1GB","16GB"],range:{min:0,max:s.length-1},step:1,tooltips:!0,format:t,pips:{mode:"steps",format:t,density:50}});var i=document.getElementById("color-slider");noUiSlider.create(i,{start:[4e3,8e3,12e3,16e3],connect:[!1,!0,!0,!0,!0],range:{min:[2e3],max:[2e4]}});var d=i.querySelectorAll(".noUi-connect"),v=["c-1-color","c-2-color","c-3-color","c-4-color","c-5-color"];for(var o=0;o<d.length;o++)d[o].classList.add(v[o]);var n=document.getElementById("slider-toggle");noUiSlider.create(n,{orientation:"vertical",start:0,range:{min:[0,1],max:1},format:wNumb({decimals:0})});n.noUiSlider.on("update",function(e,r){e[r]==="1"?n.classList.add("off"):n.classList.remove("off")});var a=document.getElementById("soft");noUiSlider.create(a,{start:50,range:{min:0,max:100},pips:{mode:"values",values:[20,80],density:4}});a.noUiSlider.on("change",function(e,r){e[r]<20?a.noUiSlider.set(20):e[r]>80&&a.noUiSlider.set(80)});var p=document.getElementById("overlapping-tooltip");noUiSlider.create(p,{start:[20,32,50,70,80,90],connect:!0,tooltips:[!1,!0,!0,!0,!0,!0],range:{min:0,max:100}});var m=document.getElementById("result"),S=document.querySelectorAll(".sliders"),u=[0,0,0];S.forEach(function(e,r){noUiSlider.create(e,{start:127,connect:[!0,!1],orientation:"vertical",range:{min:0,max:255},format:wNumb({decimals:0})}),e.noUiSlider.on("update",function(){u[r]=e.noUiSlider.get();var c="rgb("+u.join(",")+")";m.style.background=c,m.style.color=c})});
