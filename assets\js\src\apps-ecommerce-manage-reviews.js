import{c as A,i as D}from"../../admin.bundle-DI9_jvUJ.js";import{A as R}from"../../air-datepicker-DlUcrly3.js";import{l as S}from"../../en-BXs2wRZT.js";import"../../main-Cyta4iCA.js";const M=[document.getElementById("rating-1"),document.getElementById("rating-2"),document.getElementById("rating-3"),document.getElementById("rating-4"),document.getElementById("rating-5")],y=[document.getElementById("emoji-1"),document.getElementById("emoji-2"),document.getElementById("emoji-3"),document.getElementById("emoji-4"),document.getElementById("emoji-5")],w=document.getElementById("rating-input");function g(e){e=Math.max(0,Math.min(4,e)),M.forEach((t,n)=>{n<=e?(t.classList.remove("bg-body-tertiary"),t.classList.add("bg-warning")):(t.classList.remove("bg-warning"),t.classList.add("bg-body-tertiary"))}),y.forEach(t=>{t.classList.add("d-none")}),y[e].classList.remove("d-none"),w.value=e+1}M.forEach((e,t)=>{e.addEventListener("click",()=>{g(t)})});y.forEach((e,t)=>{e.addEventListener("click",()=>{g(t)})});w.addEventListener("change",function(){let e=parseInt(this.value);isNaN(e)&&(e=1),e=Math.max(1,Math.min(5,e)),this.value=e,g(e-1)});w.addEventListener("input",function(){let e=parseInt(this.value)||0;e=Math.max(0,Math.min(5,e)),e>=1&&e<=5&&g(e-1)});g(0);const l=[{name:"John Doe",date:"24 May, 2024",location:"New York",image:"assets/images/avatar/user-1.png",rating:4.5,title:"Code Quality",message:"Was a really good move to add this as part of our project, we had great comments from the business regarding design and user experience. Install with no dependency issues or deprecated packages and was really good overall."},{name:"Jane Smith",date:"22 May, 2024",location:"Los Angeles",image:"assets/images/avatar/user-2.png",rating:5,title:"Documentation Quality",message:"Excellent product! Exceeded all my expectations. Highly recommend to anyone looking for quality."},{name:"Michael Brown",date:"20 May, 2024",location:"Chicago",image:"assets/images/avatar/user-3.png",rating:3.5,title:"Design Quality",message:"The product is decent but has some room for improvement. The installation was smooth, but some features were a bit underwhelming."},{name:"Emily Davis",date:"18 May, 2024",location:"Houston",image:"assets/images/avatar/user-4.png",rating:2,title:"Flexibility",message:"Not very satisfied with the product. It did not meet my expectations and had several issues during installation."},{name:"Chris Wilson",date:"15 May, 2024",location:"Phoenix",image:"assets/images/avatar/user-5.png",rating:4,title:"Code Quality",message:"Good product overall, but there are a few bugs that need to be addressed. Customer support was helpful in resolving some issues."},{name:"Sarah Lee",date:"12 May, 2024",location:"San Francisco",image:"assets/images/avatar/user-6.png",rating:4.5,title:"Customizability",message:"Great product with excellent features. The user interface is very intuitive and easy to navigate."},{name:"David Johnson",date:"10 May, 2024",location:"Miami",image:"assets/images/avatar/user-7.png",rating:3.5,title:"Code Quality",message:"The product is average. It works as expected but lacks some advanced features that competitors offer."},{name:"Nancy Adams",date:"8 May, 2024",location:"Seattle",image:"assets/images/avatar/user-8.png",rating:4.5,title:"Design Quality",message:"Had some issues with the product initially, but customer support was able to help resolve them. Still, it's not as seamless as I hoped."},{name:"Paul White",date:"5 May, 2024",location:"Boston",image:"assets/images/avatar/user-9.png",rating:3.5,title:"Feature Availability",message:"The product did not meet my expectations at all. It was difficult to install and had many bugs."},{name:"Lisa Green",date:"3 May, 2024",location:"Denver",image:"assets/images/avatar/user-10.png",rating:5,title:"Design Quality",message:"Absolutely fantastic product! It has all the features I need and works flawlessly."},{name:"James Clark",date:"1 May, 2024",location:"Atlanta",image:"assets/images/avatar/user-11.png",rating:5,title:"Feature Availability",message:"Overall, I'm happy with the product. It performs well and has a good range of features."},{name:"Patricia Martinez",date:"28 April, 2024",location:"Dallas",image:"assets/images/avatar/user-12.png",rating:4.5,title:"Flexibility",message:"The product is good but could use some improvements. The user interface could be more user-friendly."},{name:"Charles Brown",date:"25 April, 2024",location:"Orlando",image:"assets/images/avatar/user-13.png",rating:1,title:"Code Quality",message:"I had high hopes for this product, but it didn't deliver as expected. There were several issues that made it difficult to use."},{name:"Mary Johnson",date:"22 April, 2024",location:"Philadelphia",image:"assets/images/avatar/user-14.png",rating:4,title:"Feature Availability",message:"Overall, a good product. It has most of the features I need and works well."},{name:"Richard Wilson",date:"20 April, 2024",location:"San Diego",image:"assets/images/avatar/user-15.png",rating:3.5,title:"Design Quality",message:"Decent product, but the design could be more modern. It's a bit outdated."},{name:"Karen Taylor",date:"18 April, 2024",location:"Las Vegas",image:"assets/images/avatar/user-16.png",rating:2.5,title:"Flexibility",message:"Not very flexible. It's difficult to customize according to our needs."},{name:"Daniel Thomas",date:"15 April, 2024",location:"Austin",image:"assets/images/avatar/user-17.png",rating:4,title:"Documentation Quality",message:"Good documentation. It helped us understand the product better."},{name:"Barbara Hernandez",date:"12 April, 2024",location:"San Antonio",image:"assets/images/avatar/user-18.png",rating:3,title:"Feature Availability",message:"Lacking some features we expected. It needs more customization options."},{name:"Matthew Martinez",date:"10 April, 2024",location:"Charlotte",image:"assets/images/avatar/user-19.png",rating:4.5,title:"Flexibility",message:"Very flexible product. We were able to customize it according to our needs."},{name:"Amanda Young",date:"8 April, 2024",location:"San Jose",image:"assets/images/avatar/user-20.png",rating:5,title:"Code Quality",message:"Excellent code quality. It's well-structured and easy to maintain."},{name:"Robert Lopez",date:"5 April, 2024",location:"Indianapolis",image:"assets/images/avatar/user-21.png",rating:3,title:"Documentation Quality",message:"Documentation could be improved. It lacks some details and examples."},{name:"Dorothy Gonzalez",date:"3 April, 2024",location:"Jacksonville",image:"assets/images/avatar/user-22.png",rating:4.5,title:"Design Quality",message:"Great design! It's visually appealing and easy to navigate."},{name:"Joseph Perez",date:"1 April, 2024",location:"San Francisco",image:"assets/images/avatar/user-23.png",rating:4,title:"Flexibility",message:"Flexible enough for our needs. We were able to customize it to fit our workflow."},{name:"Donna Flores",date:"29 March, 2024",location:"Columbus",image:"assets/images/avatar/user-24.png",rating:2,title:"Feature Availability",message:"Lacks some important features we were looking for. Disappointing."},{name:"Kenneth Scott",date:"27 March, 2024",location:"Fort Worth",image:"assets/images/avatar/user-25.png",rating:4.5,title:"Design Quality",message:"The design is top-notch! It's clean, modern, and intuitive."},{name:"Jennifer King",date:"24 March, 2024",location:"Memphis",image:"assets/images/avatar/user-26.png",rating:3,title:"Feature Availability",message:"Average feature set. It meets our basic needs but lacks advanced functionality."},{name:"Gerald Hernandez",date:"22 March, 2024",location:"Baltimore",image:"assets/images/avatar/user-27.png",rating:4,title:"Documentation Quality",message:"Good documentation. It helped us get started with the product quickly."},{name:"Megan Sanchez",date:"20 March, 2024",location:"Washington",image:"assets/images/avatar/user-28.png",rating:4.5,title:"Code Quality",message:"High-quality codebase. It's well-written and easy to understand."}],u=10;let d=1;const B=document.getElementById("searchReviewInput");let f=null,m=null;function k(e){let t="";for(let n=1;n<=5;n++)e>=n?t+='<i class="ri-star-fill text-warning"></i>':e>=n-.5?t+='<i class="ri-star-half-fill text-warning"></i>':t+='<i class="ri-star-line text-warning"></i>';return t}function p(){const e=x(),t=(d-1)*u,n=t+u,i=e.slice(t,n),s=document.getElementById("reviewTableBody");i.length===0?s.innerHTML=`
          <tr>
            <td colspan="3" class="text-center py-4">
              <div class="d-flex flex-column align-items-center">
                <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" class="mx-auto size-12" viewBox="0 0 48 48">
                  <linearGradient id="SVGID_1__h35ynqzIJzH4_gr1" x1="34.598" x2="15.982" y1="15.982" y2="34.598" gradientUnits="userSpaceOnUse">
                    <stop offset="0" stop-color="#60e8fe"></stop>
                    <stop offset=".033" stop-color="#6ae9fe"></stop>
                    <stop offset=".197" stop-color="#97f0fe"></stop>
                    <stop offset=".362" stop-color="#bdf5ff"></stop>
                    <stop offset=".525" stop-color="#dafaff"></stop>
                    <stop offset=".687" stop-color="#eefdff"></stop>
                    <stop offset=".846" stop-color="#fbfeff"></stop>
                    <stop offset="1" stop-color="#fff"></stop>
                  </linearGradient>
                  <path fill="url(#SVGID_1__h35ynqzIJzH4_gr1)" d="M40.036,33.826L31.68,25.6c0.847-1.739,1.335-3.684,1.335-5.748c0-7.27-5.894-13.164-13.164-13.164
                    S6.688,12.582,6.688,19.852c0,7.27,5.894,13.164,13.164,13.164c2.056,0,3.995-0.485,5.728-1.326l3.914,4.015l4.331,4.331
                    c1.715,1.715,4.496,1.715,6.211,0C41.751,38.321,41.751,35.541,40.036,33.826z"></path>
                  <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M31.95,25.739l8.086,8.086c1.715,1.715,1.715,4.496,0,6.211l0,0c-1.715,1.715-4.496,1.715-6.211,0
                    l-4.331-4.331"></path>
                  <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M7.525,24.511c-1.771-4.694-0.767-10.196,3.011-13.975c3.847-3.847,9.48-4.817,14.228-2.912"></path>
                  <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M30.856,12.603c3.376,5.114,2.814,12.063-1.688,16.565c-4.858,4.858-12.565,5.129-17.741,0.814"></path>
                </svg>
                <p class="mt-2 text-center text-gray-500 dark:text-dark-500">No matching records found</p>
                <p class="text-muted mb-0">We couldn't find any reviews matching your search.</p>
              </div>
            </td>
          </tr>
        `:s.innerHTML=i.map((a,o)=>`
          <tr class="gap-2">
            <td class="align-top whitespace-nowrap text-nowrap">
              <div class="d-flex align-items-center gap-3 flex-nowrap">
                <img src="${a.image}" alt="" class="rounded-2 flex-shrink-0 size-16" loading="lazy">
                <div class="overflow-hidden flex-grow-1">
                  <h6 class="mb-1"><a href="#!" class="link link-custom">${a.name}</a></h6>
                  <p class="mb-1 fs-sm text-truncate">${a.date}</p>
                  <p class="fs-sm text-muted">Location: <span>${a.location}</span></p>
                </div>
              </div>
            </td>
            <td class="text-wrap">
              <div class="w-350px">
                <div class="d-flex align-items-center gap-2 mb-3">
                  <div class="text-warning d-flex align-items-center">
                    ${k(a.rating)}
                  </div>
                  <h6 class="mb-0">(${a.rating})</h6>
                </div>
                <h6 class="mb-1 lh-base">${a.title}</h6>
                <p class="text-muted">${a.message}</p>
              </div>
            </td>
            <td class="align-top text-nowrap">
              <div class="d-flex align-items-center justify-content-end gap-3 flex-wrap">
                <button class="btn btn-light flex-shrink-0">Direct Message</button>
                <div class="dropdown">
                  <button class="btn btn-primary btn-icon" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="ri-more-2-fill"></i>
                  </button>
                  <ul class="dropdown-menu dropdown-menu-end">
                    <li><a href="#addReviewModal" class="dropdown-item edit-review-btn" data-bs-toggle="modal" data-index="${o}"><i class="align-middle me-2 ri-pencil-line"></i> Edit</a></li>
                    <li><a href="#deleteModal" class="dropdown-item delete-review-btn" data-bs-toggle="modal" data-username="${a.name}"><i class="align-middle me-2 ri-delete-bin-line"></i> Delete</a></li>
                  </ul>
                </div>
              </div>
            </td>
          </tr>
        `).join(""),document.getElementById("paginationSummary").innerHTML=`Showing <b>${e.length?t+1:0}</b>-<b>${Math.min(n,e.length)}</b> of <b>${e.length}</b> Results`}function h(){const e=x(),t=Math.ceil(e.length/u),n=document.getElementById("paginationControls");n.innerHTML="";const i=document.createElement("li");i.className=`page-item ${d===1?"disabled":""}`,i.innerHTML='<a class="page-link" href="#!"><i data-lucide="chevron-left" class="size-4"></i>Previous</a>',i.onclick=()=>v(d-1),n.appendChild(i);for(let a=1;a<=t;a++){const o=document.createElement("li");o.className=`page-item ${a===d?"active":""}`,o.innerHTML=`<a class="page-link" href="#!">${a}</a>`,o.onclick=()=>v(a),n.appendChild(o)}const s=document.createElement("li");s.className=`page-item ${d===t?"disabled":""}`,s.innerHTML='<a class="page-link" href="#!">Next <i data-lucide="chevron-right" class="size-4"></i></a>',s.onclick=()=>v(d+1),n.appendChild(s),A({icons:D})}function v(e){const t=Math.ceil(l.length/u);e<1||e>t||(d=e,p(),h())}function b(){const e=l.length,t=l.reduce((o,r)=>o+r.rating,0),n=e?(t/e).toFixed(1):"0";document.getElementById("totalReviewCount").textContent=e;const i=100,s=document.getElementById("reviewGrowthBadge");s.textContent=`${i}%`,s.classList.toggle("bg-success-subtle",i>=0),s.classList.toggle("bg-danger-subtle",i<0),s.classList.toggle("text-success",i>=0),s.classList.toggle("text-danger",i<0),document.getElementById("averageRating").textContent=n;const a=document.getElementById("averageStars");a.innerHTML=k(parseFloat(n))}function I(){const e=[0,0,0,0,0];l.forEach(s=>{const a=Math.round(s.rating);a>=1&&a<=5&&e[a-1]++});const t=e.reduce((s,a)=>s+a,0),n=document.getElementById("starBreakdown");n.innerHTML="";const i=["bg-danger","bg-info","bg-warning","bg-pink","bg-success"];for(let s=4;s>=0;s--){const a=e[s],o=t?(a/t*100).toFixed(0):0,r=i[s]||"bg-warning",c=document.createElement("div");c.className="d-flex align-items-center gap-2 mt-1",c.innerHTML=`
        <p class="flex-shrink-0"><i class="text-warning ri-star-fill"></i> ${s+1}</p>
        <div class="progress progress-1" role="progressbar" style="height: 8px; width: ${o}%;" aria-valuenow="${o}" aria-valuemin="0" aria-valuemax="100">
          <div class="progress-bar w-100 ${r}"></div>
        </div>
        <h6 class="mb-0">${a}</h6>
      `,n.appendChild(c)}}function x(){const e=B.value.trim().toLowerCase();return e?l.filter(t=>t.name.toLowerCase().includes(e)||t.date.toLowerCase().includes(e)||t.location.toLowerCase().includes(e)||t.title.toLowerCase().includes(e)||t.message.toLowerCase().includes(e)):l}B.addEventListener("input",()=>{d=1,p(),h()});new R("#createDateInput",{dateFormat:"dd-MM-yyyy",locale:S});document.getElementById("addReviewBtn").addEventListener("click",function(){const e=document.getElementById("userNameInput").value.trim(),t=document.getElementById("createDateInput").value.trim(),n=document.getElementById("locationInput").value.trim(),i=document.getElementById("titleInput").value.trim(),s=document.getElementById("writeReviewInput").value.trim(),a=parseFloat(document.getElementById("rating-input").value),o=L=>{const C=document.querySelector("#addReviewModal #alertContainer");C.innerHTML=`
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <span>${L}</span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            `};if(!e||!t||!n||!i||!s||isNaN(a)){o("Please fill all fields properly!");return}const r={name:e,date:t,location:n,title:i,message:s,rating:a};m!==null&&m>=0&&m<l.length?(r.image=l[m].image,l[m]=r):(r.image="assets/images/avatar/user-45.png",l.unshift(r),d=1),m=null,p(),h(),b(),I();const c=document.getElementById("addReviewModal"),E=window.bootstrap.Modal.getInstance(c);E&&E.hide(),document.querySelector("form").reset(),g(0),document.querySelector("#addReviewModal h6").textContent="Add Review",document.getElementById("addReviewBtn").textContent="Add Review"});document.addEventListener("click",function(e){const t=e.target.closest(".edit-review-btn");if(!t)return;const n=parseInt(t.getAttribute("data-index")),i=x(),s=(d-1)*u,a=i[s+n],o=l.findIndex(c=>c.name===a.name&&c.date===a.date&&c.message===a.message);if(o===-1)return;m=o,document.getElementById("userNameInput").value=a.name,document.getElementById("createDateInput").value=a.date,document.getElementById("locationInput").value=a.location,document.getElementById("titleInput").value=a.title,document.getElementById("writeReviewInput").value=a.message;const r=a.rating;document.getElementById("rating-input").value=r,g(Math.ceil(r)-1),document.querySelector("#addReviewModal h6").textContent="Edit Review",document.getElementById("addReviewBtn").textContent="Save Changes"});document.getElementById("addReviewModal").addEventListener("hidden.bs.modal",function(){m=null,document.querySelector("form").reset(),g(0),document.querySelector("#addReviewModal h6").textContent="Add Review",document.getElementById("addReviewBtn").textContent="Add Review"});document.addEventListener("click",function(e){e.target.closest(".delete-review-btn")&&(f=e.target.closest(".delete-review-btn").getAttribute("data-username"))});document.getElementById("confirmDeleteBtn").addEventListener("click",function(){if(!f)return;for(let n=0;n<l.length;n++)if(l[n].name===f){l.splice(n,1);break}f=null,d=1,p(),h(),b(),I();const e=document.getElementById("deleteModal");window.bootstrap.Modal.getInstance(e).hide()});p();h();b();I();
