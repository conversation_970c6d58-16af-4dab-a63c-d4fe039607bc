import"../../admin.bundle-DI9_jvUJ.js";import"../../main-Cyta4iCA.js";document.addEventListener("DOMContentLoaded",function(){const e=document.getElementById("audio"),u=document.getElementById("play-pause"),s=document.getElementById("skip-backward"),c=document.getElementById("skip-forward"),d=document.getElementById("mute"),i=document.getElementById("volume-range"),m=document.getElementById("progress-bar").children[0],p=document.getElementById("current-time"),v=document.getElementById("duration"),t=document.getElementById("volume-icon");let o=!1,n=!1;e.addEventListener("loadedmetadata",function(){v.textContent=r(e.duration)}),e.addEventListener("timeupdate",function(){const l=e.currentTime;p.textContent=r(l),m.style.width=l/e.duration*100+"%"}),u.addEventListener("click",function(){o?(e.pause(),u.querySelector("i").classList.replace("ri-pause-line","ri-play-line")):(e.play(),u.querySelector("i").classList.replace("ri-play-line","ri-pause-line")),o=!o}),s.addEventListener("click",function(){e.currentTime=Math.max(0,e.currentTime-10)}),c.addEventListener("click",function(){e.currentTime=Math.min(e.duration,e.currentTime+10)}),d.addEventListener("click",function(){n=!n,e.muted=n,n?(t.classList.replace("ri-volume-up-line","ri-volume-mute-line"),t.classList.add("text-danger"),i.value=0):(t.classList.replace("ri-volume-mute-line","ri-volume-up-line"),t.classList.remove("text-danger"),i.value=50)}),i.addEventListener("input",function(){e.volume=i.value/100,i.value==0?(n=!0,t.classList.replace("ri-volume-up-line","ri-volume-mute-line")):(n=!1,t.classList.replace("ri-volume-mute-line","ri-volume-up-line"))});function r(l){const y=Math.floor(l/60),a=Math.floor(l%60);return`${y}:${a<10?"0":""}${a}`}});
