import{c as f,i as v}from"../../admin.bundle-DI9_jvUJ.js";/* empty css                               */import"../../main-Cyta4iCA.js";VirtualSelect.init({ele:"#subscriberSelect",options:[{label:"Yes",value:"1"},{label:"No",value:"2"}]});VirtualSelect.init({ele:"#statusSelect",options:[{label:"Active",value:"1"},{label:"In Active",value:"2"}]});class y{constructor(t={}){this.tableEl=document.getElementById(t.tableId||"usersTable"),this.tableBody=this.tableEl.querySelector("tbody"),this.tableHead=this.tableEl.querySelector("thead"),this.searchInput=document.getElementById(t.searchInputId||"searchCustomerInput"),this.checkAllBox=document.getElementById(t.checkAllId||"checkDataAll"),this.paginationContainer=document.querySelector(".pagination"),this.resultsInfo=document.querySelector("#showingResults"),this.addModal=document.getElementById("addCustomerModals"),this.overviewModal=document.getElementById("overviewCustomerModals"),this.deleteModal=document.getElementById("deleteModal"),this.customers=[],this.filteredCustomers=[],this.currentPage=1,this.itemsPerPage=t.itemsPerPage||10,this.selectedCustomerIds=new Set,this.uploadedImageData=null,this.isEditing=!1,this.currentEditId=null,this.currentSortColumn="id",this.currentSortDirection="asc",this.init()}init(){this.loadCustomers(),this.setupEventListeners(),this.renderTable(),this.renderPagination(),this.setupSortableColumns()}setupEventListeners(){this.searchInput.addEventListener("input",this.handleSearch.bind(this)),this.checkAllBox.addEventListener("change",this.handleSelectAll.bind(this)),document.querySelector(".btn-danger.btn-icon").addEventListener("click",this.handleBulkDelete.bind(this)),this.addModal&&(this.addModal.querySelector("form").querySelector(".btn-primary").addEventListener("click",r=>{r.preventDefault(),this.isEditing?this.handleUpdateCustomer():this.handleAddCustomer()}),this.addModal.querySelector("#imageInput").addEventListener("change",this.handleImageUpload.bind(this)),this.addModal.addEventListener("hidden.bs.modal",()=>{const r=this.addModal.querySelector("label.avatar");if(r){r.style.backgroundImage="",r.style.backgroundSize="",r.style.backgroundPosition="";const s=r.querySelector("svg");s&&(s.style.display="")}this.isEditing=!1,this.currentEditId=null})),this.deleteModal&&this.deleteModal.querySelector(".btn-danger").addEventListener("click",this.confirmDelete.bind(this)),this.paginationContainer.addEventListener("click",this.handlePaginationClick.bind(this))}resetAddEditModal(){if(this.addModal){const t=this.addModal.querySelector("form");t&&t.reset();const e=this.addModal.querySelector(".btn-primary");e.textContent="Add Customer";const a=this.addModal.querySelector(".modal-title");if(a&&(a.textContent="Add Customer"),window.VirtualSelect){const s=document.querySelector("#subscriberSelect"),i=document.querySelector("#statusSelect");s&&s.reset&&s.reset(),i&&i.reset&&i.reset()}this.resetImageUpload(),this.isEditing=!1,this.currentEditId=null;const r=this.addModal.querySelector("#alertContainer");r&&(r.innerHTML="")}}resetImageUpload(){if(!this.addModal)return;const t=this.addModal.querySelector("label.avatar");if(t){t.style.backgroundImage="",t.style.backgroundSize="",t.style.backgroundPosition="";const a=t.querySelector("svg");a&&(a.style.display="")}this.uploadedImageData=null;const e=this.addModal.querySelector("#imageInput");e&&(e.value="")}setupSortableColumns(){this.tableHead.querySelectorAll("th:not(:first-child):not(:last-child)").forEach(e=>{const a=this.getColumnNameFromHeader(e.textContent.trim());if(a){e.classList.add("sortable"),e.style.cursor="pointer";const r=e.textContent;e.innerHTML=`
                    <div class="d-flex align-items-center justify-content-between">
                        <span>${r}</span>
                        <span class="sort-icon ms-2">
                            <i class="ri-arrow-up-down-line text-muted"></i>
                        </span>
                    </div>
                `,e.addEventListener("click",()=>this.handleSort(a))}})}getColumnNameFromHeader(t){return{ID:"id",Name:"name",Email:"email","Phone Number":"phone",Subscriber:"subscriber",Gender:"gender",Location:"location",Status:"status"}[t]}handleSort(t){this.currentSortColumn===t?this.currentSortDirection=this.currentSortDirection==="asc"?"desc":"asc":(this.currentSortColumn=t,this.currentSortDirection="asc"),this.sortData(t,this.currentSortDirection),this.updateSortIcons(),this.currentPage=1,this.renderTable(),this.renderPagination()}sortData(t,e){this.filteredCustomers.sort((a,r)=>{let s=a[t],i=r[t];return t==="name"&&(s=s||"",i=i||""),typeof s=="string"&&typeof i=="string"&&(s=s.toLowerCase(),i=i.toLowerCase()),t==="phone"&&(s=s.replace(/\D/g,""),i=i.replace(/\D/g,"")),s<i?e==="asc"?-1:1:s>i?e==="asc"?1:-1:0})}updateSortIcons(){this.tableHead.querySelectorAll("th.sortable").forEach(e=>{const a=this.getColumnNameFromHeader(e.textContent.trim()),r=e.querySelector(".sort-icon");a===this.currentSortColumn?r.innerHTML=this.currentSortDirection==="asc"?'<i class="ri-arrow-up-line text-primary"></i>':'<i class="ri-arrow-down-line text-primary"></i>':r.innerHTML='<i class="ri-arrow-up-down-line text-muted"></i>'})}handleImageUpload(t){const e=t.target.files[0];if(!e)return;const a=s=>{const i=this.addModal.querySelector("#alertContainer");i&&(i.innerHTML=`
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <span>${s}</span>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                `)};if(!e.type.match("image.*")){a("Please select an image file");return}if(e.size>2*1024*1024){a("Image size should be less than 2MB");return}const r=new FileReader;r.onload=s=>{this.uploadedImageData=s.target.result;const i=this.addModal.querySelector("label.avatar");if(i){i.style.backgroundImage=`url(${this.uploadedImageData})`,i.style.backgroundSize="cover",i.style.backgroundPosition="center";const o=i.querySelector("svg");o&&(o.style.display="none")}const l=this.addModal.querySelector("#alertContainer");l&&(l.innerHTML="")},r.readAsDataURL(e)}loadCustomers(){this.customers=[{id:"PEC-24151",name:"John Doe",email:"<EMAIL>",phone:"+1234567890",subscriber:"Yes",gender:"Male",location:"New York",status:"Active",avatar:"assets/images/avatar/user-1.png"},{id:"PEC-24152",name:"Jane Smith",email:"<EMAIL>",phone:"+1987654321",subscriber:"Yes",gender:"Female",location:"Los Angeles",status:"Inactive",avatar:"assets/images/avatar/user-2.png"},{id:"PEC-24161",name:"Liam Parker",email:"<EMAIL>",phone:"+1234509876",subscriber:"No",gender:"Male",location:"Seattle",status:"Inactive",avatar:"assets/images/avatar/user-1.png"},{id:"PEC-24162",name:"Ava Morgan",email:"<EMAIL>",phone:"+1987632450",subscriber:"Yes",gender:"Female",location:"Denver",status:"Active",avatar:"assets/images/avatar/user-2.png"},{id:"PEC-24163",name:"Noah Reed",email:"<EMAIL>",phone:"+1654789032",subscriber:"No",gender:"Male",location:"Atlanta",status:"Inactive",avatar:"assets/images/avatar/user-3.png"},{id:"PEC-24164",name:"Isabella Rivera",email:"<EMAIL>",phone:"+1789567340",subscriber:"Yes",gender:"Female",location:"Miami",status:"Active",avatar:"assets/images/avatar/user-4.png"},{id:"PEC-24165",name:"Mason Hayes",email:"<EMAIL>",phone:"+1324098765",subscriber:"Yes",gender:"Male",location:"Portland",status:"Inactive",avatar:"assets/images/avatar/user-5.png"},{id:"PEC-24166",name:"Sophia Simmons",email:"<EMAIL>",phone:"+1897432650",subscriber:"No",gender:"Female",location:"Orlando",status:"Active",avatar:"assets/images/avatar/user-6.png"},{id:"PEC-24167",name:"Ethan James",email:"<EMAIL>",phone:"+1654327809",subscriber:"Yes",gender:"Male",location:"Las Vegas",status:"Active",avatar:"assets/images/avatar/user-7.png"},{id:"PEC-24168",name:"Amelia Brooks",email:"<EMAIL>",phone:"+1789432765",subscriber:"No",gender:"Female",location:"Charlotte",status:"Inactive",avatar:"assets/images/avatar/user-8.png"},{id:"PEC-24169",name:"Logan Bennett",email:"<EMAIL>",phone:"+1324765890",subscriber:"Yes",gender:"Male",location:"Indianapolis",status:"Active",avatar:"assets/images/avatar/user-9.png"},{id:"PEC-24170",name:"Mia Watson",email:"<EMAIL>",phone:"+1897654912",subscriber:"Yes",gender:"Female",location:"Nashville",status:"Inactive",avatar:"assets/images/avatar/user-10.png"},{id:"PEC-24171",name:"Jacob Flores",email:"<EMAIL>",phone:"+1456789234",subscriber:"No",gender:"Male",location:"Austin",status:"Active",avatar:"assets/images/avatar/user-1.png"},{id:"PEC-24172",name:"Ella Cooper",email:"<EMAIL>",phone:"+1908763452",subscriber:"Yes",gender:"Female",location:"Tucson",status:"Inactive",avatar:"assets/images/avatar/user-2.png"},{id:"PEC-24173",name:"William Ward",email:"<EMAIL>",phone:"+1346789052",subscriber:"Yes",gender:"Male",location:"Cleveland",status:"Inactive",avatar:"assets/images/avatar/user-3.png"},{id:"PEC-24174",name:"Chloe Bailey",email:"<EMAIL>",phone:"+1765432987",subscriber:"No",gender:"Female",location:"Minneapolis",status:"Active",avatar:"assets/images/avatar/user-4.png"},{id:"PEC-24175",name:"Alexander Hughes",email:"<EMAIL>",phone:"+1223456789",subscriber:"Yes",gender:"Male",location:"Kansas City",status:"Active",avatar:"assets/images/avatar/user-5.png"},{id:"PEC-24176",name:"Grace Foster",email:"<EMAIL>",phone:"+1876543210",subscriber:"Yes",gender:"Female",location:"Columbus",status:"Inactive",avatar:"assets/images/avatar/user-6.png"},{id:"PEC-24177",name:"James Howard",email:"<EMAIL>",phone:"+1678905432",subscriber:"No",gender:"Male",location:"El Paso",status:"Active",avatar:"assets/images/avatar/user-7.png"},{id:"PEC-24178",name:"Lily Ramirez",email:"<EMAIL>",phone:"+1789456129",subscriber:"Yes",gender:"Female",location:"Fort Worth",status:"Inactive",avatar:"assets/images/avatar/user-8.png"},{id:"PEC-24179",name:"Daniel Bryant",email:"<EMAIL>",phone:"+1324098754",subscriber:"No",gender:"Male",location:"Detroit",status:"Active",avatar:"assets/images/avatar/user-9.png"},{id:"PEC-24180",name:"Aria Griffin",email:"<EMAIL>",phone:"+1897632456",subscriber:"Yes",gender:"Female",location:"Memphis",status:"Active",avatar:"assets/images/avatar/user-10.png"}],this.filteredCustomers=[...this.customers],this.sortData("id","asc")}renderTable(){const t=(this.currentPage-1)*this.itemsPerPage,e=t+this.itemsPerPage,a=this.filteredCustomers.slice(t,e);if(this.tableBody.innerHTML="",a.length===0){const r=document.createElement("tr");r.innerHTML=`
              <td colspan="10" class="text-center py-4">
                <div class="d-flex flex-column align-items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" class="mx-auto size-12" viewBox="0 0 48 48">
                    <linearGradient id="SVGID_1__h35ynqzIJzH4_gr1" x1="34.598" x2="15.982" y1="15.982" y2="34.598" gradientUnits="userSpaceOnUse">
                      <stop offset="0" stop-color="#60e8fe"></stop>
                      <stop offset=".033" stop-color="#6ae9fe"></stop>
                      <stop offset=".197" stop-color="#97f0fe"></stop>
                      <stop offset=".362" stop-color="#bdf5ff"></stop>
                      <stop offset=".525" stop-color="#dafaff"></stop>
                      <stop offset=".687" stop-color="#eefdff"></stop>
                      <stop offset=".846" stop-color="#fbfeff"></stop>
                      <stop offset="1" stop-color="#fff"></stop>
                    </linearGradient>
                    <path fill="url(#SVGID_1__h35ynqzIJzH4_gr1)" d="M40.036,33.826L31.68,25.6c0.847-1.739,1.335-3.684,1.335-5.748c0-7.27-5.894-13.164-13.164-13.164 S6.688,12.582,6.688,19.852c0,7.27,5.894,13.164,13.164,13.164c2.056,0,3.995-0.485,5.728-1.326l3.914,4.015l4.331,4.331 c1.715,1.715,4.496,1.715,6.211,0C41.751,38.321,41.751,35.541,40.036,33.826z"></path>
                    <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M31.95,25.739l8.086,8.086c1.715,1.715,1.715,4.496,0,6.211l0,0c-1.715,1.715-4.496,1.715-6.211,0 l-4.331-4.331"></path>
                    <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M7.525,24.511c-1.771-4.694-0.767-10.196,3.011-13.975c3.847-3.847,9.48-4.817,14.228-2.912"></path>
                    <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M30.856,12.603c3.376,5.114,2.814,12.063-1.688,16.565c-4.858,4.858-12.565,5.129-17.741,0.814"></path>
                  </svg>
                  <p class="mt-2 text-center text-gray-500 dark:text-dark-500">No matching records found</p>
                  <p class="text-muted mb-0">We couldn't find any categories matching your search.</p>
                </div>
              </td>
            `,this.tableBody.appendChild(r),this.updateResultsInfo(),this.updateSelectAllState();return}a.forEach((r,s)=>{const i=document.createElement("tr");i.dataset.id=r.id;const l=r.status==="Active"?"bg-success-subtle text-success border border-success-subtle":"bg-danger-subtle text-danger border border-danger-subtle",o=this.selectedCustomerIds.has(r.id)?"checked":"";i.innerHTML=`
              <td>
                <div class="form-check check-primary">
                  <input class="form-check-input customer-check" type="checkbox" aria-label="Check Data Checkbox" id="checkData${t+s+1}" data-customer-id="${r.id}" ${o}>
                  <label class="form-check-label d-none" for="checkData${t+s+1}">Data ${t+s+1}</label>
                </div>
              </td>
              <td>${r.id}</td>
              <td>
                <div class="d-flex gap-2 align-items-center">
                  <img src="${r.avatar}" loading="lazy" alt="" class="size-8 rounded-circle">
                  <a href="#!" class="link link-custom-primary">${r.name}</a>
                </div>
              </td>
              <td>${r.email}</td>
              <td>${r.phone}</td>
              <td>${r.subscriber}</td>
              <td>${r.gender}</td>
              <td>${r.location}</td>
              <td><span class="badge ${l}">${r.status}</span></td>
              <td>
                <a href="#!" class="link link-custom-primary action-dropdown" type="button" id="actionDropdown${t+s+1}" data-bs-toggle="dropdown" aria-expanded="false" aria-label="dropdown-button">
                  <i class="ri-more-2-fill"></i>
                </a>
                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="actionDropdown${t+s+1}">
                  <li>
                    <a href="#overviewCustomerModals" data-bs-toggle="modal" class="dropdown-item d-flex gap-3 align-items-center overview-btn">
                      <i class="ri-eye-line"></i>
                      <span>Overview</span>
                    </a>
                  </li>
                  <li>
                    <a href="#!" class="dropdown-item d-flex gap-3 align-items-center edit-btn">
                      <i class="ri-pencil-line"></i>
                      Edit
                    </a>
                  </li>
                  <li>
                    <a href="#deleteModal" class="dropdown-item d-flex gap-3 align-items-center delete-btn" data-bs-toggle="modal">
                      <i class="ri-delete-bin-line"></i>
                      <span>Delete</span>
                    </a>
                  </li>
                </ul>
              </td>
            `,this.tableBody.appendChild(i)}),this.attachRowEventListeners(),this.updateResultsInfo(),this.updateSelectAllState()}attachRowEventListeners(){this.tableBody.querySelectorAll(".overview-btn").forEach(s=>{s.addEventListener("click",i=>{const o=i.target.closest("tr").dataset.id;this.showCustomerOverview(o)})}),this.tableBody.querySelectorAll(".edit-btn").forEach(s=>{s.addEventListener("click",i=>{i.preventDefault();const o=i.target.closest("tr").dataset.id;this.editCustomer(o)})}),this.tableBody.querySelectorAll(".delete-btn").forEach(s=>{s.addEventListener("click",i=>{const o=i.target.closest("tr").dataset.id;this.currentDeleteId=o})}),this.tableBody.querySelectorAll(".customer-check").forEach(s=>{s.addEventListener("change",i=>{const l=i.target.dataset.customerId,o=document.querySelector("#deleteCustomer");i.target.checked?(this.selectedCustomerIds.add(l),o.classList.remove("d-none")):(this.selectedCustomerIds.delete(l),this.selectedCustomerIds.size===0&&o.classList.add("d-none")),this.updateDeleteButtonVisibility(),this.updateSelectAllState()})})}renderPagination(){const t=Math.ceil(this.filteredCustomers.length/this.itemsPerPage);let e="";e+=`
        <li class="page-item ${this.currentPage===1?"disabled":""}">
          <a class="page-link" href="#!" data-page="prev">
            <i data-lucide="chevron-left" class="size-4"></i> Previous
          </a>
        </li>
      `;for(let a=1;a<=t;a++)a===1||a===t||a>=this.currentPage-1&&a<=this.currentPage+1?e+=`
            <li class="page-item ${a===this.currentPage?"active":""}">
              <a class="page-link" href="#!" data-page="${a}">${a}</a>
            </li>
          `:(a===this.currentPage-2||a===this.currentPage+2)&&(e+='<li class="page-item disabled"><a class="page-link" href="#!">...</a></li>');e+=`
        <li class="page-item ${this.currentPage===t?"disabled":""}">
          <a class="page-link" href="#!" data-page="next">
            Next <i data-lucide="chevron-right" class="size-4"></i>
          </a>
        </li>
      `,this.paginationContainer.innerHTML=e,f({icons:v})}updateResultsInfo(){const t=(this.currentPage-1)*this.itemsPerPage+1,e=Math.min(t+this.itemsPerPage-1,this.filteredCustomers.length),a=this.filteredCustomers.length;this.resultsInfo.innerHTML=`Showing <b class="me-1">${t}-${e}</b>of<b class="ms-1">${a}</b> Results`}handlePaginationClick(t){if(t.preventDefault(),!t.target.matches("a.page-link")||t.target.parentElement.classList.contains("disabled"))return;const e=t.target.dataset.page,a=Math.ceil(this.filteredCustomers.length/this.itemsPerPage);e==="prev"?this.currentPage=Math.max(1,this.currentPage-1):e==="next"?this.currentPage=Math.min(a,this.currentPage+1):this.currentPage=parseInt(e,10),this.renderTable(),this.renderPagination()}handleSearch(t){const e=t.target.value.toLowerCase();this.filteredCustomers=this.customers.filter(a=>a.name.toLowerCase().includes(e)||a.email.toLowerCase().includes(e)||a.gender.toLowerCase().includes(e)||a.location.toLowerCase().includes(e)||a.status.toLowerCase().includes(e)||a.id.toLowerCase().includes(e)),this.currentPage=1,this.renderTable(),this.renderPagination()}updateDeleteButtonVisibility(){const t=document.querySelector("#deleteCustomer");this.selectedCustomerIds.size>0?t.classList.remove("d-none"):t.classList.add("d-none")}handleSelectAll(t){const e=t.target.checked,a=document.querySelector("#deleteCustomer");e?(this.filteredCustomers.forEach(s=>{this.selectedCustomerIds.add(s.id)}),a.classList.remove("d-none")):(this.selectedCustomerIds.clear(),a.classList.add("d-none")),this.tableBody.querySelectorAll(".customer-check").forEach(s=>{s.checked=e}),this.updateDeleteButtonVisibility()}updateSelectAllState(){const t=this.filteredCustomers.length,e=this.filteredCustomers.filter(a=>this.selectedCustomerIds.has(a.id)).length;t===0?this.checkAllBox.checked=!1:e===0?this.checkAllBox.checked=!1:e===t?this.checkAllBox.checked=!0:this.checkAllBox.checked=!1}handleBulkDelete(){const t=document.querySelector("#deleteCustomer");if(confirm(`Are you sure you want to delete ${this.selectedCustomerIds.size} customer(s)?`)){const e=Array.from(this.selectedCustomerIds);this.customers=this.customers.filter(a=>!e.includes(a.id)),this.filteredCustomers=this.filteredCustomers.filter(a=>!e.includes(a.id)),this.selectedCustomerIds.clear(),t.classList.add("d-none"),this.renderTable(),this.renderPagination()}}confirmDelete(){this.currentDeleteId&&(this.customers=this.customers.filter(e=>e.id!==this.currentDeleteId),this.filteredCustomers=this.filteredCustomers.filter(e=>e.id!==this.currentDeleteId),this.selectedCustomerIds.delete(this.currentDeleteId),window.bootstrap.Modal.getInstance(this.deleteModal).hide(),this.renderTable(),this.renderPagination(),this.currentDeleteId=null)}showCustomerOverview(t){const e=this.customers.find(l=>l.id===t);if(!e)return;const a=this.overviewModal;a.querySelector("img.custom-image").src=e.avatar;const r=a.querySelectorAll("table tbody tr");r[0].querySelector("td").textContent=e.name,r[1].querySelector("td").textContent=e.email,r[2].querySelector("td").textContent=e.phone,r[3].querySelector("td").textContent=e.subscriber,r[4].querySelector("td").textContent=e.location;const s=a.querySelector(".btn-primary"),i=s.cloneNode(!0);s.parentNode.replaceChild(i,s),i.addEventListener("click",()=>{window.bootstrap.Modal.getInstance(a).hide(),this.editCustomer(t)})}resetImageUpload(){const t=this.addModal.querySelector("label.avatar");t.style.backgroundImage="",t.style.backgroundSize="",t.style.backgroundPosition="";const e=t.querySelector("i");e&&(e.style.display=""),this.uploadedImageData=null;const a=this.addModal.querySelector("#imageInput");a.value=""}editCustomer(t){const e=this.customers.find(o=>o.id===t);if(!e)return;this.resetAddEditModal(),this.isEditing=!0,this.currentEditId=t;const a=this.addModal.querySelector(".modal-title");a&&(a.textContent="Edit Customer");const r=this.addModal.querySelector(".btn-primary");r.textContent="Update Customer";const s=this.addModal.querySelector("form");s.querySelector("#orderIDInput").value=e.name.split(" ")[0]||"",s.querySelector("#lastNameInput").value=e.name.split(" ")[1]||"",s.querySelector("#emailInput").value=e.email||"",s.querySelector("#phoneNumberInput").value=e.phone?e.phone.replace(/\D/g,""):"",e.gender==="Male"?s.querySelector("#maleGender").checked=!0:s.querySelector("#femaleGender").checked=!0;const i=s.querySelector('input[placeholder="Location"]');if(i&&(i.value=e.location||""),window.VirtualSelect){const o=document.querySelector("#subscriberSelect"),n=document.querySelector("#statusSelect");o&&o.setValue&&o.setValue(e.subscriber==="Yes"?"1":"2"),n&&n.setValue&&n.setValue(e.status==="Active"?"1":"2")}if(e.avatar){const o=this.addModal.querySelector("label.avatar");if(o){o.style.backgroundImage=`url(${e.avatar})`,o.style.backgroundSize="cover",o.style.backgroundPosition="center";const n=o.querySelector("svg");n&&(n.style.display="none")}}new window.bootstrap.Modal(this.addModal).show()}handleAddCustomer(){if(this.isEditing)return;const t=this.addModal.querySelector("form"),e=t.querySelector("#orderIDInput").value.trim(),a=t.querySelector("#lastNameInput").value.trim(),r=t.querySelector("#emailInput").value.trim(),s=t.querySelector("#phoneNumberInput").value.trim(),i=t.querySelector("#maleGender").checked?"Male":"Female",l=t.querySelector('input[placeholder="Location"]').value.trim();let o="1",n="1";if(window.VirtualSelect){const d=document.querySelector("#subscriberSelect"),u=document.querySelector("#statusSelect");d&&(o=d.value||"1"),u&&(n=u.value||"1")}const m=d=>{const u=this.addModal.querySelector("#alertContainer");u&&(u.innerHTML=`
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <span>${d}</span>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                `)};if(!e||!a||!r||!s||!l){m("Please fill all required fields");return}const h=this.uploadedImageData||"assets/images/avatar/user-1.png",p=o==="1"?"Yes":"No",g=n==="1"?"Active":"Inactive",b={id:`PEC-${Math.floor(Math.random()*1e4)+24e3}`,name:`${e} ${a}`,email:r,phone:`+${s}`,subscriber:p,gender:i,location:l,status:g,avatar:h};this.customers.unshift(b),this.filteredCustomers.unshift(b),window.bootstrap.Modal.getInstance(this.addModal).hide(),this.renderTable(),this.renderPagination()}handleUpdateCustomer(){if(!this.isEditing||!this.currentEditId)return;const t=this.customers.find(c=>c.id===this.currentEditId);if(!t)return;const e=this.addModal.querySelector("form"),a=e.querySelector("#orderIDInput").value.trim(),r=e.querySelector("#lastNameInput").value.trim(),s=e.querySelector("#emailInput").value.trim(),i=e.querySelector("#phoneNumberInput").value.trim(),l=e.querySelector("#maleGender").checked?"Male":"Female",o=e.querySelector('input[placeholder="Location"]').value.trim();let n="",m="";if(window.VirtualSelect){const c=document.querySelector("#subscriberSelect"),d=document.querySelector("#statusSelect");c&&(n=c.value||"1"),d&&(m=d.value||"1")}const h=c=>{const d=this.addModal.querySelector("#alertContainer");d&&(d.innerHTML=`
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <span>${c}</span>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                `)};if(!a||!r||!s||!i||!o){h("Please fill all required fields");return}const p=n==="1"?"Yes":"No",g=m==="1"?"Active":"Inactive";t.name=`${a} ${r}`,t.email=s,t.phone=`+${i}`,t.gender=l,t.location=o,t.subscriber=p,t.status=g,this.uploadedImageData&&(t.avatar=this.uploadedImageData),window.bootstrap.Modal.getInstance(this.addModal).hide(),this.renderTable()}}document.addEventListener("DOMContentLoaded",()=>{new y({tableId:"usersTable",searchInputId:"searchCustomerInput",checkAllId:"checkDataAll",itemsPerPage:10})});
