import{c as E,i as b}from"../../admin.bundle-DI9_jvUJ.js";/* empty css                               */import{A as S}from"../../air-datepicker-DlUcrly3.js";import{l as M}from"../../en-BXs2wRZT.js";import"../../main-Cyta4iCA.js";VirtualSelect.init({ele:"#StatusSelect",options:[{label:"Published",value:"Published"},{label:"Coming Soon",value:"Coming Soon"},{label:"Expired",value:"Expired"}]});VirtualSelect.init({ele:"#eventTypeSelect",options:[{label:"Offline",value:"Offline"},{label:"Online",value:"Online"}]});class w{constructor(e={}){this.containerId=e.containerId||"event-grid-container",this.container=document.getElementById(this.containerId),this.itemsPerPage=e.itemsPerPage||6,this.currentPage=1,this.sortBy=e.sortBy||null,this.sortDirection=e.sortDirection||"asc",this.filterValue="",this.events=[],this.filteredEvents=[],this.selectedEvent=null,this.init=this.init.bind(this),this.renderEvents=this.renderEvents.bind(this),this.renderPagination=this.renderPagination.bind(this),this.handlePageChange=this.handlePageChange.bind(this),this.handleSort=this.handleSort.bind(this),this.handleFilter=this.handleFilter.bind(this),this.openAddModal=this.openAddModal.bind(this),this.openEditModal=this.openEditModal.bind(this),this.openViewModal=this.openViewModal.bind(this),this.deleteEvent=this.deleteEvent.bind(this),this.saveEvent=this.saveEvent.bind(this),this.updateEventCounter=this.updateEventCounter.bind(this),this.setupListeners=this.setupListeners.bind(this)}init(e=[]){return this.events=e,this.filteredEvents=[...this.events],this.setupListeners(),this.renderEvents(),this.renderPagination(),this.updateEventCounter(),this}setupListeners(){const e=document.getElementById("filterDropdown");e&&e.querySelectorAll(".dropdown-item").forEach(i=>{i.addEventListener("click",a=>{a.preventDefault();const r=a.target.textContent.trim();this.handleSort(r)})}),document.querySelectorAll(".dropdown-item[data-sort]").forEach(i=>{i.addEventListener("click",a=>{a.preventDefault();const r=a.target.getAttribute("data-sort")||a.target.textContent.trim();this.handleSort(r)})});const t=document.querySelector('button[data-bs-target="#addEventModal"]');t&&t.addEventListener("click",this.openAddModal);const s=document.querySelector("#addEventModal form");s&&s.querySelector("button.btn-primary").addEventListener("click",()=>{this.saveEvent(s)});const n=document.querySelector("#bookEventModal form");if(n){const i=n.querySelector("#totalTicketInput"),a=n.querySelector("#pricePerTicketInput"),r=n.querySelector("#totalAmountInput");[i,a].forEach(o=>{o.addEventListener("input",()=>{const d=parseInt(i.value)||0,l=parseInt(a.value)||0;r.value=d*l})})}}renderEvents(){const e=document.querySelector("#event-grid");if(!e)return;e.innerHTML="";const t=(this.currentPage-1)*this.itemsPerPage,s=t+this.itemsPerPage,n=this.filteredEvents.slice(t,s);if(n.length===0){e.innerHTML=`
          <div class="col-12 text-center py-5">
            <p>No events found.</p>
          </div>
        `;return}n.forEach(i=>{const a=document.createElement("div");a.className="col-md-6 col-xl-4",a.innerHTML=this.getEventCardHTML(i),e.appendChild(a),a.querySelectorAll(".dropdown-item").forEach(d=>{d.addEventListener("click",l=>{l.preventDefault();const c=l.target.textContent.trim();c==="Overview"?this.openViewModal(i):c==="Edit"?this.openEditModal(i):c==="Delete"&&this.deleteEvent(i.id)})});const o=a.querySelector('button[data-bs-target="#bookEventModal"]');o&&o.addEventListener("click",()=>this.setupBookModal(i))})}getEventCardHTML(e){return`
        <div class="card card-h-100">
          <div class="card-body">
            <div class="d-flex align-items-center gap-3">
              <img src="${e.organizerAvatar}" loading="lazy" alt="" class="size-12 rounded-circle flex-shrink-0" id="eventOrganizerAvatar">
              <div class="flex-grow-1">
                <h6 class="mb-1"><a href="#!" class="text-reset">${e.organizer}</a></h6>
                <p class="fs-sm text-muted">${e.dateFormatted}, ${e.time}</p>
              </div>
              <div class="dropdown">
                <a href="#!" class="link link-custom-primary" id="actionDropdown${e.id}" type="button" data-bs-toggle="dropdown" aria-expanded="false" title="dropdown-button">
                  <i class="ri-more-fill"></i>
                </a>
                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="actionDropdown${e.id}">
                  <li><a class="dropdown-item" href="#!"><i class="me-3 ri-eye-line"></i><span>Overview</span></a></li>
                  <li><a class="dropdown-item" href="#!"><i class="me-3 ri-pencil-line"></i>Edit</a></li>
                  <li><a class="dropdown-item" href="#!"><i class="me-3 ri-delete-bin-line"></i><span>Delete</span></a></li>
                </ul>
              </div>
            </div>
            <div class="mt-5">
              <img src="${e.image}" loading="lazy" alt="" class="w-100 h-48 rounded object-fit-cover" id="eventImage">
            </div>
            <div class="d-flex mt-5 gap-3">
              <div>
                <div class="size-16 mx-auto mb-10px rounded-2 bg-danger-subtle border border-danger-subtle avatar flex-column" id="eventDate">
                  <p class="mb-0.5 text-danger">${e.dayOfWeek}</p>
                  <h3 class="mb-0 fs-2xl text-danger">${e.day}</h3>
                </div>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#bookEventModal">
                  Book
                </button>
              </div>
              <div>
                <h6 class="mb-1"><a href="#!" class="link link-custom event-name">${e.name}</a></h6>
                <p class="mb-2 text-muted" id="eventLocation"><span>${e.dateFormatted}</span><span class="ps-2 ms-2 border-start">${e.location}</span></p>
                <p class="mb-1 text-muted">Contributors</p>
                <div class="avatar-group">
                  ${this.getContributorsHTML(e.contributors)}
                </div>
              </div>
            </div>
          </div>
        </div>
      `}getContributorsHTML(e){return e.map(t=>`
        <a href="#!" class="avatar-group-item" aria-label="avatar-link">
          <img src="${t.avatar}" loading="lazy" alt="" class="size-8">
        </a>
      `).join("")}setupBookModal(e){const t=document.getElementById("bookEventModal");if(!t)return;t.querySelector("#eventCount").textContent=e.name,t.querySelector("#eventOrganizer h6 a").textContent=e.organizer,t.querySelector("#eventOrganizer p").textContent=`${e.dateFormatted}, ${e.time}`;const s=t.querySelector("#eventOrganizerAvatar");s&&(s.src=e.organizerAvatar),t.querySelector("#eventImage").src=e.image,t.querySelector("#eventDate p").textContent=e.dayOfWeek,t.querySelector("#eventDate h3").textContent=e.day,t.querySelector("h6 a.event-name").textContent=e.name,t.querySelector("p#eventLocation span:first-child").textContent=e.dateFormatted,t.querySelector("p#eventLocation span:last-child").textContent=e.location;const n=t.querySelector("form");n.reset(),n.querySelector("#totalAmountInput").value="0"}renderPagination(){const e=document.querySelector(".pagination");if(!e)return;const t=Math.ceil(this.filteredEvents.length/this.itemsPerPage),s=document.querySelector("#showingResults");if(s){const a=(this.currentPage-1)*this.itemsPerPage+1,r=Math.min(a+this.itemsPerPage-1,this.filteredEvents.length);s.innerHTML=`Showing <b class="me-1">${a}-${r}</b>of<b class="ms-1">${this.filteredEvents.length}</b> Results`}e.innerHTML="";const n=document.createElement("li");n.className=`page-item ${this.currentPage===1?"disabled":""}`,n.innerHTML='<a class="page-link" href="#!"><i data-lucide="chevron-left" class="size-4"></i> Previous</a>',e.appendChild(n),this.currentPage>1&&n.addEventListener("click",a=>{a.preventDefault(),this.handlePageChange(this.currentPage-1)});for(let a=1;a<=t;a++){const r=document.createElement("li");r.className=`page-item ${a===this.currentPage?"active":""}`,r.innerHTML=`<a class="page-link" href="#!">${a}</a>`,e.appendChild(r),a!==this.currentPage&&r.addEventListener("click",o=>{o.preventDefault(),this.handlePageChange(a)})}const i=document.createElement("li");i.className=`page-item ${this.currentPage===t?"disabled":""}`,i.innerHTML='<a class="page-link" href="#!">Next <i data-lucide="chevron-right" class="size-4"></i></a>',e.appendChild(i),this.currentPage<t&&i.addEventListener("click",a=>{a.preventDefault(),this.handlePageChange(this.currentPage+1)}),E({icons:b})}handlePageChange(e){this.currentPage=e,this.renderEvents(),this.renderPagination()}handleSort(e){switch(e){case"No Sorting":this.filteredEvents=[...this.events];break;case"Alphabetical (A -> Z)":this.filteredEvents.sort((t,s)=>t.name.localeCompare(s.name));break;case"Reverse Alphabetical (Z -> A)":this.filteredEvents.sort((t,s)=>s.name.localeCompare(t.name));break;case"Status":this.filteredEvents.sort((t,s)=>t.status.localeCompare(s.status));break;default:this.filteredEvents.sort((t,s)=>new Date(s.date)-new Date(t.date))}this.currentPage=1,this.renderEvents(),this.renderPagination()}handleFilter(e){this.filterValue=e.toLowerCase(),this.filterValue?this.filteredEvents=this.events.filter(t=>t.name.toLowerCase().includes(this.filterValue)||t.location.toLowerCase().includes(this.filterValue)||t.organizer.toLowerCase().includes(this.filterValue)):this.filteredEvents=[...this.events],this.currentPage=1,this.renderEvents(),this.renderPagination(),this.updateEventCounter()}openAddModal(){const e=document.getElementById("addEventModal");if(!e)return;const t=e.querySelector("form");t.reset(),t.querySelectorAll(".text-danger").forEach(a=>a.style.display="none");const s=document.getElementById("eventLogoPreview");s&&s.classList.add("d-none");const n=document.querySelector("#uploadLabel span");n&&n.classList.remove("d-none");const i=t.querySelector("button.btn-primary");i&&(i.textContent="Add Event"),this.selectedEvent=null}openEditModal(e){const t=document.getElementById("addEventModal");if(!t)return;this.selectedEvent=e,t.querySelector(".modal-header h6").textContent="Edit Event";const s=t.querySelector("form");if(s.querySelector('input[name="event-name"]').value=e.name,C.selectDate(new Date(e.dateStr)),s.querySelector('input[name="event-time"]').value=e.time,s.querySelector('input[name="total-people"]').value=e.capacity,s.querySelector('input[name="price"]').value=e.price,s.querySelector('input[name="location"]').value=e.location,e.image){const a=document.getElementById("eventLogoPreview"),r=document.getElementById("eventLogoImage");if(a&&r){a.classList.remove("d-none"),r.src=e.image;const o=document.querySelector("#uploadLabel span");o&&o.classList.add("d-none")}}const n=s.querySelector("button.btn-primary");n&&(n.textContent="Update Event"),new window.bootstrap.Modal(t).show()}openViewModal(e){this.setupBookModal(e);const t=document.getElementById("bookEventModal");new window.bootstrap.Modal(t).show()}deleteEvent(e){const t=document.getElementById("deleteModal");if(!t)return;const s=t.querySelector("h5");s&&(s.textContent="Are you sure you want to delete this event?");const n=t.querySelector(".btn-danger"),i=n.cloneNode(!0);n.parentNode.replaceChild(i,n),i.addEventListener("click",()=>{const r=this.events.findIndex(d=>d.id===e);r>-1&&(this.events.splice(r,1),this.filteredEvents=this.filteredEvents.filter(d=>d.id!==e),this.renderEvents(),this.renderPagination(),this.updateEventCounter()),window.bootstrap.Modal.getInstance(t).hide()}),new window.bootstrap.Modal(t).show()}saveEvent(e){if(!this.validateEventForm(e))return;const t=e.querySelector('input[name="event-name"]').value,s=v.value,n=e.querySelector('input[name="event-time"]').value,i=e.querySelector('input[name="total-people"]').value,a=e.querySelector('input[name="price"]').value,r=e.querySelector('input[name="location"]').value;let o="assets/images/event/img-01.jpg";const d=document.getElementById("eventLogoImage");d&&d.src&&(o=d.src);const l=new Date(s),c=l.getDate(),h=l.toLocaleString("default",{month:"short"}),y=l.getFullYear(),g=l.toLocaleString("default",{weekday:"short"}),p=`${c} ${h} ${y}`;if(this.selectedEvent){const m=this.events.findIndex(f=>f.id===this.selectedEvent.id);m>-1&&(this.events[m]={...this.selectedEvent,name:t,date:l.toISOString(),dateFormatted:p,time:n,capacity:parseInt(i),price:parseInt(a),location:r,image:o,day:c,dayOfWeek:g})}else{const m={id:Date.now(),name:t,organizer:"Current User",organizerAvatar:"assets/images/avatar/user-5.png",date:l.toISOString(),dateFormatted:p,time:n,capacity:parseInt(i),price:parseInt(a),location:r,image:o,day:c,dayOfWeek:g,status:"Active",contributors:[{avatar:"assets/images/avatar/user-5.png"}]};this.events.unshift(m)}this.filterValue?this.handleFilter(this.filterValue):this.filteredEvents=[...this.events],this.renderEvents(),this.renderPagination(),this.updateEventCounter(),window.bootstrap.Modal.getInstance(document.getElementById("addEventModal")).hide()}validateEventForm(e){let t=!0;return["event-name","event-date","event-time","total-people","price","location"].forEach(n=>{const i=e.querySelector(`[name="${n}"]`),a=i.parentElement.querySelector(".text-danger");i.value.trim()?a.style.display="none":(a.style.display="block",t=!1)}),t}updateEventCounter(){const e=document.querySelector("#eventCount span");e&&(e.textContent=this.events.length)}setupSearch(e){const t=document.getElementById(e);t&&t.addEventListener("input",s=>{this.handleFilter(s.target.value)})}setupFileUpload(e='input[type="file"]'){const t=document.querySelector(e);t&&t.addEventListener("change",s=>{const n=s.target.files[0];if(!n)return;if(!n.type.match("image.*")){alert("Please select an image file");return}const i=new FileReader;i.onload=a=>{const r=document.getElementById("eventLogoPreview"),o=document.getElementById("eventLogoImage"),d=document.querySelector("#uploadLabel span");r&&o&&(r.classList.remove("d-none"),o.src=a.target.result,d&&d.classList.add("d-none"))},i.readAsDataURL(n)})}}const A=[{id:1,name:"Tech Innovations Summit",organizer:"Declan Grieve",organizerAvatar:"assets/images/avatar/user-5.png",date:"2024-05-19T10:00:00",dateFormatted:"19 May 2024",time:"10:00 am",day:"19",dayOfWeek:"Sun",location:"San Francisco, CA",image:"assets/images/event/img-01.jpg",capacity:200,price:500,status:"Active",contributors:[{avatar:"assets/images/avatar/user-5.png"},{avatar:"assets/images/avatar/user-20.png"},{avatar:"assets/images/avatar/user-13.png"}]},{id:2,name:"Health and Wellness Expo",organizer:"Callum Burston",organizerAvatar:"assets/images/avatar/user-20.png",date:"2024-06-24T09:00:00",dateFormatted:"24 Jun 2024",time:"9:00 am",day:"24",dayOfWeek:"Mon",location:"New York, NY",image:"assets/images/event/img-02.jpg",capacity:150,price:350,status:"Active",contributors:[{avatar:"assets/images/avatar/user-20.png"},{avatar:"assets/images/avatar/user-5.png"}]},{id:3,name:"Marketing Conference",organizer:"Sophia Adams",organizerAvatar:"assets/images/avatar/user-10.png",date:"2024-07-15T13:30:00",dateFormatted:"15 Jul 2024",time:"1:30 pm",day:"15",dayOfWeek:"Mon",location:"Los Angeles, CA",image:"assets/images/event/img-03.jpg",capacity:300,price:450,status:"Active",contributors:[{avatar:"assets/images/avatar/user-10.png"},{avatar:"assets/images/avatar/user-15.png"}]},{id:4,name:"AI & Robotics Expo",organizer:"Liam Carter",organizerAvatar:"assets/images/avatar/user-15.png",date:"2024-08-10T11:00:00",dateFormatted:"10 Aug 2024",time:"11:00 am",day:"10",dayOfWeek:"Sat",location:"Boston, MA",image:"assets/images/event/img-01.jpg",capacity:250,price:400,status:"Active",contributors:[{avatar:"assets/images/avatar/user-15.png"},{avatar:"assets/images/avatar/user-20.png"}]},{id:5,name:"Global Education Fair",organizer:"Emma Liu",organizerAvatar:"assets/images/avatar/user-13.png",date:"2024-09-05T10:00:00",dateFormatted:"5 Sep 2024",time:"10:00 am",day:"5",dayOfWeek:"Thu",location:"Chicago, IL",image:"assets/images/event/img-02.jpg",capacity:180,price:300,status:"Active",contributors:[{avatar:"assets/images/avatar/user-13.png"},{avatar:"assets/images/avatar/user-10.png"}]},{id:6,name:"Startup Pitch Night",organizer:"Noah Smith",organizerAvatar:"assets/images/avatar/user-20.png",date:"2024-10-14T17:00:00",dateFormatted:"14 Oct 2024",time:"5:00 pm",day:"14",dayOfWeek:"Mon",location:"Austin, TX",image:"assets/images/event/img-03.jpg",capacity:100,price:200,status:"Active",contributors:[{avatar:"assets/images/avatar/user-20.png"},{avatar:"assets/images/avatar/user-5.png"}]},{id:7,name:"UX/UI Design Bootcamp",organizer:"Olivia Martinez",organizerAvatar:"assets/images/avatar/user-5.png",date:"2024-11-02T09:30:00",dateFormatted:"2 Nov 2024",time:"9:30 am",day:"2",dayOfWeek:"Sat",location:"Seattle, WA",image:"assets/images/event/img-01.jpg",capacity:120,price:280,status:"Active",contributors:[{avatar:"assets/images/avatar/user-5.png"},{avatar:"assets/images/avatar/user-10.png"}]},{id:8,name:"Finance Leaders Summit",organizer:"Ava Thompson",organizerAvatar:"assets/images/avatar/user-10.png",date:"2024-12-11T14:00:00",dateFormatted:"11 Dec 2024",time:"2:00 pm",day:"11",dayOfWeek:"Wed",location:"Denver, CO",image:"assets/images/event/img-02.jpg",capacity:220,price:520,status:"Active",contributors:[{avatar:"assets/images/avatar/user-10.png"},{avatar:"assets/images/avatar/user-13.png"}]},{id:9,name:"Green Energy Forum",organizer:"Ethan Patel",organizerAvatar:"assets/images/avatar/user-15.png",date:"2025-01-20T10:30:00",dateFormatted:"20 Jan 2025",time:"10:30 am",day:"20",dayOfWeek:"Mon",location:"Portland, OR",image:"assets/images/event/img-03.jpg",capacity:270,price:480,status:"Active",contributors:[{avatar:"assets/images/avatar/user-15.png"},{avatar:"assets/images/avatar/user-20.png"}]},{id:10,name:"Blockchain & Crypto Meet",organizer:"Isabella Ross",organizerAvatar:"assets/images/avatar/user-13.png",date:"2025-02-16T15:00:00",dateFormatted:"16 Feb 2025",time:"3:00 pm",day:"16",dayOfWeek:"Sun",location:"Miami, FL",image:"assets/images/event/img-01.jpg",capacity:160,price:450,status:"Active",contributors:[{avatar:"assets/images/avatar/user-13.png"},{avatar:"assets/images/avatar/user-5.png"}]},{id:11,name:"Creative Arts Festival",organizer:"Lucas Nguyen",organizerAvatar:"assets/images/avatar/user-20.png",date:"2025-03-08T12:00:00",dateFormatted:"8 Mar 2025",time:"12:00 pm",day:"8",dayOfWeek:"Sat",location:"Philadelphia, PA",image:"assets/images/event/img-02.jpg",capacity:350,price:320,status:"Active",contributors:[{avatar:"assets/images/avatar/user-20.png"},{avatar:"assets/images/avatar/user-10.png"}]},{id:12,name:"NextGen Tech Forum",organizer:"Mia Johnson",organizerAvatar:"assets/images/avatar/user-10.png",date:"2025-04-05T13:00:00",dateFormatted:"5 Apr 2025",time:"1:00 pm",day:"5",dayOfWeek:"Sat",location:"Las Vegas, NV",image:"assets/images/event/img-03.jpg",capacity:300,price:550,status:"Active",contributors:[{avatar:"assets/images/avatar/user-10.png"},{avatar:"assets/images/avatar/user-15.png"}]},{id:13,name:"Women in Tech Meetup",organizer:"Charlotte King",organizerAvatar:"assets/images/avatar/user-5.png",date:"2025-05-18T16:00:00",dateFormatted:"18 May 2025",time:"4:00 pm",day:"18",dayOfWeek:"Sun",location:"Atlanta, GA",image:"assets/images/event/img-01.jpg",capacity:200,price:250,status:"Active",contributors:[{avatar:"assets/images/avatar/user-5.png"},{avatar:"assets/images/avatar/user-13.png"}]}],v=document.getElementById("dueDateInput"),C=new S(v,{dateFormat:"yyyy-MM-dd",autoClose:!0,locale:M});document.addEventListener("DOMContentLoaded",()=>{const u=new w({containerId:"event-listing-container",itemsPerPage:6});u.init(A),u.setupSearch("event-search"),u.setupFileUpload('input[name="event-logo"]'),window.gridManager=u});
