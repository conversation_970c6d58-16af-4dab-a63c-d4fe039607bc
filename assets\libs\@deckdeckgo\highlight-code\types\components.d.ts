/* eslint-disable */
/* tslint:disable */
/**
 * This is an autogenerated file created by the Stencil compiler.
 * It contains typing information for all components that exist in this project.
 */
import { HTMLStencilElement, JSXBase } from "./stencil-public-runtime";
import { DeckdeckgoHighlightCodeTerminal } from "./declarations/terminal";
import { DeckdeckgoHighlightCodeCarbonTheme } from "./declarations/carbon-theme";
export namespace Components {
    interface DeckgoHighlightCode {
        /**
          * Display a button user can click to edit the code. Edition has to find place on the comsumer side, the button emits an event
         */
        "editable": boolean;
        /**
          * An optional label for the `aria-label` attribute of the editable button
         */
        "editableLabel": string;
        "hide": () => Promise<void>;
        "hideAll": () => Promise<void>;
        /**
          * If you wish to highlight some lines of your code. The lines number should be provided as a number (one line) or numbers separated with coma or dash (many lines), group separated with space. For example: 1 3,5 8 14-17 which highlight lines  1, 3 to 5, 8 and 14 to 17
         */
        "highlightLines": string;
        /**
          * Define the language to be used for the syntax highlighting. The list of supported languages is defined by Prism.js
         */
        "language": string;
        /**
          * Display the number of the lines of code
         */
        "lineNumbers": boolean;
        /**
          * Load or reload the component
         */
        "load": () => Promise<void>;
        /**
          * Animate highlighted lines and, apply "focus" on next group
         */
        "nextHighlight": () => Promise<void>;
        /**
          * Animate highlighted lines and, apply "focus" on previous group
         */
        "prevHighlight": () => Promise<void>;
        "reveal": () => Promise<void>;
        "revealAll": () => Promise<void>;
        "revealProgress": 'start' | 'partial' | 'end';
        /**
          * Present the code in a stylish "windowed" card
         */
        "terminal": DeckdeckgoHighlightCodeTerminal;
        /**
          * The theme of the selected terminal (applied only in case of carbon)
         */
        "theme": DeckdeckgoHighlightCodeCarbonTheme;
    }
    interface DeckgoHighlightCodeEdit {
        "label": string;
    }
}
export interface DeckgoHighlightCodeCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLDeckgoHighlightCodeElement;
}
export interface DeckgoHighlightCodeEditCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLDeckgoHighlightCodeEditElement;
}
declare global {
    interface HTMLDeckgoHighlightCodeElement extends Components.DeckgoHighlightCode, HTMLStencilElement {
    }
    var HTMLDeckgoHighlightCodeElement: {
        prototype: HTMLDeckgoHighlightCodeElement;
        new (): HTMLDeckgoHighlightCodeElement;
    };
    interface HTMLDeckgoHighlightCodeEditElement extends Components.DeckgoHighlightCodeEdit, HTMLStencilElement {
    }
    var HTMLDeckgoHighlightCodeEditElement: {
        prototype: HTMLDeckgoHighlightCodeEditElement;
        new (): HTMLDeckgoHighlightCodeEditElement;
    };
    interface HTMLElementTagNameMap {
        "deckgo-highlight-code": HTMLDeckgoHighlightCodeElement;
        "deckgo-highlight-code-edit": HTMLDeckgoHighlightCodeEditElement;
    }
}
declare namespace LocalJSX {
    interface DeckgoHighlightCode {
        /**
          * Display a button user can click to edit the code. Edition has to find place on the comsumer side, the button emits an event
         */
        "editable"?: boolean;
        /**
          * An optional label for the `aria-label` attribute of the editable button
         */
        "editableLabel"?: string;
        /**
          * If you wish to highlight some lines of your code. The lines number should be provided as a number (one line) or numbers separated with coma or dash (many lines), group separated with space. For example: 1 3,5 8 14-17 which highlight lines  1, 3 to 5, 8 and 14 to 17
         */
        "highlightLines"?: string;
        /**
          * Define the language to be used for the syntax highlighting. The list of supported languages is defined by Prism.js
         */
        "language"?: string;
        /**
          * Display the number of the lines of code
         */
        "lineNumbers"?: boolean;
        /**
          * Emitted when a language could not be loaded. The component fallback to javascript language to display the code anyway.
         */
        "onPrismLanguageError"?: (event: DeckgoHighlightCodeCustomEvent<string>) => void;
        /**
          * Emitted when a language is fetched and loaded
         */
        "onPrismLanguageLoaded"?: (event: DeckgoHighlightCodeCustomEvent<string>) => void;
        /**
          * Present the code in a stylish "windowed" card
         */
        "terminal"?: DeckdeckgoHighlightCodeTerminal;
        /**
          * The theme of the selected terminal (applied only in case of carbon)
         */
        "theme"?: DeckdeckgoHighlightCodeCarbonTheme;
    }
    interface DeckgoHighlightCodeEdit {
        "label"?: string;
        "onEditCode"?: (event: DeckgoHighlightCodeEditCustomEvent<void>) => void;
    }
    interface IntrinsicElements {
        "deckgo-highlight-code": DeckgoHighlightCode;
        "deckgo-highlight-code-edit": DeckgoHighlightCodeEdit;
    }
}
export { LocalJSX as JSX };
declare module "@stencil/core" {
    export namespace JSX {
        interface IntrinsicElements {
            "deckgo-highlight-code": LocalJSX.DeckgoHighlightCode & JSXBase.HTMLAttributes<HTMLDeckgoHighlightCodeElement>;
            "deckgo-highlight-code-edit": LocalJSX.DeckgoHighlightCodeEdit & JSXBase.HTMLAttributes<HTMLDeckgoHighlightCodeEditElement>;
        }
    }
}
