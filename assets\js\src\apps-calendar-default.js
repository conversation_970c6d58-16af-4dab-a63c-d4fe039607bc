import"../../admin.bundle-DI9_jvUJ.js";/* empty css                               */import"../../main-Cyta4iCA.js";document.addEventListener("DOMContentLoaded",function(){var d=new Date().getFullYear(),r=new Date().getMonth(),g=document.querySelector(".btn-info .fc-event-mai");g&&(g.textContent="Festival Function",g.className="fc-event-main fs-13");var B=document.getElementById("external-events");new FullCalendar.Draggable(B,{itemSelector:".draggable-event",eventData:function(t){var n=t.querySelector(".fc-event-main");if(!n)return console.error("Event title element not found"),{title:"Untitled Event"};var e="bg-secondary";return t.classList.contains("btn-primary")&&(e="bg-primary"),t.classList.contains("btn-success")&&(e="bg-success"),t.classList.contains("btn-info")&&(e="bg-info"),{title:n.textContent,backgroundColor:e.replace("bg-",""),className:e+" text-white p-2 rounded-2"}}});var h=document.getElementById("calendar"),p=new FullCalendar.Calendar(h,{timeZone:"local",initialView:"dayGridMonth",headerToolbar:{left:"prev,next today",center:"title",right:"dayGridMonth,timeGridWeek,timeGridDay"},events:[{id:"1",title:"Meeting",start:new Date(d,r,1),backgroundColor:"success",className:"bg-success text-white p-2 rounded-2",extendedProps:{location:"surat",guests:[]}},{id:"2",title:"Update Weekly",start:new Date(d,r,7),backgroundColor:"primary",className:"bg-primary text-white p-2 rounded-2",extendedProps:{location:"surat",guests:[]}},{id:"3",title:"Family Dinner",start:new Date(d,r,14),backgroundColor:"secondary",className:"bg-secondary text-white p-2 rounded-2",extendedProps:{location:"surat",guests:[]}},{id:"4",title:"School Reunion",start:new Date(d,r,10),backgroundColor:"info",className:"bg-info text-white p-2 rounded-2",extendedProps:{location:"surat",guests:[]}},{id:"5",title:"Holiday Tour",start:new Date(d,r,14),backgroundColor:"success",className:"bg-success text-white p-2 rounded-2",extendedProps:{location:"surat",guests:[]}},{id:"6",title:"Meeting",start:new Date(d,r,23),backgroundColor:"success",className:"bg-success text-white p-2 rounded-2",extendedProps:{location:"surat",guests:[]}},{id:"7",title:"Marriage Function",start:new Date(d,r,18),backgroundColor:"secondary",className:"bg-secondary text-white p-2 rounded-2",extendedProps:{location:"surat",guests:[]}}],editable:!0,droppable:!0,eventContent:function(t){const n=document.createElement("div");n.classList.add("overflow-hidden");const e=document.createElement("div");return e.classList.add("fc-event-title","text-truncate","text-white","fs-sm"),e.innerText=t.event.title,n.appendChild(e),{domNodes:[n]}},dateClick:function(t){m();var n=new window.bootstrap.Modal(document.getElementById("addEventModal"));document.getElementById("eventDateInput").value=t.dateStr,document.getElementById("endEventDateInput").value=t.dateStr,document.getElementById("modalTitle").textContent="Add Event",document.getElementById("submitEventBtn").textContent="Create Event",document.getElementById("eventForm").removeAttribute("data-event-id"),n.show()},eventClick:function(t){var i,c;m();var n=new window.bootstrap.Modal(document.getElementById("addEventModal"));document.getElementById("modalTitle").textContent="Edit Event",document.getElementById("submitEventBtn").textContent="Update Event";const e=t.event;document.getElementById("eventNameInput").value=e.title;const a=e.start?y(e.start):"";document.getElementById("eventDateInput").value=a;const o=e.end?y(e.end):a;if(document.getElementById("endEventDateInput").value=o,!e.allDay&&e.start){const u=e.start.getHours().toString().padStart(2,"0"),v=e.start.getMinutes().toString().padStart(2,"0");document.getElementById("eventTimeInput").value=`${u}:${v}`}else document.getElementById("eventTimeInput").value="";document.getElementById("locationInput").value=((i=e.extendedProps)==null?void 0:i.location)||"",document.getElementById("eventForm").setAttribute("data-event-id",e.id),C(((c=e.extendedProps)==null?void 0:c.guests)||[]),n.show()},drop:function(t){}});p.render();function y(t){const n=t.getFullYear(),e=(t.getMonth()+1).toString().padStart(2,"0"),a=t.getDate().toString().padStart(2,"0");return`${n}-${e}-${a}`}function m(){document.getElementById("eventForm").reset(),document.getElementById("guestList").innerHTML=""}function C(t){const n=document.getElementById("guestList");n.innerHTML="",t&&t.length>0&&t.forEach(e=>{const a=I(e.email,e.avatarId);n.appendChild(a)})}function I(t,n){const e=document.createElement("div");e.className="position-relative rounded-circle size-9",e.setAttribute("data-email",t);const a=document.createElement("img");a.src=`assets/images/avatar/user-${n}.png`,a.alt=t,a.className="size-9 rounded-circle";const o=document.createElement("a");return o.href="#!",o.className="position-absolute d-inline-flex align-items-center justify-content-center text-white bg-dark fs-13 border-2 rounded-circle size-4 top-0 end-0 me-n1 mt-n1",o.innerHTML='<i class="text-xs ri-close-line"></i>',o.addEventListener("click",function(i){i.preventDefault(),e.remove()}),e.appendChild(a),e.appendChild(o),e}document.getElementById("addGuestBtn").addEventListener("click",function(){const t=document.getElementById("guestInput"),n=t.value.trim();if(n&&D(n)){const e=Math.floor(Math.random()*30)+1,a=I(n,e);document.getElementById("guestList").appendChild(a),t.value=""}else alert("Please enter a valid email address")});function D(t){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t)}if(document.getElementById("eventForm").addEventListener("submit",function(t){t.preventDefault();const n=document.getElementById("eventNameInput").value,e=document.getElementById("eventDateInput").value,a=document.getElementById("endEventDateInput").value,o=document.getElementById("eventTimeInput").value,i=document.getElementById("locationInput").value,c=this.getAttribute("data-event-id");let u="primary";if(typeof VirtualSelect<"u"){const l=document.querySelector("#ColorSelect").querySelector(".vscomp-value-tag");l&&(u=l.getAttribute("data-value")||"primary")}let v=new Date(e),f=new Date(a||e);if(o){const[l,s]=o.split(":");v.setHours(parseInt(l),parseInt(s)),f.setHours(parseInt(l),parseInt(s))}const w=document.getElementById("guestList").querySelectorAll("[data-email]"),x=Array.from(w).map(l=>{const s=l.getAttribute("data-email"),b=l.querySelector("img").src,S=b.match(/user-(\d+)\.png/)?parseInt(b.match(/user-(\d+)\.png/)[1]):1;return{email:s,avatarId:S}}),E={title:n,start:v,end:f,allDay:!o,backgroundColor:u,className:"bg-"+u+" text-white p-2 rounded-2",extendedProps:{location:i,guests:x}};if(n){if(c){const s=p.getEventById(c);s&&s.remove(),E.id=c}else E.id="event-"+new Date().getTime();p.addEvent(E),window.bootstrap.Modal.getInstance(document.getElementById("addEventModal")).hide(),m()}}),document.getElementById("newEvent").addEventListener("click",function(){const t=new Date().toISOString().split("T")[0];document.getElementById("eventDateInput").value=t,document.getElementById("endEventDateInput").value=t,m(),document.getElementById("modalTitle").textContent="Add Event",document.getElementById("submitEventBtn").textContent="Create Event",document.getElementById("eventForm").removeAttribute("data-event-id")}),typeof VirtualSelect<"u")VirtualSelect.init({ele:"#ColorSelect",options:[{label:"Blue",value:"primary"},{label:"Green",value:"success"},{label:"Purple",value:"secondary"},{label:"Cyan",value:"info"}],search:!1,defaultValue:"primary"});else{console.error("VirtualSelect is not loaded");const t=document.getElementById("ColorSelect");if(t){const n=document.createElement("select");n.className="form-select",n.id="ColorSelectFallback",[{label:"Blue",value:"primary"},{label:"Green",value:"success"},{label:"Purple",value:"secondary"},{label:"Cyan",value:"info"}].forEach(a=>{const o=document.createElement("option");o.value=a.value,o.textContent=a.label,n.appendChild(o)}),t.parentNode.replaceChild(n,t)}}});
