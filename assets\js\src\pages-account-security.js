import"../../admin.bundle-DI9_jvUJ.js";import"../../main-Cyta4iCA.js";document.addEventListener("DOMContentLoaded",function(){const n=document.querySelectorAll("#googleAuthenticationModal .pattern-input"),c=document.getElementById("verifyButton");function o(e,t){e.value.length===1&&t&&t.focus()}function a(e,t){e.value.length===0&&t&&t.focus()}n.forEach((e,t)=>{e.addEventListener("input",function(){this.value.match(/\d/)?o(this,n[t+1]):this.value=""}),e.addEventListener("keydown",function(s){s.key==="Backspace"&&this.value.length===0&&a(this,n[t-1])})}),c.addEventListener("click",function(){let e="",t=!0;n.forEach(s=>{s.value.match(/\d/)?e+=s.value:t=!1}),t&&e.length===6?(window.bootstrap.Modal.getInstance(document.getElementById("googleAuthenticationModal")).hide(),new window.bootstrap.Modal(document.getElementById("googleAuthenticationModal2")).show()):alert("Please fill all the fields with valid digits.")}),window.validateInput=function(e,t){if(!e.value.match(/\d/))e.value="";else{const s=e.nextElementSibling;s&&s.focus()}}});document.addEventListener("DOMContentLoaded",function(){const n=document.getElementById("passwordResetForm"),c=document.getElementById("updatePasswordAlert");document.querySelectorAll("#button-addon1, #button-addon2, #button-addon3").forEach(o=>{o.addEventListener("click",()=>{const a=o.closest(".password").querySelector("input"),e=o.querySelector('[data-lucide="eye-off"]'),t=o.querySelector('[data-lucide="eye"]'),s=a.getAttribute("type")==="password"?"text":"password";a.setAttribute("type",s),e.classList.toggle("d-none",s==="text"),t.classList.toggle("d-none",s==="password")})}),n.addEventListener("submit",function(o){o.preventDefault();let a=!0;const e=(r,i,w)=>{r.classList.add("is-invalid"),i.textContent=w,i.classList.add("d-block"),a=!1},t=(r,i)=>{r.classList.remove("is-invalid"),i.textContent="",i.classList.remove("d-block")},s=document.getElementById("currentPasswordInput"),u=document.getElementById("currentPasswordError");s.value.trim()===""?e(s,u,"Current password is required."):t(s,u);const d=document.getElementById("newPasswordInput"),m=document.getElementById("newPasswordError");d.value.trim()===""?e(d,m,"New password is required."):d.value.length<8?e(d,m,"New password must be at least 8 characters long."):t(d,m);const l=document.getElementById("confirmPasswordInput"),f=document.getElementById("confirmPasswordError");l.value.trim()===""?e(l,f,"Please confirm your new password."):l.value!==d.value?e(l,f,"Passwords do not match."):t(l,f),a&&(n.reset(),c.innerHTML=`
                <div class="alert alert-success alert-dismissible mb-5 fade show" role="alert">
                    <span>Password updated successfully!</span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            `,setTimeout(()=>{const r=c.querySelector(".alert");r&&r.remove()},5e3))})});
