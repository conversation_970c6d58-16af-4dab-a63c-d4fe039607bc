import"../../admin.bundle-DI9_jvUJ.js";import"../../main-Cyta4iCA.js";document.addEventListener("DOMContentLoaded",function(){var e=document.getElementById("dateNavLinkCalendar"),n=new FullCalendar.Calendar(e,{navLinks:!0,headerToolbar:{left:"prev,next today",center:"title",right:"dayGridMonth,timeGridWeek,timeGridDay"},events:"https://fullcalendar.io/api/demo-feeds/events.json?single-day&for-resource-timeline"});n.render()});
