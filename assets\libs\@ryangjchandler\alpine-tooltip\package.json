{"name": "@ryangjchandler/alpine-tooltip", "description": "Add tooltips to your Alpine 3.x components with a custom directive.", "version": "2.0.1", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://ryangjchandler.co.uk"}, "repository": {"type": "git", "url": "https://github.com/ryangjchandler/alpine-tooltip"}, "main": "src/index.js", "module": "dist/module.esm.js", "type": "module", "dependencies": {"tippy.js": "^6.3.1"}, "devDependencies": {"@types/jest": "^27.0.2", "brotli-size": "^4.0.0", "esbuild": "^0.8.39", "eslint": "^7.22.0", "eslint-config-standard": "^16.0.2", "eslint-plugin-import": "^2.22.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.3.1", "eslint-plugin-standard": "^4.1.0", "jest": "^27.2.4", "lint-staged": "^10.5.4"}, "scripts": {"test": "npm run build && node --experimental-vm-modules node_modules/.bin/jest", "build": "node ./scripts/build.js", "watch": "node ./scripts/build.js --watch", "lint": "eslint .", "lint-fix": "eslint . --fix"}}