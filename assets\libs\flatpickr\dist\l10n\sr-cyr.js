(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
  typeof define === 'function' && define.amd ? define(['exports'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global['sr-cyr'] = {}));
}(this, (function (exports) { 'use strict';

  var fp = typeof window !== "undefined" && window.flatpickr !== undefined
      ? window.flatpickr
      : {
          l10ns: {},
      };
  var SerbianCyrillic = {
      weekdays: {
          shorthand: ["Нед", "Пон", "Уто", "Сре", "Чет", "Пет", "Суб"],
          longhand: [
              "Недеља",
              "Понедељак",
              "Уторак",
              "Среда",
              "Четвртак",
              "Петак",
              "Субота",
          ],
      },
      months: {
          shorthand: [
              "<PERSON>ан",
              "<PERSON>е<PERSON>",
              "<PERSON><PERSON><PERSON>",
              "<PERSON><PERSON>р",
              "<PERSON>а<PERSON>",
              "Јун",
              "Јул",
              "Авг",
              "Сеп",
              "Окт",
              "Нов",
              "Дец",
          ],
          longhand: [
              "Јануар",
              "Фебруар",
              "Март",
              "Април",
              "Мај",
              "Јун",
              "Јул",
              "Август",
              "Септембар",
              "Октобар",
              "Новембар",
              "Децембар",
          ],
      },
      firstDayOfWeek: 1,
      weekAbbreviation: "Нед.",
      rangeSeparator: " до ",
  };
  fp.l10ns.sr = SerbianCyrillic;
  var srCyr = fp.l10ns;

  exports.SerbianCyrillic = SerbianCyrillic;
  exports.default = srCyr;

  Object.defineProperty(exports, '__esModule', { value: true });

})));
