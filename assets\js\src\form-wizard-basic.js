import"../../admin.bundle-DI9_jvUJ.js";import"../../main-Cyta4iCA.js";document.addEventListener("DOMContentLoaded",()=>{const s={step1:document.getElementById("step1"),step2:document.getElementById("step2"),step3:document.getElementById("step3"),step4:document.getElementById("step4")},o={progressBar1:document.getElementById("progress-bar"),progressBar2:document.getElementById("progress-bar2"),progressBar3:document.getElementById("progress-bar3")},i={nextStep1:document.getElementById("nextStep1"),prevStep1:document.getElementById("prevStep1"),nextStep2:document.getElementById("nextStep2"),prevStep2:document.getElementById("prevStep2"),completeStep:document.getElementById("completeStep"),backHome:document.getElementById("backHome"),passwordToggle:document.getElementById("passwordToggle")},a={firstName:document.getElementById("firstName"),email:document.getElementById("email"),password:document.getElementById("passwordInput"),profession:document.getElementById("profession"),gender:document.getElementsByName("gender"),fileInput:document.getElementById("fileInput")},t={firstNameError:document.getElementById("firstNameError"),emailError:document.getElementById("emailError"),genderError:document.getElementById("genderError"),professionError:document.getElementById("professionError"),imageError:document.getElementById("imageError"),passwordError:document.getElementById("passwordError")},p=document.getElementById("profile-image"),m=(e,r,l)=>{e.classList.add("d-none"),r.classList.remove("d-none"),l&&l()},d=(e,r)=>{e&&(e.style.width=r)},g=()=>{let e=!0;return a.firstName.value.trim()===""?(t.firstNameError.textContent="First name is required.",e=!1):t.firstNameError.textContent="",a.email.value.trim()===""?(t.emailError.textContent="Email is required.",e=!1):t.emailError.textContent="",e},E=()=>{const e=a.password.value.trim(),r={lowerCaseLetter:document.getElementById("lowerCaseLetterError"),upperCaseLetter:document.getElementById("upperCaseLetterError"),number:document.getElementById("numberError"),specialCharacter:document.getElementById("specialCharacterError"),length:document.getElementById("lengthError")};t.passwordError.textContent="",Object.values(r).forEach(n=>{n.classList.remove("text-danger","text-success")});const l=[{condition:e==="",message:"Password is required."},{condition:!/[a-z]/.test(e),message:"Password must contain at least one lowercase letter.",errorElement:r.lowerCaseLetter},{condition:!/[A-Z]/.test(e),message:"Password must contain at least one uppercase letter.",errorElement:r.upperCaseLetter},{condition:!/[0-9]/.test(e),message:"Password must contain at least one number.",errorElement:r.number},{condition:!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(e),message:"Password must contain at least one special character.",errorElement:r.specialCharacter},{condition:e.length<8,message:"Password must be at least 8 characters long.",errorElement:r.length}];let c=!0;for(const n of l)n.condition?(t.passwordError.textContent=n.message,n.errorElement&&(c=!1,n.errorElement.classList.add("text-danger"))):n.errorElement&&n.errorElement.classList.add("text-success");return c},u=()=>{let e=!0;return Array.from(a.gender).some(l=>l.checked)?t.genderError.textContent="":(t.genderError.textContent="Please select your gender.",e=!1),a.profession.value.trim()===""?(t.professionError.textContent="Profession is required.",e=!1):t.professionError.textContent="",e};i.nextStep1.addEventListener("click",()=>{g()&&m(s.step1,s.step2,()=>{d(o.progressBar1,"66%"),d(o.progressBar2,"66%")})}),i.nextStep2.addEventListener("click",()=>{E()&&m(s.step2,s.step3,()=>{d(o.progressBar2,"100%"),d(o.progressBar3,"100%")})}),i.prevStep1.addEventListener("click",()=>{m(s.step2,s.step1,()=>{d(o.progressBar1,"33%"),d(o.progressBar2,"66%")})}),i.prevStep2.addEventListener("click",()=>{m(s.step3,s.step2,()=>{d(o.progressBar2,"66%"),d(o.progressBar3,"100%")})}),i.completeStep.addEventListener("click",()=>{u()&&m(s.step3,s.step4)}),i.backHome.addEventListener("click",()=>{window.location.href="/"}),passwordToggle.addEventListener("click",()=>{const e=a.password;e.type=e.type==="password"?"text":"password",passwordToggle.firstElementChild.classList.toggle("d-none"),passwordToggle.lastElementChild.classList.toggle("d-none")}),a.fileInput.addEventListener("change",()=>{const e=a.fileInput.files[0];if(t.imageError.textContent="",!e){t.imageError.textContent="Please upload a profile picture.";return}if(!["image/jpeg","image/png","image/gif"].includes(e.type)){t.imageError.textContent="Only JPEG, PNG, and GIF files are allowed.";return}const l=5*1024*1024;if(e.size>l){t.imageError.textContent="The file is too large. Maximum size allowed is 5MB.";return}const c=new FileReader;c.onload=n=>{p.src=n.target.result},c.readAsDataURL(e)})});
