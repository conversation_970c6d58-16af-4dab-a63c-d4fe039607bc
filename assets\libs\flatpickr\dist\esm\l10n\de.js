var fp = typeof window !== "undefined" && window.flatpickr !== undefined
    ? window.flatpickr
    : {
        l10ns: {},
    };
export var German = {
    weekdays: {
        shorthand: ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"],
        longhand: [
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON>",
            "Dienstag",
            "Mitt<PERSON><PERSON>",
            "Donnerstag",
            "Freitag",
            "Samstag",
        ],
    },
    months: {
        shorthand: [
            "<PERSON>",
            "Feb",
            "<PERSON><PERSON><PERSON>",
            "Apr",
            "Mai",
            "Jun",
            "Jul",
            "Aug",
            "Sep",
            "Okt",
            "Nov",
            "Dez",
        ],
        longhand: [
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "April",
            "<PERSON>",
            "Juni",
            "Juli",
            "August",
            "September",
            "Oktober",
            "November",
            "Dezember",
        ],
    },
    firstDayOfWeek: 1,
    weekAbbreviation: "KW",
    rangeSeparator: " bis ",
    scrollTitle: "Zum Ändern scrollen",
    toggleTitle: "Zum Umschalten klicken",
    time_24hr: true,
};
fp.l10ns.de = German;
export default fp.l10ns;
