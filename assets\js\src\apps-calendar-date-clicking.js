import"../../admin.bundle-DI9_jvUJ.js";import"../../main-Cyta4iCA.js";document.addEventListener("DOMContentLoaded",function(){var t=document.getElementById("dateClickingSelectingCalendar"),r=new FullCalendar.Calendar(t,{selectable:!0,headerToolbar:{left:"prev,next today",center:"title",right:"dayGridMonth,timeGridWeek,timeGridDay"},dateClick:function(e){alert("clicked "+e.dateStr)},select:function(e){alert("selected "+e.startStr+" to "+e.endStr)}});r.render()});
