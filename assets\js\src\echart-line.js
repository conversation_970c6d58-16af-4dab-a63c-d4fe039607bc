import"../../admin.bundle-DI9_jvUJ.js";import"../../echarts.min-CRgBYnJL.js";import{i as p}from"../../index-u3wYLQ4o.js";import"../../main-Cyta4iCA.js";function u(o){const t=getComputedStyle(document.documentElement).getPropertyValue(o).trim();return/^\d{1,3},\s*\d{1,3},\s*\d{1,3}$/.test(t)?`rgb(${t})`:t}var a=[];const b=o=>{const t=JSON.parse(JSON.stringify(o)),i=r=>{for(const l in r)typeof r[l]=="string"&&r[l].startsWith("--dx-")?r[l]=u(r[l]):typeof r[l]=="object"&&r[l]!==null&&i(r[l])};return i(t),t};function d(o=""){o&&document.documentElement.setAttribute("data-colors",o),a.forEach(t=>{var c;const i=JSON.parse(JSON.stringify(t[0].data)),r=b(structuredClone(i));t[0].chart&&((c=t[0].chart)==null||c.dispose());var l=document.getElementById(t[0].id),s=p(l);t[0].chart=s,r&&typeof r=="object"&&s.setOption(r)})}document.querySelectorAll('input[name="data-colors"]').forEach(o=>{o.addEventListener("change",function(){n(this.value)})});document.querySelectorAll('input[name="data-bs-theme"]').forEach(o=>{o.addEventListener("change",function(){n(this.value)})});var y;(y=document.getElementById("darkModeButton"))==null||y.addEventListener("click",function(){n(this.value)});function n(o){setTimeout(()=>{d(o)},0)}var x;(x=document.getElementById("darkModeButton"))==null||x.addEventListener("click",function(){setTimeout(()=>{d()},0)});var e;e={xAxis:{type:"category",data:["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],axisLabel:{color:"--dx-secondary-color"},axisLine:{lineStyle:{color:["--dx-border-color"]}}},legend:{textStyle:{color:["--dx-body-color"]}},yAxis:{type:"value",splitLine:{lineStyle:{color:["--dx-border-color"]}},axisLabel:{color:"--dx-secondary-color"}},axisLine:{lineStyle:{color:["--dx-border-color"]}},series:[{data:[150,230,224,218,135,147,260],type:"line"}],color:"--dx-primary",grid:{top:"5%",left:"6%",right:"0%",bottom:"8%"}};a.push([{id:"basicLineChart",data:e}]);var e;e={xAxis:{type:"category",data:["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],axisLabel:{color:"--dx-secondary-color"},axisLine:{lineStyle:{color:["--dx-border-color"]}}},legend:{textStyle:{color:["--dx-body-color"]}},yAxis:{type:"value",splitLine:{lineStyle:{color:["--dx-border-color"]}},axisLabel:{color:"--dx-secondary-color"}},axisLine:{lineStyle:{color:["--dx-border-color"]}},series:[{data:[820,932,901,934,1290,1330,1320],type:"line",smooth:!0}],color:"--dx-secondary",grid:{top:"5%",left:"6%",right:"0%",bottom:"8%"}};a.push([{id:"smoothLineChart",data:e}]);var e;e={series:[{name:"Email",type:"line",stack:"Total",data:[120,132,101,134,90,230,210]},{name:"Union Ads",type:"line",stack:"Total",data:[220,182,191,234,290,330,310]},{name:"Video Ads",type:"line",stack:"Total",data:[150,232,201,154,190,330,410]},{name:"Direct",type:"line",stack:"Total",data:[320,332,301,334,390,330,320]},{name:"Search Engine",type:"line",stack:"Total",data:[820,932,901,934,1290,1330,1320]}],tooltip:{trigger:"axis"},xAxis:{type:"category",boundaryGap:!1,data:["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],axisLabel:{color:"--dx-secondary-color"},axisLine:{lineStyle:{color:["--dx-border-color"]}}},legend:{data:["Email","Union Ads","Video Ads","Direct","Search Engine"],textStyle:{color:["--dx-body-color"]}},yAxis:{type:"value",splitLine:{lineStyle:{color:["--dx-border-color"]}},axisLabel:{color:"--dx-secondary-color"}},axisLine:{lineStyle:{color:["--dx-border-color"]}},toolbox:{show:!1},color:["--dx-primary","--dx-secondary","--dx-success","--dx-danger","--dx-warning"],grid:{top:"5%",left:"6%",right:"0%",bottom:"8%"}};a.push([{id:"stackedLineChart",data:e}]);var e;e={legend:{data:["Altitude (km) vs. temperature (°C)"],textStyle:{color:["--dx-body-color"]}},tooltip:{trigger:"axis",formatter:"Temperature : <br/>{b}km : {c}°C"},xAxis:{type:"value",axisLabel:{formatter:"{value} °C",color:"--dx-secondary-color"},splitLine:{lineStyle:{color:["--dx-border-color"]}}},yAxis:{type:"category",splitLine:{lineStyle:{color:["--dx-border-color"]}},axisLabel:{formatter:"{value} km",color:"--dx-secondary-color"},axisLine:{onZero:!1,lineStyle:{color:["--dx-border-color"]}},boundaryGap:!1,data:["0","10","20","30","40","50","60","70","80"]},axisLine:{lineStyle:{color:["--dx-border-color"]}},toolbox:{show:!1},series:[{name:"Altitude (km) vs. temperature (°C)",type:"line",symbolSize:10,symbol:"circle",smooth:!0,lineStyle:{width:3,shadowColor:"rgba(0,0,0,0.3)",shadowBlur:10,shadowOffsetY:8},data:[15,-50,-56.5,-46.5,-22.1,-2.5,-27.7,-55.7,-76.5]}],color:["--dx-primary"],grid:{top:"5%",left:"6%",right:"0%",bottom:"8%"}};a.push([{id:"categoryLineChart",data:e}]);var e;e={series:[{name:"Step Start",type:"line",step:"start",data:[120,132,101,134,90,230,210]},{name:"Step Middle",type:"line",step:"middle",data:[220,282,201,234,290,430,410]},{name:"Step End",type:"line",step:"end",data:[450,432,401,454,590,530,510]}],title:{text:"Step Line",textStyle:{color:["--dx-body-color"]}},tooltip:{trigger:"axis"},legend:{data:["Step Start","Step Middle","Step End"],textStyle:{color:["--dx-body-color"]}},toolbox:{show:!1},xAxis:{type:"category",data:["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],splitLine:{lineStyle:{color:["--dx-border-color"]}},axisLabel:{color:"--dx-secondary-color"},axisLine:{lineStyle:{color:["--dx-border-color"]}}},yAxis:{type:"value",splitLine:{lineStyle:{color:["--dx-border-color"]}},axisLabel:{color:"--dx-secondary-color"}},color:["--dx-primary","--dx-indigo","--dx-pink"],grid:{top:"5%",left:"6%",right:"0%",bottom:"8%"}};a.push([{id:"stepLineChart",data:e}]);var e;e={series:[{data:[120,200,150,80,70,110,130],type:"line",symbol:"triangle",symbolSize:20,lineStyle:{color:"--dx-border-color",width:4,type:"dashed"},itemStyle:{borderWidth:3,borderColor:"#EE6666",color:"yellow"}}],legend:{textStyle:{color:["--dx-body-color"]}},toolbox:{show:!1},xAxis:{type:"category",data:["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],splitLine:{lineStyle:{color:["--dx-border-color"]}},axisLabel:{color:"--dx-secondary-color"},axisLine:{lineStyle:{color:["--dx-border-color"]}}},yAxis:{type:"value",splitLine:{lineStyle:{color:["--dx-border-color"]}},axisLabel:{color:"--dx-secondary-color"}},color:["--dx-primary","--dx-indigo","--dx-pink"],grid:{top:"5%",left:"6%",right:"0%",bottom:"8%"}};a.push([{id:"styleLineChart",data:e}]);window.addEventListener("DOMContentLoaded",()=>{d()});window.addEventListener("resize",()=>{setTimeout(()=>{d()},0)});
