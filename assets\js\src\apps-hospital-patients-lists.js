import{c as o,i as d}from"../../admin.bundle-DI9_jvUJ.js";/* empty css                               */import"../../main-Cyta4iCA.js";VirtualSelect.init({ele:"#insuranceSelect",options:[{label:"Yes",value:"Yes"},{label:"No",value:"No"}]});VirtualSelect.init({ele:"#citySelect",options:[{label:"Algeria",value:"Algeria"},{label:"Argentina",value:"Argentina"},{label:"Belgium",value:"Belgium"},{label:"Mexico",value:"Mexico"},{label:"Russia",value:"Russia"},{label:"Denmark",value:"Denmark"},{label:"Sudan",value:"Sudan"},{label:"Spain",value:"Spain"},{label:"Germany",value:"Germany"},{label:"Israel",value:"Israel"},{label:"Namibia",value:"Namibia"},{label:"Brazil",value:"Brazil"},{label:"Poland",value:"Poland"},{label:"Serbia",value:"Serbia"},{label:"Malaysia",value:"Malaysia"},{label:"Norway",value:"Norway"},{label:"Romania",value:"Romania"},{label:"USA",value:"USA"},{label:"Canada",value:"Canada"}]});VirtualSelect.init({ele:"#genderSelect",options:[{label:"Male",value:"Male"},{label:"Female",value:"Female"},{label:"Others",value:"Others"}]});VirtualSelect.init({ele:"#doctorsSelect",options:[{label:"Dr. Michael",value:"Dr. Michael"},{label:"Dr. Sarah",value:"Dr. Sarah"},{label:"Dr. Robert",value:"Dr. Robert"},{label:"Dr. Emily",value:"Dr. Emily"},{label:"Dr. James",value:"Dr. James"},{label:"Dr. Olivia",value:"Dr. Olivia"},{label:"Dr. David",value:"Dr. David"},{label:"Dr. Sophia",value:"Dr. Sophia"},{label:"Dr. William",value:"Dr. William"},{label:"Dr. Charlotte",value:"Dr. Charlotte"}]});VirtualSelect.init({ele:"#patientStatusSelect",options:[{label:"New",value:"New"},{label:"Follow Up",value:"Follow Up"},{label:"Old",value:"Old"}]});class m{constructor(e){this.data=e.data||[],this.container=document.getElementById(e.containerId),this.itemsPerPage=e.itemsPerPage||16,this.currentPage=1,this.searchFields=e.searchFields||[],this.filteredData=[...this.data],this.renderTable=this.renderTable.bind(this),this.renderPagination=this.renderPagination.bind(this),this.handleSearch=this.handleSearch.bind(this),this.goToPage=this.goToPage.bind(this),this.deleteItem=this.deleteItem.bind(this),this.setupEventListeners(),this.renderTable(),this.renderPagination()}setupEventListeners(){const e=document.getElementById("searchLeaveInput");e&&e.addEventListener("input",this.handleSearch),document.addEventListener("click",a=>{if(a.target.classList.contains("delete-btn")||a.target.closest(".delete-btn")&&a.target.tagName==="I"){const t=a.target.closest(".delete-btn").dataset.id,s=new window.bootstrap.Modal(document.getElementById("deleteModal"));s.show();const i=document.querySelector("#deleteModal .btn-danger");i.onclick=()=>{this.deleteItem(t),s.hide()}}})}renderTable(){if(!this.container)return;const e=(this.currentPage-1)*this.itemsPerPage,a=e+this.itemsPerPage,t=this.filteredData.slice(e,a),s=document.querySelector("#patientTable");if(s.innerHTML="",t.length===0){s.innerHTML=`
                <td colspan="6" class="text-center py-4">
                    <div class="d-flex flex-column align-items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" class="mx-auto size-12" viewBox="0 0 48 48">
                            <linearGradient id="SVGID_1" x1="34.598" x2="15.982" y1="15.982" y2="34.598" gradientUnits="userSpaceOnUse">
                                <stop offset="0" stop-color="#60e8fe"></stop>
                                <stop offset=".033" stop-color="#6ae9fe"></stop>
                                <stop offset=".197" stop-color="#97f0fe"></stop>
                                <stop offset=".362" stop-color="#bdf5ff"></stop>
                                <stop offset=".525" stop-color="#dafaff"></stop>
                                <stop offset=".687" stop-color="#eefdff"></stop>
                                <stop offset=".846" stop-color="#fbfeff"></stop>
                                <stop offset="1" stop-color="#ffffff"></stop>
                            </linearGradient>
                            <path fill="url(#SVGID_1)" d="M40.036,33.826L31.68,25.6c0.847-1.739,1.335-3.684,1.335-5.748
                                c0-7.27-5.894-13.164-13.164-13.164S6.688,12.582,6.688,19.852c0,7.27,5.894,13.164,13.164,13.164
                                c2.056,0,3.995-0.485,5.728-1.326l3.914,4.015l4.331,4.331c1.715,1.715,4.496,1.715,6.211,0
                                C41.751,38.321,41.751,35.541,40.036,33.826z">
                            </path>
                            <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round"
                                stroke-miterlimit="10" stroke-width="3"
                                d="M31.95,25.739l8.086,8.086c1.715,1.715,1.715,4.496,0,6.211l0,0c-1.715,1.715-4.496,1.715-6.211,0l-4.331-4.331">
                            </path>
                            <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round"
                                stroke-miterlimit="10" stroke-width="3"
                                d="M7.525,24.511c-1.771-4.694-0.767-10.196,3.011-13.975c3.847-3.847,9.48-4.817,14.228-2.912">
                            </path>
                            <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round"
                                stroke-miterlimit="10" stroke-width="3"
                                d="M30.856,12.603c3.376,5.114,2.814,12.063-1.688,16.565c-4.858,4.858-12.565,5.129-17.741,0.814">
                            </path>
                        </svg>
                        <p class="mt-2 text-center text-gray-500 dark:text-dark-500">No matching records found</p>
                        <p class="text-muted mb-0">We couldn't find any categories matching your search.</p>
                    </div>
                </td>
            `;return}t.forEach(i=>{const l=document.createElement("div");l.className="col-md-6 col-xxl-3",l.innerHTML=`
          <div class="card" data-id="${i.id}">
            <div class="card-body">
              <div class="dropdown float-end">
                <a href="#!" class="link link-custom-primary" type="button" data-bs-toggle="dropdown" aria-expanded="true" title="dropdown-button">
                  <i class="ri-more-fill"></i>
                </a>
                <ul class="dropdown-menu dropdown-menu-end">
                  <li><a class="dropdown-item" href="#!"><i class="me-2 ri-eye-line"></i><span>Overview</span></a></li>
                  <li><a class="dropdown-item" href="#!"><i class="me-2 ri-pencil-line"></i>Edit</a></li>
                  <li><a class="dropdown-item delete-btn" data-id="${i.id}" href="#!"><i class="me-2 ri-delete-bin-line"></i><span>Delete</span></a></li>
                </ul>
              </div>
              <div class="d-flex align-items-center gap-3">
                <img src="${i.avatar}" loading="lazy" alt="" class="rounded-2 flex-shrink-0 size-20">
                <div class="flex-grow-1 overflow-hidden">
                  <h6 class="mb-2"><a href="#!" class="text-reset">${i.name}</a></h6>
                  <p class="mb-1 text-muted text-truncate"><i class="ri-mail-line me-2"></i><span>${i.email}</span></p>
                  <p class="text-muted"><i class="ri-phone-line me-2"></i><span>${i.phone}</span></p>
                </div>
              </div>
            </div>
          </div>
        `,s.appendChild(l)}),this.updateResultsCount()}updateResultsCount(){const e=document.querySelector("#showingResults");if(e){const a=(this.currentPage-1)*this.itemsPerPage+1,t=Math.min(a+this.itemsPerPage-1,this.filteredData.length);e.innerHTML=`Showing <b class="me-1">${a}-${t}</b>of<b class="ms-1">${this.filteredData.length}</b> Results`}}renderPagination(){const e=Math.ceil(this.filteredData.length/this.itemsPerPage),a=document.querySelector(".pagination");if(!a)return;a.innerHTML="";const t=document.createElement("li");t.className=`page-item ${this.currentPage===1?"disabled":""}`,t.innerHTML='<a class="page-link" href="#!"><i data-lucide="chevron-left" class="size-4"></i> Previous</a>',t.addEventListener("click",i=>{i.preventDefault(),this.currentPage>1&&this.goToPage(this.currentPage-1)}),a.appendChild(t);for(let i=1;i<=e;i++){const l=document.createElement("li");l.className=`page-item ${this.currentPage===i?"active":""}`,l.innerHTML=`<a class="page-link" href="#!">${i}</a>`,l.addEventListener("click",r=>{r.preventDefault(),this.goToPage(i)}),a.appendChild(l)}const s=document.createElement("li");s.className=`page-item ${this.currentPage===e?"disabled":""}`,s.innerHTML='<a class="page-link" href="#!">Next <i data-lucide="chevron-right" class="size-4"></i></a>',s.addEventListener("click",i=>{i.preventDefault(),this.currentPage<e&&this.goToPage(this.currentPage+1)}),a.appendChild(s),o({icons:d})}handleSearch(e){const a=e.target.value.toLowerCase().trim();a?this.filteredData=this.data.filter(t=>this.searchFields.some(s=>{const i=t[s];return i&&String(i).toLowerCase().includes(a)})):this.filteredData=[...this.data],this.currentPage=1,this.renderTable(),this.renderPagination()}goToPage(e){this.currentPage=e,this.renderTable(),this.renderPagination(),this.container&&this.container.scrollIntoView({behavior:"smooth"})}deleteItem(e){this.data=this.data.filter(t=>t.id!==e),this.filteredData=this.filteredData.filter(t=>t.id!==e);const a=Math.ceil(this.filteredData.length/this.itemsPerPage);this.currentPage>a&&a>0&&(this.currentPage=a),this.renderTable(),this.renderPagination()}addItem(e){e.id||(e.id=this.generateUniqueId()),this.data.push(e),this.filteredData=[...this.data],this.renderTable(),this.renderPagination()}generateUniqueId(){return Date.now().toString(36)+Math.random().toString(36).substr(2)}updateItem(e,a){this.data=this.data.map(t=>t.id===e?{...t,...a}:t),this.filteredData=this.filteredData.map(t=>t.id===e?{...t,...a}:t),this.renderTable()}}const c=[{id:"1",name:"Alice Johnson",email:"<EMAIL>",phone:"+33 1 42 68 53 00",avatar:"assets/images/avatar/user-1.png"},{id:"2",name:"Alice Johnson",email:"<EMAIL>",phone:"+44 20 7946 0958",avatar:"assets/images/avatar/user-2.png"},{id:"3",name:"Michael Wilson",email:"<EMAIL>",phone:"+61 2 9374 4000",avatar:"assets/images/avatar/user-3.png"},{id:"4",name:"Alice Johnson",email:"<EMAIL>",phone:"****** 123 4567",avatar:"assets/images/avatar/user-4.png"},{id:"5",name:"Sarah Moore",email:"<EMAIL>",phone:"+44 20 7946 0958",avatar:"assets/images/avatar/user-5.png"},{id:"6",name:"Jane Smith",email:"<EMAIL>",phone:"+33 1 42 68 53 00",avatar:"assets/images/avatar/user-6.png"},{id:"7",name:"Michael Wilson",email:"<EMAIL>",phone:"+81 3 1234 5678",avatar:"assets/images/avatar/user-7.png"},{id:"8",name:"Robert Brown",email:"<EMAIL>",phone:"****** 123 4567",avatar:"assets/images/avatar/user-8.png"},{id:"9",name:"Michael Wilson",email:"<EMAIL>",phone:"+61 3 9876 5432",avatar:"assets/images/avatar/user-9.png"},{id:"10",name:"David Taylor",email:"<EMAIL>",phone:"+34 91 123 45 67",avatar:"assets/images/avatar/user-10.png"},{id:"11",name:"Emily Davis",email:"<EMAIL>",phone:"****** 555 1234",avatar:"assets/images/avatar/user-11.png"},{id:"12",name:"David Taylor",email:"<EMAIL>",phone:"+44 20 7946 0958",avatar:"assets/images/avatar/user-12.png"},{id:"13",name:"Jane Smith",email:"<EMAIL>",phone:"****** 123 4567",avatar:"assets/images/avatar/user-13.png"},{id:"14",name:"Jane Smith",email:"<EMAIL>",phone:"+81 3 1234 5678",avatar:"assets/images/avatar/user-14.png"},{id:"15",name:"Thomas Martin",email:"<EMAIL>",phone:"+49 30 123456",avatar:"assets/images/avatar/user-15.png"},{id:"16",name:"Sarah Moore",email:"<EMAIL>",phone:"+34 91 123 45 67",avatar:"assets/images/avatar/user-16.png"},{id:"17",name:"Lisa Anderson",email:"<EMAIL>",phone:"+33 1 42 68 53 00",avatar:"assets/images/avatar/user-17.png"},{id:"18",name:"Lisa Anderson",email:"<EMAIL>",phone:"+44 20 7946 0958",avatar:"assets/images/avatar/user-18.png"},{id:"19",name:"Thomas Martin",email:"<EMAIL>",phone:"+61 2 9374 4000",avatar:"assets/images/avatar/user-19.png"},{id:"20",name:"David Taylor",email:"<EMAIL>",phone:"+49 89 1234567",avatar:"assets/images/avatar/user-20.png"},{id:"21",name:"Michael Wilson",email:"<EMAIL>",phone:"****** 555 1234",avatar:"assets/images/avatar/user-21.png"},{id:"22",name:"Lisa Anderson",email:"<EMAIL>",phone:"+34 91 123 45 67",avatar:"assets/images/avatar/user-22.png"},{id:"23",name:"Emily Davis",email:"<EMAIL>",phone:"+61 3 9876 5432",avatar:"assets/images/avatar/user-23.png"},{id:"24",name:"Emily Davis",email:"<EMAIL>",phone:"+49 30 123456",avatar:"assets/images/avatar/user-24.png"},{id:"25",name:"Robert Brown",email:"<EMAIL>",phone:"+81 3 1234 5678",avatar:"assets/images/avatar/user-25.png"},{id:"26",name:"Sarah Moore",email:"<EMAIL>",phone:"+61 2 9374 4000",avatar:"assets/images/avatar/user-26.png"},{id:"27",name:"John Doe",email:"<EMAIL>",phone:"+49 89 1234567",avatar:"assets/images/avatar/user-27.png"}];document.addEventListener("DOMContentLoaded",()=>{const n=new m({data:c,containerId:"patientTableContainer",itemsPerPage:16,searchFields:["name","email","phone"]});window.tableManager=n});
