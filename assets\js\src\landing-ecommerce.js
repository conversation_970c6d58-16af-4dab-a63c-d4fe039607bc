import"../../admin.bundle-DI9_jvUJ.js";document.addEventListener("DOMContentLoaded",function(){const c=document.querySelector("#navbarEcommerce"),l=100;function n(){window.scrollY>l?c.classList.add("scroll-sticky"):c.classList.remove("scroll-sticky")}let o;window.addEventListener("scroll",function(){window.clearTimeout(o),o=setTimeout(n,50)},!1),n()});document.addEventListener("DOMContentLoaded",function(){var c=document.getElementById("theme-toggle-btn"),l=document.getElementById("moon-icon"),n=document.getElementById("sun-icon"),o=localStorage.getItem("theme")||"light";document.documentElement.setAttribute("data-bs-theme",o),l.style.display=o==="light"?"block":"none",n.style.display=o==="dark"?"block":"none",c.addEventListener("click",function(){var s=document.documentElement.getAttribute("data-bs-theme"),e=s==="dark"?"light":"dark";document.documentElement.setAttribute("data-bs-theme",e),localStorage.setItem("theme",e),l.style.display=e==="light"?"block":"none",n.style.display=e==="dark"?"block":"none"})});document.addEventListener("DOMContentLoaded",function(){const c=document.querySelectorAll(".navbar-nav .nav-link");c.forEach(e=>{e.addEventListener("click",function(){c.forEach(a=>a.classList.remove("active")),this.classList.add("active")})});const l=document.querySelectorAll("#filterTabs .nav-link");l.forEach(e=>{e.addEventListener("click",function(){l.forEach(t=>t.classList.remove("active")),this.classList.add("active");const a=this.id.replace("Products","").toLowerCase();n(a)})});function n(e){document.querySelectorAll(".row.g-8 > [data-category]").forEach(t=>{if(e==="all")t.style.display="block";else{const d=t.getAttribute("data-category");t.style.display=d===e?"block":"none"}const r=document.querySelector(".product-toolbar a.active").id;s(r)})}const o=document.querySelectorAll(".product-toolbar a");o.forEach(e=>{e.addEventListener("click",function(a){a.preventDefault(),o.forEach(t=>t.classList.remove("active")),this.classList.add("active"),s(this.id)})});function s(e){document.querySelectorAll(".row.g-8 > [data-category]").forEach(t=>{t.classList.remove("col-xxl-3","col-xxl-4"),e==="columnsFour"?t.classList.add("col-xxl-3"):e==="columnsThree"&&t.classList.add("col-xxl-4")})}n("all"),s("columnsFour")});new SlickImageCompare("#beforeAfterImages");
