/**
 * prism.js default theme for JavaScript, CSS and HTML
 * Based on dabblet (http://dabblet.com)
 * <AUTHOR> Verou
 */

code[class*="language-"],
pre[class*="language-"] {
	color: black;
	background: none;
	text-shadow: 0 1px white;
	font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
	font-size: 1em;
	text-align: left;
	white-space: pre;
	word-spacing: normal;
	word-break: normal;
	word-wrap: normal;
	line-height: 1.5;

	-moz-tab-size: 4;
	-o-tab-size: 4;
	tab-size: 4;

	-webkit-hyphens: none;
	-moz-hyphens: none;
	-ms-hyphens: none;
	hyphens: none;
}

pre[class*="language-"]::-moz-selection, pre[class*="language-"] ::-moz-selection,
code[class*="language-"]::-moz-selection, code[class*="language-"] ::-moz-selection {
	text-shadow: none;
	background: #b3d4fc;
}

pre[class*="language-"]::selection, pre[class*="language-"] ::selection,
code[class*="language-"]::selection, code[class*="language-"] ::selection {
	text-shadow: none;
	background: #b3d4fc;
}

@media print {
	code[class*="language-"],
	pre[class*="language-"] {
		text-shadow: none;
	}
}

/* Code blocks */
pre[class*="language-"] {
	padding: 1em;
	margin: .5em 0;
	overflow: auto;
}

:not(pre) > code[class*="language-"],
pre[class*="language-"] {
	background: #f5f2f0;
}

/* Inline code */
:not(pre) > code[class*="language-"] {
	padding: .1em;
	border-radius: .3em;
	white-space: normal;
}

.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
	color: slategray;
}

.token.punctuation {
	color: #999;
}

.token.namespace {
	opacity: .7;
}

.token.property,
.token.tag,
.token.boolean,
.token.number,
.token.constant,
.token.symbol,
.token.deleted {
	color: #905;
}

.token.selector,
.token.attr-name,
.token.string,
.token.char,
.token.builtin,
.token.inserted {
	color: #690;
}

.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string {
	color: #9a6e3a;
	/* This background color was intended by the author of this theme. */
	background: hsla(0, 0%, 100%, .5);
}

.token.atrule,
.token.attr-value,
.token.keyword {
	color: #07a;
}

.token.function,
.token.class-name {
	color: #DD4A68;
}

.token.regex,
.token.important,
.token.variable {
	color: #e90;
}

.token.important,
.token.bold {
	font-weight: bold;
}
.token.italic {
	font-style: italic;
}

.token.entity {
	cursor: help;
}

:host ::slotted([slot=code]) {
  display: none;
}

:host(.deckgo-highlight-code-carbon) {
  /**
   * @prop --deckgo-highlight-code-carbon-display: The display property of the host container
   * @default block
   */
  display: var(--deckgo-highlight-code-carbon-display, block);
  /**
   * @prop --deckgo-highlight-code-carbon-overflow: The overflow property of the host container.
   * @default auto
   */
  overflow: var(--deckgo-highlight-code-carbon-overflow, auto);
  /**
   * @prop --deckgo-highlight-code-carbon-border: The border property of the host container
   */
  border: var(--deckgo-highlight-code-carbon-border);
  /**
   * @prop --deckgo-highlight-code-carbon-border-radius: The border-radius property of the host container
   * @default 4px
   */
  border-radius: var(--deckgo-highlight-code-carbon-border-radius, 4px);
  /**
   * @prop --deckgo-highlight-code-carbon-background: The background property of the host container
   * @default #282a36
   */
  background: var(--deckgo-highlight-code-carbon-background, #282a36);
  /**
   * @prop --deckgo-highlight-code-carbon-color: The color property of the host container
   * @default white
   */
  color: var(--deckgo-highlight-code-carbon-color, white);
  /**
   * @prop --deckgo-highlight-code-carbon-box-shadow: The box-shadow property of the host container
   * @default rgba(0, 0, 0, 0.55) 0 8px 16px)
   */
  box-shadow: var(--deckgo-highlight-code-carbon-box-shadow, 0 4px 16px 0 rgba(0, 0, 0, 0.12));
  /**
   * @prop --deckgo-highlight-code-carbon-margin: The margin property of the host container
   * @default 16px 0
   */
  margin: var(--deckgo-highlight-code-carbon-margin, 16px 0);
}
:host(.deckgo-highlight-code-carbon) div.container {
  /**
   * @prop --deckgo-highlight-code-margin: The margin property of the code container
   * @default 0 0 1em
   */
  margin: var(--deckgo-highlight-code-margin, 0 0 1em);
}

:host(.deckgo-highlight-code-carbon) ::slotted([slot=code]) {
  /**
   * @prop --deckgo-highlight-code-carbon-color: The color property of the host container
   * @default white
   */
  color: var(--deckgo-highlight-code-carbon-color, white);
}

:host(.deckgo-highlight-code-ubuntu) {
  /**
   * @prop --deckgo-highlight-code-ubuntu-display: The display property of the host container
   * @default block
   */
  display: var(--deckgo-highlight-code-ubuntu-display, block);
  /**
   * @prop --deckgo-highlight-code-ubuntu-overflow: The overflow property of the host container
   * @default auto
   */
  overflow: var(--deckgo-highlight-code-ubuntu-overflow, auto);
  /**
   * @prop --deckgo-highlight-code-ubuntu-border: The border property of the host container
   */
  border: var(--deckgo-highlight-code-ubuntu-border);
  /**
   * @prop --deckgo-highlight-code-ubuntu-border-radius: The border-radius property of the host container
   * @default 6px 6px 0 0
   */
  border-radius: var(--deckgo-highlight-code-ubuntu-border-radius, 6px 6px 0 0);
  /**
   * @prop --deckgo-highlight-code-ubuntu-background: The background property of the host container
   * @default #4c1e3d
   */
  background: var(--deckgo-highlight-code-ubuntu-background, #4c1e3d);
  /**
   * @prop --deckgo-highlight-code-ubuntu-color: The color property of the host container
   * @default #ddd
   */
  color: var(--deckgo-highlight-code-ubuntu-color, #ddd);
  /**
   * @prop --deckgo-highlight-code-ubuntu-box-shadow: The box-shadow property of the host container
   * @default 2px 4px 10px rgba(0, 0, 0, 0.5)
   */
  box-shadow: var(--deckgo-highlight-code-ubuntu-box-shadow, 0 4px 16px 0 rgba(0, 0, 0, 0.12));
  /**
   * @prop --deckgo-highlight-code-ubuntu-margin: The margin property of the host container
   * @default 16px 0
   */
  margin: var(--deckgo-highlight-code-ubuntu-margin, 16px 0);
}
:host(.deckgo-highlight-code-ubuntu) div.container {
  margin: var(--deckgo-highlight-code-margin, 0 0 16px);
  /**
   * @prop --deckgo-highlight-code-padding: The padding of the displayed code
   * @default 2px 0 0
   */
  padding: var(--deckgo-highlight-code-padding, 2px 0 0);
  background: transparent;
}
:host(.deckgo-highlight-code-ubuntu) div.container code {
  /**
   * @prop --deckgo-highlight-code-font-family: Ubuntu font family
   * @default Ubuntu mono
   */
  font-family: var(--deckgo-highlight-code-font-family, "Ubuntu mono");
}
:host(.deckgo-highlight-code-ubuntu) div.container code > div.line-number:before {
  /**
   * @prop --deckgo-highlight-code-ubuntu-background: Ubunut line number background
   * @default #4c1e3d
   */
  background: var(--deckgo-highlight-code-ubuntu-background, #4c1e3d);
}

:host(.deckgo-highlight-code-ubuntu) ::slotted([slot=code]) {
  /**
   * @prop --deckgo-highlight-code-ubuntu-color: Ubuntu color
   * @default #ddd
   */
  color: var(--deckgo-highlight-code-ubuntu-color, #ddd);
}

div.container {
  /**
   * @prop --deckgo-highlight-code-color: Ubuntu container color
   * @default inherit
   */
  color: var(--deckgo-highlight-code-color, inherit);
  /**
   * @prop --deckgo-highlight-code-background: Ubuntu code background
   */
  background: var(--deckgo-highlight-code-background);
  padding: var(--deckgo-highlight-code-padding, 0 16px);
  /**
   * @prop --deckgo-highlight-code-border-radius: The border radius of the displayed code
   */
  border-radius: var(--deckgo-highlight-code-border-radius);
  margin: var(--deckgo-highlight-code-margin, 16px 0);
  transform-origin: bottom left;
  transition: all 0.2s ease-in-out;
  /**
   * @prop --deckgo-highlight-code-zoom: If you wish to manually zoom the code
   * @default 1
   */
  transform: scale(var(--deckgo-highlight-code-zoom, 1));
  /**
   * @prop --deckgo-highlight-code-direction: The direction of the displayed code
   * @default ltr
   */
  direction: var(--deckgo-highlight-code-direction, ltr);
  /**
   * @prop --deckgo-highlight-code-text-align: The text alignment of your code
   * @default start
   */
  text-align: var(--deckgo-highlight-code-text-align, start);
  /**
   * @prop --deckgo-highlight-code-container-width: The attribute width of the code's container
   */
  width: var(--deckgo-highlight-code-container-width);
  /**
   * @prop --deckgo-highlight-code-container-height: The attribute height of the code's container
   */
  height: var(--deckgo-highlight-code-container-height);
  /**
   * @prop --deckgo-highlight-code-container-display: The attribute display of the code's container
   * @default bloack
   */
  display: var(--deckgo-highlight-code-container-display, block);
  /**
   * @prop --deckgo-highlight-code-container-justify-content: The attribute justify-content of the code's container
   */
  justify-content: var(--deckgo-highlight-code-container-justify-content);
  /**
   * @prop --deckgo-highlight-code-container-flex-direction: The attribute flex-direction of the code's container
   */
  flex-direction: var(--deckgo-highlight-code-container-flex-direction);
  /**
   * @prop --deckgo-highlight-code-container-align-items: The attribute align-items of the code's container
   */
  align-items: var(--deckgo-highlight-code-container-align-items);
}
div.container code {
  /**
   * @prop --deckgo-highlight-code-scroll: In case you would like to change the scroll property of the shadowed code block
   * @default auto
   */
  overflow-y: var(--deckgo-highlight-code-scroll, auto);
  /**
   * @prop --deckgo-highlight-code-white-space: The attribute white-space of the displayed
   * @default pre-wrap
   */
  white-space: var(--deckgo-highlight-code-white-space, pre-wrap);
  /**
   * @prop --deckgo-highlight-code-font-size: The size of the font for the code
   */
  font-size: var(--deckgo-highlight-code-font-size);
  /**
   * @prop --deckgo-highlight-code-font-family: The family of the font for the code
   * @default monospace
   */
  font-family: var(--deckgo-highlight-code-font-family, monospace);
  /**
   * @prop --deckgo-highlight-code-line-height: The line height of the font for the code
   */
  line-height: var(--deckgo-highlight-code-line-height);
  /**
   * @prop --deckgo-highlight-code-display: The display property of the code
   * @default block
   */
  display: var(--deckgo-highlight-code-display, block);
  counter-reset: linenumber;
  /**
   * @prop --deckgo-highlight-code-height: Height property of the shadowed code block
   * @default 100%
   */
  height: var(--deckgo-highlight-code-height, 100%);
  /**
   * @prop --deckgo-highlight-code-width: Width property of the shadowed code block
   */
  width: var(--deckgo-highlight-code-width);
  /**
   * @prop --deckgo-highlight-code-line-background: The background of the lines you wish to highlight
   */
  /**
   * @prop --deckgo-highlight-code-line-border-top: The border-top property of the lines you wish to highlight
   */
  /**
   * @prop --deckgo-highlight-code-line-border-bottom: The border-bottom property of the lines you wish to highlight
   */
  /**
   * @prop --deckgo-highlight-code-line-font-weight: The font-weight of the lines you wish to highlight
   */
  /**
   * @prop --deckgo-highlight-code-line-opacity: The opacity of the lines you do wish to highlight
   * @default 1
   */
  /**
   * @prop --deckgo-highlight-code-line-numbers-color: The color property of the line numbers
   * @default var(--deckgo-highlight-code-token-comment, #6272a4)
  */
  /* Prism style override */
}
div.container code > div.line-number {
  counter-increment: linenumber;
  position: relative;
  padding-left: 3.5em;
}
div.container code > div.line-number:before {
  content: counter(linenumber);
  display: inline-block;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  width: 2.5em;
  /**
   * @prop --deckgo-highlight-code-line-numbers-background: The background property of the line numbers
   */
  background: var(--deckgo-highlight-code-line-numbers-background);
  /**
   * @prop --deckgo-highlight-code-line-numbers-border-right: The border right property of the line numbers
   * @default 1px solid rgba(var(--deckgo-highlight-code-token-comment-rgb, 98, 114, 164), 0.32)
  */
  border-right: var(--deckgo-highlight-code-line-numbers-border-right, 1px solid rgba(var(--deckgo-highlight-code-token-comment-rgb, 98, 114, 164), 0.32));
  /**
    * @prop --deckgo-lowlight-code-line-numbers-color: The color property of the line numbers if not highlighted
    * @default rgba(var(var(--deckgo-highlight-code-token-comment-rgb, 98, 114, 164), 0.32))
  */
  color: var(--deckgo-lowlight-code-line-numbers-color, rgba(var(--deckgo-highlight-code-token-comment-rgb, 98, 114, 164), 0.32));
}
div.container code span.deckgo-highlight-code-anchor-hidden {
  visibility: hidden;
}
div.container code.highlight div > * {
  /**
   * @prop --deckgo-lowlight-code-line-color: The color of the lines you do not with to highlight
   */
  color: var(--deckgo-lowlight-code-line-color);
  /**
   * @prop --deckgo-lowlight-code-line-background: The background of the lines you do not wish to highlight
   */
  background: var(--deckgo-lowlight-code-line-background);
  /**
   * @prop --deckgo-lowlight-code-line-border-top: The border-top property of the lines you do not wish to highlight
   */
  border-top: var(--deckgo-lowlight-code-line-border-top);
  /**
   * @prop --deckgo-lowlight-code-line-border-bottom: The border-bottom property of the lines you do not wish to highlight
   */
  border-bottom: var(--deckgo-lowlight-code-line-border-bottom);
  /**
   * @prop --deckgo-lowlight-code-line-font-weight: The font-weight of the lines you do not wish to highlight
   */
  font-weight: var(--deckgo-lowlight-code-line-font-weight);
  /**
   * @prop --deckgo-lowlight-code-line-opacity: The opacity of the lines you do not wish to highlight
   * @default 0.32
   */
  opacity: var(--deckgo-lowlight-code-line-opacity, 0.32);
}
div.container code.highlight.animate div > * {
  /**
   * --deckgo-highlight-code-line-transition: Transition between lowlight and highlight
   * @default all 0.35s ease-in
   */
  transition: var(--deckgo-highlight-code-line-transition, all 0.35s ease-in);
}
div.container code.highlight.animate > div.line-number:before {
  transition: var(--deckgo-highlight-code-line-transition, all 0.35s ease-in);
}
div.container code .language-css .token.string:not(.deckgo-highlight-code-line),
div.container code .style .token.string:not(.deckgo-highlight-code-line),
div.container code .token.entity:not(.deckgo-highlight-code-line),
div.container code .token.operator:not(.deckgo-highlight-code-line),
div.container code .token.url:not(.deckgo-highlight-code-line) {
  background: inherit;
}
div.container code .token.comment,
div.container code .token.prolog,
div.container code .token.doctype,
div.container code .token.cdata {
  /**
   * @prop --deckgo-highlight-code-token-comment: Highlighted code tokens comment, prolog, doctype and cdata
   * @default #6272a4
   */
  color: var(--deckgo-highlight-code-token-comment, #6272a4);
}
div.container code .token.punctuation {
  /**
   * @prop --deckgo-highlight-code-token-punctuation: Highlighted code token punctuation
   * @default #6272a4
   */
  color: var(--deckgo-highlight-code-token-punctuation, #6272a4);
}
div.container code .token.property,
div.container code .token.tag,
div.container code .token.boolean,
div.container code .token.number,
div.container code .token.constant,
div.container code .token.symbol,
div.container code .token.deleted {
  /**
   * @prop --deckgo-highlight-code-token-property: Highlighted code tokens property, tag, boolean, number, constant, symbol, deleted
   * @default #bd93f9
   */
  color: var(--deckgo-highlight-code-token-property, #bd93f9);
}
div.container code .token.selector,
div.container code .token.attr-name,
div.container code .token.string,
div.container code .token.char,
div.container code .token.builtin,
div.container code .token.inserted {
  /**
   * @prop --deckgo-highlight-code-token-selector: Highlighted code tokens selector, attr-name, string, char, builtin, inserted
   * @default #50fa7b
   */
  color: var(--deckgo-highlight-code-token-selector, #50fa7b);
}
div.container code .token.operator,
div.container code .token.entity,
div.container code .token.url,
div.container code .language-css .token.string,
div.container code .style .token.string {
  /**
   * @prop --deckgo-highlight-code-token-operator: Highlighted code tokens operator, entity, url, string
   * @default #ff79c6
   */
  color: var(--deckgo-highlight-code-token-operator, #ff79c6);
}
div.container code .token.atrule,
div.container code .token.attr-value,
div.container code .token.keyword {
  /**
   * @prop --deckgo-highlight-code-token-atrule: Highlighted code tokens atrule, attr-value, keyword
   * @default #ff79c6
   */
  color: var(--deckgo-highlight-code-token-atrule, #ff79c6);
}
div.container code .token.function,
div.container code .token.class-name {
  /**
   * @prop --deckgo-highlight-code-token-function: Highlighted code function, class-name
   * @default #ffb86c
   */
  color: var(--deckgo-highlight-code-token-function, #ffb86c);
}
div.container code .token.regex,
div.container code .token.important,
div.container code .token.variable {
  /**
   * @prop --deckgo-highlight-code-token-regex: Highlighted code tokens regex, important, variable
   * @default #f1fa8c
   */
  color: var(--deckgo-highlight-code-token-regex, #f1fa8c);
}

div.carbon {
  display: flex;
  justify-content: flex-start;
  /**
   * @prop --deckgo-highlight-code-carbon-header-padding: The padding property of the card header.
   * @default 8px 16px
   */
  padding: var(--deckgo-highlight-code-carbon-header-padding, 0.5em 1em);
  /**
   * @prop --deckgo-highlight-code-carbon-header-margin: 	The margin property of the card header
   * @default 0
   */
  margin: var(--deckgo-highlight-code-carbon-header-margin, 0);
}
div.carbon > div {
  /**
   * @prop --deckgo-highlight-code-carbon-toolbar-display: The display property of the toolbar container
   * @default bloack
   */
  display: var(--deckgo-highlight-code-carbon-toolbar-display, block);
  /**
   * @prop --deckgo-highlight-code-carbon-header-button-width: The width of a button of the card header
   * @default 0.75em
   */
  width: var(--deckgo-highlight-code-carbon-header-button-width, 0.75em);
  /**
   * @prop --deckgo-highlight-code-carbon-header-button-height: The height of a button of the card header
   * @default 0.75em
   */
  height: var(--deckgo-highlight-code-carbon-header-button-height, 0.75em);
  /**
   * @prop --deckgo-highlight-code-carbon-header-button-border-radius: 	The border-radius of a button of the card header
   * @default 50%
   */
  border-radius: var(--deckgo-highlight-code-carbon-header-button-border-radius, 50%);
  /**
   * @prop --deckgo-highlight-code-carbon-header-button-margin: The margin of a button of the card header
   * @default 0.5em 0.375em 0.5em 0
   */
  margin: var(--deckgo-highlight-code-carbon-header-button-margin, 0.5em 0.375em 0.5em 0);
}
div.carbon > div.red {
  /**
   * @prop --deckgo-highlight-code-carbon-header-button-red-background: The background of the first button of the card header
   * @default #ff5f56
   */
  background: var(--deckgo-highlight-code-carbon-header-button-red-background, #ff5f56);
}
div.carbon > div.yellow {
  /**
   * @prop --deckgo-highlight-code-carbon-header-button-yellow-background: The background of the second button of the card header
   * @default #ffbd2e
   */
  background: var(--deckgo-highlight-code-carbon-header-button-yellow-background, #ffbd2e);
}
div.carbon > div.green {
  /**
   * @prop --deckgo-highlight-code-carbon-header-button-green-background: The background of the third button of the card header
   * @default #27c93f
   */
  background: var(--deckgo-highlight-code-carbon-header-button-green-background, #27c93f);
}

div.ubuntu {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  /**
   * @prop --deckgo-highlight-code-ubuntu-header-padding: The padding property of the card header.
   * @default 0 0.5em
   */
  padding: var(--deckgo-highlight-code-ubuntu-header-padding, 0 0.5em);
  /**
   * @prop --deckgo-highlight-code-ubuntu-header-height: The height property of the card header
   * @default 25px
   */
  height: var(--deckgo-highlight-code-ubuntu-header-height, 25px);
  /**
   * @prop --deckgo-highlight-code-ubuntu-header-background: The background property of the card header
   * @default linear-gradient(#504b45 0%, #3c3b37 100%)
   */
  background: var(--deckgo-highlight-code-ubuntu-header-background, linear-gradient(#504b45 0%, #3c3b37 100%));
  /**
   * @prop --deckgo-highlight-code-ubuntu-header-font-family: The font-family property of the card header
   * @default "Ubuntu"
   */
  font-family: var(--deckgo-highlight-code-ubuntu-header-font-family, "Ubuntu");
}
div.ubuntu > div {
  display: flex;
  align-items: center;
  justify-content: center;
  /**
   * @prop --deckgo-highlight-code-ubuntu-header-button-width: The width of a button of the card header
   * @default 12px
   */
  width: var(--deckgo-highlight-code-ubuntu-header-button-width, 12px);
  /**
   * @prop --deckgo-highlight-code-ubuntu-header-button-height: The height of a button of the card header
   * @default 12px
   */
  height: var(--deckgo-highlight-code-ubuntu-header-button-height, 12px);
  /**
   * @prop --deckgo-highlight-code-ubuntu-header-button-border-radius: The border-radius of a button of the card header
   * @default 50%
   */
  border-radius: var(--deckgo-highlight-code-ubuntu-header-button-border-radius, 50%);
  /**
   * @prop --deckgo-highlight-code-ubuntu-header-button-margin: The margin of a button of the card header
   * @default 0 0.25em 0 0
   */
  margin: var(--deckgo-highlight-code-ubuntu-header-button-margin, 0 0.25em 0 0);
  /**
   * @prop --deckgo-highlight-code-ubuntu-header-button-font-size: The font-size of a button of the card header
   * @default 0.4375em
   */
  font-size: var(--deckgo-highlight-code-ubuntu-header-button-font-size, 0.4375em);
  /**
   * @prop --deckgo-highlight-code-ubuntu-header-button-color: The color of a button of the card header
   * @default black
   */
  color: var(--deckgo-highlight-code-ubuntu-header-button-color, black);
  /**
   * @prop --deckgo-highlight-code-ubuntu-header-button-text-shadow: The text-shadow of a button of the card header
   * @default 0px 1px 0px rgba(255, 255, 255, 0.2)
   */
  text-shadow: var(--deckgo-highlight-code-ubuntu-header-button-text-shadow, 0px 1px 0px rgba(255, 255, 255, 0.2));
  /**
   * @prop --deckgo-highlight-code-ubuntu-header-button-box-shadow: The box-shadow of a button of the card header
   * @default  0px 0px 1px 0px #41403a, 0px 1px 1px 0px #474642
   */
  box-shadow: var(--deckgo-highlight-code-ubuntu-header-button-box-shadow, 0px 0px 1px 0px #41403a, 0px 1px 1px 0px #474642);
}
div.ubuntu > div.close {
  /**
   * @prop --deckgo-highlight-code-ubuntu-header-button-close-background: The close button background of the card header
   * @default linear-gradient(#f37458 0%, #de4c12 100%)
   */
  background: var(--deckgo-highlight-code-ubuntu-header-button-close-background, linear-gradient(#f37458 0%, #de4c12 100%));
  /**
   * @prop --deckgo-highlight-code-ubuntu-header-button-close-border: The close button border of the card header
   */
  border: var(--deckgo-highlight-code-ubuntu-header-button-close-border);
}
div.ubuntu > div.minimize {
  /**
   * @prop --deckgo-highlight-code-ubuntu-header-button-minimize-background: The minimize button background of the card header
   * @default linear-gradient(#7d7871 0%, #595953 100%)
   */
  background: var(--deckgo-highlight-code-ubuntu-header-button-minimize-background, linear-gradient(#7d7871 0%, #595953 100%));
  /**
   * @prop --deckgo-highlight-code-ubuntu-header-button-minimize-border: The minimize button border of the card header
   */
  border: var(--deckgo-highlight-code-ubuntu-header-button-minimize-border);
}
div.ubuntu > div.maximize {
  /**
   * @prop --deckgo-highlight-code-ubuntu-header-button-maximize-background: The maximize button background of the card header
   * @default linear-gradient(#7d7871 0%, #595953 100%)
   */
  background: var(--deckgo-highlight-code-ubuntu-header-button-maximize-background, linear-gradient(#7d7871 0%, #595953 100%));
  /**
   * @prop --deckgo-highlight-code-ubuntu-header-button-maximize-border: The maximize button border of the card header
   */
  border: var(--deckgo-highlight-code-ubuntu-header-button-maximize-border);
}
div.ubuntu > div.close span, div.ubuntu > div.minimize span, div.ubuntu > div.maximize span {
  /**
   * @prop --deckgo-highlight-code-ubuntu-header-button-span-display: The Ubuntu buttons span display
   * @default inherit
   */
  display: var(--deckgo-highlight-code-ubuntu-header-button-span-display, inherit);
}
div.ubuntu > p {
  /**
   * @prop --deckgo-highlight-code-ubuntu-header-user-color: The user's color of the card header.
   * @default #d5d0ce
   */
  color: var(--deckgo-highlight-code-ubuntu-header-user-color, #d5d0ce);
  /**
   * @prop --deckgo-highlight-code-ubuntu-header-user-font-size: The user's font-size of the card header
   * @default 12px
   */
  font-size: var(--deckgo-highlight-code-ubuntu-header-user-font-size, 12px);
  /**
   * @prop --deckgo-highlight-code-ubuntu-header-user-line-height: The user's line-height of the card header
   * @default 14px
   */
  line-height: var(--deckgo-highlight-code-ubuntu-header-user-line-height, 14px);
  /**
   * @prop --deckgo-highlight-code-ubuntu-header-user-margin: The user's margin of the card header
   * @default 0 0 1px 4px
   */
  margin: var(--deckgo-highlight-code-ubuntu-header-user-margin, 0 0 1px 4px);
}

:host(.deckgo-highlight-code-papyrs) {
  display: block;
  overflow: auto;
  border-radius: 0;
  background: #172121;
  color: white;
  border: 1px solid black;
  box-shadow: 3px 3px black;
  margin: 1.25rem 0;
  --deckgo-highlight-code-token-atrule: #fde74c;
  --deckgo-highlight-code-token-comment: #ef476f;
  --deckgo-highlight-code-token-comment-rgb: 92, 158, 173;
  --deckgo-highlight-code-token-function: white;
  --deckgo-highlight-code-token-operator: #fde74c;
  --deckgo-highlight-code-token-property: #ef476f;
  --deckgo-highlight-code-token-punctuation: #ef476f;
  --deckgo-highlight-code-token-selector: #7dbbc3;
  --deckgo-highlight-code-token-regex: #fff6bc;
}
:host(.deckgo-highlight-code-papyrs) div.container {
  margin: 0.75rem 0;
}

:host(.deckgo-highlight-code-carbon) ::slotted([slot=code]) {
  color: white;
}