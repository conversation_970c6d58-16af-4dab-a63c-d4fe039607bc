import{c as h,i as u}from"../../admin.bundle-DI9_jvUJ.js";/* empty css                               */import"../../main-Cyta4iCA.js";const d=[{id:"PEC-19115",category:"Fashion",image:"assets/images/products/img-01.png",quantity:154,status:"Active"},{id:"PEC-19116",category:"Electronics",image:"assets/images/products/img-24.png",quantity:187,status:"Active"},{id:"PEC-19117",category:"Footwear",image:"assets/images/products/img-03.png",quantity:487,status:"Inactive"},{id:"PEC-19118",category:"Furniture",image:"assets/images/products/img-23.png",quantity:177,status:"Inactive"},{id:"PEC-19119",category:"Groceries",image:"assets/images/products/img-20.png",quantity:183,status:"Active"},{id:"PEC-19120",image:"assets/images/products/img-21.png",category:"Books",quantity:326,status:"Active"},{id:"PEC-19121",image:"assets/images/products/img-22.png",category:"Food and beverage",quantity:147,status:"Inactive"},{id:"PEC-19122",image:"assets/images/products/img-19.png",category:"Jewellery",quantity:98,status:"Active"},{id:"PEC-19123",image:"assets/images/products/img-18.png",category:"Sports",quantity:246,status:"Active"},{id:"PEC-19124",image:"assets/images/products/img-17.png",category:"Accessories",quantity:213,status:"Inactive"},{id:"PEC-19125",category:"Fashion",image:"assets/images/products/img-01.png",quantity:154,status:"Active"},{id:"PEC-19126",category:"Electronics",image:"assets/images/products/img-24.png",quantity:187,status:"Active"},{id:"PEC-19127",category:"Footwear",image:"assets/images/products/img-03.png",quantity:487,status:"Inactive"},{id:"PEC-19128",category:"Furniture",image:"assets/images/products/img-23.png",quantity:177,status:"Inactive"},{id:"PEC-19129",category:"Groceries",image:"assets/images/products/img-20.png",quantity:183,status:"Active"},{id:"PEC-19130",image:"assets/images/products/img-21.png",category:"Books",quantity:326,status:"Active"},{id:"PEC-19131",image:"assets/images/products/img-22.png",category:"Food and beverage",quantity:147,status:"Inactive"},{id:"PEC-19132",image:"assets/images/products/img-19.png",category:"Jewellery",quantity:98,status:"Active"},{id:"PEC-19133",image:"assets/images/products/img-18.png",category:"Sports",quantity:246,status:"Active"},{id:"PEC-19134",image:"assets/images/products/img-17.png",category:"Accessories",quantity:213,status:"Inactive"}];VirtualSelect.init({ele:"#statusSelect",options:[{label:"Active",value:"Active"},{label:"Inactive",value:"Inactive"}]});class g{constructor(){if(this.table=document.querySelector(".table"),this.tableBody=this.table?this.table.querySelector("tbody"):null,this.tableHead=this.table?this.table.querySelector("thead"):null,this.categoryForm=document.querySelector("#categoryForm"),this.deleteModal=document.getElementById("deleteModal"),!this.table||!this.tableBody||!this.categoryForm){console.error("Required DOM elements are missing.");return}this.bulkDeleteButton=document.querySelector("button#deleteCategory"),this.searchInput=document.getElementById("searchCategoryInput"),this.paginationContainer=document.querySelector(".pagination"),this.resultsInfoElement=document.querySelector("#showingResults"),this.imageInput=document.getElementById("imageInput"),this.imagePreview=document.getElementById("imagePreview"),this.uploadText=document.getElementById("uploadText"),this.uploadIcon=document.getElementById("uploadIcon"),this.categoryNameInput=this.categoryForm.querySelector('input[type="email"]'),this.descriptionInput=this.categoryForm.querySelector('textarea[name="description"]'),this.statusSelect=document.getElementById("statusSelect"),this.resetButton=this.categoryForm.querySelector("button#resetBtn"),this.addButton=this.categoryForm.querySelector("button#addCategoryBtn"),this.deleteConfirmButton=this.deleteModal?this.deleteModal.querySelector("button#confirmDeleteBtn"):null,this.editMode=!1,this.currentEditId=null,this.categories=[],this.lastId=0,this.currentPage=1,this.itemsPerPage=10,this.filteredCategories=[],this.searchTerm="",this.selectedItems=new Set,this.currentSortField=null,this.currentSortDirection="asc",this.init()}init(){this.loadDataFromJSON(),this.setupEventListeners(),this.initStatusSelect(),this.setupSortableHeaders(),this.filteredCategories=[...this.categories],this.renderPaginatedCategories(),window.lucide&&window.lucide.createIcons()}loadDataFromJSON(){this.categories=Array.isArray(d)?d:[],this.categories.length>0&&(this.lastId=Math.max(...this.categories.map(e=>{const t=e.id.match(/PEC-(\d+)/);return t?parseInt(t[1]):0})))}setupEventListeners(){this.imageInput&&this.imageInput.addEventListener("change",this.handleImageUpload.bind(this)),this.addButton&&this.addButton.addEventListener("click",this.handleFormSubmit.bind(this)),this.resetButton&&this.resetButton.addEventListener("click",this.resetForm.bind(this)),this.deleteConfirmButton&&this.deleteConfirmButton.addEventListener("click",this.confirmDelete.bind(this)),this.searchInput&&this.searchInput.addEventListener("input",this.debounce(this.handleSearch.bind(this),300)),this.bulkDeleteButton&&this.bulkDeleteButton.addEventListener("click",this.bulkDelete.bind(this));const e=document.getElementById("checkAllData");e&&this.tableBody&&e.addEventListener("change",t=>{t.target.checked?this.filteredCategories.forEach(s=>{this.selectedItems.add(s.id)}):this.selectedItems.clear(),this.tableBody.querySelectorAll('input[type="checkbox"]').forEach(s=>{const a=s.closest("tr");a&&a.dataset.categoryId&&(s.checked=this.selectedItems.has(a.dataset.categoryId))}),this.updateBulkDeleteButtonVisibility()}),this.tableBody&&this.tableBody.addEventListener("change",t=>{t.target.type==="checkbox"&&(this.handleCheckboxChange(t.target),this.updateBulkDeleteButtonVisibility())}),document.addEventListener("click",this.handleGlobalClick.bind(this))}setupSortableHeaders(){if(!this.tableHead)return;const e=this.tableHead.querySelectorAll("th");[{index:1,field:"id",label:"Category ID"},{index:2,field:"category",label:"Category Name"},{index:3,field:"quantity",label:"Products"},{index:4,field:"status",label:"Status"}].forEach(i=>{if(e[i.index]){const s=e[i.index];s.classList.add("sortable"),s.style.cursor="pointer";const a=s.textContent.trim();s.innerHTML=`
                    <div class="d-flex align-items-center">
                        <span>${a}</span>
                        <span class="sort-icon ms-1">
                            <i class="ri-arrow-up-down-line text-muted opacity-50"></i>
                        </span>
                    </div>
                `,s.addEventListener("click",()=>{this.handleSortClick(i.field)})}})}handleSortClick(e){this.currentSortField===e?this.currentSortDirection=this.currentSortDirection==="asc"?"desc":"asc":(this.currentSortField=e,this.currentSortDirection="asc"),this.updateSortIndicators(e,this.currentSortDirection),this.sortCategories(e,this.currentSortDirection)}updateSortIndicators(e,t){if(!this.tableHead)return;const i=this.tableHead.querySelectorAll("th");i.forEach(n=>{const o=n.querySelector(".sort-icon");o&&(o.innerHTML='<i class="ri-arrow-up-down-line text-muted opacity-50"></i>')});const a={id:1,category:2,quantity:3,status:4}[e];if(a!==void 0&&i[a]){const n=i[a].querySelector(".sort-icon");n&&(n.innerHTML=t==="asc"?'<i class="ri-arrow-up-line"></i>':'<i class="ri-arrow-down-line"></i>')}}handleGlobalClick(e){const t=e.target;if(t.closest('a[class*="edit"]')||t.closest(".dropdown-item")&&t.closest(".dropdown-item").innerHTML.includes("Edit")){e.preventDefault();const i=t.closest("tr");if(i){const s=i.dataset.categoryId;this.editCategory(s)}}if(t.closest('a[href="#deleteModal"]')||t.closest(".dropdown-item")&&t.closest(".dropdown-item").innerHTML.includes("Delete")){e.preventDefault();const i=t.closest("tr");if(i){const s=i.dataset.categoryId;this.openDeleteModal(s)}}if(t.closest(".page-link")){e.preventDefault();const i=t.closest(".page-link");if(i.textContent.includes("Previous"))this.goToPreviousPage();else if(i.textContent.includes("Next"))this.goToNextPage();else{const s=parseInt(i.textContent);isNaN(s)||this.goToPage(s)}}}handleCheckboxChange(e){const t=e.closest("tr");if(!t)return;const i=t.dataset.categoryId;i&&(e.checked?this.selectedItems.add(i):this.selectedItems.delete(i),this.updateBulkDeleteButtonVisibility())}updateBulkDeleteButtonVisibility(){this.bulkDeleteButton&&(this.selectedItems.size>0?this.bulkDeleteButton.classList.remove("d-none"):this.bulkDeleteButton.classList.add("d-none"))}debounce(e,t){let i;return function(...s){clearTimeout(i),i=setTimeout(()=>e.apply(this,s),t)}}updateCheckAllState(){const e=document.getElementById("checkAllData");if(!e)return;const t=this.filteredCategories.length>0&&this.filteredCategories.every(i=>this.selectedItems.has(i.id));e.checked=t}handleSearch(e){const t=e.target.value.trim().toLowerCase();this.searchTerm=t,this.currentPage=1,this.filterCategories(t),this.renderPaginatedCategories(),this.updateBulkDeleteButtonVisibility()}filterCategories(e){e?this.filteredCategories=this.categories.filter(t=>t.category.toLowerCase().includes(e.toLowerCase())||t.id.toLowerCase().includes(e.toLowerCase())||t.description&&t.description.toLowerCase().includes(e.toLowerCase())):this.filteredCategories=[...this.categories]}goToPreviousPage(){this.currentPage>1&&(this.currentPage--,this.renderPaginatedCategories())}goToNextPage(){const e=Math.ceil(this.filteredCategories.length/this.itemsPerPage);this.currentPage<e&&(this.currentPage++,this.renderPaginatedCategories())}goToPage(e){const t=Math.ceil(this.filteredCategories.length/this.itemsPerPage);e>=1&&e<=t&&(this.currentPage=e,this.renderPaginatedCategories())}renderPagination(){const e=this.filteredCategories.length,t=Math.ceil(e/this.itemsPerPage),i=(this.currentPage-1)*this.itemsPerPage+1,s=Math.min(this.currentPage*this.itemsPerPage,e);if(this.resultsInfoElement&&(this.resultsInfoElement.innerHTML=`Showing <b class="me-1">${e>0?i:0}-${s}</b>of<b class="ms-1">${e}</b> Results`),this.paginationContainer){const a=this.createPaginationHTML(t);this.paginationContainer.innerHTML=a,h({icons:u})}}createPaginationHTML(e){let t="";t+=`<li class="page-item${this.currentPage===1?" disabled":""}">
            <a class="page-link" href="#!"><i data-lucide="chevron-left" class="size-4"></i> Previous</a>
        </li>`;for(let i=1;i<=e;i++)t+=`<li class="page-item${i===this.currentPage?" active":""}">
                <a class="page-link" href="#!">${i}</a>
            </li>`;return t+=`<li class="page-item${this.currentPage===e||e===0?" disabled":""}">
            <a class="page-link" href="#!">Next <i data-lucide="chevron-right" class="size-4"></i></a>
        </li>`,t}renderPaginatedCategories(){if(!this.tableBody)return;this.tableBody.innerHTML="";const e=(this.currentPage-1)*this.itemsPerPage,t=Math.min(e+this.itemsPerPage,this.filteredCategories.length),i=this.filteredCategories.slice(e,t);if(i.length===0){const s=document.createElement("tr");s.innerHTML=`
                 <td colspan="6" class="text-center py-4">
                    <div class="d-flex flex-column align-items-center">
                 <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" class="mx-auto size-12" viewBox="0 0 48 48">
                                                <linearGradient id="SVGID_1__h35ynqzIJzH4_gr1" x1="34.598" x2="15.982" y1="15.982" y2="34.598" gradientUnits="userSpaceOnUse">
                                                    <stop offset="0" stop-color="#60e8fe"></stop>
                                                    <stop offset=".033" stop-color="#6ae9fe"></stop>
                                                    <stop offset=".197" stop-color="#97f0fe"></stop>
                                                    <stop offset=".362" stop-color="#bdf5ff"></stop>
                                                    <stop offset=".525" stop-color="#dafaff"></stop>
                                                    <stop offset=".687" stop-color="#eefdff"></stop>
                                                    <stop offset=".846" stop-color="#fbfeff"></stop>
                                                    <stop offset="1" stop-color="#fff"></stop>
                                                </linearGradient>
                                                <path fill="url(#SVGID_1__h35ynqzIJzH4_gr1)" d="M40.036,33.826L31.68,25.6c0.847-1.739,1.335-3.684,1.335-5.748c0-7.27-5.894-13.164-13.164-13.164	S6.688,12.582,6.688,19.852c0,7.27,5.894,13.164,13.164,13.164c2.056,0,3.995-0.485,5.728-1.326l3.914,4.015l4.331,4.331	c1.715,1.715,4.496,1.715,6.211,0C41.751,38.321,41.751,35.541,40.036,33.826z"></path>
                                                <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M31.95,25.739l8.086,8.086c1.715,1.715,1.715,4.496,0,6.211l0,0c-1.715,1.715-4.496,1.715-6.211,0	l-4.331-4.331"></path>
                                                <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M7.525,24.511c-1.771-4.694-0.767-10.196,3.011-13.975c3.847-3.847,9.48-4.817,14.228-2.912"></path>
                                                <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M30.856,12.603c3.376,5.114,2.814,12.063-1.688,16.565c-4.858,4.858-12.565,5.129-17.741,0.814"></path>
                                            </svg>
                                            <p class="mt-2 text-center text-gray-500 dark:text-dark-500">No matching records found</p>
                        <p class="text-muted mb-0">We couldn't find any categories matching your search.</p>
                    </div>
                </td>
            `,this.tableBody.appendChild(s)}else{const s=document.createDocumentFragment();i.forEach(a=>{const n=this.createCategoryRow(a),o=n.querySelector('input[type="checkbox"]');o&&(o.checked=this.selectedItems.has(a.id)),s.appendChild(n)}),this.tableBody.appendChild(s)}this.updateCheckAllState(),this.renderPagination(),this.updateBulkDeleteButtonVisibility()}handleImageUpload(e){const t=e.target.files[0];if(t&&this.imagePreview&&this.uploadText){const i=new FileReader;i.onload=s=>{this.imagePreview.src=s.target.result,this.imagePreview.style.display="block",this.uploadText.classList.add("d-none")},i.readAsDataURL(t)}}resetForm(){this.categoryForm&&this.categoryForm.reset(),this.imagePreview&&(this.imagePreview.style.display="none"),this.uploadText&&this.uploadText.classList.remove("d-none"),this.editMode=!1,this.currentEditId=null,this.addButton&&(this.addButton.textContent="Add Category")}handleFormSubmit(e){if(e.preventDefault(),!this.categoryNameInput){console.error("Category name input element not found");return}const t=this.categoryNameInput.value,i=this.descriptionInput?this.descriptionInput.value:"",s=this.getSelectedStatus(),a=this.imagePreview&&this.imagePreview.style.display!=="none"?this.imagePreview.src:"assets/images/products/img-01.png",n=r=>{const c=document.getElementById("alertContainer");c&&(c.innerHTML=`
                    <div class="alert alert-danger alert-dismissible fade show" role="alert" id="formAlert">
                        <span>${r}</span>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                `)};if(document.querySelector('input[placeholder="Category Name"]').addEventListener("input",()=>{const r=document.getElementById("formAlert");r&&r.remove()}),!t){n("Please enter a category name");return}if(this.editMode&&this.currentEditId){const r=this.getCategoryById(this.currentEditId);r&&this.updateCategory({...r,category:t,description:i,status:s,image:a})}else this.addCategory({id:`PEC-${++this.lastId}`,category:t,description:i,status:s,image:a,quantity:0});this.resetForm()}getSelectedStatus(){return this.statusSelect.options[this.statusSelect.selectedIndex].textContent}initStatusSelect(){const e=["Active","Inactive","Draft"],t=document.createElement("select");t.className="form-select",e.forEach(i=>{const s=document.createElement("option");s.value=i.toLowerCase(),s.textContent=i,t.appendChild(s)}),this.statusSelect.parentNode.replaceChild(t,this.statusSelect),this.statusSelect=t}createCategoryRow(e){const t=document.createElement("tr");t.dataset.categoryId=e.id;const i=e.status.toLowerCase()==="active"?"success":e.status.toLowerCase()==="draft"?"warning":"danger";return t.innerHTML=`
            <td>
                <div class="form-check check-primary">
                    <input class="form-check-input" type="checkbox" aria-label="Check Data Checkbox" id="check${e.id}">
                    <label class="form-check-label d-none" for="check${e.id}">
                        ${e.category}
                    </label>
                </div>
            </td>
            <td><a href="#!" class="link link-custom-primary">${e.id}</a></td>
            <td>
                <div class="d-flex align-items-center gap-2">
                    <div class="avatar size-9 border rounded p-1">
                        <img src="${e.image}" loading="lazy" alt="${e.category}" class="img-fluid rounded-pill">
                    </div>
                    <h6 class="mb-0"><a href="#" class="text-reset">${e.category}</a></h6>
                </div>
            </td>
            <td>${e.quantity}</td>
            <td><span class="badge bg-${i}-subtle text-${i} border border-${i}-subtle">${e.status}</span></td>
            <td>
                <div class="dropdown">
                    <a href="#!" class="link link-custom-primary" type="button" data-bs-toggle="dropdown" aria-expanded="true" title="dropdown-button">
                        <i class="ri-more-2-fill"></i>
                    </a>
                    <ul class="dropdown-menu">
                        <li>
                            <a href="#!" class="dropdown-item d-flex gap-3 align-items-center">
                                <i class="ri-eye-line"></i>
                                <span>Overview</span>
                            </a>
                        </li>
                        <li>
                            <a href="#!" class="dropdown-item d-flex gap-3 align-items-center edit-category">
                                <i class="ri-pencil-line"></i>
                                Edit
                            </a>
                        </li>
                        <li>
                            <a href="#deleteModal" data-bs-toggle="modal" class="dropdown-item d-flex gap-3 align-items-center delete-category">
                                <i class="ri-delete-bin-line"></i>
                                <span>Delete</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </td>
        `,t}addCategory(e){this.categories.unshift(e),this.filterCategories(this.searchTerm),this.currentPage=1,this.renderPaginatedCategories()}getCategoryById(e){return this.categories.find(t=>t.id===e)}editCategory(e){const t=this.getCategoryById(e);if(t){if(this.categoryNameInput&&(this.categoryNameInput.value=t.category),this.descriptionInput&&(this.descriptionInput.value=t.description||""),t.status){for(let i=0;i<this.statusSelect.options.length;i++)if(this.statusSelect.options[i].textContent===t.status){this.statusSelect.selectedIndex=i;break}}t.image&&this.imagePreview&&this.uploadText&&(this.imagePreview.src=t.image,this.imagePreview.style.display="block",this.uploadText.classList.add("d-none")),this.editMode=!0,this.currentEditId=e,this.addButton&&(this.addButton.textContent="Update Category"),this.categoryForm&&this.categoryForm.scrollIntoView({behavior:"smooth"})}}updateCategory(e){const t=this.categories.findIndex(i=>i.id===e.id);t!==-1&&(this.categories[t]=e,this.filterCategories(this.searchTerm),this.renderPaginatedCategories())}openDeleteModal(e){this.currentEditId=e}confirmDelete(){if(this.currentEditId&&(this.deleteCategory(this.currentEditId),this.currentEditId=null,typeof bootstrap<"u"&&this.deleteModal)){const e=window.bootstrap.Modal.getInstance(this.deleteModal);e&&e.hide()}}deleteCategory(e){const t=this.categories.findIndex(i=>i.id===e);if(t!==-1){this.categories.splice(t,1),this.filterCategories(this.searchTerm);const i=Math.ceil(this.filteredCategories.length/this.itemsPerPage);this.currentPage>i&&i>0&&(this.currentPage=i),this.renderPaginatedCategories()}}sortCategories(e,t="asc"){const s={name:"category",products:"quantity",imageUrl:"image"}[e]||e;this.categories.sort((a,n)=>{let o=a[s],r=n[s];return typeof o=="string"&&(o=o.toLowerCase()),typeof r=="string"&&(r=r.toLowerCase()),o<r?t==="asc"?-1:1:o>r?t==="asc"?1:-1:0}),this.filterCategories(this.searchTerm),this.renderPaginatedCategories()}bulkDelete(){confirm(`Are you sure you want to delete ${this.selectedItems.size} selected categories?`)&&(Array.from(this.selectedItems).forEach(e=>{this.deleteCategory(e)}),this.selectedItems.clear(),this.updateBulkDeleteButtonVisibility())}bulkChangeStatus(e){Array.from(this.selectedItems).forEach(t=>{const i=this.getCategoryById(t);i&&this.updateCategory({...i,status:e})})}}document.addEventListener("DOMContentLoaded",()=>{const l=new g;window.tableManager=l});
