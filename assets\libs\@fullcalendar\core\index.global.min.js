/*!
FullCalendar Core v6.1.17
Docs & License: https://fullcalendar.io
(c) 2024 <PERSON>
*/
var FullCalendar=function(e){"use strict";const t=[],n=new Map;function r(e){t.push(e),n.forEach(t=>{o(t,e)})}function i(e){let r=n.get(e);if(!r||!r.isConnected){if(r=e.querySelector("style[data-fullcalendar]"),!r){r=document.createElement("style"),r.setAttribute("data-fullcalendar","");const t=function(){void 0===s&&(s=function(){const e=document.querySelector('meta[name="csp-nonce"]');if(e&&e.hasAttribute("content"))return e.getAttribute("content");const t=document.querySelector("script[nonce]");if(t)return t.nonce||"";return""}());return s}();t&&(r.nonce=t);const n=e===document?document.head:e,i=e===document?n.querySelector("script,link[rel=stylesheet],link[as=style],style"):n.firstChild;n.insertBefore(r,i)}n.set(e,r),function(e){for(const n of t)o(e,n)}(r)}}function o(e,t){const{sheet:n}=e,r=n.cssRules.length;t.split("}").forEach((e,t)=>{(e=e.trim())&&n.insertRule(e+"}",r+t)})}let s;"undefined"!=typeof document&&i(document);function a(e){e.parentNode&&e.parentNode.removeChild(e)}function l(e,t){if(e.closest)return e.closest(t);if(!document.documentElement.contains(e))return null;do{if(c(e,t))return e;e=e.parentElement||e.parentNode}while(null!==e&&1===e.nodeType);return null}function c(e,t){return(e.matches||e.matchesSelector||e.msMatchesSelector).call(e,t)}function u(e,t){let n=e instanceof HTMLElement?[e]:e,r=[];for(let e=0;e<n.length;e+=1){let i=n[e].querySelectorAll(t);for(let e=0;e<i.length;e+=1)r.push(i[e])}return r}r(':root{--fc-small-font-size:.85em;--fc-page-bg-color:#fff;--fc-neutral-bg-color:hsla(0,0%,82%,.3);--fc-neutral-text-color:grey;--fc-border-color:#ddd;--fc-button-text-color:#fff;--fc-button-bg-color:#2c3e50;--fc-button-border-color:#2c3e50;--fc-button-hover-bg-color:#1e2b37;--fc-button-hover-border-color:#1a252f;--fc-button-active-bg-color:#1a252f;--fc-button-active-border-color:#151e27;--fc-event-bg-color:#3788d8;--fc-event-border-color:#3788d8;--fc-event-text-color:#fff;--fc-event-selected-overlay-color:rgba(0,0,0,.25);--fc-more-link-bg-color:#d0d0d0;--fc-more-link-text-color:inherit;--fc-event-resizer-thickness:8px;--fc-event-resizer-dot-total-width:8px;--fc-event-resizer-dot-border-width:1px;--fc-non-business-color:hsla(0,0%,84%,.3);--fc-bg-event-color:#8fdf82;--fc-bg-event-opacity:0.3;--fc-highlight-color:rgba(188,232,241,.3);--fc-today-bg-color:rgba(255,220,40,.15);--fc-now-indicator-color:red}.fc-not-allowed,.fc-not-allowed .fc-event{cursor:not-allowed}.fc{display:flex;flex-direction:column;font-size:1em}.fc,.fc *,.fc :after,.fc :before{box-sizing:border-box}.fc table{border-collapse:collapse;border-spacing:0;font-size:1em}.fc th{text-align:center}.fc td,.fc th{padding:0;vertical-align:top}.fc a[data-navlink]{cursor:pointer}.fc a[data-navlink]:hover{text-decoration:underline}.fc-direction-ltr{direction:ltr;text-align:left}.fc-direction-rtl{direction:rtl;text-align:right}.fc-theme-standard td,.fc-theme-standard th{border:1px solid var(--fc-border-color)}.fc-liquid-hack td,.fc-liquid-hack th{position:relative}@font-face{font-family:fcicons;font-style:normal;font-weight:400;src:url("data:application/x-font-ttf;charset=utf-8;base64,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") format("truetype")}.fc-icon{speak:none;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;display:inline-block;font-family:fcicons!important;font-style:normal;font-variant:normal;font-weight:400;height:1em;line-height:1;text-align:center;text-transform:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;width:1em}.fc-icon-chevron-left:before{content:"\\e900"}.fc-icon-chevron-right:before{content:"\\e901"}.fc-icon-chevrons-left:before{content:"\\e902"}.fc-icon-chevrons-right:before{content:"\\e903"}.fc-icon-minus-square:before{content:"\\e904"}.fc-icon-plus-square:before{content:"\\e905"}.fc-icon-x:before{content:"\\e906"}.fc .fc-button{border-radius:0;font-family:inherit;font-size:inherit;line-height:inherit;margin:0;overflow:visible;text-transform:none}.fc .fc-button:focus{outline:1px dotted;outline:5px auto -webkit-focus-ring-color}.fc .fc-button{-webkit-appearance:button}.fc .fc-button:not(:disabled){cursor:pointer}.fc .fc-button{background-color:transparent;border:1px solid transparent;border-radius:.25em;display:inline-block;font-size:1em;font-weight:400;line-height:1.5;padding:.4em .65em;text-align:center;-webkit-user-select:none;-moz-user-select:none;user-select:none;vertical-align:middle}.fc .fc-button:hover{text-decoration:none}.fc .fc-button:focus{box-shadow:0 0 0 .2rem rgba(44,62,80,.25);outline:0}.fc .fc-button:disabled{opacity:.65}.fc .fc-button-primary{background-color:var(--fc-button-bg-color);border-color:var(--fc-button-border-color);color:var(--fc-button-text-color)}.fc .fc-button-primary:hover{background-color:var(--fc-button-hover-bg-color);border-color:var(--fc-button-hover-border-color);color:var(--fc-button-text-color)}.fc .fc-button-primary:disabled{background-color:var(--fc-button-bg-color);border-color:var(--fc-button-border-color);color:var(--fc-button-text-color)}.fc .fc-button-primary:focus{box-shadow:0 0 0 .2rem rgba(76,91,106,.5)}.fc .fc-button-primary:not(:disabled).fc-button-active,.fc .fc-button-primary:not(:disabled):active{background-color:var(--fc-button-active-bg-color);border-color:var(--fc-button-active-border-color);color:var(--fc-button-text-color)}.fc .fc-button-primary:not(:disabled).fc-button-active:focus,.fc .fc-button-primary:not(:disabled):active:focus{box-shadow:0 0 0 .2rem rgba(76,91,106,.5)}.fc .fc-button .fc-icon{font-size:1.5em;vertical-align:middle}.fc .fc-button-group{display:inline-flex;position:relative;vertical-align:middle}.fc .fc-button-group>.fc-button{flex:1 1 auto;position:relative}.fc .fc-button-group>.fc-button.fc-button-active,.fc .fc-button-group>.fc-button:active,.fc .fc-button-group>.fc-button:focus,.fc .fc-button-group>.fc-button:hover{z-index:1}.fc-direction-ltr .fc-button-group>.fc-button:not(:first-child){border-bottom-left-radius:0;border-top-left-radius:0;margin-left:-1px}.fc-direction-ltr .fc-button-group>.fc-button:not(:last-child){border-bottom-right-radius:0;border-top-right-radius:0}.fc-direction-rtl .fc-button-group>.fc-button:not(:first-child){border-bottom-right-radius:0;border-top-right-radius:0;margin-right:-1px}.fc-direction-rtl .fc-button-group>.fc-button:not(:last-child){border-bottom-left-radius:0;border-top-left-radius:0}.fc .fc-toolbar{align-items:center;display:flex;justify-content:space-between}.fc .fc-toolbar.fc-header-toolbar{margin-bottom:1.5em}.fc .fc-toolbar.fc-footer-toolbar{margin-top:1.5em}.fc .fc-toolbar-title{font-size:1.75em;margin:0}.fc-direction-ltr .fc-toolbar>*>:not(:first-child){margin-left:.75em}.fc-direction-rtl .fc-toolbar>*>:not(:first-child){margin-right:.75em}.fc-direction-rtl .fc-toolbar-ltr{flex-direction:row-reverse}.fc .fc-scroller{-webkit-overflow-scrolling:touch;position:relative}.fc .fc-scroller-liquid{height:100%}.fc .fc-scroller-liquid-absolute{bottom:0;left:0;position:absolute;right:0;top:0}.fc .fc-scroller-harness{direction:ltr;overflow:hidden;position:relative}.fc .fc-scroller-harness-liquid{height:100%}.fc-direction-rtl .fc-scroller-harness>.fc-scroller{direction:rtl}.fc-theme-standard .fc-scrollgrid{border:1px solid var(--fc-border-color)}.fc .fc-scrollgrid,.fc .fc-scrollgrid table{table-layout:fixed;width:100%}.fc .fc-scrollgrid table{border-left-style:hidden;border-right-style:hidden;border-top-style:hidden}.fc .fc-scrollgrid{border-bottom-width:0;border-collapse:separate;border-right-width:0}.fc .fc-scrollgrid-liquid{height:100%}.fc .fc-scrollgrid-section,.fc .fc-scrollgrid-section table,.fc .fc-scrollgrid-section>td{height:1px}.fc .fc-scrollgrid-section-liquid>td{height:100%}.fc .fc-scrollgrid-section>*{border-left-width:0;border-top-width:0}.fc .fc-scrollgrid-section-footer>*,.fc .fc-scrollgrid-section-header>*{border-bottom-width:0}.fc .fc-scrollgrid-section-body table,.fc .fc-scrollgrid-section-footer table{border-bottom-style:hidden}.fc .fc-scrollgrid-section-sticky>*{background:var(--fc-page-bg-color);position:sticky;z-index:3}.fc .fc-scrollgrid-section-header.fc-scrollgrid-section-sticky>*{top:0}.fc .fc-scrollgrid-section-footer.fc-scrollgrid-section-sticky>*{bottom:0}.fc .fc-scrollgrid-sticky-shim{height:1px;margin-bottom:-1px}.fc-sticky{position:sticky}.fc .fc-view-harness{flex-grow:1;position:relative}.fc .fc-view-harness-active>.fc-view{bottom:0;left:0;position:absolute;right:0;top:0}.fc .fc-col-header-cell-cushion{display:inline-block;padding:2px 4px}.fc .fc-bg-event,.fc .fc-highlight,.fc .fc-non-business{bottom:0;left:0;position:absolute;right:0;top:0}.fc .fc-non-business{background:var(--fc-non-business-color)}.fc .fc-bg-event{background:var(--fc-bg-event-color);opacity:var(--fc-bg-event-opacity)}.fc .fc-bg-event .fc-event-title{font-size:var(--fc-small-font-size);font-style:italic;margin:.5em}.fc .fc-highlight{background:var(--fc-highlight-color)}.fc .fc-cell-shaded,.fc .fc-day-disabled{background:var(--fc-neutral-bg-color)}a.fc-event,a.fc-event:hover{text-decoration:none}.fc-event.fc-event-draggable,.fc-event[href]{cursor:pointer}.fc-event .fc-event-main{position:relative;z-index:2}.fc-event-dragging:not(.fc-event-selected){opacity:.75}.fc-event-dragging.fc-event-selected{box-shadow:0 2px 7px rgba(0,0,0,.3)}.fc-event .fc-event-resizer{display:none;position:absolute;z-index:4}.fc-event-selected .fc-event-resizer,.fc-event:hover .fc-event-resizer{display:block}.fc-event-selected .fc-event-resizer{background:var(--fc-page-bg-color);border-color:inherit;border-radius:calc(var(--fc-event-resizer-dot-total-width)/2);border-style:solid;border-width:var(--fc-event-resizer-dot-border-width);height:var(--fc-event-resizer-dot-total-width);width:var(--fc-event-resizer-dot-total-width)}.fc-event-selected .fc-event-resizer:before{bottom:-20px;content:"";left:-20px;position:absolute;right:-20px;top:-20px}.fc-event-selected,.fc-event:focus{box-shadow:0 2px 5px rgba(0,0,0,.2)}.fc-event-selected:before,.fc-event:focus:before{bottom:0;content:"";left:0;position:absolute;right:0;top:0;z-index:3}.fc-event-selected:after,.fc-event:focus:after{background:var(--fc-event-selected-overlay-color);bottom:-1px;content:"";left:-1px;position:absolute;right:-1px;top:-1px;z-index:1}.fc-h-event{background-color:var(--fc-event-bg-color);border:1px solid var(--fc-event-border-color);display:block}.fc-h-event .fc-event-main{color:var(--fc-event-text-color)}.fc-h-event .fc-event-main-frame{display:flex}.fc-h-event .fc-event-time{max-width:100%;overflow:hidden}.fc-h-event .fc-event-title-container{flex-grow:1;flex-shrink:1;min-width:0}.fc-h-event .fc-event-title{display:inline-block;left:0;max-width:100%;overflow:hidden;right:0;vertical-align:top}.fc-h-event.fc-event-selected:before{bottom:-10px;top:-10px}.fc-direction-ltr .fc-daygrid-block-event:not(.fc-event-start),.fc-direction-rtl .fc-daygrid-block-event:not(.fc-event-end){border-bottom-left-radius:0;border-left-width:0;border-top-left-radius:0}.fc-direction-ltr .fc-daygrid-block-event:not(.fc-event-end),.fc-direction-rtl .fc-daygrid-block-event:not(.fc-event-start){border-bottom-right-radius:0;border-right-width:0;border-top-right-radius:0}.fc-h-event:not(.fc-event-selected) .fc-event-resizer{bottom:0;top:0;width:var(--fc-event-resizer-thickness)}.fc-direction-ltr .fc-h-event:not(.fc-event-selected) .fc-event-resizer-start,.fc-direction-rtl .fc-h-event:not(.fc-event-selected) .fc-event-resizer-end{cursor:w-resize;left:calc(var(--fc-event-resizer-thickness)*-.5)}.fc-direction-ltr .fc-h-event:not(.fc-event-selected) .fc-event-resizer-end,.fc-direction-rtl .fc-h-event:not(.fc-event-selected) .fc-event-resizer-start{cursor:e-resize;right:calc(var(--fc-event-resizer-thickness)*-.5)}.fc-h-event.fc-event-selected .fc-event-resizer{margin-top:calc(var(--fc-event-resizer-dot-total-width)*-.5);top:50%}.fc-direction-ltr .fc-h-event.fc-event-selected .fc-event-resizer-start,.fc-direction-rtl .fc-h-event.fc-event-selected .fc-event-resizer-end{left:calc(var(--fc-event-resizer-dot-total-width)*-.5)}.fc-direction-ltr .fc-h-event.fc-event-selected .fc-event-resizer-end,.fc-direction-rtl .fc-h-event.fc-event-selected .fc-event-resizer-start{right:calc(var(--fc-event-resizer-dot-total-width)*-.5)}.fc .fc-popover{box-shadow:0 2px 6px rgba(0,0,0,.15);position:absolute;z-index:9999}.fc .fc-popover-header{align-items:center;display:flex;flex-direction:row;justify-content:space-between;padding:3px 4px}.fc .fc-popover-title{margin:0 2px}.fc .fc-popover-close{cursor:pointer;font-size:1.1em;opacity:.65}.fc-theme-standard .fc-popover{background:var(--fc-page-bg-color);border:1px solid var(--fc-border-color)}.fc-theme-standard .fc-popover-header{background:var(--fc-neutral-bg-color)}');const d=/(top|left|right|bottom|width|height)$/i;function f(e,t){for(let n in t)h(e,n,t[n])}function h(e,t,n){null==n?e.style[t]="":"number"==typeof n&&d.test(t)?e.style[t]=n+"px":e.style[t]=n}function p(e){var t,n;return null!==(n=null===(t=e.composedPath)||void 0===t?void 0:t.call(e)[0])&&void 0!==n?n:e.target}let g=0;function m(){return g+=1,"fc-dom-"+g}function v(e){e.preventDefault()}function b(e,t,n,r){let i=function(e,t){return n=>{let r=l(n.target,e);r&&t.call(r,n,r)}}(n,r);return e.addEventListener(t,i),()=>{e.removeEventListener(t,i)}}const A=["webkitTransitionEnd","otransitionend","oTransitionEnd","msTransitionEnd","transitionend"];function y(e){return Object.assign({onClick:e},_(e))}function _(e){return{tabIndex:0,onKeyDown(t){"Enter"!==t.key&&" "!==t.key||(e(t),t.preventDefault())}}}let E=0;function D(){return E+=1,String(E)}function C(e){let t,n,r=[],i=[];for("string"==typeof e?i=e.split(/\s*,\s*/):"function"==typeof e?i=[e]:Array.isArray(e)&&(i=e),t=0;t<i.length;t+=1)n=i[t],"string"==typeof n?r.push("-"===n.charAt(0)?{field:n.substring(1),order:-1}:{field:n,order:1}):"function"==typeof n&&r.push({func:n});return r}function w(e,t,n){let r,i;for(r=0;r<n.length;r+=1)if(i=S(e,t,n[r]),i)return i;return 0}function S(e,t,n){return n.func?n.func(e,t):T(e[n.field],t[n.field])*(n.order||1)}function T(e,t){return e||t?null==t?-1:null==e?1:"string"==typeof e||"string"==typeof t?String(e).localeCompare(String(t)):e-t:0}function k(e,t){let n=String(e);return"000".substr(0,t-n.length)+n}function R(e,t,n){return"function"==typeof e?e(...t):"string"==typeof e?t.reduce((e,t,n)=>e.replace("$"+n,t||""),e):n}function x(e){return e%1==0}function O(e){let t=e.querySelector(".fc-scrollgrid-shrink-frame"),n=e.querySelector(".fc-scrollgrid-shrink-cushion");if(!t)throw new Error("needs fc-scrollgrid-shrink-frame className");if(!n)throw new Error("needs fc-scrollgrid-shrink-cushion className");return e.getBoundingClientRect().width-t.getBoundingClientRect().width+n.getBoundingClientRect().width}const M=["years","months","days","milliseconds"],I=/^(-?)(?:(\d+)\.)?(\d+):(\d\d)(?::(\d\d)(?:\.(\d\d\d))?)?/;function N(e,t){return"string"==typeof e?function(e){let t=I.exec(e);if(t){let e=t[1]?-1:1;return{years:0,months:0,days:e*(t[2]?parseInt(t[2],10):0),milliseconds:e*(60*(t[3]?parseInt(t[3],10):0)*60*1e3+60*(t[4]?parseInt(t[4],10):0)*1e3+1e3*(t[5]?parseInt(t[5],10):0)+(t[6]?parseInt(t[6],10):0))}}return null}(e):"object"==typeof e&&e?B(e):"number"==typeof e?B({[t||"milliseconds"]:e}):null}function B(e){let t={years:e.years||e.year||0,months:e.months||e.month||0,days:e.days||e.day||0,milliseconds:60*(e.hours||e.hour||0)*60*1e3+60*(e.minutes||e.minute||0)*1e3+1e3*(e.seconds||e.second||0)+(e.milliseconds||e.millisecond||e.ms||0)},n=e.weeks||e.week;return n&&(t.days+=7*n,t.specifiedWeeks=!0),t}function H(e){return P(e)/864e5}function P(e){return 31536e6*e.years+2592e6*e.months+864e5*e.days+e.milliseconds}function U(e){let t=e.milliseconds;if(t){if(t%1e3!=0)return{unit:"millisecond",value:t};if(t%6e4!=0)return{unit:"second",value:t/1e3};if(t%36e5!=0)return{unit:"minute",value:t/6e4};if(t)return{unit:"hour",value:t/36e5}}return e.days?e.specifiedWeeks&&e.days%7==0?{unit:"week",value:e.days/7}:{unit:"day",value:e.days}:e.months?{unit:"month",value:e.months}:e.years?{unit:"year",value:e.years}:{unit:"millisecond",value:0}}const{hasOwnProperty:j}=Object.prototype;function z(e,t){let n={};if(t)for(let r in t)if(t[r]===Ue){let t=[];for(let i=e.length-1;i>=0;i-=1){let o=e[i][r];if("object"==typeof o&&o)t.unshift(o);else if(void 0!==o){n[r]=o;break}}t.length&&(n[r]=z(t))}for(let t=e.length-1;t>=0;t-=1){let r=e[t];for(let e in r)e in n||(n[e]=r[e])}return n}function F(e,t){let n={};for(let r in e)t(e[r],r)&&(n[r]=e[r]);return n}function L(e,t){let n={};for(let r in e)n[r]=t(e[r],r);return n}function V(e){let t={};for(let n of e)t[n]=!0;return t}function W(e){let t=[];for(let n in e)t.push(e[n]);return t}function Q(e,t){if(e===t)return!0;for(let n in e)if(j.call(e,n)&&!(n in t))return!1;for(let n in t)if(j.call(t,n)&&e[n]!==t[n])return!1;return!0}const G=/^on[A-Z]/;function Z(e,t){let n=[];for(let r in e)j.call(e,r)&&(r in t||n.push(r));for(let r in t)j.call(t,r)&&e[r]!==t[r]&&n.push(r);return n}function q(e,t,n={}){if(e===t)return!0;for(let r in t)if(!(r in e)||!Y(e[r],t[r],n[r]))return!1;for(let n in e)if(!(n in t))return!1;return!0}function Y(e,t,n){return e===t||!0===n||!!n&&n(e,t)}function J(e,t=0,n,r=1){let i=[];null==n&&(n=Object.keys(e).length);for(let o=t;o<n;o+=r){let t=e[o];void 0!==t&&i.push(t)}return i}function $(e,t,n){if(e===t)return!0;let r,i=e.length;if(i!==t.length)return!1;for(r=0;r<i;r+=1)if(!(n?n(e[r],t[r]):e[r]===t[r]))return!1;return!0}const X=["sun","mon","tue","wed","thu","fri","sat"];function K(e,t){let n=ue(e);return n[2]+=t,de(n)}function ee(e,t){let n=ue(e);return n[6]+=t,de(n)}function te(e,t){return(t.valueOf()-e.valueOf())/864e5}function ne(e,t){let n=oe(e),r=oe(t);return{years:0,months:0,days:Math.round(te(n,r)),milliseconds:t.valueOf()-r.valueOf()-(e.valueOf()-n.valueOf())}}function re(e,t){let n=ie(e,t);return null!==n&&n%7==0?n/7:null}function ie(e,t){return he(e)===he(t)?Math.round(te(e,t)):null}function oe(e){return de([e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate()])}function se(e,t,n,r){let i=de([t,0,1+ae(t,n,r)]),o=oe(e),s=Math.round(te(i,o));return Math.floor(s/7)+1}function ae(e,t,n){let r=7+t-n;return-((7+de([e,0,r]).getUTCDay()-t)%7)+r-1}function le(e){return[e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()]}function ce(e){return new Date(e[0],e[1]||0,null==e[2]?1:e[2],e[3]||0,e[4]||0,e[5]||0)}function ue(e){return[e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate(),e.getUTCHours(),e.getUTCMinutes(),e.getUTCSeconds(),e.getUTCMilliseconds()]}function de(e){return 1===e.length&&(e=e.concat([0])),new Date(Date.UTC(...e))}function fe(e){return!isNaN(e.valueOf())}function he(e){return 1e3*e.getUTCHours()*60*60+1e3*e.getUTCMinutes()*60+1e3*e.getUTCSeconds()+e.getUTCMilliseconds()}function pe(e,t,n=!1){let r=e.toISOString();return r=r.replace(".000",""),n&&(r=r.replace("T00:00:00Z","")),r.length>10&&(null==t?r=r.replace("Z",""):0!==t&&(r=r.replace("Z",me(t,!0)))),r}function ge(e){return e.toISOString().replace(/T.*$/,"")}function me(e,t=!1){let n=e<0?"-":"+",r=Math.abs(e),i=Math.floor(r/60),o=Math.round(r%60);return t?`${n+k(i,2)}:${k(o,2)}`:`GMT${n}${i}${o?":"+k(o,2):""}`}function ve(e,t,n){let r,i;return function(...o){if(r){if(!$(r,o)){n&&n(i);let r=e.apply(this,o);t&&t(r,i)||(i=r)}}else i=e.apply(this,o);return r=o,i}}function be(e,t,n){let r,i;return o=>{if(r){if(!Q(r,o)){n&&n(i);let r=e.call(this,o);t&&t(r,i)||(i=r)}}else i=e.call(this,o);return r=o,i}}const Ae={week:3,separator:9,omitZeroMinute:9,meridiem:9,omitCommas:9},ye={timeZoneName:7,era:6,year:5,month:4,day:2,weekday:2,hour:1,minute:1,second:1},_e=/\s*([ap])\.?m\.?/i,Ee=/,/g,De=/\s+/g,Ce=/\u200e/g,we=/UTC|GMT/;class Se{constructor(e){let t={},n={},r=9;for(let i in e)if(i in Ae){n[i]=e[i];Ae[i]<9&&(r=Math.min(Ae[i],r))}else t[i]=e[i],i in ye&&(r=Math.min(ye[i],r));this.standardDateProps=t,this.extendedSettings=n,this.smallestUnitNum=r,this.buildFormattingFunc=ve(Te)}format(e,t){return this.buildFormattingFunc(this.standardDateProps,this.extendedSettings,t)(e)}formatRange(e,t,n,r){let{standardDateProps:i,extendedSettings:o}=this,s=function(e,t,n){if(n.getMarkerYear(e)!==n.getMarkerYear(t))return 5;if(n.getMarkerMonth(e)!==n.getMarkerMonth(t))return 4;if(n.getMarkerDay(e)!==n.getMarkerDay(t))return 2;if(he(e)!==he(t))return 1;return 0}(e.marker,t.marker,n.calendarSystem);if(!s)return this.format(e,n);let a=s;!(a>1)||"numeric"!==i.year&&"2-digit"!==i.year||"numeric"!==i.month&&"2-digit"!==i.month||"numeric"!==i.day&&"2-digit"!==i.day||(a=1);let l=this.format(e,n),c=this.format(t,n);if(l===c)return l;let u=Te(function(e,t){let n={};for(let r in e)(!(r in ye)||ye[r]<=t)&&(n[r]=e[r]);return n}(i,a),o,n),d=u(e),f=u(t),h=function(e,t,n,r){let i=0;for(;i<e.length;){let o=e.indexOf(t,i);if(-1===o)break;let s=e.substr(0,o);i=o+t.length;let a=e.substr(i),l=0;for(;l<n.length;){let e=n.indexOf(r,l);if(-1===e)break;let t=n.substr(0,e);l=e+r.length;let i=n.substr(l);if(s===t&&a===i)return{before:s,after:a}}}return null}(l,d,c,f),p=o.separator||r||n.defaultSeparator||"";return h?h.before+d+p+f+h.after:l+p+c}getSmallestUnit(){switch(this.smallestUnitNum){case 7:case 6:case 5:return"year";case 4:return"month";case 3:return"week";case 2:return"day";default:return"time"}}}function Te(e,t,n){let r=Object.keys(e).length;return 1===r&&"short"===e.timeZoneName?e=>me(e.timeZoneOffset):0===r&&t.week?e=>function(e,t,n,r,i){let o=[];"long"===i?o.push(n):"short"!==i&&"narrow"!==i||o.push(t);"long"!==i&&"short"!==i||o.push(" ");o.push(r.simpleNumberFormat.format(e)),"rtl"===r.options.direction&&o.reverse();return o.join("")}(n.computeWeekNumber(e.marker),n.weekText,n.weekTextLong,n.locale,t.week):function(e,t,n){e=Object.assign({},e),t=Object.assign({},t),function(e,t){e.timeZoneName&&(e.hour||(e.hour="2-digit"),e.minute||(e.minute="2-digit"));"long"===e.timeZoneName&&(e.timeZoneName="short");t.omitZeroMinute&&(e.second||e.millisecond)&&delete t.omitZeroMinute}(e,t),e.timeZone="UTC";let r,i=new Intl.DateTimeFormat(n.locale.codes,e);if(t.omitZeroMinute){let t=Object.assign({},e);delete t.minute,r=new Intl.DateTimeFormat(n.locale.codes,t)}return o=>{let s,{marker:a}=o;return s=r&&!a.getUTCMinutes()?r:i,function(e,t,n,r,i){e=e.replace(Ce,""),"short"===n.timeZoneName&&(e=function(e,t){let n=!1;e=e.replace(we,()=>(n=!0,t)),n||(e+=" "+t);return e}(e,"UTC"===i.timeZone||null==t.timeZoneOffset?"UTC":me(t.timeZoneOffset)));r.omitCommas&&(e=e.replace(Ee,"").trim());r.omitZeroMinute&&(e=e.replace(":00",""));!1===r.meridiem?e=e.replace(_e,"").trim():"narrow"===r.meridiem?e=e.replace(_e,(e,t)=>t.toLocaleLowerCase()):"short"===r.meridiem?e=e.replace(_e,(e,t)=>t.toLocaleLowerCase()+"m"):"lowercase"===r.meridiem&&(e=e.replace(_e,e=>e.toLocaleLowerCase()));return e=(e=e.replace(De," ")).trim()}(s.format(a),o,e,t,n)}}(e,t,n)}function ke(e,t){let n=t.markerToArray(e.marker);return{marker:e.marker,timeZoneOffset:e.timeZoneOffset,array:n,year:n[0],month:n[1],day:n[2],hour:n[3],minute:n[4],second:n[5],millisecond:n[6]}}function Re(e,t,n,r){let i=ke(e,n.calendarSystem);return{date:i,start:i,end:t?ke(t,n.calendarSystem):null,timeZone:n.timeZone,localeCodes:n.locale.codes,defaultSeparator:r||n.defaultSeparator}}class xe{constructor(e){this.cmdStr=e}format(e,t,n){return t.cmdFormatter(this.cmdStr,Re(e,null,t,n))}formatRange(e,t,n,r){return n.cmdFormatter(this.cmdStr,Re(e,t,n,r))}}class Oe{constructor(e){this.func=e}format(e,t,n){return this.func(Re(e,null,t,n))}formatRange(e,t,n,r){return this.func(Re(e,t,n,r))}}function Me(e){return"object"==typeof e&&e?new Se(e):"string"==typeof e?new xe(e):"function"==typeof e?new Oe(e):null}const Ie={navLinkDayClick:Ve,navLinkWeekClick:Ve,duration:N,bootstrapFontAwesome:Ve,buttonIcons:Ve,customButtons:Ve,defaultAllDayEventDuration:N,defaultTimedEventDuration:N,nextDayThreshold:N,scrollTime:N,scrollTimeReset:Boolean,slotMinTime:N,slotMaxTime:N,dayPopoverFormat:Me,slotDuration:N,snapDuration:N,headerToolbar:Ve,footerToolbar:Ve,defaultRangeSeparator:String,titleRangeSeparator:String,forceEventDuration:Boolean,dayHeaders:Boolean,dayHeaderFormat:Me,dayHeaderClassNames:Ve,dayHeaderContent:Ve,dayHeaderDidMount:Ve,dayHeaderWillUnmount:Ve,dayCellClassNames:Ve,dayCellContent:Ve,dayCellDidMount:Ve,dayCellWillUnmount:Ve,initialView:String,aspectRatio:Number,weekends:Boolean,weekNumberCalculation:Ve,weekNumbers:Boolean,weekNumberClassNames:Ve,weekNumberContent:Ve,weekNumberDidMount:Ve,weekNumberWillUnmount:Ve,editable:Boolean,viewClassNames:Ve,viewDidMount:Ve,viewWillUnmount:Ve,nowIndicator:Boolean,nowIndicatorClassNames:Ve,nowIndicatorContent:Ve,nowIndicatorDidMount:Ve,nowIndicatorWillUnmount:Ve,showNonCurrentDates:Boolean,lazyFetching:Boolean,startParam:String,endParam:String,timeZoneParam:String,timeZone:String,locales:Ve,locale:Ve,themeSystem:String,dragRevertDuration:Number,dragScroll:Boolean,allDayMaintainDuration:Boolean,unselectAuto:Boolean,dropAccept:Ve,eventOrder:C,eventOrderStrict:Boolean,handleWindowResize:Boolean,windowResizeDelay:Number,longPressDelay:Number,eventDragMinDistance:Number,expandRows:Boolean,height:Ve,contentHeight:Ve,direction:String,weekNumberFormat:Me,eventResizableFromStart:Boolean,displayEventTime:Boolean,displayEventEnd:Boolean,weekText:String,weekTextLong:String,progressiveEventRendering:Boolean,businessHours:Ve,initialDate:Ve,now:Ve,eventDataTransform:Ve,stickyHeaderDates:Ve,stickyFooterScrollbar:Ve,viewHeight:Ve,defaultAllDay:Boolean,eventSourceFailure:Ve,eventSourceSuccess:Ve,eventDisplay:String,eventStartEditable:Boolean,eventDurationEditable:Boolean,eventOverlap:Ve,eventConstraint:Ve,eventAllow:Ve,eventBackgroundColor:String,eventBorderColor:String,eventTextColor:String,eventColor:String,eventClassNames:Ve,eventContent:Ve,eventDidMount:Ve,eventWillUnmount:Ve,selectConstraint:Ve,selectOverlap:Ve,selectAllow:Ve,droppable:Boolean,unselectCancel:String,slotLabelFormat:Ve,slotLaneClassNames:Ve,slotLaneContent:Ve,slotLaneDidMount:Ve,slotLaneWillUnmount:Ve,slotLabelClassNames:Ve,slotLabelContent:Ve,slotLabelDidMount:Ve,slotLabelWillUnmount:Ve,dayMaxEvents:Ve,dayMaxEventRows:Ve,dayMinWidth:Number,slotLabelInterval:N,allDayText:String,allDayClassNames:Ve,allDayContent:Ve,allDayDidMount:Ve,allDayWillUnmount:Ve,slotMinWidth:Number,navLinks:Boolean,eventTimeFormat:Me,rerenderDelay:Number,moreLinkText:Ve,moreLinkHint:Ve,selectMinDistance:Number,selectable:Boolean,selectLongPressDelay:Number,eventLongPressDelay:Number,selectMirror:Boolean,eventMaxStack:Number,eventMinHeight:Number,eventMinWidth:Number,eventShortHeight:Number,slotEventOverlap:Boolean,plugins:Ve,firstDay:Number,dayCount:Number,dateAlignment:String,dateIncrement:N,hiddenDays:Ve,fixedWeekCount:Boolean,validRange:Ve,visibleRange:Ve,titleFormat:Ve,eventInteractive:Boolean,noEventsText:String,viewHint:Ve,navLinkHint:Ve,closeHint:String,timeHint:String,eventHint:String,moreLinkClick:Ve,moreLinkClassNames:Ve,moreLinkContent:Ve,moreLinkDidMount:Ve,moreLinkWillUnmount:Ve,monthStartFormat:Me,handleCustomRendering:Ve,customRenderingMetaMap:Ve,customRenderingReplaces:Boolean},Ne={eventDisplay:"auto",defaultRangeSeparator:" - ",titleRangeSeparator:" – ",defaultTimedEventDuration:"01:00:00",defaultAllDayEventDuration:{day:1},forceEventDuration:!1,nextDayThreshold:"00:00:00",dayHeaders:!0,initialView:"",aspectRatio:1.35,headerToolbar:{start:"title",center:"",end:"today prev,next"},weekends:!0,weekNumbers:!1,weekNumberCalculation:"local",editable:!1,nowIndicator:!1,scrollTime:"06:00:00",scrollTimeReset:!0,slotMinTime:"00:00:00",slotMaxTime:"24:00:00",showNonCurrentDates:!0,lazyFetching:!0,startParam:"start",endParam:"end",timeZoneParam:"timeZone",timeZone:"local",locales:[],locale:"",themeSystem:"standard",dragRevertDuration:500,dragScroll:!0,allDayMaintainDuration:!1,unselectAuto:!0,dropAccept:"*",eventOrder:"start,-duration,allDay,title",dayPopoverFormat:{month:"long",day:"numeric",year:"numeric"},handleWindowResize:!0,windowResizeDelay:100,longPressDelay:1e3,eventDragMinDistance:5,expandRows:!1,navLinks:!1,selectable:!1,eventMinHeight:15,eventMinWidth:30,eventShortHeight:30,monthStartFormat:{month:"long",day:"numeric"}},Be={datesSet:Ve,eventsSet:Ve,eventAdd:Ve,eventChange:Ve,eventRemove:Ve,windowResize:Ve,eventClick:Ve,eventMouseEnter:Ve,eventMouseLeave:Ve,select:Ve,unselect:Ve,loading:Ve,_unmount:Ve,_beforeprint:Ve,_afterprint:Ve,_noEventDrop:Ve,_noEventResize:Ve,_resize:Ve,_scrollRequest:Ve},He={buttonText:Ve,buttonHints:Ve,views:Ve,plugins:Ve,initialEvents:Ve,events:Ve,eventSources:Ve},Pe={headerToolbar:Ue,footerToolbar:Ue,buttonText:Ue,buttonHints:Ue,buttonIcons:Ue,dateIncrement:Ue,plugins:je,events:je,eventSources:je,resources:je};function Ue(e,t){return"object"==typeof e&&"object"==typeof t&&e&&t?Q(e,t):e===t}function je(e,t){return Array.isArray(e)&&Array.isArray(t)?$(e,t):e===t}const ze={type:String,component:Ve,buttonText:String,buttonTextKey:String,dateProfileGeneratorClass:Ve,usesMinMaxTime:Boolean,classNames:Ve,content:Ve,didMount:Ve,willUnmount:Ve};function Fe(e){return z(e,Pe)}function Le(e,t){let n={},r={};for(let r in t)r in e&&(n[r]=t[r](e[r]));for(let n in e)n in t||(r[n]=e[n]);return{refined:n,extra:r}}function Ve(e){return e}function We(e,t,n,r){return{instanceId:D(),defId:e,range:t,forcedStartTzo:null==n?null:n,forcedEndTzo:null==r?null:r}}function Qe(e,t,n){let{dateEnv:r,pluginHooks:i,options:o}=n,{defs:s,instances:a}=e;a=F(a,e=>!s[e.defId].recurringDef);for(let e in s){let n=s[e];if(n.recurringDef){let{duration:s}=n.recurringDef;s||(s=n.allDay?o.defaultAllDayEventDuration:o.defaultTimedEventDuration);let l=Ge(n,s,t,r,i.recurringTypes);for(let t of l){let n=We(e,{start:t,end:r.add(t,s)});a[n.instanceId]=n}}}return{defs:s,instances:a}}function Ge(e,t,n,r,i){let o=i[e.recurringDef.typeId].expand(e.recurringDef.typeData,{start:r.subtract(n.start,t),end:n.end},r);return e.allDay&&(o=o.map(oe)),o}function Ze(e,t,n,r,i,o){let s={defs:{},instances:{}},a=ut(n);for(let l of e){let e=lt(l,t,n,r,a,i,o);e&&qe(e,s)}return s}function qe(e,t={defs:{},instances:{}}){return t.defs[e.def.defId]=e.def,e.instance&&(t.instances[e.instance.instanceId]=e.instance),t}function Ye(e,t){let n=e.instances[t];if(n){let t=e.defs[n.defId],r=Xe(e,e=>{return n=t,r=e,Boolean(n.groupId&&n.groupId===r.groupId);var n,r});return r.defs[t.defId]=t,r.instances[n.instanceId]=n,r}return{defs:{},instances:{}}}function Je(){return{defs:{},instances:{}}}function $e(e,t){return{defs:Object.assign(Object.assign({},e.defs),t.defs),instances:Object.assign(Object.assign({},e.instances),t.instances)}}function Xe(e,t){let n=F(e.defs,t),r=F(e.instances,e=>n[e.defId]);return{defs:n,instances:r}}function Ke(e){return Array.isArray(e)?e:"string"==typeof e?e.split(/\s+/):[]}const et={display:String,editable:Boolean,startEditable:Boolean,durationEditable:Boolean,constraint:Ve,overlap:Ve,allow:Ve,className:Ke,classNames:Ke,color:String,backgroundColor:String,borderColor:String,textColor:String},tt={display:null,startEditable:null,durationEditable:null,constraints:[],overlap:null,allows:[],backgroundColor:"",borderColor:"",textColor:"",classNames:[]};function nt(e,t){let n=function(e,t){return Array.isArray(e)?Ze(e,null,t,!0):"object"==typeof e&&e?Ze([e],null,t,!0):null!=e?String(e):null}(e.constraint,t);return{display:e.display||null,startEditable:null!=e.startEditable?e.startEditable:e.editable,durationEditable:null!=e.durationEditable?e.durationEditable:e.editable,constraints:null!=n?[n]:[],overlap:null!=e.overlap?e.overlap:null,allows:null!=e.allow?[e.allow]:[],backgroundColor:e.backgroundColor||e.color||"",borderColor:e.borderColor||e.color||"",textColor:e.textColor||"",classNames:(e.className||[]).concat(e.classNames||[])}}function rt(e){return e.reduce(it,tt)}function it(e,t){return{display:null!=t.display?t.display:e.display,startEditable:null!=t.startEditable?t.startEditable:e.startEditable,durationEditable:null!=t.durationEditable?t.durationEditable:e.durationEditable,constraints:e.constraints.concat(t.constraints),overlap:"boolean"==typeof t.overlap?t.overlap:e.overlap,allows:e.allows.concat(t.allows),backgroundColor:t.backgroundColor||e.backgroundColor,borderColor:t.borderColor||e.borderColor,textColor:t.textColor||e.textColor,classNames:e.classNames.concat(t.classNames)}}const ot={id:String,groupId:String,title:String,url:String,interactive:Boolean},st={start:Ve,end:Ve,date:Ve,allDay:Boolean},at=Object.assign(Object.assign(Object.assign({},ot),st),{extendedProps:Ve});function lt(e,t,n,r,i=ut(n),o,s){let{refined:a,extra:l}=ct(e,n,i),c=function(e,t){let n=null;e&&(n=e.defaultAllDay);null==n&&(n=t.options.defaultAllDay);return n}(t,n),u=function(e,t,n,r){for(let i=0;i<r.length;i+=1){let o=r[i].parse(e,n);if(o){let{allDay:n}=e;return null==n&&(n=t,null==n&&(n=o.allDayGuess,null==n&&(n=!1))),{allDay:n,duration:o.duration,typeData:o.typeData,typeId:i}}}return null}(a,c,n.dateEnv,n.pluginHooks.recurringTypes);if(u){let e=dt(a,l,t?t.sourceId:"",u.allDay,Boolean(u.duration),n,o);return e.recurringDef={typeId:u.typeId,typeData:u.typeData,duration:u.duration},{def:e,instance:null}}let d=function(e,t,n,r){let i,o,{allDay:s}=e,a=null,l=!1,c=null,u=null!=e.start?e.start:e.date;if(i=n.dateEnv.createMarkerMeta(u),i)a=i.marker;else if(!r)return null;null!=e.end&&(o=n.dateEnv.createMarkerMeta(e.end));null==s&&(s=null!=t?t:(!i||i.isTimeUnspecified)&&(!o||o.isTimeUnspecified));s&&a&&(a=oe(a));o&&(c=o.marker,s&&(c=oe(c)),a&&c<=a&&(c=null));c?l=!0:r||(l=n.options.forceEventDuration||!1,c=n.dateEnv.add(a,s?n.options.defaultAllDayEventDuration:n.options.defaultTimedEventDuration));return{allDay:s,hasEnd:l,range:{start:a,end:c},forcedStartTzo:i?i.forcedTzo:null,forcedEndTzo:o?o.forcedTzo:null}}(a,c,n,r);if(d){let e=dt(a,l,t?t.sourceId:"",d.allDay,d.hasEnd,n,o),r=We(e.defId,d.range,d.forcedStartTzo,d.forcedEndTzo);return s&&e.publicId&&s[e.publicId]&&(r.instanceId=s[e.publicId]),{def:e,instance:r}}return null}function ct(e,t,n=ut(t)){return Le(e,n)}function ut(e){return Object.assign(Object.assign(Object.assign({},et),at),e.pluginHooks.eventRefiners)}function dt(e,t,n,r,i,o,s){let a={title:e.title||"",groupId:e.groupId||"",publicId:e.id||"",url:e.url||"",recurringDef:null,defId:(s&&e.id?s[e.id]:"")||D(),sourceId:n,allDay:r,hasEnd:i,interactive:e.interactive,ui:nt(e,o),extendedProps:Object.assign(Object.assign({},e.extendedProps||{}),t)};for(let t of o.pluginHooks.eventDefMemberAdders)Object.assign(a,t(e));return Object.freeze(a.ui.classNames),Object.freeze(a.extendedProps),a}const ft={startTime:"09:00",endTime:"17:00",daysOfWeek:[1,2,3,4,5],display:"inverse-background",classNames:"fc-non-business",groupId:"_businessHours"};function ht(e,t){return Ze(function(e){let t;t=!0===e?[{}]:Array.isArray(e)?e.filter(e=>e.daysOfWeek):"object"==typeof e&&e?[e]:[];return t=t.map(e=>Object.assign(Object.assign({},ft),e)),t}(e),null,t)}function pt(e){let t=Math.floor(te(e.start,e.end))||1,n=oe(e.start);return{start:n,end:K(n,t)}}function gt(e,t=N(0)){let n=null,r=null;if(e.end){r=oe(e.end);let n=e.end.valueOf()-r.valueOf();n&&n>=P(t)&&(r=K(r,1))}return e.start&&(n=oe(e.start),r&&r<=n&&(r=K(n,1))),{start:n,end:r}}function mt(e,t,n,r){return"year"===r?N(n.diffWholeYears(e,t),"year"):"month"===r?N(n.diffWholeMonths(e,t),"month"):ne(e,t)}function vt(e,t){let n={left:Math.max(e.left,t.left),right:Math.min(e.right,t.right),top:Math.max(e.top,t.top),bottom:Math.min(e.bottom,t.bottom)};return n.left<n.right&&n.top<n.bottom&&n}let bt;function At(){return null==bt&&(bt=function(){if("undefined"==typeof document)return!0;let e=document.createElement("div");e.style.position="absolute",e.style.top="0px",e.style.left="0px",e.innerHTML="<table><tr><td><div></div></td></tr></table>",e.querySelector("table").style.height="100px",e.querySelector("div").style.height="100%",document.body.appendChild(e);let t=e.querySelector("div").offsetHeight>0;return document.body.removeChild(e),t}()),bt}const yt={defs:{},instances:{}};function _t(e,t,n){let r=[];e&&r.push(e),t&&r.push(t);let i={"":rt(r)};return n&&Object.assign(i,n),i}function Et(e,t){let n,r,i=[],{start:o}=t;for(e.sort(Dt),n=0;n<e.length;n+=1)r=e[n],r.start>o&&i.push({start:o,end:r.start}),r.end>o&&(o=r.end);return o<t.end&&i.push({start:o,end:t.end}),i}function Dt(e,t){return e.start.valueOf()-t.start.valueOf()}function Ct(e,t){let{start:n,end:r}=e,i=null;return null!==t.start&&(n=null===n?t.start:new Date(Math.max(n.valueOf(),t.start.valueOf()))),null!=t.end&&(r=null===r?t.end:new Date(Math.min(r.valueOf(),t.end.valueOf()))),(null===n||null===r||n<r)&&(i={start:n,end:r}),i}function wt(e,t){return(null===e.start?null:e.start.valueOf())===(null===t.start?null:t.start.valueOf())&&(null===e.end?null:e.end.valueOf())===(null===t.end?null:t.end.valueOf())}function St(e,t){return(null===e.end||null===t.start||e.end>t.start)&&(null===e.start||null===t.end||e.start<t.end)}function Tt(e,t){return(null===e.start||null!==t.start&&t.start>=e.start)&&(null===e.end||null!==t.end&&t.end<=e.end)}function kt(e,t){return(null===e.start||t>=e.start)&&(null===e.end||t<e.end)}function Rt(e,t,n,r){return{dow:e.getUTCDay(),isDisabled:Boolean(r&&(!r.activeRange||!kt(r.activeRange,e))),isOther:Boolean(r&&!kt(r.currentRange,e)),isToday:Boolean(t&&kt(t,e)),isPast:Boolean(n?e<n:!!t&&e<t.start),isFuture:Boolean(n?e>n:!!t&&e>=t.end)}}function xt(e,t){let n=["fc-day","fc-day-"+X[e.dow]];return e.isDisabled?n.push("fc-day-disabled"):(e.isToday&&(n.push("fc-day-today"),n.push(t.getClass("today"))),e.isPast&&n.push("fc-day-past"),e.isFuture&&n.push("fc-day-future"),e.isOther&&n.push("fc-day-other")),n}const Ot=Me({year:"numeric",month:"long",day:"numeric"}),Mt=Me({week:"long"});function It(e,t,n="day",r=!0){const{dateEnv:i,options:o,calendarApi:s}=e;let a=i.format(t,"week"===n?Mt:Ot);if(o.navLinks){let e=i.toDate(t);const l=e=>{let r="day"===n?o.navLinkDayClick:"week"===n?o.navLinkWeekClick:null;"function"==typeof r?r.call(s,i.toDate(t),e):("string"==typeof r&&(n=r),s.zoomTo(t,n))};return Object.assign({title:R(o.navLinkHint,[a,e],a),"data-navlink":""},r?y(l):{onClick:l})}return{"aria-label":a}}let Nt,Bt=null;function Ht(){return null===Bt&&(Bt=function(){let e=document.createElement("div");f(e,{position:"absolute",top:-1e3,left:0,border:0,padding:0,overflow:"scroll",direction:"rtl"}),e.innerHTML="<div></div>",document.body.appendChild(e);let t=e.firstChild.getBoundingClientRect().left>e.getBoundingClientRect().left;return a(e),t}()),Bt}function Pt(){return Nt||(Nt=function(){let e=document.createElement("div");e.style.overflow="scroll",e.style.position="absolute",e.style.top="-9999px",e.style.left="-9999px",document.body.appendChild(e);let t=Ut(e);return document.body.removeChild(e),t}()),Nt}function Ut(e){return{x:e.offsetHeight-e.clientHeight,y:e.offsetWidth-e.clientWidth}}function jt(e,t=!1){let n=window.getComputedStyle(e),r=parseInt(n.borderLeftWidth,10)||0,i=parseInt(n.borderRightWidth,10)||0,o=parseInt(n.borderTopWidth,10)||0,s=parseInt(n.borderBottomWidth,10)||0,a=Ut(e),l=a.y-r-i,c={borderLeft:r,borderRight:i,borderTop:o,borderBottom:s,scrollbarBottom:a.x-o-s,scrollbarLeft:0,scrollbarRight:0};return Ht()&&"rtl"===n.direction?c.scrollbarLeft=l:c.scrollbarRight=l,t&&(c.paddingLeft=parseInt(n.paddingLeft,10)||0,c.paddingRight=parseInt(n.paddingRight,10)||0,c.paddingTop=parseInt(n.paddingTop,10)||0,c.paddingBottom=parseInt(n.paddingBottom,10)||0),c}function zt(e){let t=e.getBoundingClientRect();return{left:t.left+window.scrollX,top:t.top+window.scrollY,right:t.right+window.scrollX,bottom:t.bottom+window.scrollY}}function Ft(e){let t=[];for(;e instanceof HTMLElement;){let n=window.getComputedStyle(e);if("fixed"===n.position)break;/(auto|scroll)/.test(n.overflow+n.overflowY+n.overflowX)&&t.push(e),e=e.parentNode}return t}function Lt(e,t,n){let r=!1,i=function(e){r||(r=!0,t(e))},o=function(e){r||(r=!0,n(e))},s=e(i,o);s&&"function"==typeof s.then&&s.then(i,o)}class Vt{constructor(){this.handlers={},this.thisContext=null}setThisContext(e){this.thisContext=e}setOptions(e){this.options=e}on(e,t){!function(e,t,n){(e[t]||(e[t]=[])).push(n)}(this.handlers,e,t)}off(e,t){!function(e,t,n){n?e[t]&&(e[t]=e[t].filter(e=>e!==n)):delete e[t]}(this.handlers,e,t)}trigger(e,...t){let n=this.handlers[e]||[],r=this.options&&this.options[e],i=[].concat(r||[],n);for(let e of i)e.apply(this.thisContext,t)}hasHandlers(e){return Boolean(this.handlers[e]&&this.handlers[e].length||this.options&&this.options[e])}}function Wt(e,t){const n=e.length;if(n!==t.length)return!1;for(let r=0;r<n;r++)if(Math.round(e[r])!==Math.round(t[r]))return!1;return!0}class Qt{getMaxScrollTop(){return this.getScrollHeight()-this.getClientHeight()}getMaxScrollLeft(){return this.getScrollWidth()-this.getClientWidth()}canScrollVertically(){return this.getMaxScrollTop()>0}canScrollHorizontally(){return this.getMaxScrollLeft()>0}canScrollUp(){return this.getScrollTop()>0}canScrollDown(){return this.getScrollTop()<this.getMaxScrollTop()}canScrollLeft(){return this.getScrollLeft()>0}canScrollRight(){return this.getScrollLeft()<this.getMaxScrollLeft()}}class Gt{constructor(e){this.iconOverrideOption&&this.setIconOverride(e[this.iconOverrideOption])}setIconOverride(e){let t,n;if("object"==typeof e&&e){for(n in t=Object.assign({},this.iconClasses),e)t[n]=this.applyIconOverridePrefix(e[n]);this.iconClasses=t}else!1===e&&(this.iconClasses={})}applyIconOverridePrefix(e){let t=this.iconOverridePrefix;return t&&0!==e.indexOf(t)&&(e=t+e),e}getClass(e){return this.classes[e]||""}getIconClass(e,t){let n;return n=t&&this.rtlIconClasses&&this.rtlIconClasses[e]||this.iconClasses[e],n?`${this.baseIconClass} ${n}`:""}getCustomButtonIconClass(e){let t;return this.iconOverrideCustomButtonOption&&(t=e[this.iconOverrideCustomButtonOption],t)?`${this.baseIconClass} ${this.applyIconOverridePrefix(t)}`:""}}Gt.prototype.classes={},Gt.prototype.iconClasses={},Gt.prototype.baseIconClass="",Gt.prototype.iconOverridePrefix="";var Zt,qt,Yt,Jt,$t,Xt,Kt,en,tn,nn={},rn=[],on=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;function sn(e,t){for(var n in t)e[n]=t[n];return e}function an(e){var t=e.parentNode;t&&t.removeChild(e)}function ln(e,t,n){var r,i,o,s={};for(o in t)"key"==o?r=t[o]:"ref"==o?i=t[o]:s[o]=t[o];if(arguments.length>2&&(s.children=arguments.length>3?Zt.call(arguments,2):n),"function"==typeof e&&null!=e.defaultProps)for(o in e.defaultProps)void 0===s[o]&&(s[o]=e.defaultProps[o]);return cn(e,s,r,i,null)}function cn(e,t,n,r,i){var o={type:e,props:t,key:n,ref:r,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:null==i?++Yt:i};return null==i&&null!=qt.vnode&&qt.vnode(o),o}function un(){return{current:null}}function dn(e){return e.children}function fn(e,t,n){"-"===t[0]?e.setProperty(t,null==n?"":n):e[t]=null==n?"":"number"!=typeof n||on.test(t)?n:n+"px"}function hn(e,t,n,r,i){var o;e:if("style"===t)if("string"==typeof n)e.style.cssText=n;else{if("string"==typeof r&&(e.style.cssText=r=""),r)for(t in r)n&&t in n||fn(e.style,t,"");if(n)for(t in n)r&&n[t]===r[t]||fn(e.style,t,n[t])}else if("o"===t[0]&&"n"===t[1])o=t!==(t=t.replace(/Capture$/,"")),t=t.toLowerCase()in e?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+o]=n,n?r||e.addEventListener(t,o?gn:pn,o):e.removeEventListener(t,o?gn:pn,o);else if("dangerouslySetInnerHTML"!==t){if(i)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!==t&&"height"!==t&&"href"!==t&&"list"!==t&&"form"!==t&&"tabIndex"!==t&&"download"!==t&&t in e)try{e[t]=null==n?"":n;break e}catch(e){}"function"==typeof n||(null==n||!1===n&&-1==t.indexOf("-")?e.removeAttribute(t):e.setAttribute(t,n))}}function pn(e){$t=!0;try{return this.l[e.type+!1](qt.event?qt.event(e):e)}finally{$t=!1}}function gn(e){$t=!0;try{return this.l[e.type+!0](qt.event?qt.event(e):e)}finally{$t=!1}}function mn(e,t){this.props=e,this.context=t}function vn(e,t){if(null==t)return e.__?vn(e.__,e.__.__k.indexOf(e)+1):null;for(var n;t<e.__k.length;t++)if(null!=(n=e.__k[t])&&null!=n.__e)return n.__e;return"function"==typeof e.type?vn(e):null}function bn(e){var t,n;if(null!=(e=e.__)&&null!=e.__c){for(e.__e=e.__c.base=null,t=0;t<e.__k.length;t++)if(null!=(n=e.__k[t])&&null!=n.__e){e.__e=e.__c.base=n.__e;break}return bn(e)}}function An(e){$t?setTimeout(e):en(e)}function yn(e){(!e.__d&&(e.__d=!0)&&Xt.push(e)&&!_n.__r++||Kt!==qt.debounceRendering)&&((Kt=qt.debounceRendering)||An)(_n)}function _n(){var e,t,n,r,i,o,s,a;for(Xt.sort((function(e,t){return e.__v.__b-t.__v.__b}));e=Xt.shift();)e.__d&&(t=Xt.length,r=void 0,i=void 0,s=(o=(n=e).__v).__e,(a=n.__P)&&(r=[],(i=sn({},o)).__v=o.__v+1,Tn(a,o,i,n.__n,void 0!==a.ownerSVGElement,null!=o.__h?[s]:null,r,null==s?vn(o):s,o.__h),kn(r,o),o.__e!=s&&bn(o)),Xt.length>t&&Xt.sort((function(e,t){return e.__v.__b-t.__v.__b})));_n.__r=0}function En(e,t,n,r,i,o,s,a,l,c){var u,d,f,h,p,g,m,v=r&&r.__k||rn,b=v.length;for(n.__k=[],u=0;u<t.length;u++)if(null!=(h=n.__k[u]=null==(h=t[u])||"boolean"==typeof h?null:"string"==typeof h||"number"==typeof h||"bigint"==typeof h?cn(null,h,null,null,h):Array.isArray(h)?cn(dn,{children:h},null,null,null):h.__b>0?cn(h.type,h.props,h.key,h.ref?h.ref:null,h.__v):h)){if(h.__=n,h.__b=n.__b+1,null===(f=v[u])||f&&h.key==f.key&&h.type===f.type)v[u]=void 0;else for(d=0;d<b;d++){if((f=v[d])&&h.key==f.key&&h.type===f.type){v[d]=void 0;break}f=null}Tn(e,h,f=f||nn,i,o,s,a,l,c),p=h.__e,(d=h.ref)&&f.ref!=d&&(m||(m=[]),f.ref&&m.push(f.ref,null,h),m.push(d,h.__c||p,h)),null!=p?(null==g&&(g=p),"function"==typeof h.type&&h.__k===f.__k?h.__d=l=Dn(h,l,e):l=wn(e,h,f,v,p,l),"function"==typeof n.type&&(n.__d=l)):l&&f.__e==l&&l.parentNode!=e&&(l=vn(f))}for(n.__e=g,u=b;u--;)null!=v[u]&&("function"==typeof n.type&&null!=v[u].__e&&v[u].__e==n.__d&&(n.__d=Sn(r).nextSibling),On(v[u],v[u]));if(m)for(u=0;u<m.length;u++)xn(m[u],m[++u],m[++u])}function Dn(e,t,n){for(var r,i=e.__k,o=0;i&&o<i.length;o++)(r=i[o])&&(r.__=e,t="function"==typeof r.type?Dn(r,t,n):wn(n,r,r,i,r.__e,t));return t}function Cn(e,t){return t=t||[],null==e||"boolean"==typeof e||(Array.isArray(e)?e.some((function(e){Cn(e,t)})):t.push(e)),t}function wn(e,t,n,r,i,o){var s,a,l;if(void 0!==t.__d)s=t.__d,t.__d=void 0;else if(null==n||i!=o||null==i.parentNode)e:if(null==o||o.parentNode!==e)e.appendChild(i),s=null;else{for(a=o,l=0;(a=a.nextSibling)&&l<r.length;l+=1)if(a==i)break e;e.insertBefore(i,o),s=o}return void 0!==s?s:i.nextSibling}function Sn(e){var t,n,r;if(null==e.type||"string"==typeof e.type)return e.__e;if(e.__k)for(t=e.__k.length-1;t>=0;t--)if((n=e.__k[t])&&(r=Sn(n)))return r;return null}function Tn(e,t,n,r,i,o,s,a,l){var c,u,d,f,h,p,g,m,v,b,A,y,_,E,D,C=t.type;if(void 0!==t.constructor)return null;null!=n.__h&&(l=n.__h,a=t.__e=n.__e,t.__h=null,o=[a]),(c=qt.__b)&&c(t);try{e:if("function"==typeof C){if(m=t.props,v=(c=C.contextType)&&r[c.__c],b=c?v?v.props.value:c.__:r,n.__c?g=(u=t.__c=n.__c).__=u.__E:("prototype"in C&&C.prototype.render?t.__c=u=new C(m,b):(t.__c=u=new mn(m,b),u.constructor=C,u.render=Mn),v&&v.sub(u),u.props=m,u.state||(u.state={}),u.context=b,u.__n=r,d=u.__d=!0,u.__h=[],u._sb=[]),null==u.__s&&(u.__s=u.state),null!=C.getDerivedStateFromProps&&(u.__s==u.state&&(u.__s=sn({},u.__s)),sn(u.__s,C.getDerivedStateFromProps(m,u.__s))),f=u.props,h=u.state,u.__v=t,d)null==C.getDerivedStateFromProps&&null!=u.componentWillMount&&u.componentWillMount(),null!=u.componentDidMount&&u.__h.push(u.componentDidMount);else{if(null==C.getDerivedStateFromProps&&m!==f&&null!=u.componentWillReceiveProps&&u.componentWillReceiveProps(m,b),!u.__e&&null!=u.shouldComponentUpdate&&!1===u.shouldComponentUpdate(m,u.__s,b)||t.__v===n.__v){for(t.__v!==n.__v&&(u.props=m,u.state=u.__s,u.__d=!1),t.__e=n.__e,t.__k=n.__k,t.__k.forEach((function(e){e&&(e.__=t)})),A=0;A<u._sb.length;A++)u.__h.push(u._sb[A]);u._sb=[],u.__h.length&&s.push(u);break e}null!=u.componentWillUpdate&&u.componentWillUpdate(m,u.__s,b),null!=u.componentDidUpdate&&u.__h.push((function(){u.componentDidUpdate(f,h,p)}))}if(u.context=b,u.props=m,u.__P=e,y=qt.__r,_=0,"prototype"in C&&C.prototype.render){for(u.state=u.__s,u.__d=!1,y&&y(t),c=u.render(u.props,u.state,u.context),E=0;E<u._sb.length;E++)u.__h.push(u._sb[E]);u._sb=[]}else do{u.__d=!1,y&&y(t),c=u.render(u.props,u.state,u.context),u.state=u.__s}while(u.__d&&++_<25);u.state=u.__s,null!=u.getChildContext&&(r=sn(sn({},r),u.getChildContext())),d||null==u.getSnapshotBeforeUpdate||(p=u.getSnapshotBeforeUpdate(f,h)),D=null!=c&&c.type===dn&&null==c.key?c.props.children:c,En(e,Array.isArray(D)?D:[D],t,n,r,i,o,s,a,l),u.base=t.__e,t.__h=null,u.__h.length&&s.push(u),g&&(u.__E=u.__=null),u.__e=!1}else null==o&&t.__v===n.__v?(t.__k=n.__k,t.__e=n.__e):t.__e=Rn(n.__e,t,n,r,i,o,s,l);(c=qt.diffed)&&c(t)}catch(e){t.__v=null,(l||null!=o)&&(t.__e=a,t.__h=!!l,o[o.indexOf(a)]=null),qt.__e(e,t,n)}}function kn(e,t){qt.__c&&qt.__c(t,e),e.some((function(t){try{e=t.__h,t.__h=[],e.some((function(e){e.call(t)}))}catch(e){qt.__e(e,t.__v)}}))}function Rn(e,t,n,r,i,o,s,a){var l,c,u,d=n.props,f=t.props,h=t.type,p=0;if("svg"===h&&(i=!0),null!=o)for(;p<o.length;p++)if((l=o[p])&&"setAttribute"in l==!!h&&(h?l.localName===h:3===l.nodeType)){e=l,o[p]=null;break}if(null==e){if(null===h)return document.createTextNode(f);e=i?document.createElementNS("http://www.w3.org/2000/svg",h):document.createElement(h,f.is&&f),o=null,a=!1}if(null===h)d===f||a&&e.data===f||(e.data=f);else{if(o=o&&Zt.call(e.childNodes),c=(d=n.props||nn).dangerouslySetInnerHTML,u=f.dangerouslySetInnerHTML,!a){if(null!=o)for(d={},p=0;p<e.attributes.length;p++)d[e.attributes[p].name]=e.attributes[p].value;(u||c)&&(u&&(c&&u.__html==c.__html||u.__html===e.innerHTML)||(e.innerHTML=u&&u.__html||""))}if(function(e,t,n,r,i){var o;for(o in n)"children"===o||"key"===o||o in t||hn(e,o,null,n[o],r);for(o in t)i&&"function"!=typeof t[o]||"children"===o||"key"===o||"value"===o||"checked"===o||n[o]===t[o]||hn(e,o,t[o],n[o],r)}(e,f,d,i,a),u)t.__k=[];else if(p=t.props.children,En(e,Array.isArray(p)?p:[p],t,n,r,i&&"foreignObject"!==h,o,s,o?o[0]:n.__k&&vn(n,0),a),null!=o)for(p=o.length;p--;)null!=o[p]&&an(o[p]);a||("value"in f&&void 0!==(p=f.value)&&(p!==e.value||"progress"===h&&!p||"option"===h&&p!==d.value)&&hn(e,"value",p,d.value,!1),"checked"in f&&void 0!==(p=f.checked)&&p!==e.checked&&hn(e,"checked",p,d.checked,!1))}return e}function xn(e,t,n){try{"function"==typeof e?e(t):e.current=t}catch(e){qt.__e(e,n)}}function On(e,t,n){var r,i;if(qt.unmount&&qt.unmount(e),(r=e.ref)&&(r.current&&r.current!==e.__e||xn(r,null,t)),null!=(r=e.__c)){if(r.componentWillUnmount)try{r.componentWillUnmount()}catch(e){qt.__e(e,t)}r.base=r.__P=null,e.__c=void 0}if(r=e.__k)for(i=0;i<r.length;i++)r[i]&&On(r[i],t,n||"function"!=typeof e.type);n||null==e.__e||an(e.__e),e.__=e.__e=e.__d=void 0}function Mn(e,t,n){return this.constructor(e,n)}function In(e,t,n){var r,i,o;qt.__&&qt.__(e,t),i=(r="function"==typeof n)?null:n&&n.__k||t.__k,o=[],Tn(t,e=(!r&&n||t).__k=ln(dn,null,[e]),i||nn,nn,void 0!==t.ownerSVGElement,!r&&n?[n]:i?null:t.firstChild?Zt.call(t.childNodes):null,o,!r&&n?n:i?i.__e:t.firstChild,r),kn(o,e)}Zt=rn.slice,qt={__e:function(e,t,n,r){for(var i,o,s;t=t.__;)if((i=t.__c)&&!i.__)try{if((o=i.constructor)&&null!=o.getDerivedStateFromError&&(i.setState(o.getDerivedStateFromError(e)),s=i.__d),null!=i.componentDidCatch&&(i.componentDidCatch(e,r||{}),s=i.__d),s)return i.__E=i}catch(t){e=t}throw e}},Yt=0,Jt=function(e){return null!=e&&void 0===e.constructor},$t=!1,mn.prototype.setState=function(e,t){var n;n=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=sn({},this.state),"function"==typeof e&&(e=e(sn({},n),this.props)),e&&sn(n,e),null!=e&&this.__v&&(t&&this._sb.push(t),yn(this))},mn.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),yn(this))},mn.prototype.render=dn,Xt=[],en="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,_n.__r=0,tn=0;var Nn,Bn,Hn,Pn=[],Un=[],jn=qt.__b,zn=qt.__r,Fn=qt.diffed,Ln=qt.__c,Vn=qt.unmount;function Wn(){for(var e;e=Pn.shift();)if(e.__P&&e.__H)try{e.__H.__h.forEach(Zn),e.__H.__h.forEach(qn),e.__H.__h=[]}catch(t){e.__H.__h=[],qt.__e(t,e.__v)}}qt.__b=function(e){Nn=null,jn&&jn(e)},qt.__r=function(e){zn&&zn(e);var t=(Nn=e.__c).__H;t&&(Bn===Nn?(t.__h=[],Nn.__h=[],t.__.forEach((function(e){e.__N&&(e.__=e.__N),e.__V=Un,e.__N=e.i=void 0}))):(t.__h.forEach(Zn),t.__h.forEach(qn),t.__h=[])),Bn=Nn},qt.diffed=function(e){Fn&&Fn(e);var t=e.__c;t&&t.__H&&(t.__H.__h.length&&(1!==Pn.push(t)&&Hn===qt.requestAnimationFrame||((Hn=qt.requestAnimationFrame)||Gn)(Wn)),t.__H.__.forEach((function(e){e.i&&(e.__H=e.i),e.__V!==Un&&(e.__=e.__V),e.i=void 0,e.__V=Un}))),Bn=Nn=null},qt.__c=function(e,t){t.some((function(e){try{e.__h.forEach(Zn),e.__h=e.__h.filter((function(e){return!e.__||qn(e)}))}catch(n){t.some((function(e){e.__h&&(e.__h=[])})),t=[],qt.__e(n,e.__v)}})),Ln&&Ln(e,t)},qt.unmount=function(e){Vn&&Vn(e);var t,n=e.__c;n&&n.__H&&(n.__H.__.forEach((function(e){try{Zn(e)}catch(e){t=e}})),n.__H=void 0,t&&qt.__e(t,n.__v))};var Qn="function"==typeof requestAnimationFrame;function Gn(e){var t,n=function(){clearTimeout(r),Qn&&cancelAnimationFrame(t),setTimeout(e)},r=setTimeout(n,100);Qn&&(t=requestAnimationFrame(n))}function Zn(e){var t=Nn,n=e.__c;"function"==typeof n&&(e.__c=void 0,n()),Nn=t}function qn(e){var t=Nn;e.__c=e.__(),Nn=t}function Yn(e,t){for(var n in e)if("__source"!==n&&!(n in t))return!0;for(var r in t)if("__source"!==r&&e[r]!==t[r])return!0;return!1}function Jn(e){this.props=e}(Jn.prototype=new mn).isPureReactComponent=!0,Jn.prototype.shouldComponentUpdate=function(e,t){return Yn(this.props,e)||Yn(this.state,t)};var $n=qt.__b;qt.__b=function(e){e.type&&e.type.__f&&e.ref&&(e.props.ref=e.ref,e.ref=null),$n&&$n(e)};var Xn=qt.__e;qt.__e=function(e,t,n,r){if(e.then)for(var i,o=t;o=o.__;)if((i=o.__c)&&i.__c)return null==t.__e&&(t.__e=n.__e,t.__k=n.__k),i.__c(e,t);Xn(e,t,n,r)};var Kn=qt.unmount;function er(){this.__u=0,this.t=null,this.__b=null}function tr(e){var t=e.__.__c;return t&&t.__a&&t.__a(e)}function nr(){this.u=null,this.o=null}qt.unmount=function(e){var t=e.__c;t&&t.__R&&t.__R(),t&&!0===e.__h&&(e.type=null),Kn&&Kn(e)},(er.prototype=new mn).__c=function(e,t){var n=t.__c,r=this;null==r.t&&(r.t=[]),r.t.push(n);var i=tr(r.__v),o=!1,s=function(){o||(o=!0,n.__R=null,i?i(a):a())};n.__R=s;var a=function(){if(!--r.__u){if(r.state.__a){var e=r.state.__a;r.__v.__k[0]=function e(t,n,r){return t&&(t.__v=null,t.__k=t.__k&&t.__k.map((function(t){return e(t,n,r)})),t.__c&&t.__c.__P===n&&(t.__e&&r.insertBefore(t.__e,t.__d),t.__c.__e=!0,t.__c.__P=r)),t}(e,e.__c.__P,e.__c.__O)}var t;for(r.setState({__a:r.__b=null});t=r.t.pop();)t.forceUpdate()}},l=!0===t.__h;r.__u++||l||r.setState({__a:r.__b=r.__v.__k[0]}),e.then(s,s)},er.prototype.componentWillUnmount=function(){this.t=[]},er.prototype.render=function(e,t){if(this.__b){if(this.__v.__k){var n=document.createElement("div"),r=this.__v.__k[0].__c;this.__v.__k[0]=function e(t,n,r){return t&&(t.__c&&t.__c.__H&&(t.__c.__H.__.forEach((function(e){"function"==typeof e.__c&&e.__c()})),t.__c.__H=null),null!=(t=function(e,t){for(var n in t)e[n]=t[n];return e}({},t)).__c&&(t.__c.__P===r&&(t.__c.__P=n),t.__c=null),t.__k=t.__k&&t.__k.map((function(t){return e(t,n,r)}))),t}(this.__b,n,r.__O=r.__P)}this.__b=null}var i=t.__a&&ln(dn,null,e.fallback);return i&&(i.__h=null),[ln(dn,null,t.__a?null:e.children),i]};var rr=function(e,t,n){if(++n[1]===n[0]&&e.o.delete(t),e.props.revealOrder&&("t"!==e.props.revealOrder[0]||!e.o.size))for(n=e.u;n;){for(;n.length>3;)n.pop()();if(n[1]<n[0])break;e.u=n=n[2]}};function ir(e){return this.getChildContext=function(){return e.context},e.children}function or(e){var t=this,n=e.i;t.componentWillUnmount=function(){In(null,t.l),t.l=null,t.i=null},t.i&&t.i!==n&&t.componentWillUnmount(),e.__v?(t.l||(t.i=n,t.l={nodeType:1,parentNode:n,childNodes:[],appendChild:function(e){this.childNodes.push(e),t.i.appendChild(e)},insertBefore:function(e,n){this.childNodes.push(e),t.i.appendChild(e)},removeChild:function(e){this.childNodes.splice(this.childNodes.indexOf(e)>>>1,1),t.i.removeChild(e)}}),In(ln(ir,{context:t.context},e.__v),t.l)):t.l&&t.componentWillUnmount()}function sr(e,t){var n=ln(or,{__v:e,i:t});return n.containerInfo=t,n}(nr.prototype=new mn).__a=function(e){var t=this,n=tr(t.__v),r=t.o.get(e);return r[0]++,function(i){var o=function(){t.props.revealOrder?(r.push(i),rr(t,e,r)):i()};n?n(o):o()}},nr.prototype.render=function(e){this.u=null,this.o=new Map;var t=Cn(e.children);e.revealOrder&&"b"===e.revealOrder[0]&&t.reverse();for(var n=t.length;n--;)this.o.set(t[n],this.u=[1,0,this.u]);return e.children},nr.prototype.componentDidUpdate=nr.prototype.componentDidMount=function(){var e=this;this.o.forEach((function(t,n){rr(e,n,t)}))};var ar="undefined"!=typeof Symbol&&Symbol.for&&Symbol.for("react.element")||60103,lr=/^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|image|letter|lighting|marker(?!H|W|U)|overline|paint|pointer|shape|stop|strikethrough|stroke|text(?!L)|transform|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/,cr="undefined"!=typeof document,ur=function(e){return("undefined"!=typeof Symbol&&"symbol"==typeof Symbol()?/fil|che|rad/i:/fil|che|ra/i).test(e)};mn.prototype.isReactComponent={},["componentWillMount","componentWillReceiveProps","componentWillUpdate"].forEach((function(e){Object.defineProperty(mn.prototype,e,{configurable:!0,get:function(){return this["UNSAFE_"+e]},set:function(t){Object.defineProperty(this,e,{configurable:!0,writable:!0,value:t})}})}));var dr=qt.event;function fr(){}function hr(){return this.cancelBubble}function pr(){return this.defaultPrevented}qt.event=function(e){return dr&&(e=dr(e)),e.persist=fr,e.isPropagationStopped=hr,e.isDefaultPrevented=pr,e.nativeEvent=e};var gr={configurable:!0,get:function(){return this.class}},mr=qt.vnode;qt.vnode=function(e){var t=e.type,n=e.props,r=n;if("string"==typeof t){var i=-1===t.indexOf("-");for(var o in r={},n){var s=n[o];cr&&"children"===o&&"noscript"===t||"value"===o&&"defaultValue"in n&&null==s||("defaultValue"===o&&"value"in n&&null==n.value?o="value":"download"===o&&!0===s?s="":/ondoubleclick/i.test(o)?o="ondblclick":/^onchange(textarea|input)/i.test(o+t)&&!ur(n.type)?o="oninput":/^onfocus$/i.test(o)?o="onfocusin":/^onblur$/i.test(o)?o="onfocusout":/^on(Ani|Tra|Tou|BeforeInp|Compo)/.test(o)?o=o.toLowerCase():i&&lr.test(o)?o=o.replace(/[A-Z0-9]/g,"-$&").toLowerCase():null===s&&(s=void 0),/^oninput$/i.test(o)&&(o=o.toLowerCase(),r[o]&&(o="oninputCapture")),r[o]=s)}"select"==t&&r.multiple&&Array.isArray(r.value)&&(r.value=Cn(n.children).forEach((function(e){e.props.selected=-1!=r.value.indexOf(e.props.value)}))),"select"==t&&null!=r.defaultValue&&(r.value=Cn(n.children).forEach((function(e){e.props.selected=r.multiple?-1!=r.defaultValue.indexOf(e.props.value):r.defaultValue==e.props.value}))),e.props=r,n.class!=n.className&&(gr.enumerable="className"in n,null!=n.className&&(r.class=n.className),Object.defineProperty(r,"className",gr))}e.$$typeof=ar,mr&&mr(e)};var vr=qt.__r;function br(e){e();let t=qt.debounceRendering,n=[];for(qt.debounceRendering=function(e){n.push(e)},In(ln(Ar,{}),document.createElement("div"));n.length;)n.shift()();qt.debounceRendering=t}qt.__r=function(e){vr&&vr(e),e.__c};class Ar extends mn{render(){return ln("div",{})}componentDidMount(){this.setState({})}}function yr(e){let t=function(e,t){var n={__c:t="__cC"+tn++,__:e,Consumer:function(e,t){return e.children(t)},Provider:function(e){var n,r;return this.getChildContext||(n=[],(r={})[t]=this,this.getChildContext=function(){return r},this.shouldComponentUpdate=function(e){this.props.value!==e.value&&n.some((function(e){e.__e=!0,yn(e)}))},this.sub=function(e){n.push(e);var t=e.componentWillUnmount;e.componentWillUnmount=function(){n.splice(n.indexOf(e),1),t&&t.call(e)}}),e.children}};return n.Provider.__=n.Consumer.contextType=n}(e),n=t.Provider;return t.Provider=function(){let e=!this.getChildContext,t=n.apply(this,arguments);if(e){let e=[];this.shouldComponentUpdate=t=>{this.props.value!==t.value&&e.forEach(e=>{e.context=t.value,e.forceUpdate()})},this.sub=t=>{e.push(t);let n=t.componentWillUnmount;t.componentWillUnmount=()=>{e.splice(e.indexOf(t),1),n&&n.call(t)}}}return t},t}var _r={__proto__:null,flushSync:br,createContext:yr,createPortal:sr,Component:mn,Fragment:dn,cloneElement:function(e,t,n){var r,i,o,s=sn({},e.props);for(o in t)"key"==o?r=t[o]:"ref"==o?i=t[o]:s[o]=t[o];return arguments.length>2&&(s.children=arguments.length>3?Zt.call(arguments,2):n),cn(e.type,s,r||e.key,i||e.ref,null)},createElement:ln,createRef:un,h:ln,hydrate:function e(t,n){In(t,n,e)},get isValidElement(){return Jt},get options(){return qt},render:In,toChildArray:Cn};class Er{constructor(e,t,n,r){this.execFunc=e,this.emitter=t,this.scrollTime=n,this.scrollTimeReset=r,this.handleScrollRequest=e=>{this.queuedRequest=Object.assign({},this.queuedRequest||{},e),this.drain()},t.on("_scrollRequest",this.handleScrollRequest),this.fireInitialScroll()}detach(){this.emitter.off("_scrollRequest",this.handleScrollRequest)}update(e){e&&this.scrollTimeReset?this.fireInitialScroll():this.drain()}fireInitialScroll(){this.handleScrollRequest({time:this.scrollTime})}drain(){this.queuedRequest&&this.execFunc(this.queuedRequest)&&(this.queuedRequest=null)}}const Dr=yr({});function Cr(e,t,n,r,i,o,s,a,l,c,u,d,f,h){return{dateEnv:i,nowManager:o,options:n,pluginHooks:a,emitter:u,dispatch:l,getCurrentData:c,calendarApi:d,viewSpec:e,viewApi:t,dateProfileGenerator:r,theme:s,isRtl:"rtl"===n.direction,addResizeHandler(e){u.on("_resize",e)},removeResizeHandler(e){u.off("_resize",e)},createScrollResponder:e=>new Er(e,u,N(n.scrollTime),n.scrollTimeReset),registerInteractiveComponent:f,unregisterInteractiveComponent:h}}class wr extends mn{shouldComponentUpdate(e,t){return this.debug&&console.log(Z(e,this.props),Z(t,this.state)),!q(this.props,e,this.propEquality)||!q(this.state,t,this.stateEquality)}safeSetState(e){q(this.state,Object.assign(Object.assign({},this.state),e),this.stateEquality)||this.setState(e)}}wr.addPropsEquality=function(e){let t=Object.create(this.prototype.propEquality);Object.assign(t,e),this.prototype.propEquality=t},wr.addStateEquality=function(e){let t=Object.create(this.prototype.stateEquality);Object.assign(t,e),this.prototype.stateEquality=t},wr.contextType=Dr,wr.prototype.propEquality={},wr.prototype.stateEquality={};class Sr extends wr{}function Tr(e,t){"function"==typeof e?e(t):e&&(e.current=t)}Sr.contextType=Dr;class kr extends Sr{constructor(){super(...arguments),this.uid=D()}prepareHits(){}queryHit(e,t,n,r){return null}isValidSegDownEl(e){return!this.props.eventDrag&&!this.props.eventResize&&!l(e,".fc-event-mirror")}isValidDateDownEl(e){return!(l(e,".fc-event:not(.fc-bg-event)")||l(e,".fc-more-link")||l(e,"a[data-navlink]")||l(e,".fc-popover"))}}class Rr{constructor(e){this.props=e,this.initHiddenDays()}buildPrev(e,t,n){let{dateEnv:r}=this.props,i=r.subtract(r.startOf(t,e.currentRangeUnit),e.dateIncrement);return this.build(i,-1,n)}buildNext(e,t,n){let{dateEnv:r}=this.props,i=r.add(r.startOf(t,e.currentRangeUnit),e.dateIncrement);return this.build(i,1,n)}build(e,t,n=!0){let r,i,o,s,a,l,{props:c}=this;var u,d;return r=this.buildValidRange(),r=this.trimHiddenDays(r),n&&(u=e,e=null!=(d=r).start&&u<d.start?d.start:null!=d.end&&u>=d.end?new Date(d.end.valueOf()-1):u),i=this.buildCurrentRangeInfo(e,t),o=/^(year|month|week|day)$/.test(i.unit),s=this.buildRenderRange(this.trimHiddenDays(i.range),i.unit,o),s=this.trimHiddenDays(s),a=s,c.showNonCurrentDates||(a=Ct(a,i.range)),a=this.adjustActiveRange(a),a=Ct(a,r),l=St(i.range,r),kt(s,e)||(e=s.start),{currentDate:e,validRange:r,currentRange:i.range,currentRangeUnit:i.unit,isRangeAllDay:o,activeRange:a,renderRange:s,slotMinTime:c.slotMinTime,slotMaxTime:c.slotMaxTime,isValid:l,dateIncrement:this.buildDateIncrement(i.duration)}}buildValidRange(){let e=this.props.validRangeInput,t="function"==typeof e?e.call(this.props.calendarApi,this.props.dateEnv.toDate(this.props.nowManager.getDateMarker())):e;return this.refineRange(t)||{start:null,end:null}}buildCurrentRangeInfo(e,t){let n,{props:r}=this,i=null,o=null,s=null;return r.duration?(i=r.duration,o=r.durationUnit,s=this.buildRangeFromDuration(e,t,i,o)):(n=this.props.dayCount)?(o="day",s=this.buildRangeFromDayCount(e,t,n)):(s=this.buildCustomVisibleRange(e))?o=r.dateEnv.greatestWholeUnit(s.start,s.end).unit:(i=this.getFallbackDuration(),o=U(i).unit,s=this.buildRangeFromDuration(e,t,i,o)),{duration:i,unit:o,range:s}}getFallbackDuration(){return N({day:1})}adjustActiveRange(e){let{dateEnv:t,usesMinMaxTime:n,slotMinTime:r,slotMaxTime:i}=this.props,{start:o,end:s}=e;return n&&(H(r)<0&&(o=oe(o),o=t.add(o,r)),H(i)>1&&(s=oe(s),s=K(s,-1),s=t.add(s,i))),{start:o,end:s}}buildRangeFromDuration(e,t,n,r){let i,o,s,{dateEnv:a,dateAlignment:l}=this.props;if(!l){let{dateIncrement:e}=this.props;l=e&&P(e)<P(n)?U(e).unit:r}function c(){i=a.startOf(e,l),o=a.add(i,n),s={start:i,end:o}}return H(n)<=1&&this.isHiddenDay(i)&&(i=this.skipHiddenDays(i,t),i=oe(i)),c(),this.trimHiddenDays(s)||(e=this.skipHiddenDays(e,t),c()),s}buildRangeFromDayCount(e,t,n){let r,{dateEnv:i,dateAlignment:o}=this.props,s=0,a=e;o&&(a=i.startOf(a,o)),a=oe(a),a=this.skipHiddenDays(a,t),r=a;do{r=K(r,1),this.isHiddenDay(r)||(s+=1)}while(s<n);return{start:a,end:r}}buildCustomVisibleRange(e){let{props:t}=this,n=t.visibleRangeInput,r="function"==typeof n?n.call(t.calendarApi,t.dateEnv.toDate(e)):n,i=this.refineRange(r);return!i||null!=i.start&&null!=i.end?i:null}buildRenderRange(e,t,n){return e}buildDateIncrement(e){let t,{dateIncrement:n}=this.props;return n||((t=this.props.dateAlignment)?N(1,t):e||N({days:1}))}refineRange(e){if(e){let t=function(e,t){let n=null,r=null;return e.start&&(n=t.createMarker(e.start)),e.end&&(r=t.createMarker(e.end)),n||r?n&&r&&r<n?null:{start:n,end:r}:null}(e,this.props.dateEnv);return t&&(t=gt(t)),t}return null}initHiddenDays(){let e,t=this.props.hiddenDays||[],n=[],r=0;for(!1===this.props.weekends&&t.push(0,6),e=0;e<7;e+=1)(n[e]=-1!==t.indexOf(e))||(r+=1);if(!r)throw new Error("invalid hiddenDays");this.isHiddenDayHash=n}trimHiddenDays(e){let{start:t,end:n}=e;return t&&(t=this.skipHiddenDays(t)),n&&(n=this.skipHiddenDays(n,-1,!0)),null==t||null==n||t<n?{start:t,end:n}:null}isHiddenDay(e){return e instanceof Date&&(e=e.getUTCDay()),this.isHiddenDayHash[e]}skipHiddenDays(e,t=1,n=!1){for(;this.isHiddenDayHash[(e.getUTCDay()+(n?t:0)+7)%7];)e=K(e,t);return e}}function xr(e,t,n){n.emitter.trigger("select",Object.assign(Object.assign({},Or(e,n)),{jsEvent:t?t.origEvent:null,view:n.viewApi||n.calendarApi.view}))}function Or(e,t){let n={};for(let r of t.pluginHooks.dateSpanTransforms)Object.assign(n,r(e,t));var r,i;return Object.assign(n,(r=e,i=t.dateEnv,Object.assign(Object.assign({},ti(r.range,i,r.allDay)),{allDay:r.allDay}))),n}function Mr(e,t,n){let{dateEnv:r,options:i}=n,o=t;return e?(o=oe(o),o=r.add(o,i.defaultAllDayEventDuration)):o=r.add(o,i.defaultTimedEventDuration),o}function Ir(e,t,n,r){let i=Vr(e.defs,t),o={defs:{},instances:{}};for(let t in e.defs){let s=e.defs[t];o.defs[t]=Nr(s,i[t],n,r)}for(let t in e.instances){let s=e.instances[t],a=o.defs[s.defId];o.instances[t]=Br(s,a,i[s.defId],n,r)}return o}function Nr(e,t,n,r){let i=n.standardProps||{};null==i.hasEnd&&t.durationEditable&&(n.startDelta||n.endDelta)&&(i.hasEnd=!0);let o=Object.assign(Object.assign(Object.assign({},e),i),{ui:Object.assign(Object.assign({},e.ui),i.ui)});n.extendedProps&&(o.extendedProps=Object.assign(Object.assign({},o.extendedProps),n.extendedProps));for(let e of r.pluginHooks.eventDefMutationAppliers)e(o,n,r);return!o.hasEnd&&r.options.forceEventDuration&&(o.hasEnd=!0),o}function Br(e,t,n,r,i){let{dateEnv:o}=i,s=r.standardProps&&!0===r.standardProps.allDay,a=r.standardProps&&!1===r.standardProps.hasEnd,l=Object.assign({},e);return s&&(l.range=pt(l.range)),r.datesDelta&&n.startEditable&&(l.range={start:o.add(l.range.start,r.datesDelta),end:o.add(l.range.end,r.datesDelta)}),r.startDelta&&n.durationEditable&&(l.range={start:o.add(l.range.start,r.startDelta),end:l.range.end}),r.endDelta&&n.durationEditable&&(l.range={start:l.range.start,end:o.add(l.range.end,r.endDelta)}),a&&(l.range={start:l.range.start,end:Mr(t.allDay,l.range.start,i)}),t.allDay&&(l.range={start:oe(l.range.start),end:oe(l.range.end)}),l.range.end<l.range.start&&(l.range.end=Mr(t.allDay,l.range.start,i)),l}class Hr{constructor(e,t){this.context=e,this.internalEventSource=t}remove(){this.context.dispatch({type:"REMOVE_EVENT_SOURCE",sourceId:this.internalEventSource.sourceId})}refetch(){this.context.dispatch({type:"FETCH_EVENT_SOURCES",sourceIds:[this.internalEventSource.sourceId],isRefetch:!0})}get id(){return this.internalEventSource.publicId}get url(){return this.internalEventSource.meta.url}get format(){return this.internalEventSource.meta.format}}class Pr{constructor(e,t,n){this._context=e,this._def=t,this._instance=n||null}setProp(e,t){if(e in st)console.warn("Could not set date-related prop 'name'. Use one of the date-related methods instead.");else if("id"===e)t=ot[e](t),this.mutate({standardProps:{publicId:t}});else if(e in ot)t=ot[e](t),this.mutate({standardProps:{[e]:t}});else if(e in et){let n=et[e](t);n="color"===e?{backgroundColor:t,borderColor:t}:"editable"===e?{startEditable:t,durationEditable:t}:{[e]:t},this.mutate({standardProps:{ui:n}})}else console.warn(`Could not set prop '${e}'. Use setExtendedProp instead.`)}setExtendedProp(e,t){this.mutate({extendedProps:{[e]:t}})}setStart(e,t={}){let{dateEnv:n}=this._context,r=n.createMarker(e);if(r&&this._instance){let e=mt(this._instance.range.start,r,n,t.granularity);t.maintainDuration?this.mutate({datesDelta:e}):this.mutate({startDelta:e})}}setEnd(e,t={}){let n,{dateEnv:r}=this._context;if((null==e||(n=r.createMarker(e),n))&&this._instance)if(n){let e=mt(this._instance.range.end,n,r,t.granularity);this.mutate({endDelta:e})}else this.mutate({standardProps:{hasEnd:!1}})}setDates(e,t,n={}){let r,{dateEnv:i}=this._context,o={allDay:n.allDay},s=i.createMarker(e);var a,l;if(s&&((null==t||(r=i.createMarker(t),r))&&this._instance)){let e=this._instance.range;!0===n.allDay&&(e=pt(e));let t=mt(e.start,s,i,n.granularity);if(r){let s=mt(e.end,r,i,n.granularity);l=s,(a=t).years===l.years&&a.months===l.months&&a.days===l.days&&a.milliseconds===l.milliseconds?this.mutate({datesDelta:t,standardProps:o}):this.mutate({startDelta:t,endDelta:s,standardProps:o})}else o.hasEnd=!1,this.mutate({datesDelta:t,standardProps:o})}}moveStart(e){let t=N(e);t&&this.mutate({startDelta:t})}moveEnd(e){let t=N(e);t&&this.mutate({endDelta:t})}moveDates(e){let t=N(e);t&&this.mutate({datesDelta:t})}setAllDay(e,t={}){let n={allDay:e},{maintainDuration:r}=t;null==r&&(r=this._context.options.allDayMaintainDuration),this._def.allDay!==e&&(n.hasEnd=r),this.mutate({standardProps:n})}formatRange(e){let{dateEnv:t}=this._context,n=this._instance,r=Me(e);return this._def.hasEnd?t.formatRange(n.range.start,n.range.end,r,{forcedStartTzo:n.forcedStartTzo,forcedEndTzo:n.forcedEndTzo}):t.format(n.range.start,r,{forcedTzo:n.forcedStartTzo})}mutate(e){let t=this._instance;if(t){let n=this._def,r=this._context,{eventStore:i}=r.getCurrentData(),o=Ye(i,t.instanceId);o=Ir(o,{"":{display:"",startEditable:!0,durationEditable:!0,constraints:[],overlap:null,allows:[],backgroundColor:"",borderColor:"",textColor:"",classNames:[]}},e,r);let s=new Pr(r,n,t);this._def=o.defs[n.defId],this._instance=o.instances[t.instanceId],r.dispatch({type:"MERGE_EVENTS",eventStore:o}),r.emitter.trigger("eventChange",{oldEvent:s,event:this,relatedEvents:jr(o,r,t),revert(){r.dispatch({type:"RESET_EVENTS",eventStore:i})}})}}remove(){let e=this._context,t=Ur(this);e.dispatch({type:"REMOVE_EVENTS",eventStore:t}),e.emitter.trigger("eventRemove",{event:this,relatedEvents:[],revert(){e.dispatch({type:"MERGE_EVENTS",eventStore:t})}})}get source(){let{sourceId:e}=this._def;return e?new Hr(this._context,this._context.getCurrentData().eventSources[e]):null}get start(){return this._instance?this._context.dateEnv.toDate(this._instance.range.start):null}get end(){return this._instance&&this._def.hasEnd?this._context.dateEnv.toDate(this._instance.range.end):null}get startStr(){let e=this._instance;return e?this._context.dateEnv.formatIso(e.range.start,{omitTime:this._def.allDay,forcedTzo:e.forcedStartTzo}):""}get endStr(){let e=this._instance;return e&&this._def.hasEnd?this._context.dateEnv.formatIso(e.range.end,{omitTime:this._def.allDay,forcedTzo:e.forcedEndTzo}):""}get id(){return this._def.publicId}get groupId(){return this._def.groupId}get allDay(){return this._def.allDay}get title(){return this._def.title}get url(){return this._def.url}get display(){return this._def.ui.display||"auto"}get startEditable(){return this._def.ui.startEditable}get durationEditable(){return this._def.ui.durationEditable}get constraint(){return this._def.ui.constraints[0]||null}get overlap(){return this._def.ui.overlap}get allow(){return this._def.ui.allows[0]||null}get backgroundColor(){return this._def.ui.backgroundColor}get borderColor(){return this._def.ui.borderColor}get textColor(){return this._def.ui.textColor}get classNames(){return this._def.ui.classNames}get extendedProps(){return this._def.extendedProps}toPlainObject(e={}){let t=this._def,{ui:n}=t,{startStr:r,endStr:i}=this,o={allDay:t.allDay};return t.title&&(o.title=t.title),r&&(o.start=r),i&&(o.end=i),t.publicId&&(o.id=t.publicId),t.groupId&&(o.groupId=t.groupId),t.url&&(o.url=t.url),n.display&&"auto"!==n.display&&(o.display=n.display),e.collapseColor&&n.backgroundColor&&n.backgroundColor===n.borderColor?o.color=n.backgroundColor:(n.backgroundColor&&(o.backgroundColor=n.backgroundColor),n.borderColor&&(o.borderColor=n.borderColor)),n.textColor&&(o.textColor=n.textColor),n.classNames.length&&(o.classNames=n.classNames),Object.keys(t.extendedProps).length&&(e.collapseExtendedProps?Object.assign(o,t.extendedProps):o.extendedProps=t.extendedProps),o}toJSON(){return this.toPlainObject()}}function Ur(e){let t=e._def,n=e._instance;return{defs:{[t.defId]:t},instances:n?{[n.instanceId]:n}:{}}}function jr(e,t,n){let{defs:r,instances:i}=e,o=[],s=n?n.instanceId:"";for(let e in i){let n=i[e],a=r[n.defId];n.instanceId!==s&&o.push(new Pr(t,a,n))}return o}function zr(e,t,n,r){let i={},o={},s={},a=[],l=[],c=Vr(e.defs,t);for(let t in e.defs){let n=e.defs[t];"inverse-background"===c[n.defId].display&&(n.groupId?(i[n.groupId]=[],s[n.groupId]||(s[n.groupId]=n)):o[t]=[])}for(let t in e.instances){let s=e.instances[t],u=e.defs[s.defId],d=c[u.defId],f=s.range,h=!u.allDay&&r?gt(f,r):f,p=Ct(h,n);p&&("inverse-background"===d.display?u.groupId?i[u.groupId].push(p):o[s.defId].push(p):"none"!==d.display&&("background"===d.display?a:l).push({def:u,ui:d,instance:s,range:p,isStart:h.start&&h.start.valueOf()===p.start.valueOf(),isEnd:h.end&&h.end.valueOf()===p.end.valueOf()}))}for(let e in i){let t=Et(i[e],n);for(let n of t){let t=s[e],r=c[t.defId];a.push({def:t,ui:r,instance:null,range:n,isStart:!1,isEnd:!1})}}for(let t in o){let r=Et(o[t],n);for(let n of r)a.push({def:e.defs[t],ui:c[t],instance:null,range:n,isStart:!1,isEnd:!1})}return{bg:a,fg:l}}function Fr(e,t){e.fcSeg=t}function Lr(e){return e.fcSeg||e.parentNode.fcSeg||null}function Vr(e,t){return L(e,e=>Wr(e,t))}function Wr(e,t){let n=[];return t[""]&&n.push(t[""]),t[e.defId]&&n.push(t[e.defId]),n.push(e.ui),rt(n)}function Qr(e){let{eventRange:t}=e,n=t.def,r=t.instance?t.instance.range:t.range,i=r.start?r.start.valueOf():0,o=r.end?r.end.valueOf():0;return Object.assign(Object.assign(Object.assign({},n.extendedProps),n),{id:n.publicId,start:i,end:o,duration:o-i,allDay:Number(n.allDay),_seg:e})}function Gr(e,t){let{pluginHooks:n}=t,r=n.isDraggableTransformers,{def:i,ui:o}=e.eventRange,s=o.startEditable;for(let e of r)s=e(s,i,o,t);return s}function Zr(e,t){return e.isStart&&e.eventRange.ui.durationEditable&&t.options.eventResizableFromStart}function qr(e,t){return e.isEnd&&e.eventRange.ui.durationEditable}function Yr(e,t,n,r,i,o,s){let{dateEnv:a,options:l}=n,{displayEventTime:c,displayEventEnd:u}=l,d=e.eventRange.def,f=e.eventRange.instance;null==c&&(c=!1!==r),null==u&&(u=!1!==i);let h=f.range.start,p=f.range.end,g=o||e.start||e.eventRange.range.start,m=s||e.end||e.eventRange.range.end,v=oe(h).valueOf()===oe(g).valueOf(),b=oe(ee(p,-1)).valueOf()===oe(ee(m,-1)).valueOf();return c&&!d.allDay&&(v||b)?(g=v?h:g,m=b?p:m,u&&d.hasEnd?a.formatRange(g,m,t,{forcedStartTzo:o?null:f.forcedStartTzo,forcedEndTzo:s?null:f.forcedEndTzo}):a.format(g,t,{forcedTzo:o?null:f.forcedStartTzo})):""}function Jr(e){let t=["fc-event"];return e.isMirror&&t.push("fc-event-mirror"),e.isDraggable&&t.push("fc-event-draggable"),(e.isStartResizable||e.isEndResizable)&&t.push("fc-event-resizable"),e.isDragging&&t.push("fc-event-dragging"),e.isResizing&&t.push("fc-event-resizing"),e.isSelected&&t.push("fc-event-selected"),e.isStart&&t.push("fc-event-start"),e.isEnd&&t.push("fc-event-end"),e.isPast&&t.push("fc-event-past"),e.isToday&&t.push("fc-event-today"),e.isFuture&&t.push("fc-event-future"),t}function $r(e,t){let{def:n,instance:r}=e.eventRange,{url:i}=n;if(i)return{href:i};let{emitter:o,options:s}=t,{eventInteractive:a}=s;return null==a&&(a=n.interactive,null==a&&(a=Boolean(o.hasHandlers("eventClick")))),a?_(e=>{o.trigger("eventClick",{el:e.target,event:new Pr(t,n,r),jsEvent:e,view:t.viewApi})}):{}}const Xr={start:Ve,end:Ve,allDay:Boolean};function Kr(e,t,n){let r=function(e,t){let{refined:n,extra:r}=Le(e,Xr),i=n.start?t.createMarkerMeta(n.start):null,o=n.end?t.createMarkerMeta(n.end):null,{allDay:s}=n;null==s&&(s=i&&i.isTimeUnspecified&&(!o||o.isTimeUnspecified));return Object.assign({range:{start:i?i.marker:null,end:o?o.marker:null},allDay:s},r)}(e,t),{range:i}=r;if(!i.start)return null;if(!i.end){if(null==n)return null;i.end=t.add(i.start,n)}return r}function ei(e,t,n){return Object.assign(Object.assign({},ti(e,t,n)),{timeZone:t.timeZone})}function ti(e,t,n){return{start:t.toDate(e.start),end:t.toDate(e.end),startStr:t.formatIso(e.start,{omitTime:n}),endStr:t.formatIso(e.end,{omitTime:n})}}let ni={};var ri,ii;ri="gregory",ii=class{getMarkerYear(e){return e.getUTCFullYear()}getMarkerMonth(e){return e.getUTCMonth()}getMarkerDay(e){return e.getUTCDate()}arrayToMarker(e){return de(e)}markerToArray(e){return ue(e)}},ni[ri]=ii;const oi=/^\s*(\d{4})(-?(\d{2})(-?(\d{2})([T ](\d{2}):?(\d{2})(:?(\d{2})(\.(\d+))?)?(Z|(([-+])(\d{2})(:?(\d{2}))?))?)?)?)?$/;function si(e){let t=oi.exec(e);if(t){let e=new Date(Date.UTC(Number(t[1]),t[3]?Number(t[3])-1:0,Number(t[5]||1),Number(t[7]||0),Number(t[8]||0),Number(t[10]||0),t[12]?1e3*Number("0."+t[12]):0));if(fe(e)){let n=null;return t[13]&&(n=("-"===t[15]?-1:1)*(60*Number(t[16]||0)+Number(t[18]||0))),{marker:e,isTimeUnspecified:!t[6],timeZoneOffset:n}}}return null}class ai{constructor(e){let t=this.timeZone=e.timeZone,n="local"!==t&&"UTC"!==t;e.namedTimeZoneImpl&&n&&(this.namedTimeZoneImpl=new e.namedTimeZoneImpl(t)),this.canComputeOffset=Boolean(!n||this.namedTimeZoneImpl),this.calendarSystem=function(e){return new ni[e]}(e.calendarSystem),this.locale=e.locale,this.weekDow=e.locale.week.dow,this.weekDoy=e.locale.week.doy,"ISO"===e.weekNumberCalculation&&(this.weekDow=1,this.weekDoy=4),"number"==typeof e.firstDay&&(this.weekDow=e.firstDay),"function"==typeof e.weekNumberCalculation&&(this.weekNumberFunc=e.weekNumberCalculation),this.weekText=null!=e.weekText?e.weekText:e.locale.options.weekText,this.weekTextLong=(null!=e.weekTextLong?e.weekTextLong:e.locale.options.weekTextLong)||this.weekText,this.cmdFormatter=e.cmdFormatter,this.defaultSeparator=e.defaultSeparator}createMarker(e){let t=this.createMarkerMeta(e);return null===t?null:t.marker}createNowMarker(){return this.canComputeOffset?this.timestampToMarker((new Date).valueOf()):de(le(new Date))}createMarkerMeta(e){if("string"==typeof e)return this.parse(e);let t=null;return"number"==typeof e?t=this.timestampToMarker(e):e instanceof Date?(e=e.valueOf(),isNaN(e)||(t=this.timestampToMarker(e))):Array.isArray(e)&&(t=de(e)),null!==t&&fe(t)?{marker:t,isTimeUnspecified:!1,forcedTzo:null}:null}parse(e){let t=si(e);if(null===t)return null;let{marker:n}=t,r=null;return null!==t.timeZoneOffset&&(this.canComputeOffset?n=this.timestampToMarker(n.valueOf()-60*t.timeZoneOffset*1e3):r=t.timeZoneOffset),{marker:n,isTimeUnspecified:t.isTimeUnspecified,forcedTzo:r}}getYear(e){return this.calendarSystem.getMarkerYear(e)}getMonth(e){return this.calendarSystem.getMarkerMonth(e)}getDay(e){return this.calendarSystem.getMarkerDay(e)}add(e,t){let n=this.calendarSystem.markerToArray(e);return n[0]+=t.years,n[1]+=t.months,n[2]+=t.days,n[6]+=t.milliseconds,this.calendarSystem.arrayToMarker(n)}subtract(e,t){let n=this.calendarSystem.markerToArray(e);return n[0]-=t.years,n[1]-=t.months,n[2]-=t.days,n[6]-=t.milliseconds,this.calendarSystem.arrayToMarker(n)}addYears(e,t){let n=this.calendarSystem.markerToArray(e);return n[0]+=t,this.calendarSystem.arrayToMarker(n)}addMonths(e,t){let n=this.calendarSystem.markerToArray(e);return n[1]+=t,this.calendarSystem.arrayToMarker(n)}diffWholeYears(e,t){let{calendarSystem:n}=this;return he(e)===he(t)&&n.getMarkerDay(e)===n.getMarkerDay(t)&&n.getMarkerMonth(e)===n.getMarkerMonth(t)?n.getMarkerYear(t)-n.getMarkerYear(e):null}diffWholeMonths(e,t){let{calendarSystem:n}=this;return he(e)===he(t)&&n.getMarkerDay(e)===n.getMarkerDay(t)?n.getMarkerMonth(t)-n.getMarkerMonth(e)+12*(n.getMarkerYear(t)-n.getMarkerYear(e)):null}greatestWholeUnit(e,t){let n=this.diffWholeYears(e,t);return null!==n?{unit:"year",value:n}:(n=this.diffWholeMonths(e,t),null!==n?{unit:"month",value:n}:(n=re(e,t),null!==n?{unit:"week",value:n}:(n=ie(e,t),null!==n?{unit:"day",value:n}:(n=function(e,t){return(t.valueOf()-e.valueOf())/36e5}(e,t),x(n)?{unit:"hour",value:n}:(n=function(e,t){return(t.valueOf()-e.valueOf())/6e4}(e,t),x(n)?{unit:"minute",value:n}:(n=function(e,t){return(t.valueOf()-e.valueOf())/1e3}(e,t),x(n)?{unit:"second",value:n}:{unit:"millisecond",value:t.valueOf()-e.valueOf()}))))))}countDurationsBetween(e,t,n){let r;return n.years&&(r=this.diffWholeYears(e,t),null!==r)?r/(H(n)/365):n.months&&(r=this.diffWholeMonths(e,t),null!==r)?r/function(e){return H(e)/30}(n):n.days&&(r=ie(e,t),null!==r)?r/H(n):(t.valueOf()-e.valueOf())/P(n)}startOf(e,t){return"year"===t?this.startOfYear(e):"month"===t?this.startOfMonth(e):"week"===t?this.startOfWeek(e):"day"===t?oe(e):"hour"===t?function(e){return de([e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate(),e.getUTCHours()])}(e):"minute"===t?function(e){return de([e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate(),e.getUTCHours(),e.getUTCMinutes()])}(e):"second"===t?function(e){return de([e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate(),e.getUTCHours(),e.getUTCMinutes(),e.getUTCSeconds()])}(e):null}startOfYear(e){return this.calendarSystem.arrayToMarker([this.calendarSystem.getMarkerYear(e)])}startOfMonth(e){return this.calendarSystem.arrayToMarker([this.calendarSystem.getMarkerYear(e),this.calendarSystem.getMarkerMonth(e)])}startOfWeek(e){return this.calendarSystem.arrayToMarker([this.calendarSystem.getMarkerYear(e),this.calendarSystem.getMarkerMonth(e),e.getUTCDate()-(e.getUTCDay()-this.weekDow+7)%7])}computeWeekNumber(e){return this.weekNumberFunc?this.weekNumberFunc(this.toDate(e)):function(e,t,n){let r=e.getUTCFullYear(),i=se(e,r,t,n);if(i<1)return se(e,r-1,t,n);let o=se(e,r+1,t,n);return o>=1?Math.min(i,o):i}(e,this.weekDow,this.weekDoy)}format(e,t,n={}){return t.format({marker:e,timeZoneOffset:null!=n.forcedTzo?n.forcedTzo:this.offsetForMarker(e)},this)}formatRange(e,t,n,r={}){return r.isEndExclusive&&(t=ee(t,-1)),n.formatRange({marker:e,timeZoneOffset:null!=r.forcedStartTzo?r.forcedStartTzo:this.offsetForMarker(e)},{marker:t,timeZoneOffset:null!=r.forcedEndTzo?r.forcedEndTzo:this.offsetForMarker(t)},this,r.defaultSeparator)}formatIso(e,t={}){let n=null;return t.omitTimeZoneOffset||(n=null!=t.forcedTzo?t.forcedTzo:this.offsetForMarker(e)),pe(e,n,t.omitTime)}timestampToMarker(e){return"local"===this.timeZone?de(le(new Date(e))):"UTC"!==this.timeZone&&this.namedTimeZoneImpl?de(this.namedTimeZoneImpl.timestampToArray(e)):new Date(e)}offsetForMarker(e){return"local"===this.timeZone?-ce(ue(e)).getTimezoneOffset():"UTC"===this.timeZone?0:this.namedTimeZoneImpl?this.namedTimeZoneImpl.offsetForArray(ue(e)):null}toDate(e,t){return"local"===this.timeZone?ce(ue(e)):"UTC"===this.timeZone?new Date(e.valueOf()):this.namedTimeZoneImpl?new Date(e.valueOf()-1e3*this.namedTimeZoneImpl.offsetForArray(ue(e))*60):new Date(e.valueOf()-(t||0))}}function li(e){return e.span.end}function ci(e){return e.index+":"+e.span.start}function ui(e,t){let n=Math.max(e.start,t.start),r=Math.min(e.end,t.end);return n<r?{start:n,end:r}:null}function di(e,t,n){e.splice(t,0,n)}function fi(e,t,n){let r=0,i=e.length;if(!i||t<n(e[r]))return[0,0];if(t>n(e[i-1]))return[i,0];for(;r<i;){let o=Math.floor(r+(i-r)/2),s=n(e[o]);if(t<s)i=o;else{if(!(t>s))return[o,1];r=o+1}}return[r,0]}class hi{constructor(e){this.component=e.component,this.isHitComboAllowed=e.isHitComboAllowed||null}destroy(){}}const pi={};const gi={startTime:N,duration:N,create:Boolean,sourceId:String};class mi extends Sr{constructor(){super(...arguments),this.state={forPrint:!1},this.handleBeforePrint=()=>{br(()=>{this.setState({forPrint:!0})})},this.handleAfterPrint=()=>{br(()=>{this.setState({forPrint:!1})})}}render(){let{props:e}=this,{options:t}=e,{forPrint:n}=this.state,r=n||"auto"===t.height||"auto"===t.contentHeight,i=r||null==t.height?"":t.height,o=["fc",n?"fc-media-print":"fc-media-screen","fc-direction-"+t.direction,e.theme.getClass("root")];return At()||o.push("fc-liquid-hack"),e.children(o,i,r,n)}componentDidMount(){let{emitter:e}=this.props;e.on("_beforeprint",this.handleBeforePrint),e.on("_afterprint",this.handleAfterPrint)}componentWillUnmount(){let{emitter:e}=this.props;e.off("_beforeprint",this.handleBeforePrint),e.off("_afterprint",this.handleAfterPrint)}}function vi(e,t){return Me(!e||t>10?{weekday:"short"}:t>1?{weekday:"short",month:"numeric",day:"numeric",omitCommas:!0}:{weekday:"long"})}const bi="fc-col-header-cell";function Ai(e){return e.text}class yi extends Sr{constructor(){super(...arguments),this.id=D(),this.queuedDomNodes=[],this.currentDomNodes=[],this.handleEl=e=>{const{options:t}=this.context,{generatorName:n}=this.props;t.customRenderingReplaces&&_i(n,t)||this.updateElRef(e)},this.updateElRef=e=>{this.props.elRef&&Tr(this.props.elRef,e)}}render(){const{props:e,context:t}=this,{options:n}=t,{customGenerator:r,defaultGenerator:i,renderProps:o}=e,s=Ei(e,[],this.handleEl);let a,l,c=!1,u=[];if(null!=r){const e="function"==typeof r?r(o,ln):r;if(!0===e)c=!0;else{const t=e&&"object"==typeof e;t&&"html"in e?s.dangerouslySetInnerHTML={__html:e.html}:t&&"domNodes"in e?u=Array.prototype.slice.call(e.domNodes):(t?Jt(e):"function"!=typeof e)?a=e:l=e}}else c=!_i(e.generatorName,n);return c&&i&&(a=i(o)),this.queuedDomNodes=u,this.currentGeneratorMeta=l,ln(e.elTag,s,a)}componentDidMount(){this.applyQueueudDomNodes(),this.triggerCustomRendering(!0)}componentDidUpdate(){this.applyQueueudDomNodes(),this.triggerCustomRendering(!0)}componentWillUnmount(){this.triggerCustomRendering(!1)}triggerCustomRendering(e){var t;const{props:n,context:r}=this,{handleCustomRendering:i,customRenderingMetaMap:o}=r.options;if(i){const r=null!==(t=this.currentGeneratorMeta)&&void 0!==t?t:null==o?void 0:o[n.generatorName];r&&i(Object.assign(Object.assign({id:this.id,isActive:e,containerEl:this.base,reportNewContainerEl:this.updateElRef,generatorMeta:r},n),{elClasses:(n.elClasses||[]).filter(Di)}))}}applyQueueudDomNodes(){const{queuedDomNodes:e,currentDomNodes:t}=this,n=this.base;if(!$(e,t)){t.forEach(a);for(let t of e)n.appendChild(t);this.currentDomNodes=e}}}function _i(e,t){var n;return Boolean(t.handleCustomRendering&&e&&(null===(n=t.customRenderingMetaMap)||void 0===n?void 0:n[e]))}function Ei(e,t,n){const r=Object.assign(Object.assign({},e.elAttrs),{ref:n});return(e.elClasses||t)&&(r.className=(e.elClasses||[]).concat(t||[]).concat(r.className||[]).filter(Boolean).join(" ")),e.elStyle&&(r.style=e.elStyle),r}function Di(e){return Boolean(e)}yi.addPropsEquality({elClasses:$,elStyle:Q,elAttrs:function(e,t){const n=Z(e,t);for(let e of n)if(!G.test(e))return!1;return!0},renderProps:Q});const Ci=yr(0);class wi extends mn{constructor(){super(...arguments),this.InnerContent=Si.bind(void 0,this),this.handleEl=e=>{this.el=e,this.props.elRef&&(Tr(this.props.elRef,e),e&&this.didMountMisfire&&this.componentDidMount())}}render(){const{props:e}=this,t=function(e,t){const n="function"==typeof e?e(t):e||[];return"string"==typeof n?[n]:n}(e.classNameGenerator,e.renderProps);if(e.children){const n=Ei(e,t,this.handleEl),r=e.children(this.InnerContent,e.renderProps,n);return e.elTag?ln(e.elTag,n,r):r}return ln(yi,Object.assign(Object.assign({},e),{elRef:this.handleEl,elTag:e.elTag||"div",elClasses:(e.elClasses||[]).concat(t),renderId:this.context}))}componentDidMount(){var e,t;this.el?null===(t=(e=this.props).didMount)||void 0===t||t.call(e,Object.assign(Object.assign({},this.props.renderProps),{el:this.el})):this.didMountMisfire=!0}componentWillUnmount(){var e,t;null===(t=(e=this.props).willUnmount)||void 0===t||t.call(e,Object.assign(Object.assign({},this.props.renderProps),{el:this.el}))}}function Si(e,t){const n=e.props;return ln(yi,Object.assign({renderProps:n.renderProps,generatorName:n.generatorName,customGenerator:n.customGenerator,defaultGenerator:n.defaultGenerator,renderId:e.context},t))}wi.contextType=Ci;class Ti extends Sr{render(){let{dateEnv:e,options:t,theme:n,viewApi:r}=this.context,{props:i}=this,{date:o,dateProfile:s}=i,a=Rt(o,i.todayRange,null,s),l=[bi].concat(xt(a,n)),c=e.format(o,i.dayHeaderFormat),u=!a.isDisabled&&i.colCnt>1?It(this.context,o):{},d=e.toDate(o);e.namedTimeZoneImpl&&(d=ee(d,36e5));let f=Object.assign(Object.assign(Object.assign({date:d,view:r},i.extraRenderProps),{text:c}),a);return ln(wi,{elTag:"th",elClasses:l,elAttrs:Object.assign({role:"columnheader",colSpan:i.colSpan,"data-date":a.isDisabled?void 0:ge(o)},i.extraDataAttrs),renderProps:f,generatorName:"dayHeaderContent",customGenerator:t.dayHeaderContent,defaultGenerator:Ai,classNameGenerator:t.dayHeaderClassNames,didMount:t.dayHeaderDidMount,willUnmount:t.dayHeaderWillUnmount},e=>ln("div",{className:"fc-scrollgrid-sync-inner"},!a.isDisabled&&ln(e,{elTag:"a",elAttrs:u,elClasses:["fc-col-header-cell-cushion",i.isSticky&&"fc-sticky"]})))}}const ki=Me({weekday:"long"});class Ri extends Sr{render(){let{props:e}=this,{dateEnv:t,theme:n,viewApi:r,options:i}=this.context,o=K(new Date(2592e5),e.dow),s={dow:e.dow,isDisabled:!1,isFuture:!1,isPast:!1,isToday:!1,isOther:!1},a=t.format(o,e.dayHeaderFormat),l=Object.assign(Object.assign(Object.assign(Object.assign({date:o},s),{view:r}),e.extraRenderProps),{text:a});return ln(wi,{elTag:"th",elClasses:[bi,...xt(s,n),...e.extraClassNames||[]],elAttrs:Object.assign({role:"columnheader",colSpan:e.colSpan},e.extraDataAttrs),renderProps:l,generatorName:"dayHeaderContent",customGenerator:i.dayHeaderContent,defaultGenerator:Ai,classNameGenerator:i.dayHeaderClassNames,didMount:i.dayHeaderDidMount,willUnmount:i.dayHeaderWillUnmount},n=>ln("div",{className:"fc-scrollgrid-sync-inner"},ln(n,{elTag:"a",elClasses:["fc-col-header-cell-cushion",e.isSticky&&"fc-sticky"],elAttrs:{"aria-label":t.format(o,ki)}})))}}class xi extends mn{constructor(e,t){super(e,t),this.handleRefresh=()=>{let e=this.computeTiming();e.state.nowDate.valueOf()!==this.state.nowDate.valueOf()&&this.setState(e.state),this.clearTimeout(),this.setTimeout(e.waitMs)},this.handleVisibilityChange=()=>{document.hidden||this.handleRefresh()},this.state=this.computeTiming().state}render(){let{props:e,state:t}=this;return e.children(t.nowDate,t.todayRange)}componentDidMount(){this.setTimeout(),this.context.nowManager.addResetListener(this.handleRefresh),document.addEventListener("visibilitychange",this.handleVisibilityChange)}componentDidUpdate(e){e.unit!==this.props.unit&&(this.clearTimeout(),this.setTimeout())}componentWillUnmount(){this.clearTimeout(),this.context.nowManager.removeResetListener(this.handleRefresh),document.removeEventListener("visibilitychange",this.handleVisibilityChange)}computeTiming(){let{props:e,context:t}=this,n=t.nowManager.getDateMarker(),r=t.dateEnv.startOf(n,e.unit),i=t.dateEnv.add(r,N(1,e.unit)).valueOf()-n.valueOf();return i=Math.min(864e5,i),{state:{nowDate:r,todayRange:Oi(r)},waitMs:i}}setTimeout(e=this.computeTiming().waitMs){this.timeoutId=setTimeout(()=>{const e=this.computeTiming();this.setState(e.state,()=>{this.setTimeout(e.waitMs)})},e)}clearTimeout(){this.timeoutId&&clearTimeout(this.timeoutId)}}function Oi(e){let t=oe(e);return{start:t,end:K(t,1)}}xi.contextType=Dr;function Mi(e,t,n){return e||vi(t,n)}function Ii(e,t){let n=e.activeRange;return t?n:{start:ee(n.start,e.slotMinTime.milliseconds),end:ee(n.end,e.slotMaxTime.milliseconds-864e5)}}function Ni(e,t,n,r,i){switch(t.type){case"RECEIVE_EVENTS":return function(e,t,n,r,i,o){if(t&&n===t.latestFetchId){let n=Ze(Bi(i,t,o),t,o);return r&&(n=Qe(n,r,o)),$e(Ui(e,t.sourceId),n)}return e}(e,n[t.sourceId],t.fetchId,t.fetchRange,t.rawEvents,i);case"RESET_RAW_EVENTS":return function(e,t,n,r,i){const{defIdMap:o,instanceIdMap:s}=function(e){const{defs:t,instances:n}=e,r={},i={};for(let e in t){const n=t[e],{publicId:i}=n;i&&(r[i]=e)}for(let e in n){const r=n[e],o=t[r.defId],{publicId:s}=o;s&&(i[s]=e)}return{defIdMap:r,instanceIdMap:i}}(e);return Qe(Ze(Bi(n,t,i),t,i,!1,o,s),r,i)}(e,n[t.sourceId],t.rawEvents,r.activeRange,i);case"ADD_EVENTS":return function(e,t,n,r){n&&(t=Qe(t,n,r));return $e(e,t)}(e,t.eventStore,r?r.activeRange:null,i);case"RESET_EVENTS":return t.eventStore;case"MERGE_EVENTS":return $e(e,t.eventStore);case"PREV":case"NEXT":case"CHANGE_DATE":case"CHANGE_VIEW_TYPE":return r?Qe(e,r.activeRange,i):e;case"REMOVE_EVENTS":return function(e,t){let{defs:n,instances:r}=e,i={},o={};for(let e in n)t.defs[e]||(i[e]=n[e]);for(let e in r)!t.instances[e]&&i[r[e].defId]&&(o[e]=r[e]);return{defs:i,instances:o}}(e,t.eventStore);case"REMOVE_EVENT_SOURCE":return Ui(e,t.sourceId);case"REMOVE_ALL_EVENT_SOURCES":return Xe(e,e=>!e.sourceId);case"REMOVE_ALL_EVENTS":return{defs:{},instances:{}};default:return e}}function Bi(e,t,n){let r=n.options.eventDataTransform,i=t?t.eventDataTransform:null;return i&&(e=Hi(e,i)),r&&(e=Hi(e,r)),e}function Hi(e,t){let n;if(t){n=[];for(let r of e){let e=t(r);e?n.push(e):null==e&&n.push(r)}}else n=e;return n}function Pi(e,t,n){let{defs:r}=e,i=L(e.instances,e=>r[e.defId].allDay?e:Object.assign(Object.assign({},e),{range:{start:n.createMarker(t.toDate(e.range.start,e.forcedStartTzo)),end:n.createMarker(t.toDate(e.range.end,e.forcedEndTzo))},forcedStartTzo:n.canComputeOffset?null:e.forcedStartTzo,forcedEndTzo:n.canComputeOffset?null:e.forcedEndTzo}));return{defs:r,instances:i}}function Ui(e,t){return Xe(e,e=>e.sourceId!==t)}function ji(e,t){let n=t.getCurrentData(),r=Object.assign({businessHours:n.businessHours,dateSelection:"",eventStore:n.eventStore,eventUiBases:n.eventUiBases,eventSelection:"",eventDrag:null,eventResize:null},e);return(t.pluginHooks.isPropsValid||zi)(r,t)}function zi(e,t,n={},r){return!(e.eventDrag&&!function(e,t,n,r){let i=t.getCurrentData(),o=e.eventDrag,s=o.mutatedEvents,a=s.defs,l=s.instances,c=Vr(a,o.isEvent?e.eventUiBases:{"":i.selectionConfig});r&&(c=L(c,r));let u=(p=e.eventStore,g=o.affectedEvents.instances,{defs:p.defs,instances:F(p.instances,e=>!g[e.instanceId])}),d=u.defs,f=u.instances,h=Vr(d,e.eventUiBases);var p,g;for(let r in l){let s=l[r],p=s.range,g=c[s.defId],m=a[s.defId];if(!Fi(g.constraints,p,u,e.businessHours,t))return!1;let{eventOverlap:v}=t.options,b="function"==typeof v?v:null;for(let e in f){let n=f[e];if(St(p,n.range)){if(!1===h[n.defId].overlap&&o.isEvent)return!1;if(!1===g.overlap)return!1;if(b&&!b(new Pr(t,d[n.defId],n),new Pr(t,m,s)))return!1}}let A=i.eventStore;for(let e of g.allows){let i,o=Object.assign(Object.assign({},n),{range:s.range,allDay:m.allDay}),a=A.defs[m.defId],l=A.instances[r];if(i=a?new Pr(t,a,l):new Pr(t,m),!e(Or(o,t),i))return!1}}return!0}(e,t,n,r))&&!(e.dateSelection&&!function(e,t,n,r){let i=e.eventStore,o=i.defs,s=i.instances,a=e.dateSelection,l=a.range,{selectionConfig:c}=t.getCurrentData();r&&(c=r(c));if(!Fi(c.constraints,l,i,e.businessHours,t))return!1;let{selectOverlap:u}=t.options,d="function"==typeof u?u:null;for(let e in s){let n=s[e];if(St(l,n.range)){if(!1===c.overlap)return!1;if(d&&!d(new Pr(t,o[n.defId],n),null))return!1}}for(let e of c.allows){let r=Object.assign(Object.assign({},n),a);if(!e(Or(r,t),null))return!1}return!0}(e,t,n,r))}function Fi(e,t,n,r,i){for(let o of e)if(!Wi(Li(o,t,n,r,i),t))return!1;return!0}function Li(e,t,n,r,i){return"businessHours"===e?Vi(Qe(r,t,i)):"string"==typeof e?Vi(Xe(n,t=>t.groupId===e)):"object"==typeof e&&e?Vi(Qe(e,t,i)):[]}function Vi(e){let{instances:t}=e,n=[];for(let e in t)n.push(t[e].range);return n}function Wi(e,t){for(let n of e)if(Tt(n,t))return!0;return!1}class Qi extends Error{constructor(e,t){super(e),this.response=t}}function Gi(e,t,n){const r={method:e=e.toUpperCase()};return"GET"===e?t+=(-1===t.indexOf("?")?"?":"&")+new URLSearchParams(n):(r.body=new URLSearchParams(n),r.headers={"Content-Type":"application/x-www-form-urlencoded"}),fetch(t,r).then(e=>{if(e.ok)return e.json().then(t=>[t,e],()=>{throw new Qi("Failure parsing JSON",e)});throw new Qi("Request failed",e)})}class Zi{constructor(e){this.drainedOption=e,this.isRunning=!1,this.isDirty=!1,this.pauseDepths={},this.timeoutId=0}request(e){this.isDirty=!0,this.isPaused()||(this.clearTimeout(),null==e?this.tryDrain():this.timeoutId=setTimeout(this.tryDrain.bind(this),e))}pause(e=""){let{pauseDepths:t}=this;t[e]=(t[e]||0)+1,this.clearTimeout()}resume(e="",t){let{pauseDepths:n}=this;if(e in n){if(t)delete n[e];else{n[e]-=1,n[e]<=0&&delete n[e]}this.tryDrain()}}isPaused(){return Object.keys(this.pauseDepths).length}tryDrain(){if(!this.isRunning&&!this.isPaused()){for(this.isRunning=!0;this.isDirty;)this.isDirty=!1,this.drained();this.isRunning=!1}}clear(){this.clearTimeout(),this.isDirty=!1,this.pauseDepths={}}clearTimeout(){this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=0)}drained(){this.drainedOption&&this.drainedOption()}}const qi=/^(visible|hidden)$/;class Yi extends Sr{constructor(){super(...arguments),this.handleEl=e=>{this.el=e,Tr(this.props.elRef,e)}}render(){let{props:e}=this,{liquid:t,liquidIsAbsolute:n}=e,r=t&&n,i=["fc-scroller"];return t&&(n?i.push("fc-scroller-liquid-absolute"):i.push("fc-scroller-liquid")),ln("div",{ref:this.handleEl,className:i.join(" "),style:{overflowX:e.overflowX,overflowY:e.overflowY,left:r&&-(e.overcomeLeft||0)||"",right:r&&-(e.overcomeRight||0)||"",bottom:r&&-(e.overcomeBottom||0)||"",marginLeft:!r&&-(e.overcomeLeft||0)||"",marginRight:!r&&-(e.overcomeRight||0)||"",marginBottom:!r&&-(e.overcomeBottom||0)||"",maxHeight:e.maxHeight||""}},e.children)}needsXScrolling(){if(qi.test(this.props.overflowX))return!1;let{el:e}=this,t=this.el.getBoundingClientRect().width-this.getYScrollbarWidth(),{children:n}=e;for(let e=0;e<n.length;e+=1){if(n[e].getBoundingClientRect().width>t)return!0}return!1}needsYScrolling(){if(qi.test(this.props.overflowY))return!1;let{el:e}=this,t=this.el.getBoundingClientRect().height-this.getXScrollbarWidth(),{children:n}=e;for(let e=0;e<n.length;e+=1){if(n[e].getBoundingClientRect().height>t)return!0}return!1}getXScrollbarWidth(){return qi.test(this.props.overflowX)?0:this.el.offsetHeight-this.el.clientHeight}getYScrollbarWidth(){return qi.test(this.props.overflowY)?0:this.el.offsetWidth-this.el.clientWidth}}class Ji{constructor(e){this.masterCallback=e,this.currentMap={},this.depths={},this.callbackMap={},this.handleValue=(e,t)=>{let{depths:n,currentMap:r}=this,i=!1,o=!1;null!==e?(i=t in r,r[t]=e,n[t]=(n[t]||0)+1,o=!0):(n[t]-=1,n[t]||(delete r[t],delete this.callbackMap[t],i=!0)),this.masterCallback&&(i&&this.masterCallback(null,String(t)),o&&this.masterCallback(e,String(t)))}}createRef(e){let t=this.callbackMap[e];return t||(t=this.callbackMap[e]=t=>{this.handleValue(t,String(e))}),t}collect(e,t,n){return J(this.currentMap,e,t,n)}getAll(){return W(this.currentMap)}}function $i(e){let t=u(e,".fc-scrollgrid-shrink"),n=0;for(let e of t)n=Math.max(n,O(e));return Math.ceil(n)}function Xi(e,t){return e.liquid&&t.liquid}function Ki(e,t){return null!=t.maxHeight||Xi(e,t)}function eo(e,t,n,r){let{expandRows:i}=n;return"function"==typeof t.content?t.content(n):ln("table",{role:"presentation",className:[t.tableClassName,e.syncRowHeights?"fc-scrollgrid-sync-table":""].join(" "),style:{minWidth:n.tableMinWidth,width:n.clientWidth,height:i?n.clientHeight:""}},n.tableColGroupNode,ln(r?"thead":"tbody",{role:"presentation"},"function"==typeof t.rowContent?t.rowContent(n):t.rowContent))}function to(e,t){return $(e,t,Q)}function no(e,t){let n=[];for(let r of e){let e=r.span||1;for(let i=0;i<e;i+=1)n.push(ln("col",{style:{width:"shrink"===r.width?ro(t):r.width||"",minWidth:r.minWidth||""}}))}return ln("colgroup",{},...n)}function ro(e){return null==e?4:e}function io(e){for(let t of e)if("shrink"===t.width)return!0;return!1}function oo(e,t){let n=["fc-scrollgrid",t.theme.getClass("table")];return e&&n.push("fc-scrollgrid-liquid"),n}function so(e,t){let n=["fc-scrollgrid-section","fc-scrollgrid-section-"+e.type,e.className];return t&&e.liquid&&null==e.maxHeight&&n.push("fc-scrollgrid-section-liquid"),e.isSticky&&n.push("fc-scrollgrid-section-sticky"),n}class ao extends Sr{constructor(){super(...arguments),this.processCols=ve(e=>e,to),this.renderMicroColGroup=ve(no),this.scrollerRefs=new Ji,this.scrollerElRefs=new Ji(this._handleScrollerEl.bind(this)),this.state={shrinkWidth:null,forceYScrollbars:!1,scrollerClientWidths:{},scrollerClientHeights:{}},this.handleSizing=()=>{this.safeSetState(Object.assign({shrinkWidth:this.computeShrinkWidth()},this.computeScrollerDims()))}}render(){let{props:e,state:t,context:n}=this,r=e.sections||[],i=this.processCols(e.cols),o=this.renderMicroColGroup(i,t.shrinkWidth),s=oo(e.liquid,n);e.collapsibleWidth&&s.push("fc-scrollgrid-collapsible");let a,l=r.length,c=0,u=[],d=[],f=[];for(;c<l&&"header"===(a=r[c]).type;)u.push(this.renderSection(a,o,!0)),c+=1;for(;c<l&&"body"===(a=r[c]).type;)d.push(this.renderSection(a,o,!1)),c+=1;for(;c<l&&"footer"===(a=r[c]).type;)f.push(this.renderSection(a,o,!0)),c+=1;let h=!At();const p={role:"rowgroup"};return ln("table",{role:"grid",className:s.join(" "),style:{height:e.height}},Boolean(!h&&u.length)&&ln("thead",p,...u),Boolean(!h&&d.length)&&ln("tbody",p,...d),Boolean(!h&&f.length)&&ln("tfoot",p,...f),h&&ln("tbody",p,...u,...d,...f))}renderSection(e,t,n){return"outerContent"in e?ln(dn,{key:e.key},e.outerContent):ln("tr",{key:e.key,role:"presentation",className:so(e,this.props.liquid).join(" ")},this.renderChunkTd(e,t,e.chunk,n))}renderChunkTd(e,t,n,r){if("outerContent"in n)return n.outerContent;let{props:i}=this,{forceYScrollbars:o,scrollerClientWidths:s,scrollerClientHeights:a}=this.state,l=Ki(i,e),c=Xi(i,e),u=i.liquid?o?"scroll":l?"auto":"hidden":"visible",d=e.key,f=eo(e,n,{tableColGroupNode:t,tableMinWidth:"",clientWidth:i.collapsibleWidth||void 0===s[d]?null:s[d],clientHeight:void 0!==a[d]?a[d]:null,expandRows:e.expandRows,syncRowHeights:!1,rowSyncHeights:[],reportRowHeightChange:()=>{}},r);return ln(r?"th":"td",{ref:n.elRef,role:"presentation"},ln("div",{className:"fc-scroller-harness"+(c?" fc-scroller-harness-liquid":"")},ln(Yi,{ref:this.scrollerRefs.createRef(d),elRef:this.scrollerElRefs.createRef(d),overflowY:u,overflowX:i.liquid?"hidden":"visible",maxHeight:e.maxHeight,liquid:c,liquidIsAbsolute:!0},f)))}_handleScrollerEl(e,t){let n=function(e,t){for(let n of e)if(n.key===t)return n;return null}(this.props.sections,t);n&&Tr(n.chunk.scrollerElRef,e)}componentDidMount(){this.handleSizing(),this.context.addResizeHandler(this.handleSizing)}componentDidUpdate(){this.handleSizing()}componentWillUnmount(){this.context.removeResizeHandler(this.handleSizing)}computeShrinkWidth(){return io(this.props.cols)?$i(this.scrollerElRefs.getAll()):0}computeScrollerDims(){let e=Pt(),{scrollerRefs:t,scrollerElRefs:n}=this,r=!1,i={},o={};for(let e in t.currentMap){let n=t.currentMap[e];if(n&&n.needsYScrolling()){r=!0;break}}for(let t of this.props.sections){let s=t.key,a=n.currentMap[s];if(a){let t=a.parentNode;i[s]=Math.floor(t.getBoundingClientRect().width-(r?e.y:0)),o[s]=Math.floor(t.getBoundingClientRect().height)}}return{forceYScrollbars:r,scrollerClientWidths:i,scrollerClientHeights:o}}}ao.addStateEquality({scrollerClientWidths:Q,scrollerClientHeights:Q});class lo extends Sr{constructor(){super(...arguments),this.handleEl=e=>{this.el=e,e&&Fr(e,this.props.seg)}}render(){const{props:e,context:t}=this,{options:n}=t,{seg:r}=e,{eventRange:i}=r,{ui:o}=i,s={event:new Pr(t,i.def,i.instance),view:t.viewApi,timeText:e.timeText,textColor:o.textColor,backgroundColor:o.backgroundColor,borderColor:o.borderColor,isDraggable:!e.disableDragging&&Gr(r,t),isStartResizable:!e.disableResizing&&Zr(r,t),isEndResizable:!e.disableResizing&&qr(r),isMirror:Boolean(e.isDragging||e.isResizing||e.isDateSelecting),isStart:Boolean(r.isStart),isEnd:Boolean(r.isEnd),isPast:Boolean(e.isPast),isFuture:Boolean(e.isFuture),isToday:Boolean(e.isToday),isSelected:Boolean(e.isSelected),isDragging:Boolean(e.isDragging),isResizing:Boolean(e.isResizing)};return ln(wi,Object.assign({},e,{elRef:this.handleEl,elClasses:[...Jr(s),...r.eventRange.ui.classNames,...e.elClasses||[]],renderProps:s,generatorName:"eventContent",customGenerator:n.eventContent,defaultGenerator:e.defaultGenerator,classNameGenerator:n.eventClassNames,didMount:n.eventDidMount,willUnmount:n.eventWillUnmount}))}componentDidUpdate(e){this.el&&this.props.seg!==e.seg&&Fr(this.el,this.props.seg)}}function co(e){return ln("div",{className:"fc-event-main-frame"},e.timeText&&ln("div",{className:"fc-event-time"},e.timeText),ln("div",{className:"fc-event-title-container"},ln("div",{className:"fc-event-title fc-sticky"},e.event.title||ln(dn,null," "))))}const uo=Me({day:"numeric"});class fo extends Sr{constructor(){super(...arguments),this.refineRenderProps=be(po)}render(){let{props:e,context:t}=this,{options:n}=t,r=this.refineRenderProps({date:e.date,dateProfile:e.dateProfile,todayRange:e.todayRange,isMonthStart:e.isMonthStart||!1,showDayNumber:e.showDayNumber,extraRenderProps:e.extraRenderProps,viewApi:t.viewApi,dateEnv:t.dateEnv,monthStartFormat:n.monthStartFormat});return ln(wi,Object.assign({},e,{elClasses:[...xt(r,t.theme),...e.elClasses||[]],elAttrs:Object.assign(Object.assign({},e.elAttrs),r.isDisabled?{}:{"data-date":ge(e.date)}),renderProps:r,generatorName:"dayCellContent",customGenerator:n.dayCellContent,defaultGenerator:e.defaultGenerator,classNameGenerator:r.isDisabled?void 0:n.dayCellClassNames,didMount:n.dayCellDidMount,willUnmount:n.dayCellWillUnmount}))}}function ho(e){return Boolean(e.dayCellContent||_i("dayCellContent",e))}function po(e){let{date:t,dateEnv:n,dateProfile:r,isMonthStart:i}=e,o=Rt(t,e.todayRange,null,r),s=e.showDayNumber?n.format(t,i?e.monthStartFormat:uo):"";return Object.assign(Object.assign(Object.assign({date:n.toDate(t),view:e.viewApi},o),{isMonthStart:i,dayNumberText:s}),e.extraRenderProps)}function go(e){let{title:t}=e.event;return t&&ln("div",{className:"fc-event-title"},e.event.title)}function mo(e){return e.text}class vo extends Sr{constructor(){super(...arguments),this.state={titleId:m()},this.handleRootEl=e=>{this.rootEl=e,this.props.elRef&&Tr(this.props.elRef,e)},this.handleDocumentMouseDown=e=>{const t=p(e);this.rootEl.contains(t)||this.handleCloseClick()},this.handleDocumentKeyDown=e=>{"Escape"===e.key&&this.handleCloseClick()},this.handleCloseClick=()=>{let{onClose:e}=this.props;e&&e()}}render(){let{theme:e,options:t}=this.context,{props:n,state:r}=this,i=["fc-popover",e.getClass("popover")].concat(n.extraClassNames||[]);return sr(ln("div",Object.assign({},n.extraAttrs,{id:n.id,className:i.join(" "),"aria-labelledby":r.titleId,ref:this.handleRootEl}),ln("div",{className:"fc-popover-header "+e.getClass("popoverHeader")},ln("span",{className:"fc-popover-title",id:r.titleId},n.title),ln("span",{className:"fc-popover-close "+e.getIconClass("close"),title:t.closeHint,onClick:this.handleCloseClick})),ln("div",{className:"fc-popover-body "+e.getClass("popoverContent")},n.children)),n.parentEl)}componentDidMount(){document.addEventListener("mousedown",this.handleDocumentMouseDown),document.addEventListener("keydown",this.handleDocumentKeyDown),this.updateSize()}componentWillUnmount(){document.removeEventListener("mousedown",this.handleDocumentMouseDown),document.removeEventListener("keydown",this.handleDocumentKeyDown)}updateSize(){let{isRtl:e}=this.context,{alignmentEl:t,alignGridTop:n}=this.props,{rootEl:r}=this,i=function(e){let t=Ft(e),n=e.getBoundingClientRect();for(let e of t){let t=vt(n,e.getBoundingClientRect());if(!t)return null;n=t}return n}(t);if(i){let o=r.getBoundingClientRect(),s=n?l(t,".fc-scrollgrid").getBoundingClientRect().top:i.top,a=e?i.right-o.width:i.left;s=Math.max(s,10),a=Math.min(a,document.documentElement.clientWidth-10-o.width),a=Math.max(a,10);let c=r.offsetParent.getBoundingClientRect();f(r,{top:s-c.top,left:a-c.left})}}}class bo extends kr{constructor(){super(...arguments),this.handleRootEl=e=>{this.rootEl=e,e?this.context.registerInteractiveComponent(this,{el:e,useEventCenter:!1}):this.context.unregisterInteractiveComponent(this)}}render(){let{options:e,dateEnv:t}=this.context,{props:n}=this,{startDate:r,todayRange:i,dateProfile:o}=n,s=t.format(r,e.dayPopoverFormat);return ln(fo,{elRef:this.handleRootEl,date:r,dateProfile:o,todayRange:i},(t,r,i)=>ln(vo,{elRef:i.ref,id:n.id,title:s,extraClassNames:["fc-more-popover"].concat(i.className||[]),extraAttrs:i,parentEl:n.parentEl,alignmentEl:n.alignmentEl,alignGridTop:n.alignGridTop,onClose:n.onClose},ho(e)&&ln(t,{elTag:"div",elClasses:["fc-more-popover-misc"]}),n.children))}queryHit(e,t,n,r){let{rootEl:i,props:o}=this;return e>=0&&e<n&&t>=0&&t<r?{dateProfile:o.dateProfile,dateSpan:Object.assign({allDay:!o.forceTimed,range:{start:o.startDate,end:o.endDate}},o.extraDateSpan),dayEl:i,rect:{left:0,top:0,right:n,bottom:r},layer:1}:null}}function Ao(e){return e.text}function yo(e){if(e.allDayDate)return{start:e.allDayDate,end:K(e.allDayDate,1)};let{hiddenSegs:t}=e;return{start:_o(t),end:(n=t,n.reduce(Do).eventRange.range.end)};var n}function _o(e){return e.reduce(Eo).eventRange.range.start}function Eo(e,t){return e.eventRange.range.start<t.eventRange.range.start?e:t}function Do(e,t){return e.eventRange.range.end>t.eventRange.range.end?e:t}function Co(e){return[`fc-${e.type}-view`,"fc-view"]}const wo={id:String,defaultAllDay:Boolean,url:String,format:String,events:Ve,eventDataTransform:Ve,success:Ve,failure:Ve};function So(e,t,n=To(t)){let r;if("string"==typeof e?r={url:e}:"function"==typeof e||Array.isArray(e)?r={events:e}:"object"==typeof e&&e&&(r=e),r){let{refined:i,extra:o}=Le(r,n),s=function(e,t){let n=t.pluginHooks.eventSourceDefs;for(let t=n.length-1;t>=0;t-=1){let r=n[t].parseMeta(e);if(r)return{sourceDefId:t,meta:r}}return null}(i,t);if(s)return{_raw:e,isFetching:!1,latestFetchId:"",fetchRange:null,defaultAllDay:i.defaultAllDay,eventDataTransform:i.eventDataTransform,success:i.success,failure:i.failure,publicId:i.id||"",sourceId:D(),sourceDefId:s.sourceDefId,meta:s.meta,ui:nt(i,t),extendedProps:o}}return null}function To(e){return Object.assign(Object.assign(Object.assign({},et),wo),e.pluginHooks.eventSourceRefiners)}class ko{getCurrentData(){return this.currentDataManager.getCurrentData()}dispatch(e){this.currentDataManager.dispatch(e)}get view(){return this.getCurrentData().viewApi}batchRendering(e){e()}updateSize(){this.trigger("_resize",!0)}setOption(e,t){this.dispatch({type:"SET_OPTION",optionName:e,rawOptionValue:t})}getOption(e){return this.currentDataManager.currentCalendarOptionsInput[e]}getAvailableLocaleCodes(){return Object.keys(this.getCurrentData().availableRawLocales)}on(e,t){let{currentDataManager:n}=this;n.currentCalendarOptionsRefiners[e]?n.emitter.on(e,t):console.warn(`Unknown listener name '${e}'`)}off(e,t){this.currentDataManager.emitter.off(e,t)}trigger(e,...t){this.currentDataManager.emitter.trigger(e,...t)}changeView(e,t){this.batchRendering(()=>{if(this.unselect(),t)if(t.start&&t.end)this.dispatch({type:"CHANGE_VIEW_TYPE",viewType:e}),this.dispatch({type:"SET_OPTION",optionName:"visibleRange",rawOptionValue:t});else{let{dateEnv:n}=this.getCurrentData();this.dispatch({type:"CHANGE_VIEW_TYPE",viewType:e,dateMarker:n.createMarker(t)})}else this.dispatch({type:"CHANGE_VIEW_TYPE",viewType:e})})}zoomTo(e,t){let n;t=t||"day",n=this.getCurrentData().viewSpecs[t]||this.getUnitViewSpec(t),this.unselect(),n?this.dispatch({type:"CHANGE_VIEW_TYPE",viewType:n.type,dateMarker:e}):this.dispatch({type:"CHANGE_DATE",dateMarker:e})}getUnitViewSpec(e){let t,n,{viewSpecs:r,toolbarConfig:i}=this.getCurrentData(),o=[].concat(i.header?i.header.viewsWithButtons:[],i.footer?i.footer.viewsWithButtons:[]);for(let e in r)o.push(e);for(t=0;t<o.length;t+=1)if(n=r[o[t]],n&&n.singleUnit===e)return n;return null}prev(){this.unselect(),this.dispatch({type:"PREV"})}next(){this.unselect(),this.dispatch({type:"NEXT"})}prevYear(){let e=this.getCurrentData();this.unselect(),this.dispatch({type:"CHANGE_DATE",dateMarker:e.dateEnv.addYears(e.currentDate,-1)})}nextYear(){let e=this.getCurrentData();this.unselect(),this.dispatch({type:"CHANGE_DATE",dateMarker:e.dateEnv.addYears(e.currentDate,1)})}today(){let e=this.getCurrentData();this.unselect(),this.dispatch({type:"CHANGE_DATE",dateMarker:e.nowManager.getDateMarker()})}gotoDate(e){let t=this.getCurrentData();this.unselect(),this.dispatch({type:"CHANGE_DATE",dateMarker:t.dateEnv.createMarker(e)})}incrementDate(e){let t=this.getCurrentData(),n=N(e);n&&(this.unselect(),this.dispatch({type:"CHANGE_DATE",dateMarker:t.dateEnv.add(t.currentDate,n)}))}getDate(){let e=this.getCurrentData();return e.dateEnv.toDate(e.currentDate)}formatDate(e,t){let{dateEnv:n}=this.getCurrentData();return n.format(n.createMarker(e),Me(t))}formatRange(e,t,n){let{dateEnv:r}=this.getCurrentData();return r.formatRange(r.createMarker(e),r.createMarker(t),Me(n),n)}formatIso(e,t){let{dateEnv:n}=this.getCurrentData();return n.formatIso(n.createMarker(e),{omitTime:t})}select(e,t){let n;n=null==t?null!=e.start?e:{start:e,end:null}:{start:e,end:t};let r=this.getCurrentData(),i=Kr(n,r.dateEnv,N({days:1}));i&&(this.dispatch({type:"SELECT_DATES",selection:i}),xr(i,null,r))}unselect(e){let t=this.getCurrentData();t.dateSelection&&(this.dispatch({type:"UNSELECT_DATES"}),function(e,t){t.emitter.trigger("unselect",{jsEvent:e?e.origEvent:null,view:t.viewApi||t.calendarApi.view})}(e,t))}addEvent(e,t){if(e instanceof Pr){let t=e._def,n=e._instance;return this.getCurrentData().eventStore.defs[t.defId]||(this.dispatch({type:"ADD_EVENTS",eventStore:qe({def:t,instance:n})}),this.triggerEventAdd(e)),e}let n,r=this.getCurrentData();if(t instanceof Hr)n=t.internalEventSource;else if("boolean"==typeof t)t&&([n]=W(r.eventSources));else if(null!=t){let e=this.getEventSourceById(t);if(!e)return console.warn(`Could not find an event source with ID "${t}"`),null;n=e.internalEventSource}let i=lt(e,n,r,!1);if(i){let e=new Pr(r,i.def,i.def.recurringDef?null:i.instance);return this.dispatch({type:"ADD_EVENTS",eventStore:qe(i)}),this.triggerEventAdd(e),e}return null}triggerEventAdd(e){let{emitter:t}=this.getCurrentData();t.trigger("eventAdd",{event:e,relatedEvents:[],revert:()=>{this.dispatch({type:"REMOVE_EVENTS",eventStore:Ur(e)})}})}getEventById(e){let t=this.getCurrentData(),{defs:n,instances:r}=t.eventStore;e=String(e);for(let i in n){let o=n[i];if(o.publicId===e){if(o.recurringDef)return new Pr(t,o,null);for(let e in r){let n=r[e];if(n.defId===o.defId)return new Pr(t,o,n)}}}return null}getEvents(){let e=this.getCurrentData();return jr(e.eventStore,e)}removeAllEvents(){this.dispatch({type:"REMOVE_ALL_EVENTS"})}getEventSources(){let e=this.getCurrentData(),t=e.eventSources,n=[];for(let r in t)n.push(new Hr(e,t[r]));return n}getEventSourceById(e){let t=this.getCurrentData(),n=t.eventSources;e=String(e);for(let r in n)if(n[r].publicId===e)return new Hr(t,n[r]);return null}addEventSource(e){let t=this.getCurrentData();if(e instanceof Hr)return t.eventSources[e.internalEventSource.sourceId]||this.dispatch({type:"ADD_EVENT_SOURCES",sources:[e.internalEventSource]}),e;let n=So(e,t);return n?(this.dispatch({type:"ADD_EVENT_SOURCES",sources:[n]}),new Hr(t,n)):null}removeAllEventSources(){this.dispatch({type:"REMOVE_ALL_EVENT_SOURCES"})}refetchEvents(){this.dispatch({type:"FETCH_EVENT_SOURCES",isRefetch:!0})}scrollToTime(e){let t=N(e);t&&this.trigger("_scrollRequest",{time:t})}}var Ro={__proto__:null,BASE_OPTION_DEFAULTS:Ne,identity:Ve,refineProps:Le,createEventInstance:We,parseEventDef:dt,refineEventDef:ct,parseBusinessHours:ht,padStart:k,isInt:x,parseFieldSpecs:C,compareByFieldSpecs:w,flexibleCompare:T,preventSelection:function(e){e.style.userSelect="none",e.style.webkitUserSelect="none",e.addEventListener("selectstart",v)},allowSelection:function(e){e.style.userSelect="",e.style.webkitUserSelect="",e.removeEventListener("selectstart",v)},preventContextMenu:function(e){e.addEventListener("contextmenu",v)},allowContextMenu:function(e){e.removeEventListener("contextmenu",v)},compareNumbers:function(e,t){return e-t},enableCursor:function(){document.body.classList.remove("fc-not-allowed")},disableCursor:function(){document.body.classList.add("fc-not-allowed")},guid:D,computeVisibleDayRange:gt,isMultiDayRange:function(e){let t=gt(e);return te(t.start,t.end)>1},diffDates:mt,removeExact:function(e,t){let n=0,r=0;for(;r<e.length;)e[r]===t?(e.splice(r,1),n+=1):r+=1;return n},isArraysEqual:$,memoize:ve,memoizeObjArg:be,memoizeArraylike:function(e,t,n){let r=[],i=[];return o=>{let s=r.length,a=o.length,l=0;for(;l<s;l+=1)if(o[l]){if(!$(r[l],o[l])){n&&n(i[l]);let r=e.apply(this,o[l]);t&&t(r,i[l])||(i[l]=r)}}else n&&n(i[l]);for(;l<a;l+=1)i[l]=e.apply(this,o[l]);return r=o,i.splice(a),i}},memoizeHashlike:function(e,t,n){let r={},i={};return o=>{let s={};for(let a in o)if(i[a])if($(r[a],o[a]))s[a]=i[a];else{n&&n(i[a]);let r=e.apply(this,o[a]);s[a]=t&&t(r,i[a])?i[a]:r}else s[a]=e.apply(this,o[a]);return r=o,i=s,s}},intersectRects:vt,pointInsideRect:function(e,t){return e.left>=t.left&&e.left<t.right&&e.top>=t.top&&e.top<t.bottom},constrainPoint:function(e,t){return{left:Math.min(Math.max(e.left,t.left),t.right),top:Math.min(Math.max(e.top,t.top),t.bottom)}},getRectCenter:function(e){return{left:(e.left+e.right)/2,top:(e.top+e.bottom)/2}},diffPoints:function(e,t){return{left:e.left-t.left,top:e.top-t.top}},translateRect:function(e,t,n){return{left:e.left+t,right:e.right+t,top:e.top+n,bottom:e.bottom+n}},mapHash:L,filterHash:F,isPropsEqual:Q,compareObjs:q,collectFromHash:J,findElements:u,findDirectChildren:function(e,t){let n=e instanceof HTMLElement?[e]:e,r=[];for(let e=0;e<n.length;e+=1){let i=n[e].children;for(let e=0;e<i.length;e+=1){let n=i[e];t&&!c(n,t)||r.push(n)}}return r},removeElement:a,applyStyle:f,elementMatches:c,elementClosest:l,getEventTargetViaRoot:p,getUniqueDomId:m,parseClassNames:Ke,getCanVGrowWithinCell:At,createEmptyEventStore:Je,mergeEventStores:$e,getRelevantEvents:Ye,eventTupleToStore:qe,combineEventUis:rt,createEventUi:nt,Splitter:class{constructor(){this.getKeysForEventDefs=ve(this._getKeysForEventDefs),this.splitDateSelection=ve(this._splitDateSpan),this.splitEventStore=ve(this._splitEventStore),this.splitIndividualUi=ve(this._splitIndividualUi),this.splitEventDrag=ve(this._splitInteraction),this.splitEventResize=ve(this._splitInteraction),this.eventUiBuilders={}}splitProps(e){let t=this.getKeyInfo(e),n=this.getKeysForEventDefs(e.eventStore),r=this.splitDateSelection(e.dateSelection),i=this.splitIndividualUi(e.eventUiBases,n),o=this.splitEventStore(e.eventStore,n),s=this.splitEventDrag(e.eventDrag),a=this.splitEventResize(e.eventResize),l={};this.eventUiBuilders=L(t,(e,t)=>this.eventUiBuilders[t]||ve(_t));for(let n in t){let c=t[n],u=o[n]||yt,d=this.eventUiBuilders[n];l[n]={businessHours:c.businessHours||e.businessHours,dateSelection:r[n]||null,eventStore:u,eventUiBases:d(e.eventUiBases[""],c.ui,i[n]),eventSelection:u.instances[e.eventSelection]?e.eventSelection:"",eventDrag:s[n]||null,eventResize:a[n]||null}}return l}_splitDateSpan(e){let t={};if(e){let n=this.getKeysForDateSpan(e);for(let r of n)t[r]=e}return t}_getKeysForEventDefs(e){return L(e.defs,e=>this.getKeysForEventDef(e))}_splitEventStore(e,t){let{defs:n,instances:r}=e,i={};for(let e in n)for(let r of t[e])i[r]||(i[r]={defs:{},instances:{}}),i[r].defs[e]=n[e];for(let e in r){let n=r[e];for(let r of t[n.defId])i[r]&&(i[r].instances[e]=n)}return i}_splitIndividualUi(e,t){let n={};for(let r in e)if(r)for(let i of t[r])n[i]||(n[i]={}),n[i][r]=e[r];return n}_splitInteraction(e){let t={};if(e){let n=this._splitEventStore(e.affectedEvents,this._getKeysForEventDefs(e.affectedEvents)),r=this._getKeysForEventDefs(e.mutatedEvents),i=this._splitEventStore(e.mutatedEvents,r),o=r=>{t[r]||(t[r]={affectedEvents:n[r]||yt,mutatedEvents:i[r]||yt,isEvent:e.isEvent})};for(let e in n)o(e);for(let e in i)o(e)}return t}},getDayClassNames:xt,getDateMeta:Rt,getSlotClassNames:function(e,t){let n=["fc-slot","fc-slot-"+X[e.dow]];return e.isDisabled?n.push("fc-slot-disabled"):(e.isToday&&(n.push("fc-slot-today"),n.push(t.getClass("today"))),e.isPast&&n.push("fc-slot-past"),e.isFuture&&n.push("fc-slot-future")),n},buildNavLinkAttrs:It,preventDefault:v,whenTransitionDone:function(e,t){let n=r=>{t(r),A.forEach(t=>{e.removeEventListener(t,n)})};A.forEach(t=>{e.addEventListener(t,n)})},computeInnerRect:function(e,t=!1,n){let r=n?e.getBoundingClientRect():zt(e),i=jt(e,t),o={left:r.left+i.borderLeft+i.scrollbarLeft,right:r.right-i.borderRight-i.scrollbarRight,top:r.top+i.borderTop,bottom:r.bottom-i.borderBottom-i.scrollbarBottom};return t&&(o.left+=i.paddingLeft,o.right-=i.paddingRight,o.top+=i.paddingTop,o.bottom-=i.paddingBottom),o},computeEdges:jt,getClippingParents:Ft,computeRect:zt,unpromisify:Lt,Emitter:Vt,rangeContainsMarker:kt,intersectRanges:Ct,rangesEqual:wt,rangesIntersect:St,rangeContainsRange:Tt,PositionCache:class{constructor(e,t,n,r){this.els=t;let i=this.originClientRect=e.getBoundingClientRect();n&&this.buildElHorizontals(i.left),r&&this.buildElVerticals(i.top)}buildElHorizontals(e){let t=[],n=[];for(let r of this.els){let i=r.getBoundingClientRect();t.push(i.left-e),n.push(i.right-e)}this.lefts=t,this.rights=n}buildElVerticals(e){let t=[],n=[];for(let r of this.els){let i=r.getBoundingClientRect();t.push(i.top-e),n.push(i.bottom-e)}this.tops=t,this.bottoms=n}leftToIndex(e){let t,{lefts:n,rights:r}=this,i=n.length;for(t=0;t<i;t+=1)if(e>=n[t]&&e<r[t])return t}topToIndex(e){let t,{tops:n,bottoms:r}=this,i=n.length;for(t=0;t<i;t+=1)if(e>=n[t]&&e<r[t])return t}getWidth(e){return this.rights[e]-this.lefts[e]}getHeight(e){return this.bottoms[e]-this.tops[e]}similarTo(e){return Wt(this.tops||[],e.tops||[])&&Wt(this.bottoms||[],e.bottoms||[])&&Wt(this.lefts||[],e.lefts||[])&&Wt(this.rights||[],e.rights||[])}},ScrollController:Qt,ElementScrollController:class extends Qt{constructor(e){super(),this.el=e}getScrollTop(){return this.el.scrollTop}getScrollLeft(){return this.el.scrollLeft}setScrollTop(e){this.el.scrollTop=e}setScrollLeft(e){this.el.scrollLeft=e}getScrollWidth(){return this.el.scrollWidth}getScrollHeight(){return this.el.scrollHeight}getClientHeight(){return this.el.clientHeight}getClientWidth(){return this.el.clientWidth}},WindowScrollController:class extends Qt{getScrollTop(){return window.scrollY}getScrollLeft(){return window.scrollX}setScrollTop(e){window.scroll(window.scrollX,e)}setScrollLeft(e){window.scroll(e,window.scrollY)}getScrollWidth(){return document.documentElement.scrollWidth}getScrollHeight(){return document.documentElement.scrollHeight}getClientHeight(){return document.documentElement.clientHeight}getClientWidth(){return document.documentElement.clientWidth}},Theme:Gt,ViewContextType:Dr,DateComponent:kr,DateProfileGenerator:Rr,isDateSpansEqual:function(e,t){return wt(e.range,t.range)&&e.allDay===t.allDay&&function(e,t){for(let n in t)if("range"!==n&&"allDay"!==n&&e[n]!==t[n])return!1;for(let n in e)if(!(n in t))return!1;return!0}(e,t)},addDays:K,startOfDay:oe,addMs:ee,addWeeks:function(e,t){let n=ue(e);return n[2]+=7*t,de(n)},diffWeeks:function(e,t){return te(e,t)/7},diffWholeWeeks:re,diffWholeDays:ie,diffDayAndTime:ne,diffDays:te,isValidDate:fe,createDuration:N,asCleanDays:function(e){return e.years||e.months||e.milliseconds?0:e.days},multiplyDuration:function(e,t){return{years:e.years*t,months:e.months*t,days:e.days*t,milliseconds:e.milliseconds*t}},addDurations:function(e,t){return{years:e.years+t.years,months:e.months+t.months,days:e.days+t.days,milliseconds:e.milliseconds+t.milliseconds}},asRoughMinutes:function(e){return P(e)/6e4},asRoughSeconds:function(e){return P(e)/1e3},asRoughMs:P,wholeDivideDurations:function(e,t){let n=null;for(let r=0;r<M.length;r+=1){let i=M[r];if(t[i]){let r=e[i]/t[i];if(!x(r)||null!==n&&n!==r)return null;n=r}else if(e[i])return null}return n},greatestDurationDenominator:U,DateEnv:ai,createFormatter:Me,formatIsoTimeString:function(e){return k(e.getUTCHours(),2)+":"+k(e.getUTCMinutes(),2)+":"+k(e.getUTCSeconds(),2)},formatDayString:ge,buildIsoString:pe,formatIsoMonthStr:function(e){return e.toISOString().match(/^\d{4}-\d{2}/)[0]},NamedTimeZoneImpl:class{constructor(e){this.timeZoneName=e}},parseMarker:si,SegHierarchy:class{constructor(e=(e=>e.thickness||1)){this.getEntryThickness=e,this.strictOrder=!1,this.allowReslicing=!1,this.maxCoord=-1,this.maxStackCnt=-1,this.levelCoords=[],this.entriesByLevel=[],this.stackCnts={}}addSegs(e){let t=[];for(let n of e)this.insertEntry(n,t);return t}insertEntry(e,t){let n=this.findInsertion(e);this.isInsertionValid(n,e)?this.insertEntryAt(e,n):this.handleInvalidInsertion(n,e,t)}isInsertionValid(e,t){return(-1===this.maxCoord||e.levelCoord+this.getEntryThickness(t)<=this.maxCoord)&&(-1===this.maxStackCnt||e.stackCnt<this.maxStackCnt)}handleInvalidInsertion(e,t,n){if(this.allowReslicing&&e.touchingEntry){const r=Object.assign(Object.assign({},t),{span:ui(t.span,e.touchingEntry.span)});n.push(r),this.splitEntry(t,e.touchingEntry,n)}else n.push(t)}splitEntry(e,t,n){let r=e.span,i=t.span;r.start<i.start&&this.insertEntry({index:e.index,thickness:e.thickness,span:{start:r.start,end:i.start}},n),r.end>i.end&&this.insertEntry({index:e.index,thickness:e.thickness,span:{start:i.end,end:r.end}},n)}insertEntryAt(e,t){let{entriesByLevel:n,levelCoords:r}=this;-1===t.lateral?(di(r,t.level,t.levelCoord),di(n,t.level,[e])):di(n[t.level],t.lateral,e),this.stackCnts[ci(e)]=t.stackCnt}findInsertion(e){let{levelCoords:t,entriesByLevel:n,strictOrder:r,stackCnts:i}=this,o=t.length,s=0,a=-1,l=-1,c=null,u=0;for(let d=0;d<o;d+=1){const o=t[d];if(!r&&o>=s+this.getEntryThickness(e))break;let f,h=n[d],p=fi(h,e.span.start,li),g=p[0]+p[1];for(;(f=h[g])&&f.span.start<e.span.end;){let e=o+this.getEntryThickness(f);e>s&&(s=e,c=f,a=d,l=g),e===s&&(u=Math.max(u,i[ci(f)]+1)),g+=1}}let d=0;if(c)for(d=a+1;d<o&&t[d]<s;)d+=1;let f=-1;return d<o&&t[d]===s&&(f=fi(n[d],e.span.end,li)[0]),{touchingLevel:a,touchingLateral:l,touchingEntry:c,stackCnt:u,levelCoord:s,level:d,lateral:f}}toRects(){let{entriesByLevel:e,levelCoords:t}=this,n=e.length,r=[];for(let i=0;i<n;i+=1){let n=e[i],o=t[i];for(let e of n)r.push(Object.assign(Object.assign({},e),{thickness:this.getEntryThickness(e),levelCoord:o}))}return r}},buildEntryKey:ci,getEntrySpanEnd:li,binarySearch:fi,groupIntersectingEntries:function(e){let t=[];for(let i of e){let e=[],o={span:i.span,entries:[i]};for(let i of t)ui(i.span,o.span)?o={entries:i.entries.concat(o.entries),span:(n=i.span,r=o.span,{start:Math.min(n.start,r.start),end:Math.max(n.end,r.end)})}:e.push(i);e.push(o),t=e}var n,r;return t},intersectSpans:ui,Interaction:hi,interactionSettingsToStore:function(e){return{[e.component.uid]:e}},interactionSettingsStore:pi,ElementDragging:class{constructor(e,t){this.emitter=new Vt}destroy(){}setMirrorIsVisible(e){}setMirrorNeedsRevert(e){}setAutoScrollEnabled(e){}},config:{},parseDragMeta:function(e){let{refined:t,extra:n}=Le(e,gi);return{startTime:t.startTime||null,duration:t.duration||null,create:null==t.create||t.create,sourceId:t.sourceId,leftoverProps:n}},CalendarRoot:mi,DayHeader:class extends Sr{constructor(){super(...arguments),this.createDayHeaderFormatter=ve(Mi)}render(){let{context:e}=this,{dates:t,dateProfile:n,datesRepDistinctDays:r,renderIntro:i}=this.props,o=this.createDayHeaderFormatter(e.options.dayHeaderFormat,r,t.length);return ln(xi,{unit:"day"},(e,s)=>ln("tr",{role:"row"},i&&i("day"),t.map(e=>r?ln(Ti,{key:e.toISOString(),date:e,dateProfile:n,todayRange:s,colCnt:t.length,dayHeaderFormat:o}):ln(Ri,{key:e.getUTCDay(),dow:e.getUTCDay(),dayHeaderFormat:o}))))}},computeFallbackHeaderFormat:vi,TableDateCell:Ti,TableDowCell:Ri,DaySeriesModel:class{constructor(e,t){let n=e.start,{end:r}=e,i=[],o=[],s=-1;for(;n<r;)t.isHiddenDay(n)?i.push(s+.5):(s+=1,i.push(s),o.push(n)),n=K(n,1);this.dates=o,this.indices=i,this.cnt=o.length}sliceRange(e){let t=this.getDateDayIndex(e.start),n=this.getDateDayIndex(K(e.end,-1)),r=Math.max(0,t),i=Math.min(this.cnt-1,n);return r=Math.ceil(r),i=Math.floor(i),r<=i?{firstIndex:r,lastIndex:i,isStart:t===r,isEnd:n===i}:null}getDateDayIndex(e){let{indices:t}=this,n=Math.floor(te(this.dates[0],e));return n<0?t[0]-1:n>=t.length?t[t.length-1]+1:t[n]}},sliceEventStore:zr,hasBgRendering:function(e){return"background"===e.ui.display||"inverse-background"===e.ui.display},getElSeg:Lr,buildSegTimeText:Yr,sortEventSegs:function(e,t){let n=e.map(Qr);return n.sort((e,n)=>w(e,n,t)),n.map(e=>e._seg)},getSegMeta:function(e,t,n){let r=e.eventRange.range;return{isPast:r.end<=(n||t.start),isFuture:r.start>=(n||t.end),isToday:t&&kt(t,r.start)}},buildEventRangeKey:function(e){return e.instance?e.instance.instanceId:`${e.def.defId}:${e.range.start.toISOString()}`},getSegAnchorAttrs:$r,DayTableModel:class{constructor(e,t){let n,r,i,{dates:o}=e;if(t){for(r=o[0].getUTCDay(),n=1;n<o.length&&o[n].getUTCDay()!==r;n+=1);i=Math.ceil(o.length/n)}else i=1,n=o.length;this.rowCnt=i,this.colCnt=n,this.daySeries=e,this.cells=this.buildCells(),this.headerDates=this.buildHeaderDates()}buildCells(){let e=[];for(let t=0;t<this.rowCnt;t+=1){let n=[];for(let e=0;e<this.colCnt;e+=1)n.push(this.buildCell(t,e));e.push(n)}return e}buildCell(e,t){let n=this.daySeries.dates[e*this.colCnt+t];return{key:n.toISOString(),date:n}}buildHeaderDates(){let e=[];for(let t=0;t<this.colCnt;t+=1)e.push(this.cells[0][t].date);return e}sliceRange(e){let{colCnt:t}=this,n=this.daySeries.sliceRange(e),r=[];if(n){let{firstIndex:e,lastIndex:i}=n,o=e;for(;o<=i;){let s=Math.floor(o/t),a=Math.min((s+1)*t,i+1);r.push({row:s,firstCol:o%t,lastCol:(a-1)%t,isStart:n.isStart&&o===e,isEnd:n.isEnd&&a-1===i}),o=a}}return r}},Slicer:class{constructor(){this.sliceBusinessHours=ve(this._sliceBusinessHours),this.sliceDateSelection=ve(this._sliceDateSpan),this.sliceEventStore=ve(this._sliceEventStore),this.sliceEventDrag=ve(this._sliceInteraction),this.sliceEventResize=ve(this._sliceInteraction),this.forceDayIfListItem=!1}sliceProps(e,t,n,r,...i){let{eventUiBases:o}=e,s=this.sliceEventStore(e.eventStore,o,t,n,...i);return{dateSelectionSegs:this.sliceDateSelection(e.dateSelection,t,n,o,r,...i),businessHourSegs:this.sliceBusinessHours(e.businessHours,t,n,r,...i),fgEventSegs:s.fg,bgEventSegs:s.bg,eventDrag:this.sliceEventDrag(e.eventDrag,o,t,n,...i),eventResize:this.sliceEventResize(e.eventResize,o,t,n,...i),eventSelection:e.eventSelection}}sliceNowDate(e,t,n,r,...i){return this._sliceDateSpan({range:{start:e,end:ee(e,1)},allDay:!1},t,n,{},r,...i)}_sliceBusinessHours(e,t,n,r,...i){return e?this._sliceEventStore(Qe(e,Ii(t,Boolean(n)),r),{},t,n,...i).bg:[]}_sliceEventStore(e,t,n,r,...i){if(e){let o=zr(e,t,Ii(n,Boolean(r)),r);return{bg:this.sliceEventRanges(o.bg,i),fg:this.sliceEventRanges(o.fg,i)}}return{bg:[],fg:[]}}_sliceInteraction(e,t,n,r,...i){if(!e)return null;let o=zr(e.mutatedEvents,t,Ii(n,Boolean(r)),r);return{segs:this.sliceEventRanges(o.fg,i),affectedInstances:e.affectedEvents.instances,isEvent:e.isEvent}}_sliceDateSpan(e,t,n,r,i,...o){if(!e)return[];let s=Ii(t,Boolean(n)),a=Ct(e.range,s);if(a){let t=function(e,t,n){let r=ct({editable:!1},n),i=dt(r.refined,r.extra,"",e.allDay,!0,n);return{def:i,ui:Wr(i,t),instance:We(i.defId,e.range),range:e.range,isStart:!0,isEnd:!0}}(e=Object.assign(Object.assign({},e),{range:a}),r,i),n=this.sliceRange(e.range,...o);for(let e of n)e.eventRange=t;return n}return[]}sliceEventRanges(e,t){let n=[];for(let r of e)n.push(...this.sliceEventRange(r,t));return n}sliceEventRange(e,t){let n=e.range;this.forceDayIfListItem&&"list-item"===e.ui.display&&(n={start:n.start,end:K(n.start,1)});let r=this.sliceRange(n,...t);for(let t of r)t.eventRange=e,t.isStart=e.isStart&&t.isStart,t.isEnd=e.isEnd&&t.isEnd;return r}},applyMutationToEventStore:Ir,isPropsValid:zi,isInteractionValid:function(e,t,n){let{instances:r}=e.mutatedEvents;for(let e in r)if(!Tt(t.validRange,r[e].range))return!1;return ji({eventDrag:e},n)},isDateSelectionValid:function(e,t,n){return!!Tt(t.validRange,e.range)&&ji({dateSelection:e},n)},requestJson:Gi,BaseComponent:Sr,setRef:Tr,DelayedRunner:Zi,SimpleScrollGrid:ao,hasShrinkWidth:io,renderMicroColGroup:no,getScrollGridClassNames:oo,getSectionClassNames:so,getSectionHasLiquidHeight:Xi,getAllowYScrolling:Ki,renderChunkContent:eo,computeShrinkWidth:$i,sanitizeShrinkWidth:ro,isColPropsEqual:to,renderScrollShim:function(e){return ln("div",{className:"fc-scrollgrid-sticky-shim",style:{width:e.clientWidth,minWidth:e.tableMinWidth}})},getStickyFooterScrollbar:function(e){let{stickyFooterScrollbar:t}=e;return null!=t&&"auto"!==t||(t="auto"===e.height||"auto"===e.viewHeight),t},getStickyHeaderDates:function(e){let{stickyHeaderDates:t}=e;return null!=t&&"auto"!==t||(t="auto"===e.height||"auto"===e.viewHeight),t},Scroller:Yi,getScrollbarWidths:Pt,RefMap:Ji,getIsRtlScrollbarOnLeft:Ht,NowTimer:xi,ScrollResponder:Er,StandardEvent:class extends Sr{render(){let{props:e,context:t}=this,{options:n}=t,{seg:r}=e,{ui:i}=r.eventRange,o=Yr(r,n.eventTimeFormat||e.defaultTimeFormat,t,e.defaultDisplayEventTime,e.defaultDisplayEventEnd);return ln(lo,Object.assign({},e,{elTag:"a",elStyle:{borderColor:i.borderColor,backgroundColor:i.backgroundColor},elAttrs:$r(r,t),defaultGenerator:co,timeText:o}),(e,t)=>ln(dn,null,ln(e,{elTag:"div",elClasses:["fc-event-main"],elStyle:{color:t.textColor}}),Boolean(t.isStartResizable)&&ln("div",{className:"fc-event-resizer fc-event-resizer-start"}),Boolean(t.isEndResizable)&&ln("div",{className:"fc-event-resizer fc-event-resizer-end"})))}},NowIndicatorContainer:e=>ln(Dr.Consumer,null,t=>{let{options:n}=t,r={isAxis:e.isAxis,date:t.dateEnv.toDate(e.date),view:t.viewApi};return ln(wi,Object.assign({},e,{elTag:e.elTag||"div",renderProps:r,generatorName:"nowIndicatorContent",customGenerator:n.nowIndicatorContent,classNameGenerator:n.nowIndicatorClassNames,didMount:n.nowIndicatorDidMount,willUnmount:n.nowIndicatorWillUnmount}))}),DayCellContainer:fo,hasCustomDayCellContent:ho,EventContainer:lo,renderFill:function(e){return ln("div",{className:"fc-"+e})},BgEvent:class extends Sr{render(){let{props:e}=this,{seg:t}=e;return ln(lo,{elTag:"div",elClasses:["fc-bg-event"],elStyle:{backgroundColor:t.eventRange.ui.backgroundColor},defaultGenerator:go,seg:t,timeText:"",isDragging:!1,isResizing:!1,isDateSelecting:!1,isSelected:!1,isPast:e.isPast,isFuture:e.isFuture,isToday:e.isToday,disableDragging:!0,disableResizing:!0})}},WeekNumberContainer:e=>ln(Dr.Consumer,null,t=>{let{dateEnv:n,options:r}=t,{date:i}=e,o=r.weekNumberFormat||e.defaultFormat,s={num:n.computeWeekNumber(i),text:n.format(i,o),date:i};return ln(wi,Object.assign({},e,{renderProps:s,generatorName:"weekNumberContent",customGenerator:r.weekNumberContent,defaultGenerator:mo,classNameGenerator:r.weekNumberClassNames,didMount:r.weekNumberDidMount,willUnmount:r.weekNumberWillUnmount}))}),MoreLinkContainer:class extends Sr{constructor(){super(...arguments),this.state={isPopoverOpen:!1,popoverId:m()},this.handleLinkEl=e=>{this.linkEl=e,this.props.elRef&&Tr(this.props.elRef,e)},this.handleClick=e=>{let{props:t,context:n}=this,{moreLinkClick:r}=n.options,i=yo(t).start;function o(e){let{def:t,instance:r,range:i}=e.eventRange;return{event:new Pr(n,t,r),start:n.dateEnv.toDate(i.start),end:n.dateEnv.toDate(i.end),isStart:e.isStart,isEnd:e.isEnd}}"function"==typeof r&&(r=r({date:i,allDay:Boolean(t.allDayDate),allSegs:t.allSegs.map(o),hiddenSegs:t.hiddenSegs.map(o),jsEvent:e,view:n.viewApi})),r&&"popover"!==r?"string"==typeof r&&n.calendarApi.zoomTo(i,r):this.setState({isPopoverOpen:!0})},this.handlePopoverClose=()=>{this.setState({isPopoverOpen:!1})}}render(){let{props:e,state:t}=this;return ln(Dr.Consumer,null,n=>{let{viewApi:r,options:i,calendarApi:o}=n,{moreLinkText:s}=i,{moreCnt:a}=e,l=yo(e),c="function"==typeof s?s.call(o,a):`+${a} ${s}`,u=R(i.moreLinkHint,[a],c),d={num:a,shortText:"+"+a,text:c,view:r};return ln(dn,null,Boolean(e.moreCnt)&&ln(wi,{elTag:e.elTag||"a",elRef:this.handleLinkEl,elClasses:[...e.elClasses||[],"fc-more-link"],elStyle:e.elStyle,elAttrs:Object.assign(Object.assign(Object.assign({},e.elAttrs),y(this.handleClick)),{title:u,"aria-expanded":t.isPopoverOpen,"aria-controls":t.isPopoverOpen?t.popoverId:""}),renderProps:d,generatorName:"moreLinkContent",customGenerator:i.moreLinkContent,defaultGenerator:e.defaultGenerator||Ao,classNameGenerator:i.moreLinkClassNames,didMount:i.moreLinkDidMount,willUnmount:i.moreLinkWillUnmount},e.children),t.isPopoverOpen&&ln(bo,{id:t.popoverId,startDate:l.start,endDate:l.end,dateProfile:e.dateProfile,todayRange:e.todayRange,extraDateSpan:e.extraDateSpan,parentEl:this.parentEl,alignmentEl:e.alignmentElRef?e.alignmentElRef.current:this.linkEl,alignGridTop:e.alignGridTop,forceTimed:e.forceTimed,onClose:this.handlePopoverClose},e.popoverContent()))})}componentDidMount(){this.updateParentEl()}componentDidUpdate(){this.updateParentEl()}updateParentEl(){this.linkEl&&(this.parentEl=l(this.linkEl,".fc-view-harness"))}},computeEarliestSegStart:_o,ViewContainer:class extends Sr{render(){let{props:e,context:t}=this,{options:n}=t,r={view:t.viewApi};return ln(wi,Object.assign({},e,{elTag:e.elTag||"div",elClasses:[...Co(e.viewSpec),...e.elClasses||[]],renderProps:r,classNameGenerator:n.viewClassNames,generatorName:void 0,didMount:n.viewDidMount,willUnmount:n.viewWillUnmount}),()=>e.children)}},triggerDateSelect:xr,getDefaultEventEnd:Mr,injectStyles:r,CalendarImpl:ko,EventImpl:Pr,buildEventApis:jr,buildElAttrs:Ei,ContentContainer:wi,CustomRenderingStore:class extends class{constructor(){this.handlers=[]}set(e){this.currentValue=e;for(let t of this.handlers)t(e)}subscribe(e){this.handlers.push(e),void 0!==this.currentValue&&e(this.currentValue)}}{constructor(){super(...arguments),this.map=new Map}handle(e){const{map:t}=this;let n=!1;e.isActive?(t.set(e.id,e),n=!0):t.has(e.id)&&(t.delete(e.id),n=!0),n&&this.set(t)}}};const xo=[],Oo={code:"en",week:{dow:0,doy:4},direction:"ltr",buttonText:{prev:"prev",next:"next",prevYear:"prev year",nextYear:"next year",year:"year",today:"today",month:"month",week:"week",day:"day",list:"list"},weekText:"W",weekTextLong:"Week",closeHint:"Close",timeHint:"Time",eventHint:"Event",allDayText:"all-day",moreLinkText:"more",noEventsText:"No events to display"},Mo=Object.assign(Object.assign({},Oo),{buttonHints:{prev:"Previous $0",next:"Next $0",today:(e,t)=>"day"===t?"Today":"This "+e},viewHint:"$0 view",navLinkHint:"Go to $0",moreLinkHint:e=>`Show ${e} more event${1===e?"":"s"}`});function Io(e){let t=e.length>0?e[0].code:"en",n=xo.concat(e),r={en:Mo};for(let e of n)r[e.code]=e;return{map:r,defaultCode:t}}function No(e,t){return"object"!=typeof e||Array.isArray(e)?function(e,t){let n=[].concat(e||[]),r=function(e,t){for(let n=0;n<e.length;n+=1){let r=e[n].toLocaleLowerCase().split("-");for(let e=r.length;e>0;e-=1){let n=r.slice(0,e).join("-");if(t[n])return t[n]}}return null}(n,t)||Mo;return Bo(e,n,r)}(e,t):Bo(e.code,[e.code],e)}function Bo(e,t,n){let r=z([Oo,n],["buttonText"]);delete r.code;let{week:i}=r;return delete r.week,{codeArg:e,codes:t,week:i,simpleNumberFormat:new Intl.NumberFormat(e),options:r}}function Ho(e){return{id:D(),name:e.name,premiumReleaseDate:e.premiumReleaseDate?new Date(e.premiumReleaseDate):void 0,deps:e.deps||[],reducers:e.reducers||[],isLoadingFuncs:e.isLoadingFuncs||[],contextInit:[].concat(e.contextInit||[]),eventRefiners:e.eventRefiners||{},eventDefMemberAdders:e.eventDefMemberAdders||[],eventSourceRefiners:e.eventSourceRefiners||{},isDraggableTransformers:e.isDraggableTransformers||[],eventDragMutationMassagers:e.eventDragMutationMassagers||[],eventDefMutationAppliers:e.eventDefMutationAppliers||[],dateSelectionTransformers:e.dateSelectionTransformers||[],datePointTransforms:e.datePointTransforms||[],dateSpanTransforms:e.dateSpanTransforms||[],views:e.views||{},viewPropsTransformers:e.viewPropsTransformers||[],isPropsValid:e.isPropsValid||null,externalDefTransforms:e.externalDefTransforms||[],viewContainerAppends:e.viewContainerAppends||[],eventDropTransformers:e.eventDropTransformers||[],componentInteractions:e.componentInteractions||[],calendarInteractions:e.calendarInteractions||[],themeClasses:e.themeClasses||{},eventSourceDefs:e.eventSourceDefs||[],cmdFormatter:e.cmdFormatter,recurringTypes:e.recurringTypes||[],namedTimeZonedImpl:e.namedTimeZonedImpl,initialView:e.initialView||"",elementDraggingImpl:e.elementDraggingImpl,optionChangeHandlers:e.optionChangeHandlers||{},scrollGridImpl:e.scrollGridImpl||null,listenerRefiners:e.listenerRefiners||{},optionRefiners:e.optionRefiners||{},propSetHandlers:e.propSetHandlers||{}}}function Po(){let e,t=[],n=[];return(r,i)=>(e&&$(r,t)&&$(i,n)||(e=function(e,t){let n={},r={premiumReleaseDate:void 0,reducers:[],isLoadingFuncs:[],contextInit:[],eventRefiners:{},eventDefMemberAdders:[],eventSourceRefiners:{},isDraggableTransformers:[],eventDragMutationMassagers:[],eventDefMutationAppliers:[],dateSelectionTransformers:[],datePointTransforms:[],dateSpanTransforms:[],views:{},viewPropsTransformers:[],isPropsValid:null,externalDefTransforms:[],viewContainerAppends:[],eventDropTransformers:[],componentInteractions:[],calendarInteractions:[],themeClasses:{},eventSourceDefs:[],cmdFormatter:null,recurringTypes:[],namedTimeZonedImpl:null,initialView:"",elementDraggingImpl:null,optionChangeHandlers:{},scrollGridImpl:null,listenerRefiners:{},optionRefiners:{},propSetHandlers:{}};function i(e){for(let s of e){const e=s.name,a=n[e];void 0===a?(n[e]=s.id,i(s.deps),o=s,r={premiumReleaseDate:Uo((t=r).premiumReleaseDate,o.premiumReleaseDate),reducers:t.reducers.concat(o.reducers),isLoadingFuncs:t.isLoadingFuncs.concat(o.isLoadingFuncs),contextInit:t.contextInit.concat(o.contextInit),eventRefiners:Object.assign(Object.assign({},t.eventRefiners),o.eventRefiners),eventDefMemberAdders:t.eventDefMemberAdders.concat(o.eventDefMemberAdders),eventSourceRefiners:Object.assign(Object.assign({},t.eventSourceRefiners),o.eventSourceRefiners),isDraggableTransformers:t.isDraggableTransformers.concat(o.isDraggableTransformers),eventDragMutationMassagers:t.eventDragMutationMassagers.concat(o.eventDragMutationMassagers),eventDefMutationAppliers:t.eventDefMutationAppliers.concat(o.eventDefMutationAppliers),dateSelectionTransformers:t.dateSelectionTransformers.concat(o.dateSelectionTransformers),datePointTransforms:t.datePointTransforms.concat(o.datePointTransforms),dateSpanTransforms:t.dateSpanTransforms.concat(o.dateSpanTransforms),views:Object.assign(Object.assign({},t.views),o.views),viewPropsTransformers:t.viewPropsTransformers.concat(o.viewPropsTransformers),isPropsValid:o.isPropsValid||t.isPropsValid,externalDefTransforms:t.externalDefTransforms.concat(o.externalDefTransforms),viewContainerAppends:t.viewContainerAppends.concat(o.viewContainerAppends),eventDropTransformers:t.eventDropTransformers.concat(o.eventDropTransformers),calendarInteractions:t.calendarInteractions.concat(o.calendarInteractions),componentInteractions:t.componentInteractions.concat(o.componentInteractions),themeClasses:Object.assign(Object.assign({},t.themeClasses),o.themeClasses),eventSourceDefs:t.eventSourceDefs.concat(o.eventSourceDefs),cmdFormatter:o.cmdFormatter||t.cmdFormatter,recurringTypes:t.recurringTypes.concat(o.recurringTypes),namedTimeZonedImpl:o.namedTimeZonedImpl||t.namedTimeZonedImpl,initialView:t.initialView||o.initialView,elementDraggingImpl:t.elementDraggingImpl||o.elementDraggingImpl,optionChangeHandlers:Object.assign(Object.assign({},t.optionChangeHandlers),o.optionChangeHandlers),scrollGridImpl:o.scrollGridImpl||t.scrollGridImpl,listenerRefiners:Object.assign(Object.assign({},t.listenerRefiners),o.listenerRefiners),optionRefiners:Object.assign(Object.assign({},t.optionRefiners),o.optionRefiners),propSetHandlers:Object.assign(Object.assign({},t.propSetHandlers),o.propSetHandlers)}):a!==s.id&&console.warn(`Duplicate plugin '${e}'`)}var t,o}return e&&i(e),i(t),r}(r,i)),t=r,n=i,e)}function Uo(e,t){return void 0===e?t:void 0===t?e:new Date(Math.max(e.valueOf(),t.valueOf()))}class jo extends Gt{}function zo(e,t,n,r){if(t[e])return t[e];let i=function(e,t,n,r){let i=n[e],o=r[e],s=e=>i&&null!==i[e]?i[e]:o&&null!==o[e]?o[e]:null,a=s("component"),l=s("superType"),c=null;if(l){if(l===e)throw new Error("Can't have a custom view type that references itself");c=zo(l,t,n,r)}!a&&c&&(a=c.component);if(!a)return null;return{type:e,component:a,defaults:Object.assign(Object.assign({},c?c.defaults:{}),i?i.rawOptions:{}),overrides:Object.assign(Object.assign({},c?c.overrides:{}),o?o.rawOptions:{})}}(e,t,n,r);return i&&(t[e]=i),i}function Fo(e){return L(e,Lo)}function Lo(e){let t="function"==typeof e?{component:e}:e,{component:n}=t;return t.content?n=Vo(t):!n||n.prototype instanceof Sr||(n=Vo(Object.assign(Object.assign({},t),{content:n}))),{superType:t.type,component:n,rawOptions:t}}function Vo(e){return t=>ln(Dr.Consumer,null,n=>ln(wi,{elTag:"div",elClasses:Co(n.viewSpec),renderProps:Object.assign(Object.assign({},t),{nextDayThreshold:n.options.nextDayThreshold}),generatorName:void 0,customGenerator:e.content,classNameGenerator:e.classNames,didMount:e.didMount,willUnmount:e.willUnmount}))}function Wo(e,t,n,r){let i=Fo(e),o=Fo(t.views);return L(function(e,t){let n,r={};for(n in e)zo(n,r,e,t);for(n in t)zo(n,r,e,t);return r}(i,o),e=>function(e,t,n,r,i){let o=e.overrides.duration||e.defaults.duration||r.duration||n.duration,s=null,a="",l="",c={};if(o&&(s=function(e){let t=JSON.stringify(e),n=Qo[t];void 0===n&&(n=N(e),Qo[t]=n);return n}(o),s)){let e=U(s);a=e.unit,1===e.value&&(l=a,c=t[a]?t[a].rawOptions:{})}let u=t=>{let n=t.buttonText||{},r=e.defaults.buttonTextKey;return null!=r&&null!=n[r]?n[r]:null!=n[e.type]?n[e.type]:null!=n[l]?n[l]:null},d=t=>{let n=t.buttonHints||{},r=e.defaults.buttonTextKey;return null!=r&&null!=n[r]?n[r]:null!=n[e.type]?n[e.type]:null!=n[l]?n[l]:null};return{type:e.type,component:e.component,duration:s,durationUnit:a,singleUnit:l,optionDefaults:e.defaults,optionOverrides:Object.assign(Object.assign({},c),e.overrides),buttonTextOverride:u(r)||u(n)||e.overrides.buttonText,buttonTextDefault:u(i)||e.defaults.buttonText||u(Ne)||e.type,buttonTitleOverride:d(r)||d(n)||e.overrides.buttonHint,buttonTitleDefault:d(i)||e.defaults.buttonHint||d(Ne)}}(e,o,t,n,r))}jo.prototype.classes={root:"fc-theme-standard",tableCellShaded:"fc-cell-shaded",buttonGroup:"fc-button-group",button:"fc-button fc-button-primary",buttonActive:"fc-button-active"},jo.prototype.baseIconClass="fc-icon",jo.prototype.iconClasses={close:"fc-icon-x",prev:"fc-icon-chevron-left",next:"fc-icon-chevron-right",prevYear:"fc-icon-chevrons-left",nextYear:"fc-icon-chevrons-right"},jo.prototype.rtlIconClasses={prev:"fc-icon-chevron-right",next:"fc-icon-chevron-left",prevYear:"fc-icon-chevrons-right",nextYear:"fc-icon-chevrons-left"},jo.prototype.iconOverrideOption="buttonIcons",jo.prototype.iconOverrideCustomButtonOption="icon",jo.prototype.iconOverridePrefix="fc-icon-";let Qo={};function Go(e,t,n){let r=t?t.activeRange:null;return Yo({},function(e,t){let n=To(t),r=[].concat(e.eventSources||[]),i=[];e.initialEvents&&r.unshift(e.initialEvents);e.events&&r.unshift(e.events);for(let e of r){let r=So(e,t,n);r&&i.push(r)}return i}(e,n),r,n)}function Zo(e,t,n,r){let i=n?n.activeRange:null;switch(t.type){case"ADD_EVENT_SOURCES":return Yo(e,t.sources,i,r);case"REMOVE_EVENT_SOURCE":return o=e,s=t.sourceId,F(o,e=>e.sourceId!==s);case"PREV":case"NEXT":case"CHANGE_DATE":case"CHANGE_VIEW_TYPE":return n?Jo(e,i,r):e;case"FETCH_EVENT_SOURCES":return $o(e,t.sourceIds?V(t.sourceIds):Ko(e,r),i,t.isRefetch||!1,r);case"RECEIVE_EVENTS":case"RECEIVE_EVENT_ERROR":return function(e,t,n,r){let i=e[t];if(i&&n===i.latestFetchId)return Object.assign(Object.assign({},e),{[t]:Object.assign(Object.assign({},i),{isFetching:!1,fetchRange:r})});return e}(e,t.sourceId,t.fetchId,t.fetchRange);case"REMOVE_ALL_EVENT_SOURCES":return{};default:return e}var o,s}function qo(e){for(let t in e)if(e[t].isFetching)return!0;return!1}function Yo(e,t,n,r){let i={};for(let e of t)i[e.sourceId]=e;return n&&(i=Jo(i,n,r)),Object.assign(Object.assign({},e),i)}function Jo(e,t,n){return $o(e,F(e,e=>function(e,t,n){if(!es(e,n))return!e.latestFetchId;return!n.options.lazyFetching||!e.fetchRange||e.isFetching||t.start<e.fetchRange.start||t.end>e.fetchRange.end}(e,t,n)),t,!1,n)}function $o(e,t,n,r,i){let o={};for(let s in e){let a=e[s];t[s]?o[s]=Xo(a,n,r,i):o[s]=a}return o}function Xo(e,t,n,r){let{options:i,calendarApi:o}=r,s=r.pluginHooks.eventSourceDefs[e.sourceDefId],a=D();return s.fetch({eventSource:e,range:t,isRefetch:n,context:r},n=>{let{rawEvents:s}=n;i.eventSourceSuccess&&(s=i.eventSourceSuccess.call(o,s,n.response)||s),e.success&&(s=e.success.call(o,s,n.response)||s),r.dispatch({type:"RECEIVE_EVENTS",sourceId:e.sourceId,fetchId:a,fetchRange:t,rawEvents:s})},n=>{let s=!1;i.eventSourceFailure&&(i.eventSourceFailure.call(o,n),s=!0),e.failure&&(e.failure(n),s=!0),s||console.warn(n.message,n),r.dispatch({type:"RECEIVE_EVENT_ERROR",sourceId:e.sourceId,fetchId:a,fetchRange:t,error:n})}),Object.assign(Object.assign({},e),{isFetching:!0,latestFetchId:a})}function Ko(e,t){return F(e,e=>es(e,t))}function es(e,t){return!t.pluginHooks.eventSourceDefs[e.sourceDefId].ignoreRange}function ts(e,t){switch(t.type){case"UNSELECT_DATES":return null;case"SELECT_DATES":return t.selection;default:return e}}function ns(e,t){switch(t.type){case"UNSELECT_EVENT":return"";case"SELECT_EVENT":return t.eventInstanceId;default:return e}}function rs(e,t){let n;switch(t.type){case"UNSET_EVENT_DRAG":return null;case"SET_EVENT_DRAG":return n=t.state,{affectedEvents:n.affectedEvents,mutatedEvents:n.mutatedEvents,isEvent:n.isEvent};default:return e}}function is(e,t){let n;switch(t.type){case"UNSET_EVENT_RESIZE":return null;case"SET_EVENT_RESIZE":return n=t.state,{affectedEvents:n.affectedEvents,mutatedEvents:n.mutatedEvents,isEvent:n.isEvent};default:return e}}function os(e,t,n,r,i){return{header:e.headerToolbar?ss(e.headerToolbar,e,t,n,r,i):null,footer:e.footerToolbar?ss(e.footerToolbar,e,t,n,r,i):null}}function ss(e,t,n,r,i,o){let s={},a=[],l=!1;for(let c in e){let u=as(e[c],t,n,r,i,o);s[c]=u.widgets,a.push(...u.viewsWithButtons),l=l||u.hasTitle}return{sectionWidgets:s,viewsWithButtons:a,hasTitle:l}}function as(e,t,n,r,i,o){let s="rtl"===t.direction,a=t.customButtons||{},l=n.buttonText||{},c=t.buttonText||{},u=n.buttonHints||{},d=t.buttonHints||{},f=e?e.split(" "):[],h=[],p=!1;return{widgets:f.map(e=>e.split(",").map(e=>{if("title"===e)return p=!0,{buttonName:e};let n,f,g,m,v,b;if(n=a[e])g=e=>{n.click&&n.click.call(e.target,e,e.target)},(m=r.getCustomButtonIconClass(n))||(m=r.getIconClass(e,s))||(v=n.text),b=n.hint||n.text;else if(f=i[e]){h.push(e),g=()=>{o.changeView(e)},(v=f.buttonTextOverride)||(m=r.getIconClass(e,s))||(v=f.buttonTextDefault);let n=f.buttonTextOverride||f.buttonTextDefault;b=R(f.buttonTitleOverride||f.buttonTitleDefault||t.viewHint,[n,e],n)}else if(o[e])if(g=()=>{o[e]()},(v=l[e])||(m=r.getIconClass(e,s))||(v=c[e]),"prevYear"===e||"nextYear"===e){let t="prevYear"===e?"prev":"next";b=R(u[t]||d[t],[c.year||"year","year"],c[e])}else b=t=>R(u[e]||d[e],[c[t]||t,t],c[e]);return{buttonName:e,buttonClick:g,buttonIcon:m,buttonText:v,buttonHint:b}})),viewsWithButtons:h,hasTitle:p}}class ls{constructor(e,t,n){this.type=e,this.getCurrentData=t,this.dateEnv=n}get calendar(){return this.getCurrentData().calendarApi}get title(){return this.getCurrentData().viewTitle}get activeStart(){return this.dateEnv.toDate(this.getCurrentData().dateProfile.activeRange.start)}get activeEnd(){return this.dateEnv.toDate(this.getCurrentData().dateProfile.activeRange.end)}get currentStart(){return this.dateEnv.toDate(this.getCurrentData().dateProfile.currentRange.start)}get currentEnd(){return this.dateEnv.toDate(this.getCurrentData().dateProfile.currentRange.end)}getOption(e){return this.getCurrentData().options[e]}}function cs(e,t){let n=W(t.getCurrentData().eventSources);if(1===n.length&&1===e.length&&Array.isArray(n[0]._raw)&&Array.isArray(e[0]))return void t.dispatch({type:"RESET_RAW_EVENTS",sourceId:n[0].sourceId,rawEvents:e[0]});let r=[];for(let t of e){let e=!1;for(let r=0;r<n.length;r+=1)if(n[r]._raw===t){n.splice(r,1),e=!0;break}e||r.push(t)}for(let e of n)t.dispatch({type:"REMOVE_EVENT_SOURCE",sourceId:e.sourceId});for(let e of r)t.calendarApi.addEventSource(e)}const us=[Ho({name:"array-event-source",eventSourceDefs:[{ignoreRange:!0,parseMeta:e=>Array.isArray(e.events)?e.events:null,fetch(e,t){t({rawEvents:e.eventSource.meta})}}]}),Ho({name:"func-event-source",eventSourceDefs:[{parseMeta:e=>"function"==typeof e.events?e.events:null,fetch(e,t,n){const{dateEnv:r}=e.context;Lt(e.eventSource.meta.bind(null,ei(e.range,r)),e=>t({rawEvents:e}),n)}}]}),Ho({name:"json-event-source",eventSourceRefiners:{method:String,extraParams:Ve,startParam:String,endParam:String,timeZoneParam:String},eventSourceDefs:[{parseMeta:e=>!e.url||"json"!==e.format&&e.format?null:{url:e.url,format:"json",method:(e.method||"GET").toUpperCase(),extraParams:e.extraParams,startParam:e.startParam,endParam:e.endParam,timeZoneParam:e.timeZoneParam},fetch(e,t,n){const{meta:r}=e.eventSource,i=function(e,t,n){let r,i,o,s,{dateEnv:a,options:l}=n,c={};r=e.startParam,null==r&&(r=l.startParam);i=e.endParam,null==i&&(i=l.endParam);o=e.timeZoneParam,null==o&&(o=l.timeZoneParam);s="function"==typeof e.extraParams?e.extraParams():e.extraParams||{};Object.assign(c,s),c[r]=a.formatIso(t.start),c[i]=a.formatIso(t.end),"local"!==a.timeZone&&(c[o]=a.timeZone);return c}(r,e.range,e.context);Gi(r.method,r.url,i).then(([e,n])=>{t({rawEvents:e,response:n})},n)}}]}),Ho({name:"simple-recurring-event",recurringTypes:[{parse(e,t){if(e.daysOfWeek||e.startTime||e.endTime||e.startRecur||e.endRecur){let i,o={daysOfWeek:e.daysOfWeek||null,startTime:e.startTime||null,endTime:e.endTime||null,startRecur:e.startRecur?t.createMarker(e.startRecur):null,endRecur:e.endRecur?t.createMarker(e.endRecur):null,dateEnv:t};return e.duration&&(i=e.duration),!i&&e.startTime&&e.endTime&&(n=e.endTime,r=e.startTime,i={years:n.years-r.years,months:n.months-r.months,days:n.days-r.days,milliseconds:n.milliseconds-r.milliseconds}),{allDayGuess:Boolean(!e.startTime&&!e.endTime),duration:i,typeData:o}}var n,r;return null},expand(e,t,n){let r=Ct(t,{start:e.startRecur,end:e.endRecur});return r?function(e,t,n,r,i){let o=e?V(e):null,s=oe(i.start),a=i.end,l=[];for(;s<a;){let e;o&&!o[s.getUTCDay()]||(e=t?r.add(s,t):s,l.push(r.createMarker(n.toDate(e)))),s=K(s,1)}return l}(e.daysOfWeek,e.startTime,e.dateEnv,n,r):[]}}],eventRefiners:{daysOfWeek:Ve,startTime:N,endTime:N,duration:N,startRecur:Ve,endRecur:Ve}}),Ho({name:"change-handler",optionChangeHandlers:{events(e,t){cs([e],t)},eventSources:cs}}),Ho({name:"misc",isLoadingFuncs:[e=>qo(e.eventSources)],propSetHandlers:{dateProfile:function(e,t){t.emitter.trigger("datesSet",Object.assign(Object.assign({},ei(e.activeRange,t.dateEnv)),{view:t.viewApi}))},eventStore:function(e,t){let{emitter:n}=t;n.hasHandlers("eventsSet")&&n.trigger("eventsSet",jr(e,t))}}})];class ds{constructor(e,t){this.runTaskOption=e,this.drainedOption=t,this.queue=[],this.delayedRunner=new Zi(this.drain.bind(this))}request(e,t){this.queue.push(e),this.delayedRunner.request(t)}pause(e){this.delayedRunner.pause(e)}resume(e,t){this.delayedRunner.resume(e,t)}drain(){let{queue:e}=this;for(;e.length;){let t,n=[];for(;t=e.shift();)this.runTask(t),n.push(t);this.drained(n)}}runTask(e){this.runTaskOption&&this.runTaskOption(e)}drained(e){this.drainedOption&&this.drainedOption(e)}}function fs(e,t,n){let r;return r=/^(year|month)$/.test(e.currentRangeUnit)?e.currentRange:e.activeRange,n.formatRange(r.start,r.end,Me(t.titleFormat||function(e){let{currentRangeUnit:t}=e;if("year"===t)return{year:"numeric"};if("month"===t)return{year:"numeric",month:"long"};let n=ie(e.currentRange.start,e.currentRange.end);if(null!==n&&n>1)return{year:"numeric",month:"short",day:"numeric"};return{year:"numeric",month:"long",day:"numeric"}}(e)),{isEndExclusive:e.isRangeAllDay,defaultSeparator:t.titleRangeSeparator})}class hs{constructor(){this.resetListeners=new Set}handleInput(e,t){const n=this.dateEnv;if(e!==n&&("function"==typeof t?this.nowFn=t:n||(this.nowAnchorDate=e.toDate(t?e.createMarker(t):e.createNowMarker()),this.nowAnchorQueried=Date.now()),this.dateEnv=e,n))for(const e of this.resetListeners.values())e()}getDateMarker(){return this.nowAnchorDate?this.dateEnv.timestampToMarker(this.nowAnchorDate.valueOf()+(Date.now()-this.nowAnchorQueried)):this.dateEnv.createMarker(this.nowFn())}addResetListener(e){this.resetListeners.add(e)}removeResetListener(e){this.resetListeners.delete(e)}}class ps{constructor(e){this.computeCurrentViewData=ve(this._computeCurrentViewData),this.organizeRawLocales=ve(Io),this.buildLocale=ve(No),this.buildPluginHooks=Po(),this.buildDateEnv=ve(gs),this.buildTheme=ve(ms),this.parseToolbars=ve(os),this.buildViewSpecs=ve(Wo),this.buildDateProfileGenerator=be(vs),this.buildViewApi=ve(bs),this.buildViewUiProps=be(_s),this.buildEventUiBySource=ve(As,Q),this.buildEventUiBases=ve(ys),this.parseContextBusinessHours=be(Ds),this.buildTitle=ve(fs),this.nowManager=new hs,this.emitter=new Vt,this.actionRunner=new ds(this._handleAction.bind(this),this.updateData.bind(this)),this.currentCalendarOptionsInput={},this.currentCalendarOptionsRefined={},this.currentViewOptionsInput={},this.currentViewOptionsRefined={},this.currentCalendarOptionsRefiners={},this.optionsForRefining=[],this.optionsForHandling=[],this.getCurrentData=()=>this.data,this.dispatch=e=>{this.actionRunner.request(e)},this.props=e,this.actionRunner.pause(),this.nowManager=new hs;let t={},n=this.computeOptionsData(e.optionOverrides,t,e.calendarApi),r=n.calendarOptions.initialView||n.pluginHooks.initialView,i=this.computeCurrentViewData(r,n,e.optionOverrides,t);e.calendarApi.currentDataManager=this,this.emitter.setThisContext(e.calendarApi),this.emitter.setOptions(i.options);let o={nowManager:this.nowManager,dateEnv:n.dateEnv,options:n.calendarOptions,pluginHooks:n.pluginHooks,calendarApi:e.calendarApi,dispatch:this.dispatch,emitter:this.emitter,getCurrentData:this.getCurrentData},s=function(e,t,n){let r=e.initialDate;return null!=r?t.createMarker(r):n.getDateMarker()}(n.calendarOptions,n.dateEnv,this.nowManager),a=i.dateProfileGenerator.build(s);kt(a.activeRange,s)||(s=a.currentRange.start);for(let e of n.pluginHooks.contextInit)e(o);let l=Go(n.calendarOptions,a,o),c={dynamicOptionOverrides:t,currentViewType:r,currentDate:s,dateProfile:a,businessHours:this.parseContextBusinessHours(o),eventSources:l,eventUiBases:{},eventStore:{defs:{},instances:{}},renderableEventStore:{defs:{},instances:{}},dateSelection:null,eventSelection:"",eventDrag:null,eventResize:null,selectionConfig:this.buildViewUiProps(o).selectionConfig},u=Object.assign(Object.assign({},o),c);for(let e of n.pluginHooks.reducers)Object.assign(c,e(null,null,u));Es(c,o)&&this.emitter.trigger("loading",!0),this.state=c,this.updateData(),this.actionRunner.resume()}resetOptions(e,t){let{props:n}=this;void 0===t?n.optionOverrides=e:(n.optionOverrides=Object.assign(Object.assign({},n.optionOverrides||{}),e),this.optionsForRefining.push(...t)),(void 0===t||t.length)&&this.actionRunner.request({type:"NOTHING"})}_handleAction(e){let{props:t,state:n,emitter:r}=this,i=function(e,t){switch(t.type){case"SET_OPTION":return Object.assign(Object.assign({},e),{[t.optionName]:t.rawOptionValue});default:return e}}(n.dynamicOptionOverrides,e),o=this.computeOptionsData(t.optionOverrides,i,t.calendarApi),s=function(e,t){switch(t.type){case"CHANGE_VIEW_TYPE":e=t.viewType}return e}(n.currentViewType,e),a=this.computeCurrentViewData(s,o,t.optionOverrides,i);t.calendarApi.currentDataManager=this,r.setThisContext(t.calendarApi),r.setOptions(a.options);let l={nowManager:this.nowManager,dateEnv:o.dateEnv,options:o.calendarOptions,pluginHooks:o.pluginHooks,calendarApi:t.calendarApi,dispatch:this.dispatch,emitter:r,getCurrentData:this.getCurrentData},{currentDate:c,dateProfile:u}=n;this.data&&this.data.dateProfileGenerator!==a.dateProfileGenerator&&(u=a.dateProfileGenerator.build(c)),c=function(e,t){switch(t.type){case"CHANGE_DATE":return t.dateMarker;default:return e}}(c,e),u=function(e,t,n,r){let i;switch(t.type){case"CHANGE_VIEW_TYPE":return r.build(t.dateMarker||n);case"CHANGE_DATE":return r.build(t.dateMarker);case"PREV":if(i=r.buildPrev(e,n),i.isValid)return i;break;case"NEXT":if(i=r.buildNext(e,n),i.isValid)return i}return e}(u,e,c,a.dateProfileGenerator),"PREV"!==e.type&&"NEXT"!==e.type&&kt(u.currentRange,c)||(c=u.currentRange.start);let d=Zo(n.eventSources,e,u,l),f=Ni(n.eventStore,e,d,u,l),h=qo(d)&&!a.options.progressiveEventRendering&&n.renderableEventStore||f,{eventUiSingleBase:p,selectionConfig:g}=this.buildViewUiProps(l),m=this.buildEventUiBySource(d),v={dynamicOptionOverrides:i,currentViewType:s,currentDate:c,dateProfile:u,eventSources:d,eventStore:f,renderableEventStore:h,selectionConfig:g,eventUiBases:this.buildEventUiBases(h.defs,p,m),businessHours:this.parseContextBusinessHours(l),dateSelection:ts(n.dateSelection,e),eventSelection:ns(n.eventSelection,e),eventDrag:rs(n.eventDrag,e),eventResize:is(n.eventResize,e)},b=Object.assign(Object.assign({},l),v);for(let t of o.pluginHooks.reducers)Object.assign(v,t(n,e,b));let A=Es(n,l),y=Es(v,l);!A&&y?r.trigger("loading",!0):A&&!y&&r.trigger("loading",!1),this.state=v,t.onAction&&t.onAction(e)}updateData(){let{props:e,state:t}=this,n=this.data,r=this.computeOptionsData(e.optionOverrides,t.dynamicOptionOverrides,e.calendarApi),i=this.computeCurrentViewData(t.currentViewType,r,e.optionOverrides,t.dynamicOptionOverrides),o=this.data=Object.assign(Object.assign(Object.assign({nowManager:this.nowManager,viewTitle:this.buildTitle(t.dateProfile,i.options,r.dateEnv),calendarApi:e.calendarApi,dispatch:this.dispatch,emitter:this.emitter,getCurrentData:this.getCurrentData},r),i),t),s=r.pluginHooks.optionChangeHandlers,a=n&&n.calendarOptions,l=r.calendarOptions;if(a&&a!==l){a.timeZone!==l.timeZone&&(t.eventSources=o.eventSources=function(e,t,n){let r=t?t.activeRange:null;return $o(e,Ko(e,n),r,!0,n)}(o.eventSources,t.dateProfile,o),t.eventStore=o.eventStore=Pi(o.eventStore,n.dateEnv,o.dateEnv),t.renderableEventStore=o.renderableEventStore=Pi(o.renderableEventStore,n.dateEnv,o.dateEnv));for(let e in s)-1===this.optionsForHandling.indexOf(e)&&a[e]===l[e]||s[e](l[e],o)}this.optionsForHandling=[],e.onData&&e.onData(o)}computeOptionsData(e,t,n){if(!this.optionsForRefining.length&&e===this.stableOptionOverrides&&t===this.stableDynamicOptionOverrides)return this.stableCalendarOptionsData;let{refinedOptions:r,pluginHooks:i,localeDefaults:o,availableLocaleData:s,extra:a}=this.processRawCalendarOptions(e,t);Cs(a);let l=this.buildDateEnv(r.timeZone,r.locale,r.weekNumberCalculation,r.firstDay,r.weekText,i,s,r.defaultRangeSeparator),c=this.buildViewSpecs(i.views,this.stableOptionOverrides,this.stableDynamicOptionOverrides,o),u=this.buildTheme(r,i),d=this.parseToolbars(r,this.stableOptionOverrides,u,c,n);return this.stableCalendarOptionsData={calendarOptions:r,pluginHooks:i,dateEnv:l,viewSpecs:c,theme:u,toolbarConfig:d,localeDefaults:o,availableRawLocales:s.map}}processRawCalendarOptions(e,t){let{locales:n,locale:r}=Fe([Ne,e,t]),i=this.organizeRawLocales(n),o=i.map,s=this.buildLocale(r||i.defaultCode,o).options,a=this.buildPluginHooks(e.plugins||[],us),l=this.currentCalendarOptionsRefiners=Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},Ie),Be),He),a.listenerRefiners),a.optionRefiners),c={},u=Fe([Ne,s,e,t]),d={},f=this.currentCalendarOptionsInput,h=this.currentCalendarOptionsRefined,p=!1;for(let e in u)-1===this.optionsForRefining.indexOf(e)&&(u[e]===f[e]||Pe[e]&&e in f&&Pe[e](f[e],u[e]))?d[e]=h[e]:l[e]?(d[e]=l[e](u[e]),p=!0):c[e]=f[e];return p&&(this.currentCalendarOptionsInput=u,this.currentCalendarOptionsRefined=d,this.stableOptionOverrides=e,this.stableDynamicOptionOverrides=t),this.optionsForHandling.push(...this.optionsForRefining),this.optionsForRefining=[],{rawOptions:this.currentCalendarOptionsInput,refinedOptions:this.currentCalendarOptionsRefined,pluginHooks:a,availableLocaleData:i,localeDefaults:s,extra:c}}_computeCurrentViewData(e,t,n,r){let i=t.viewSpecs[e];if(!i)throw new Error(`viewType "${e}" is not available. Please make sure you've loaded all neccessary plugins`);let{refinedOptions:o,extra:s}=this.processRawViewOptions(i,t.pluginHooks,t.localeDefaults,n,r);return Cs(s),this.nowManager.handleInput(t.dateEnv,o.now),{viewSpec:i,options:o,dateProfileGenerator:this.buildDateProfileGenerator({dateProfileGeneratorClass:i.optionDefaults.dateProfileGeneratorClass,nowManager:this.nowManager,duration:i.duration,durationUnit:i.durationUnit,usesMinMaxTime:i.optionDefaults.usesMinMaxTime,dateEnv:t.dateEnv,calendarApi:this.props.calendarApi,slotMinTime:o.slotMinTime,slotMaxTime:o.slotMaxTime,showNonCurrentDates:o.showNonCurrentDates,dayCount:o.dayCount,dateAlignment:o.dateAlignment,dateIncrement:o.dateIncrement,hiddenDays:o.hiddenDays,weekends:o.weekends,validRangeInput:o.validRange,visibleRangeInput:o.visibleRange,fixedWeekCount:o.fixedWeekCount}),viewApi:this.buildViewApi(e,this.getCurrentData,t.dateEnv)}}processRawViewOptions(e,t,n,r,i){let o=Fe([Ne,e.optionDefaults,n,r,e.optionOverrides,i]),s=Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},Ie),Be),He),ze),t.listenerRefiners),t.optionRefiners),a={},l=this.currentViewOptionsInput,c=this.currentViewOptionsRefined,u=!1,d={};for(let e in o)o[e]===l[e]||Pe[e]&&Pe[e](o[e],l[e])?a[e]=c[e]:(o[e]===this.currentCalendarOptionsInput[e]||Pe[e]&&Pe[e](o[e],this.currentCalendarOptionsInput[e])?e in this.currentCalendarOptionsRefined&&(a[e]=this.currentCalendarOptionsRefined[e]):s[e]?a[e]=s[e](o[e]):d[e]=o[e],u=!0);return u&&(this.currentViewOptionsInput=o,this.currentViewOptionsRefined=a),{rawOptions:this.currentViewOptionsInput,refinedOptions:this.currentViewOptionsRefined,extra:d}}}function gs(e,t,n,r,i,o,s,a){let l=No(t||s.defaultCode,s.map);return new ai({calendarSystem:"gregory",timeZone:e,namedTimeZoneImpl:o.namedTimeZonedImpl,locale:l,weekNumberCalculation:n,firstDay:r,weekText:i,cmdFormatter:o.cmdFormatter,defaultSeparator:a})}function ms(e,t){return new(t.themeClasses[e.themeSystem]||jo)(e)}function vs(e){return new(e.dateProfileGeneratorClass||Rr)(e)}function bs(e,t,n){return new ls(e,t,n)}function As(e){return L(e,e=>e.ui)}function ys(e,t,n){let r={"":t};for(let t in e){let i=e[t];i.sourceId&&n[i.sourceId]&&(r[t]=n[i.sourceId])}return r}function _s(e){let{options:t}=e;return{eventUiSingleBase:nt({display:t.eventDisplay,editable:t.editable,startEditable:t.eventStartEditable,durationEditable:t.eventDurationEditable,constraint:t.eventConstraint,overlap:"boolean"==typeof t.eventOverlap?t.eventOverlap:void 0,allow:t.eventAllow,backgroundColor:t.eventBackgroundColor,borderColor:t.eventBorderColor,textColor:t.eventTextColor,color:t.eventColor},e),selectionConfig:nt({constraint:t.selectConstraint,overlap:"boolean"==typeof t.selectOverlap?t.selectOverlap:void 0,allow:t.selectAllow},e)}}function Es(e,t){for(let n of t.pluginHooks.isLoadingFuncs)if(n(e))return!0;return!1}function Ds(e){return ht(e.options.businessHours,e)}function Cs(e,t){for(let n in e)console.warn(`Unknown option '${n}'`+(t?` for view '${t}'`:""))}class ws extends Sr{render(){return ln("div",{className:"fc-toolbar-chunk"},...this.props.widgetGroups.map(e=>this.renderWidgetGroup(e)))}renderWidgetGroup(e){let{props:t}=this,{theme:n}=this.context,r=[],i=!0;for(let o of e){let{buttonName:e,buttonClick:s,buttonText:a,buttonIcon:l,buttonHint:c}=o;if("title"===e)i=!1,r.push(ln("h2",{className:"fc-toolbar-title",id:t.titleId},t.title));else{let i=e===t.activeButton,o=!t.isTodayEnabled&&"today"===e||!t.isPrevEnabled&&"prev"===e||!t.isNextEnabled&&"next"===e,u=[`fc-${e}-button`,n.getClass("button")];i&&u.push(n.getClass("buttonActive")),r.push(ln("button",{type:"button",title:"function"==typeof c?c(t.navUnit):c,disabled:o,"aria-pressed":i,className:u.join(" "),onClick:s},a||(l?ln("span",{className:l,role:"img"}):"")))}}if(r.length>1){return ln("div",{className:i&&n.getClass("buttonGroup")||""},...r)}return r[0]}}class Ss extends Sr{render(){let e,t,{model:n,extraClassName:r}=this.props,i=!1,o=n.sectionWidgets,s=o.center;return o.left?(i=!0,e=o.left):e=o.start,o.right?(i=!0,t=o.right):t=o.end,ln("div",{className:[r||"","fc-toolbar",i?"fc-toolbar-ltr":""].join(" ")},this.renderSection("start",e||[]),this.renderSection("center",s||[]),this.renderSection("end",t||[]))}renderSection(e,t){let{props:n}=this;return ln(ws,{key:e,widgetGroups:t,title:n.title,navUnit:n.navUnit,activeButton:n.activeButton,isTodayEnabled:n.isTodayEnabled,isPrevEnabled:n.isPrevEnabled,isNextEnabled:n.isNextEnabled,titleId:n.titleId})}}class Ts extends Sr{constructor(){super(...arguments),this.state={availableWidth:null},this.handleEl=e=>{this.el=e,Tr(this.props.elRef,e),this.updateAvailableWidth()},this.handleResize=()=>{this.updateAvailableWidth()}}render(){let{props:e,state:t}=this,{aspectRatio:n}=e,r=["fc-view-harness",n||e.liquid||e.height?"fc-view-harness-active":"fc-view-harness-passive"],i="",o="";return n?null!==t.availableWidth?i=t.availableWidth/n:o=1/n*100+"%":i=e.height||"",ln("div",{"aria-labelledby":e.labeledById,ref:this.handleEl,className:r.join(" "),style:{height:i,paddingBottom:o}},e.children)}componentDidMount(){this.context.addResizeHandler(this.handleResize)}componentWillUnmount(){this.context.removeResizeHandler(this.handleResize)}updateAvailableWidth(){this.el&&this.props.aspectRatio&&this.setState({availableWidth:this.el.offsetWidth})}}class ks extends hi{constructor(e){super(e),this.handleSegClick=(e,t)=>{let{component:n}=this,{context:r}=n,i=Lr(t);if(i&&n.isValidSegDownEl(e.target)){let o=l(e.target,".fc-event-forced-url"),s=o?o.querySelector("a[href]").href:"";r.emitter.trigger("eventClick",{el:t,event:new Pr(n.context,i.eventRange.def,i.eventRange.instance),jsEvent:e,view:r.viewApi}),s&&!e.defaultPrevented&&(window.location.href=s)}},this.destroy=b(e.el,"click",".fc-event",this.handleSegClick)}}class Rs extends hi{constructor(e){super(e),this.handleEventElRemove=e=>{e===this.currentSegEl&&this.handleSegLeave(null,this.currentSegEl)},this.handleSegEnter=(e,t)=>{Lr(t)&&(this.currentSegEl=t,this.triggerEvent("eventMouseEnter",e,t))},this.handleSegLeave=(e,t)=>{this.currentSegEl&&(this.currentSegEl=null,this.triggerEvent("eventMouseLeave",e,t))},this.removeHoverListeners=function(e,t,n,r){let i;return b(e,"mouseover",t,(e,t)=>{if(t!==i){i=t,n(e,t);let o=e=>{i=null,r(e,t),t.removeEventListener("mouseleave",o)};t.addEventListener("mouseleave",o)}})}(e.el,".fc-event",this.handleSegEnter,this.handleSegLeave)}destroy(){this.removeHoverListeners()}triggerEvent(e,t,n){let{component:r}=this,{context:i}=r,o=Lr(n);t&&!r.isValidSegDownEl(t.target)||i.emitter.trigger(e,{el:n,event:new Pr(i,o.eventRange.def,o.eventRange.instance),jsEvent:t,view:i.viewApi})}}class xs extends wr{constructor(){super(...arguments),this.buildViewContext=ve(Cr),this.buildViewPropTransformers=ve(Ms),this.buildToolbarProps=ve(Os),this.headerRef={current:null},this.footerRef={current:null},this.interactionsStore={},this.state={viewLabelId:m()},this.registerInteractiveComponent=(e,t)=>{let n=function(e,t){return{component:e,el:t.el,useEventCenter:null==t.useEventCenter||t.useEventCenter,isHitComboAllowed:t.isHitComboAllowed||null}}(e,t),r=[ks,Rs].concat(this.props.pluginHooks.componentInteractions).map(e=>new e(n));this.interactionsStore[e.uid]=r,pi[e.uid]=n},this.unregisterInteractiveComponent=e=>{let t=this.interactionsStore[e.uid];if(t){for(let e of t)e.destroy();delete this.interactionsStore[e.uid]}delete pi[e.uid]},this.resizeRunner=new Zi(()=>{this.props.emitter.trigger("_resize",!0),this.props.emitter.trigger("windowResize",{view:this.props.viewApi})}),this.handleWindowResize=e=>{let{options:t}=this.props;t.handleWindowResize&&e.target===window&&this.resizeRunner.request(t.windowResizeDelay)}}render(){let e,{props:t}=this,{toolbarConfig:n,options:r}=t,i=!1,o="";t.isHeightAuto||t.forPrint?o="":null!=r.height?i=!0:null!=r.contentHeight?o=r.contentHeight:e=Math.max(r.aspectRatio,.5);let s=this.buildViewContext(t.viewSpec,t.viewApi,t.options,t.dateProfileGenerator,t.dateEnv,t.nowManager,t.theme,t.pluginHooks,t.dispatch,t.getCurrentData,t.emitter,t.calendarApi,this.registerInteractiveComponent,this.unregisterInteractiveComponent),a=n.header&&n.header.hasTitle?this.state.viewLabelId:void 0;return ln(Dr.Provider,{value:s},ln(xi,{unit:"day"},r=>{let s=this.buildToolbarProps(t.viewSpec,t.dateProfile,t.dateProfileGenerator,t.currentDate,r,t.viewTitle);return ln(dn,null,n.header&&ln(Ss,Object.assign({ref:this.headerRef,extraClassName:"fc-header-toolbar",model:n.header,titleId:a},s)),ln(Ts,{liquid:i,height:o,aspectRatio:e,labeledById:a},this.renderView(t),this.buildAppendContent()),n.footer&&ln(Ss,Object.assign({ref:this.footerRef,extraClassName:"fc-footer-toolbar",model:n.footer,titleId:""},s)))}))}componentDidMount(){let{props:e}=this;this.calendarInteractions=e.pluginHooks.calendarInteractions.map(t=>new t(e)),window.addEventListener("resize",this.handleWindowResize);let{propSetHandlers:t}=e.pluginHooks;for(let n in t)t[n](e[n],e)}componentDidUpdate(e){let{props:t}=this,{propSetHandlers:n}=t.pluginHooks;for(let r in n)t[r]!==e[r]&&n[r](t[r],t)}componentWillUnmount(){window.removeEventListener("resize",this.handleWindowResize),this.resizeRunner.clear();for(let e of this.calendarInteractions)e.destroy();this.props.emitter.trigger("_unmount")}buildAppendContent(){let{props:e}=this;return ln(dn,{},...e.pluginHooks.viewContainerAppends.map(t=>t(e)))}renderView(e){let{pluginHooks:t}=e,{viewSpec:n}=e,r={dateProfile:e.dateProfile,businessHours:e.businessHours,eventStore:e.renderableEventStore,eventUiBases:e.eventUiBases,dateSelection:e.dateSelection,eventSelection:e.eventSelection,eventDrag:e.eventDrag,eventResize:e.eventResize,isHeightAuto:e.isHeightAuto,forPrint:e.forPrint},i=this.buildViewPropTransformers(t.viewPropsTransformers);for(let t of i)Object.assign(r,t.transform(r,e));return ln(n.component,Object.assign({},r))}}function Os(e,t,n,r,i,o){let s=n.build(i,void 0,!1),a=n.buildPrev(t,r,!1),l=n.buildNext(t,r,!1);return{title:o,activeButton:e.type,navUnit:e.singleUnit,isTodayEnabled:s.isValid&&!kt(t.currentRange,i),isPrevEnabled:a.isValid,isNextEnabled:l.isValid}}function Ms(e){return e.map(e=>new e)}function Is(e){let t=No(e.locale||"en",Io([]).map);return new ai(Object.assign(Object.assign({timeZone:Ne.timeZone,calendarSystem:"gregory"},e),{locale:t}))}return e.Calendar=class extends ko{constructor(e,t={}){super(),this.isRendering=!1,this.isRendered=!1,this.currentClassNames=[],this.customContentRenderId=0,this.handleAction=e=>{switch(e.type){case"SET_EVENT_DRAG":case"SET_EVENT_RESIZE":this.renderRunner.tryDrain()}},this.handleData=e=>{this.currentData=e,this.renderRunner.request(e.calendarOptions.rerenderDelay)},this.handleRenderRequest=()=>{if(this.isRendering){this.isRendered=!0;let{currentData:e}=this;br(()=>{In(ln(mi,{options:e.calendarOptions,theme:e.theme,emitter:e.emitter},(t,n,r,i)=>(this.setClassNames(t),this.setHeight(n),ln(Ci.Provider,{value:this.customContentRenderId},ln(xs,Object.assign({isHeightAuto:r,forPrint:i},e))))),this.el)})}else this.isRendered&&(this.isRendered=!1,In(null,this.el),this.setClassNames([]),this.setHeight(""))},function(e){e.isConnected&&e.getRootNode&&i(e.getRootNode())}(e),this.el=e,this.renderRunner=new Zi(this.handleRenderRequest),new ps({optionOverrides:t,calendarApi:this,onAction:this.handleAction,onData:this.handleData})}render(){let e=this.isRendering;e?this.customContentRenderId+=1:this.isRendering=!0,this.renderRunner.request(),e&&this.updateSize()}destroy(){this.isRendering&&(this.isRendering=!1,this.renderRunner.request())}updateSize(){br(()=>{super.updateSize()})}batchRendering(e){this.renderRunner.pause("batchRendering"),e(),this.renderRunner.resume("batchRendering")}pauseRendering(){this.renderRunner.pause("pauseRendering")}resumeRendering(){this.renderRunner.resume("pauseRendering",!0)}resetOptions(e,t){this.currentDataManager.resetOptions(e,t)}setClassNames(e){if(!$(e,this.currentClassNames)){let{classList:t}=this.el;for(let e of this.currentClassNames)t.remove(e);for(let n of e)t.add(n);this.currentClassNames=e}}setHeight(e){h(this.el,"height",e)}},e.Internal=Ro,e.JsonRequestError=Qi,e.Preact=_r,e.createPlugin=Ho,e.formatDate=function(e,t={}){let n=Is(t),r=Me(t),i=n.createMarkerMeta(e);return i?n.format(i.marker,r,{forcedTzo:i.forcedTzo}):""},e.formatRange=function(e,t,n){let r=Is("object"==typeof n&&n?n:{}),i=Me(n),o=r.createMarkerMeta(e),s=r.createMarkerMeta(t);return o&&s?r.formatRange(o.marker,s.marker,i,{forcedStartTzo:o.forcedTzo,forcedEndTzo:s.forcedTzo,isEndExclusive:n.isEndExclusive,defaultSeparator:Ne.defaultRangeSeparator}):""},e.globalLocales=xo,e.globalPlugins=us,e.sliceEvents=function(e,t){return zr(e.eventStore,e.eventUiBases,e.dateProfile.activeRange,t?e.nextDayThreshold:null).fg},e.version="6.1.17",Object.defineProperty(e,"__esModule",{value:!0}),e}({});