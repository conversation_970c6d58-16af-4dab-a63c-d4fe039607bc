import"../../admin.bundle-DI9_jvUJ.js";import"../../main-Cyta4iCA.js";function r(t,e){document.getElementById(t).textContent=e}function d(t){t.forEach(e=>r(e,""))}document.getElementById("cardPayBtn").addEventListener("click",function(){const t=document.getElementById("cardHolderNameInput").value.trim(),e=document.getElementById("debitCreditCardInput").value.replace(/\s/g,""),c=document.getElementById("expiryDateMask").value.trim(),a=document.getElementById("cvvInput").value.trim();d(["cardHolderNameError","cardNumberError","expiryError","cvvError"]);let n=!0;t===""&&(r("cardHolderNameError","Card holder name is required."),n=!1),/^\d{16}$/.test(e)||(r("cardNumberError","Card number must be 16 digits."),n=!1),/^\d{2}\/\d{2}$/.test(c)||(r("expiryError","Expiry must be in MM/YY format."),n=!1),/^\d{3}$/.test(a)||(r("cvvError","CVV must be 3 digits."),n=!1),n&&new window.bootstrap.Modal(document.getElementById("paySuccessModal")).show()});document.getElementById("bankPayBtn").addEventListener("click",function(){const t=document.getElementById("bankHolderNameInput").value.trim(),e=document.getElementById("accountNumberInput").value.trim(),c=document.getElementById("confirmAccountNumber").value.trim(),a=document.getElementById("ifscCodeInput").value.trim().toUpperCase(),n=document.getElementById("bankNameInput").value.trim();d(["bankNameError","accountNumberError","confirmAccountError","ifscError","bankInputError"]);let o=!0;t===""&&(r("bankNameError","Bank holder name is required."),o=!1),/^\d{9,18}$/.test(e)||(r("accountNumberError","Account number must be between 9-18 digits."),o=!1),e!==c&&(r("confirmAccountError","Account numbers do not match."),o=!1),/^[A-Z]{4}0[A-Z0-9]{6}$/.test(a)||(r("ifscError","Invalid IFSC code."),o=!1),n===""&&(r("bankInputError","Bank name is required."),o=!1),o&&new window.bootstrap.Modal(document.getElementById("paySuccessModal")).show()});document.getElementById("debitCreditCardInput").addEventListener("input",function(t){const e=t.target.value.replace(/\D/g,"").slice(0,16);t.target.value=e.replace(/(.{4})/g,"$1 ").trim()});document.getElementById("expiryDateMask").addEventListener("input",function(t){const e=t.target.value.replace(/\D/g,"").slice(0,4);t.target.value=e.length>=3?e.slice(0,2)+"/"+e.slice(2):e});
